<div class="county-detail-container">
  <h2>{{ countyId }} 立法委員</h2>

  <!-- 罷免狀態篩選器 - 暫時隱藏 -->
  <!-- <div class="recall-status-filter" *ngIf="!loading">
    篩選功能待實現
  </div> -->

  <div class="loading" *ngIf="loading">
    載入中...
  </div>

  <div class="politicians-container" *ngIf="!loading">
    <div class="politician-card" *ngFor="let politician of filteredPoliticians" (click)="goToPolitician(politician)">
      <!-- 立委照片 -->
      <div class="politician-photo">
        <img
          [src]="politician.image_url"
          [alt]="politician.name"
          (error)="onImageError($event)"
        />
      </div>

      <!-- 立委姓名 -->
      <div class="politician-name">
        {{ politician.name }}
      </div>

      <!-- 選區資訊 -->
      <div class="politician-district">
        {{ politician.district }}
      </div>

      <!-- 罷免狀態 - 從 status 欄位讀取 -->
      <div class="recall-status" [class]="getRecallStatusClass(politician.status)">
        <i [class]="getRecallStatusIcon(politician.status)"></i>
        {{ getRecallStatusText(politician.status) }}
      </div>
    </div>
  </div>

  <!-- 如果沒有資料 -->
  <div class="no-data" *ngIf="!loading && politicians.length === 0">
    目前沒有 {{ countyId }} 的立委資料
  </div>
</div>
