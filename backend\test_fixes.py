#!/usr/bin/env python3
"""
測試修復後的Gemini API和MongoDB存儲功能
"""

import os
import sys

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

def test_gemini_api():
    """測試Gemini API修復"""
    print("測試Gemini API...")
    try:
        import google.generativeai as genai

        # 檢查API key
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("缺少GEMINI_API_KEY環境變數")
            return False

        # 配置API
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-pro')

        print("Gemini API配置成功")
        return True

    except Exception as e:
        print(f"Gemini API測試失敗: {e}")
        return False

def test_mongodb_connection():
    """測試MongoDB連接"""
    print("測試MongoDB連接...")
    try:
        from crawler.data_to_mongo_v2 import DataToMongo

        mongo_manager = DataToMongo()

        # 測試process_legislators方法是否存在
        if hasattr(mongo_manager, 'process_legislators'):
            print("process_legislators方法存在")

            # 測試空列表調用
            result = mongo_manager.process_legislators([])
            print(f"MongoDB測試成功: {result}")
            return True
        else:
            print("process_legislators方法不存在")
            return False

    except Exception as e:
        print(f"MongoDB測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("開始測試修復...")

    gemini_ok = test_gemini_api()
    mongodb_ok = test_mongodb_connection()

    if gemini_ok and mongodb_ok:
        print("所有測試通過！")
        return 0
    else:
        print("部分測試失敗")
        return 1

if __name__ == "__main__":
    sys.exit(main())
