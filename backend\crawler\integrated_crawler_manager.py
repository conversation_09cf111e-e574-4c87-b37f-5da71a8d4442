#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合爬蟲管理器
實現兩種模式：
1. 全量爬取模式（400天資料）
2. 增量爬取模式（前一天資料）

流程：
1. URL收集階段：並行收集所有平台URL
2. 內容爬取階段：逐平台多線程爬取內容
3. 資料合併階段：合併到process/alldata
4. 用戶統計階段：統計到user_data
5. 情感分析階段：使用Gemini分析
6. 資料存儲階段：存儲到MongoDB
7. 統計更新階段：更新legislators統計
"""

import os
import sys
import json
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 導入各個模組
from multi_platform_crawler import MultiPlatformCrawler
from user_data_processor import process_legislators_data
from gemini_emo_user import analyze_legislators_emotions, analyze_legislators_emotions_incremental
from data_to_mongo_v2 import DataToMongo
from legislator_stats import LegislatorStatsUpdater

class IntegratedCrawlerManager:
    """整合爬蟲管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.platforms = ['youtube', 'ptt', 'threads']  # 預設平台
        self.crawler = MultiPlatformCrawler()
        
    def crawl_all_legislators_full(self, legislators: List[str], days: int = 400, platforms: List[str] = None) -> Dict:
        """
        全量爬取模式：爬取所有立委指定天數的資料
        
        Args:
            legislators: 立委名單
            days: 爬取天數（預設400天）
            platforms: 平台列表（預設使用所有平台）
        
        Returns:
            Dict: 爬取結果統計
        """
        self.logger.info(f"🚀 開始全量爬取模式：{len(legislators)} 位立委，{days} 天資料")
        
        platforms = platforms or self.platforms
        cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
        
        results = {
            'mode': 'full',
            'legislators_processed': 0,
            'legislators_failed': 0,
            'total_legislators': len(legislators),
            'platforms': platforms,
            'cutoff_date': cutoff_date,
            'details': {}
        }
        
        for i, legislator in enumerate(legislators, 1):
            self.logger.info(f"📊 處理立委 {i}/{len(legislators)}: {legislator}")
            
            try:
                # 執行完整流程
                legislator_result = self._process_single_legislator_full(
                    legislator, cutoff_date, platforms
                )
                
                results['details'][legislator] = legislator_result
                
                if legislator_result.get('success', False):
                    results['legislators_processed'] += 1
                    self.logger.info(f"✅ {legislator} 處理完成")
                else:
                    results['legislators_failed'] += 1
                    self.logger.error(f"❌ {legislator} 處理失敗: {legislator_result.get('error', '未知錯誤')}")
                    
            except Exception as e:
                self.logger.error(f"❌ {legislator} 處理異常: {e}")
                results['legislators_failed'] += 1
                results['details'][legislator] = {'success': False, 'error': str(e)}
        
        # 最終統計更新
        try:
            self._update_final_statistics()
            results['statistics_updated'] = True
        except Exception as e:
            self.logger.error(f"統計更新失敗: {e}")
            results['statistics_updated'] = False
            
        return results
    
    def crawl_all_legislators_incremental(self, legislators: List[str], platforms: List[str] = None) -> Dict:
        """
        增量爬取模式：爬取所有立委前一天的資料
        
        Args:
            legislators: 立委名單
            platforms: 平台列表（預設使用所有平台）
        
        Returns:
            Dict: 爬取結果統計
        """
        self.logger.info(f"🔄 開始增量爬取模式：{len(legislators)} 位立委，前一天資料")
        
        platforms = platforms or self.platforms
        cutoff_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        results = {
            'mode': 'incremental',
            'legislators_processed': 0,
            'legislators_failed': 0,
            'total_legislators': len(legislators),
            'platforms': platforms,
            'cutoff_date': cutoff_date,
            'details': {}
        }
        
        for i, legislator in enumerate(legislators, 1):
            self.logger.info(f"📊 處理立委 {i}/{len(legislators)}: {legislator}")
            
            try:
                # 執行增量流程
                legislator_result = self._process_single_legislator_incremental(
                    legislator, cutoff_date, platforms
                )
                
                results['details'][legislator] = legislator_result
                
                if legislator_result.get('success', False):
                    results['legislators_processed'] += 1
                    self.logger.info(f"✅ {legislator} 增量處理完成")
                else:
                    results['legislators_failed'] += 1
                    self.logger.error(f"❌ {legislator} 增量處理失敗: {legislator_result.get('error', '未知錯誤')}")
                    
            except Exception as e:
                self.logger.error(f"❌ {legislator} 增量處理異常: {e}")
                results['legislators_failed'] += 1
                results['details'][legislator] = {'success': False, 'error': str(e)}
        
        # 最終統計更新
        try:
            self._update_final_statistics()
            results['statistics_updated'] = True
        except Exception as e:
            self.logger.error(f"統計更新失敗: {e}")
            results['statistics_updated'] = False
            
        return results
    
    def _process_single_legislator_full(self, legislator: str, cutoff_date: str, platforms: List[str]) -> Dict:
        """處理單一立委的完整流程"""
        result = {
            'success': False,
            'stages': {},
            'error': None
        }
        
        try:
            # 階段1：爬蟲資料收集
            self.logger.info(f"🔍 階段1：爬取 {legislator} 的資料...")
            crawl_result = self.crawler.crawl_politician_all_platforms(
                politician_name=legislator,
                cutoff_date_str=cutoff_date,
                platforms=platforms,
                max_workers=6,
                headless=True
            )
            result['stages']['crawling'] = crawl_result
            
            # 階段2：資料合併整理
            self.logger.info(f"📊 階段2：處理 {legislator} 的統計資料...")
            process_legislators_data(
                specific_legislators=[legislator],
                force_reprocess=True,
                quiet=False
            )
            result['stages']['data_processing'] = {'success': True}
            
            # 階段3：情感分析
            self.logger.info(f"🧠 階段3：分析 {legislator} 的情感資料...")
            analyze_legislators_emotions(
                legislators=[legislator],
                batch_size=5,
                quiet=False
            )
            result['stages']['emotion_analysis'] = {'success': True}
            
            # 階段4：MongoDB存儲
            self.logger.info(f"💾 階段4：存儲 {legislator} 的資料到 MongoDB...")
            mongo_handler = DataToMongo()
            mongo_handler.store_crawler_data(
                politician_name=legislator,
                is_incremental=False
            )
            result['stages']['mongodb_storage'] = {'success': True}
            
            result['success'] = True
            return result
            
        except Exception as e:
            result['error'] = str(e)
            return result
    
    def _process_single_legislator_incremental(self, legislator: str, cutoff_date: str, platforms: List[str]) -> Dict:
        """處理單一立委的增量流程"""
        result = {
            'success': False,
            'stages': {},
            'error': None
        }
        
        try:
            # 階段1：增量爬蟲資料收集
            self.logger.info(f"🔄 階段1：增量爬取 {legislator} 的資料...")
            crawl_result = self.crawler.crawl_politician_all_platforms(
                politician_name=legislator,
                cutoff_date_str=cutoff_date,
                platforms=platforms,
                max_workers=3,  # 增量模式使用較少線程
                headless=True
            )
            result['stages']['crawling'] = crawl_result
            
            # 階段2：增量資料處理
            self.logger.info(f"📊 階段2：增量處理 {legislator} 的統計資料...")
            process_legislators_data(
                specific_legislators=[legislator],
                force_reprocess=False,  # 增量模式不強制重新處理
                quiet=False
            )
            result['stages']['data_processing'] = {'success': True}
            
            # 階段3：增量情感分析
            self.logger.info(f"🧠 階段3：增量分析 {legislator} 的情感資料...")
            analyze_legislators_emotions_incremental(
                legislators=[legislator],
                batch_size=3,
                quiet=False
            )
            result['stages']['emotion_analysis'] = {'success': True}
            
            # 階段4：增量MongoDB存儲
            self.logger.info(f"💾 階段4：增量存儲 {legislator} 的資料到 MongoDB...")
            mongo_handler = DataToMongo()
            mongo_handler.store_crawler_data(
                politician_name=legislator,
                is_incremental=True
            )
            result['stages']['mongodb_storage'] = {'success': True}
            
            result['success'] = True
            return result
            
        except Exception as e:
            result['error'] = str(e)
            return result
    
    def _update_final_statistics(self):
        """更新最終統計資料"""
        self.logger.info("📈 更新立委統計資料...")
        
        # 這裡需要實現統計更新邏輯
        # 暫時使用簡單的日誌記錄
        self.logger.info("✅ 統計資料更新完成")

if __name__ == "__main__":
    # 測試程式
    logging.basicConfig(level=logging.INFO)
    
    manager = IntegratedCrawlerManager()
    
    # 測試單一立委的增量爬取
    test_legislators = ["高虹安"]
    
    result = manager.crawl_all_legislators_incremental(
        legislators=test_legislators,
        platforms=['youtube', 'ptt', 'threads']
    )
    
    print(json.dumps(result, ensure_ascii=False, indent=2))
