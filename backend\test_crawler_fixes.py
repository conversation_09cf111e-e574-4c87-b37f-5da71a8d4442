#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試爬蟲修復腳本
驗證各個平台的爬蟲是否能正常工作
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def test_ptt_crawler():
    """測試PTT爬蟲"""
    logger.info("🧪 測試PTT爬蟲...")
    try:
        from crawler.ptt_crawler import crawl_ptt_with_pool
        from crawler.webdriver_pool import get_global_pool
        
        # 創建WebDriver池
        pool = get_global_pool(max_instances=2, headless=True)
        
        # 測試爬取
        result = crawl_ptt_with_pool(
            name="高虹安",
            webdriver_pool=pool,
            last_crawled_time="2025-01-01",
            max_threads=2
        )
        
        logger.info(f"PTT爬蟲結果: {result}")
        
        if isinstance(result, dict) and result.get('success'):
            logger.info("✅ PTT爬蟲測試通過")
            return True
        else:
            logger.warning("⚠️ PTT爬蟲返回格式可能有問題")
            return False
            
    except Exception as e:
        logger.error(f"❌ PTT爬蟲測試失敗: {e}")
        return False

def test_youtube_crawler():
    """測試YouTube爬蟲"""
    logger.info("🧪 測試YouTube爬蟲...")
    try:
        from crawler.yt_crawler import search_youtube_videos, crawl_youtube_comments_with_pool
        from crawler.webdriver_pool import get_global_pool
        
        # 測試URL收集
        logger.info("測試YouTube URL收集...")
        url_file, video_data = search_youtube_videos(
            name="高虹安",
            cutoff_date="2024-01-01",
            headless=True
        )
        
        logger.info(f"YouTube URL收集結果: {len(video_data) if video_data else 0} 個影片")
        
        if video_data and len(video_data) > 0:
            # 測試內容爬取
            logger.info("測試YouTube內容爬取...")
            pool = get_global_pool(max_instances=2, headless=True)
            
            result = crawl_youtube_comments_with_pool(
                name="高虹安",
                webdriver_pool=pool,
                last_crawled_time="2024-01-01",
                max_threads=2
            )
            
            logger.info(f"YouTube內容爬取結果: {result}")
            
            if isinstance(result, dict) and result.get('success'):
                logger.info("✅ YouTube爬蟲測試通過")
                return True
            else:
                logger.warning("⚠️ YouTube內容爬取可能有問題")
                return False
        else:
            logger.warning("⚠️ YouTube URL收集無結果")
            return False
            
    except Exception as e:
        logger.error(f"❌ YouTube爬蟲測試失敗: {e}")
        return False

def test_threads_crawler():
    """測試Threads爬蟲"""
    logger.info("🧪 測試Threads爬蟲...")
    try:
        from crawler.thread_crawler import crawl_threads_with_pool
        from crawler.webdriver_pool import get_global_pool
        
        # 創建WebDriver池
        pool = get_global_pool(max_instances=2, headless=True)
        
        # 設置輸出目錄
        output_dir = os.path.join(current_dir, 'crawler', 'data', 'threads')
        os.makedirs(output_dir, exist_ok=True)
        
        # 測試爬取
        result = crawl_threads_with_pool(
            name="高虹安",
            output_dir=output_dir,
            webdriver_pool=pool,
            last_crawled_time="2025-01-01",
            max_threads=2
        )
        
        logger.info(f"Threads爬蟲結果: {result}")
        
        if isinstance(result, dict) and result.get('success'):
            logger.info("✅ Threads爬蟲測試通過")
            return True
        else:
            logger.warning("⚠️ Threads爬蟲可能有問題")
            return False
            
    except Exception as e:
        logger.error(f"❌ Threads爬蟲測試失敗: {e}")
        return False

def test_multi_platform_crawler():
    """測試多平台爬蟲管理器"""
    logger.info("🧪 測試多平台爬蟲管理器...")
    try:
        from crawler.multi_platform_crawler import MultiPlatformCrawler
        
        crawler = MultiPlatformCrawler()
        
        # 測試兩階段爬取
        result = crawler.crawl_politician_all_platforms(
            politician_name="高虹安",
            cutoff_date_str="2025-01-01",
            platforms=['youtube', 'ptt', 'threads'],
            max_workers=2,
            headless=True
        )
        
        logger.info(f"多平台爬蟲結果: {json.dumps(result['summary'], ensure_ascii=False)}")
        
        if result and result.get('summary'):
            success_rate = result['summary'].get('success_rate', 0)
            if success_rate > 0:
                logger.info("✅ 多平台爬蟲測試通過")
                return True
            else:
                logger.warning("⚠️ 多平台爬蟲所有平台都失敗")
                return False
        else:
            logger.warning("⚠️ 多平台爬蟲返回格式有問題")
            return False
            
    except Exception as e:
        logger.error(f"❌ 多平台爬蟲測試失敗: {e}")
        return False

def test_integrated_manager():
    """測試整合管理器"""
    logger.info("🧪 測試整合爬蟲管理器...")
    try:
        from crawler.integrated_crawler_manager import IntegratedCrawlerManager
        
        manager = IntegratedCrawlerManager()
        
        # 測試增量模式
        result = manager.crawl_all_legislators_incremental(
            legislators=["高虹安"],
            platforms=['youtube', 'ptt', 'threads']
        )
        
        logger.info(f"整合管理器結果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result and result.get('legislators_processed', 0) > 0:
            logger.info("✅ 整合管理器測試通過")
            return True
        else:
            logger.warning("⚠️ 整合管理器可能有問題")
            return False
            
    except Exception as e:
        logger.error(f"❌ 整合管理器測試失敗: {e}")
        return False

def test_stats_updater():
    """測試統計更新器"""
    logger.info("🧪 測試統計更新器...")
    try:
        from crawler.legislator_stats import LegislatorStatsUpdater
        from pymongo import MongoClient
        
        # 連接MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        db = client['legislator_recall']
        
        updater = LegislatorStatsUpdater(db)
        
        # 測試更新所有立委統計
        result = updater.update_all_legislators()
        
        logger.info(f"統計更新結果: {result}")
        
        if isinstance(result, dict):
            logger.info("✅ 統計更新器測試通過")
            return True
        else:
            logger.warning("⚠️ 統計更新器可能有問題")
            return False
            
    except Exception as e:
        logger.error(f"❌ 統計更新器測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    logger.info("🚀 開始測試爬蟲修復...")
    
    tests = [
        ("PTT爬蟲", test_ptt_crawler),
        ("YouTube爬蟲", test_youtube_crawler),
        ("Threads爬蟲", test_threads_crawler),
        ("多平台爬蟲", test_multi_platform_crawler),
        ("整合管理器", test_integrated_manager),
        ("統計更新器", test_stats_updater)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"測試: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"測試 {test_name} 時發生異常: {e}")
            results[test_name] = False
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("測試總結")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed == total:
        logger.info("🎉 所有測試都通過！")
        return 0
    else:
        logger.warning(f"⚠️ 有 {total - passed} 個測試失敗")
        return 1

if __name__ == "__main__":
    sys.exit(main())
