#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試完整處理流程
驗證修復後的處理邏輯
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def test_youtube_data_processing():
    """測試YouTube數據處理"""
    logger.info("🧪 測試YouTube數據處理...")
    
    try:
        youtube_file = os.path.join(current_dir, 'crawler', 'data', 'youtube', '牛煦庭.json')
        
        if os.path.exists(youtube_file):
            with open(youtube_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"YouTube數據: {len(data)} 個項目")
            
            # 統計評論數
            total_comments = 0
            videos_with_comments = 0
            
            for item in data:
                comment_count = item.get('影片留言數', 0)
                if isinstance(comment_count, int) and comment_count > 0:
                    total_comments += comment_count
                    videos_with_comments += 1
                elif isinstance(item.get('留言資料'), list):
                    comment_count = len(item.get('留言資料', []))
                    total_comments += comment_count
                    if comment_count > 0:
                        videos_with_comments += 1
            
            logger.info(f"   總評論數: {total_comments}")
            logger.info(f"   有評論的影片: {videos_with_comments}/{len(data)}")
            
            if total_comments > 0:
                logger.info("✅ YouTube數據包含評論，可以進行處理")
                return True
            else:
                logger.warning("⚠️ YouTube數據沒有評論")
                return False
        else:
            logger.error("❌ YouTube數據文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ YouTube數據處理測試失敗: {e}")
        return False

def test_data_processing_functions():
    """測試數據處理函數"""
    logger.info("🧪 測試數據處理函數...")
    
    try:
        # 測試導入關鍵模塊
        logger.info("   測試導入 user_data_processor...")
        from crawler.user_data_processor import process_legislators_data
        logger.info("   ✅ user_data_processor 導入成功")
        
        logger.info("   測試導入 gemini_emo_user...")
        from crawler.gemini_emo_user import analyze_legislators_emotions_incremental
        logger.info("   ✅ gemini_emo_user 導入成功")
        
        logger.info("   測試導入 data_to_mongo_v2...")
        from crawler.data_to_mongo_v2 import DataToMongo
        logger.info("   ✅ data_to_mongo_v2 導入成功")
        
        logger.info("✅ 所有關鍵處理函數導入成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 數據處理函數測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mock_processing_flow():
    """測試模擬處理流程"""
    logger.info("🧪 測試模擬處理流程...")
    
    try:
        # 模擬args對象
        class MockArgs:
            def __init__(self):
                self.skip_processing = False
                self.skip_analysis = False
                self.force_reprocess = False
                self.batch_size = 10
                self.quiet = False
        
        args = MockArgs()
        legislator = "牛煦庭"
        
        # 模擬爬取結果
        crawl_result = {
            'summary': {
                'successful_platforms': 1,
                'total_platforms': 3
            }
        }
        
        logger.info(f"模擬處理立委: {legislator}")
        logger.info(f"爬取結果: {crawl_result['summary']['successful_platforms']}/{crawl_result['summary']['total_platforms']} 平台成功")
        
        # 測試安全檢查邏輯
        skip_processing = getattr(args, 'skip_processing', False)
        skip_analysis = getattr(args, 'skip_analysis', False)
        force_reprocess = getattr(args, 'force_reprocess', False)
        batch_size = getattr(args, 'batch_size', 10)
        quiet = getattr(args, 'quiet', False)
        
        logger.info(f"   skip_processing: {skip_processing}")
        logger.info(f"   skip_analysis: {skip_analysis}")
        logger.info(f"   force_reprocess: {force_reprocess}")
        logger.info(f"   batch_size: {batch_size}")
        logger.info(f"   quiet: {quiet}")
        
        if not skip_processing:
            logger.info("   📊 會執行資料處理")
            
            if not skip_analysis:
                logger.info("   🧠 會執行情感分析")
                
                if force_reprocess:
                    logger.info("   🔄 使用完整重新處理模式")
                else:
                    logger.info("   ⚡ 使用增量處理模式")
            else:
                logger.info("   ⏭️ 跳過情感分析")
        else:
            logger.info("   ⏭️ 跳過資料處理")
        
        logger.info("✅ 模擬處理流程邏輯正確")
        return True
        
    except Exception as e:
        logger.error(f"❌ 模擬處理流程測試失敗: {e}")
        return False

def test_mongodb_connection():
    """測試MongoDB連接"""
    logger.info("🧪 測試MongoDB連接...")
    
    try:
        from crawler.data_to_mongo_v2 import DataToMongo
        
        # 創建MongoDB處理器
        mongo_handler = DataToMongo()
        logger.info("   ✅ MongoDB處理器創建成功")
        
        # 測試連接（不實際操作數據）
        logger.info("   📡 MongoDB連接測試完成")
        
        logger.info("✅ MongoDB連接測試通過")
        return True
        
    except Exception as e:
        logger.error(f"❌ MongoDB連接測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    logger.info("🚀 開始測試完整處理流程...")
    
    tests = [
        ("YouTube數據處理", test_youtube_data_processing),
        ("數據處理函數", test_data_processing_functions),
        ("模擬處理流程", test_mock_processing_flow),
        ("MongoDB連接", test_mongodb_connection)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"測試: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"測試 {test_name} 時發生異常: {e}")
            results[test_name] = False
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("完整處理流程測試總結")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed >= 3:  # 至少3個關鍵測試通過
        logger.info("🎉 完整處理流程測試基本成功！")
        logger.info("現在可以正常進行數據處理和分析了！")
        
        # 提供下一步建議
        logger.info("\n📋 下一步建議:")
        logger.info("1. 運行完整爬蟲: python main.py --legislators 牛煦庭 --days 1 --use-optimized-crawler")
        logger.info("2. 檢查處理結果: 查看MongoDB中的數據")
        logger.info("3. 驗證情感分析: 檢查分析結果")
        
        return 0
    else:
        logger.warning(f"⚠️ 有 {total - passed} 個測試失敗")
        return 1

if __name__ == "__main__":
    sys.exit(main())
