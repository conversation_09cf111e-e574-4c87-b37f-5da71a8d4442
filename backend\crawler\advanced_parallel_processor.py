#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高級並行處理器
解決效率問題：當A用戶所有平台資料抓完開始統計時，B用戶就可以直接開始爬取
實現真正的流水線並行處理
"""

import os
import sys
import json
import time
import logging
import threading
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed, Future
from queue import Queue, Empty
from typing import List, Dict, Optional

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 導入必要模組
from webdriver_pool import get_global_pool
from optimized_crawler_manager import OptimizedCrawlerManager
from complete_data_processor import (
    step1_merge_platform_data, 
    step2_create_user_format, 
    step3_gemini_analysis, 
    step4_mongodb_storage
)

class AdvancedParallelProcessor:
    """高級並行處理器 - 流水線並行處理"""
    
    def __init__(self, max_crawl_workers=2, max_process_workers=2):
        self.logger = logging.getLogger(__name__)
        self.max_crawl_workers = max_crawl_workers  # 同時爬取的立委數
        self.max_process_workers = max_process_workers  # 同時處理的立委數
        
        # 工作隊列
        self.crawl_queue = Queue()  # 待爬取隊列
        self.process_queue = Queue()  # 待處理隊列
        self.completed_queue = Queue()  # 已完成隊列
        
        # 結果追蹤
        self.results = {}
        self.lock = threading.Lock()
        
        # 爬蟲管理器
        self.crawler_manager = OptimizedCrawlerManager()
        
    def process_legislators_pipeline(self, legislators: List[str], cutoff_date_str: str, 
                                   platforms: List[str] = None) -> Dict:
        """
        流水線並行處理立委
        
        Args:
            legislators: 立委列表
            cutoff_date_str: 截止日期
            platforms: 平台列表
        
        Returns:
            Dict: 處理結果
        """
        platforms = platforms or ['youtube', 'ptt', 'threads']
        
        self.logger.info(f"🚀 開始高級並行處理: {len(legislators)} 位立委")
        self.logger.info(f"📊 爬取並行度: {self.max_crawl_workers}, 處理並行度: {self.max_process_workers}")
        
        # 初始化隊列
        for legislator in legislators:
            self.crawl_queue.put({
                'name': legislator,
                'cutoff_date': cutoff_date_str,
                'platforms': platforms
            })
        
        # 啟動工作線程
        crawl_futures = []
        process_futures = []
        
        # 爬取線程池
        crawl_executor = ThreadPoolExecutor(max_workers=self.max_crawl_workers, thread_name_prefix="Crawler")
        
        # 處理線程池  
        process_executor = ThreadPoolExecutor(max_workers=self.max_process_workers, thread_name_prefix="Processor")
        
        try:
            # 啟動爬取工作者
            for i in range(self.max_crawl_workers):
                future = crawl_executor.submit(self._crawl_worker, f"CrawlWorker-{i+1}")
                crawl_futures.append(future)
            
            # 啟動處理工作者
            for i in range(self.max_process_workers):
                future = process_executor.submit(self._process_worker, f"ProcessWorker-{i+1}")
                process_futures.append(future)
            
            # 等待所有爬取任務完成
            self.logger.info("⏳ 等待爬取任務完成...")
            for future in as_completed(crawl_futures):
                try:
                    result = future.result()
                    self.logger.info(f"爬取工作者完成: {result}")
                except Exception as e:
                    self.logger.error(f"爬取工作者異常: {e}")
            
            # 發送處理結束信號
            for _ in range(self.max_process_workers):
                self.process_queue.put(None)  # 結束信號
            
            # 等待所有處理任務完成
            self.logger.info("⏳ 等待處理任務完成...")
            for future in as_completed(process_futures):
                try:
                    result = future.result()
                    self.logger.info(f"處理工作者完成: {result}")
                except Exception as e:
                    self.logger.error(f"處理工作者異常: {e}")
        
        finally:
            crawl_executor.shutdown(wait=True)
            process_executor.shutdown(wait=True)
        
        # 收集結果
        results = {}
        while not self.completed_queue.empty():
            try:
                result = self.completed_queue.get_nowait()
                results[result['name']] = result
            except Empty:
                break
        
        # 統計
        successful = sum(1 for r in results.values() if r.get('success', False))
        
        self.logger.info(f"🎉 高級並行處理完成: {successful}/{len(legislators)} 位立委成功")
        
        return {
            'total': len(legislators),
            'successful': successful,
            'results': results,
            'success_rate': successful / len(legislators) if legislators else 0
        }
    
    def _crawl_worker(self, worker_name: str):
        """爬取工作者"""
        self.logger.info(f"🕷️ {worker_name} 開始工作")
        processed_count = 0
        
        while True:
            try:
                # 從隊列獲取任務
                task = self.crawl_queue.get(timeout=5)
                if task is None:  # 結束信號
                    break
                
                legislator_name = task['name']
                cutoff_date = task['cutoff_date']
                platforms = task['platforms']
                
                self.logger.info(f"🕷️ {worker_name} 開始爬取: {legislator_name}")
                
                # 執行爬取
                start_time = time.time()
                crawl_result = self.crawler_manager.crawl_politician_optimized(
                    politician_name=legislator_name,
                    cutoff_date_str=cutoff_date,
                    platforms=platforms,
                    max_workers_per_platform=4
                )
                
                crawl_time = time.time() - start_time
                
                # 檢查爬取結果
                if crawl_result['summary']['successful_platforms'] > 0:
                    self.logger.info(f"✅ {worker_name} 爬取成功: {legislator_name} ({crawl_time:.1f}s)")
                    
                    # 立即加入處理隊列
                    self.process_queue.put({
                        'name': legislator_name,
                        'crawl_result': crawl_result,
                        'crawl_time': crawl_time
                    })
                    
                    processed_count += 1
                else:
                    self.logger.warning(f"⚠️ {worker_name} 爬取失敗: {legislator_name}")
                    
                    # 即使爬取失敗也加入處理隊列（可能有舊數據）
                    self.process_queue.put({
                        'name': legislator_name,
                        'crawl_result': crawl_result,
                        'crawl_time': crawl_time,
                        'crawl_failed': True
                    })
                
                self.crawl_queue.task_done()
                
            except Empty:
                # 隊列為空，繼續等待
                continue
            except Exception as e:
                self.logger.error(f"❌ {worker_name} 爬取異常: {e}")
                self.crawl_queue.task_done()
        
        # 發送處理結束信號
        self.process_queue.put(None)
        
        self.logger.info(f"🏁 {worker_name} 完成，處理了 {processed_count} 位立委")
        return f"{worker_name}: {processed_count} 位立委"
    
    def _process_worker(self, worker_name: str):
        """處理工作者"""
        self.logger.info(f"📊 {worker_name} 開始工作")
        processed_count = 0
        
        while True:
            try:
                # 從隊列獲取任務
                task = self.process_queue.get(timeout=10)
                if task is None:  # 結束信號
                    break
                
                legislator_name = task['name']
                crawl_result = task['crawl_result']
                crawl_time = task.get('crawl_time', 0)
                crawl_failed = task.get('crawl_failed', False)
                
                self.logger.info(f"📊 {worker_name} 開始處理: {legislator_name}")
                
                # 執行數據處理
                start_time = time.time()
                process_success = self._process_single_legislator(legislator_name, crawl_failed)
                process_time = time.time() - start_time
                
                # 記錄結果
                result = {
                    'name': legislator_name,
                    'success': process_success,
                    'crawl_time': crawl_time,
                    'process_time': process_time,
                    'total_time': crawl_time + process_time,
                    'crawl_result': crawl_result
                }
                
                self.completed_queue.put(result)
                
                if process_success:
                    self.logger.info(f"✅ {worker_name} 處理成功: {legislator_name} (爬取:{crawl_time:.1f}s, 處理:{process_time:.1f}s)")
                else:
                    self.logger.warning(f"⚠️ {worker_name} 處理失敗: {legislator_name}")
                
                processed_count += 1
                self.process_queue.task_done()
                
            except Empty:
                # 隊列為空，繼續等待
                continue
            except Exception as e:
                self.logger.error(f"❌ {worker_name} 處理異常: {e}")
                self.process_queue.task_done()
        
        self.logger.info(f"🏁 {worker_name} 完成，處理了 {processed_count} 位立委")
        return f"{worker_name}: {processed_count} 位立委"
    
    def _process_single_legislator(self, legislator_name: str, crawl_failed: bool = False) -> bool:
        """處理單個立委的數據"""
        try:
            legislators = [legislator_name]
            
            # 步驟1：合併平台數據
            if not step1_merge_platform_data(legislators):
                if not crawl_failed:  # 如果爬取成功但合併失敗，這是問題
                    self.logger.error(f"❌ {legislator_name} 合併平台數據失敗")
                    return False
            
            # 步驟2：創建用戶格式
            if not step2_create_user_format(legislators):
                self.logger.error(f"❌ {legislator_name} 創建用戶格式失敗")
                return False
            
            # 步驟3：Gemini分析（如果有API配置）
            try:
                if os.path.exists(os.path.join(current_dir, '..', 'api.json')):
                    step3_gemini_analysis(legislators)
                else:
                    self.logger.warning(f"⚠️ {legislator_name} 跳過Gemini分析（無API配置）")
            except Exception as e:
                self.logger.warning(f"⚠️ {legislator_name} Gemini分析失敗: {e}")
            
            # 步驟4：MongoDB存儲
            if not step4_mongodb_storage(legislators):
                self.logger.error(f"❌ {legislator_name} MongoDB存儲失敗")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ {legislator_name} 處理異常: {e}")
            return False

def main():
    """測試函數"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s'
    )
    
    processor = AdvancedParallelProcessor(max_crawl_workers=2, max_process_workers=2)
    
    # 測試立委列表
    legislators = ['牛煦庭', '丁學忠']
    cutoff_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    
    result = processor.process_legislators_pipeline(
        legislators=legislators,
        cutoff_date_str=cutoff_date,
        platforms=['youtube', 'ptt', 'threads']
    )
    
    print(f"處理結果: {result}")

if __name__ == "__main__":
    main()
