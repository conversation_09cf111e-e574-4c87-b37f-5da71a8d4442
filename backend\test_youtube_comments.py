#!/usr/bin/env python3
"""
測試YouTube評論爬取功能
"""

import os
import sys
import json

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

def test_youtube_comments():
    """測試YouTube評論爬取"""
    print("🧪 測試YouTube評論爬取功能...")

    try:
        from crawler.yt_crawler import crawl_youtube_comments_with_pool
        from crawler.webdriver_pool import WebDriverPool

        # 創建測試用的URL文件（只包含3個URL）
        test_urls = [
            {"url": "https://www.youtube.com/watch?v=KTTENyGhtoI", "time": "1 年前", "date": "2024-07-11"},
            {"url": "https://www.youtube.com/watch?v=abc123", "time": "2 年前", "date": "2023-07-11"},
            {"url": "https://www.youtube.com/watch?v=def456", "time": "3 年前", "date": "2022-07-11"}
        ]

        # 創建測試URL文件
        test_href_file = "crawler/href/youtube/林德福_test.json"
        os.makedirs(os.path.dirname(test_href_file), exist_ok=True)
        with open(test_href_file, 'w', encoding='utf-8') as f:
            json.dump(test_urls, f, ensure_ascii=False, indent=2)

        print(f"📁 創建測試URL文件: {len(test_urls)} 個URL")

        # 創建WebDriver池
        webdriver_pool = WebDriverPool(max_instances=1, headless=True)

        try:
            # 測試林德福的評論爬取（使用測試文件）
            result = crawl_youtube_comments_with_pool(
                "林德福_test",
                webdriver_pool,
                max_threads=1
            )
            
            print(f"✅ YouTube評論爬取測試結果: {result}")
            
            # 檢查是否有數據被保存
            data_file = "crawler/data/youtube/林德福_test.json"
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print(f"✅ 數據文件存在: {len(data)} 個影片記錄")
                
                # 顯示第一個影片的信息
                if data:
                    first_video = data[0]
                    print(f"📹 第一個影片: {first_video.get('影片標題', 'N/A')}")
                    comments = first_video.get('留言資料', [])
                    print(f"💬 評論數量: {len(comments)}")
            else:
                print("❌ 數據文件不存在")
                
            return True
            
        finally:
            webdriver_pool.close_all()
            
    except Exception as e:
        print(f"❌ YouTube評論爬取測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🔧 開始YouTube評論爬取測試...")
    
    success = test_youtube_comments()
    
    if success:
        print("🎉 YouTube評論爬取測試通過！")
        return 0
    else:
        print("❌ YouTube評論爬取測試失敗")
        return 1

if __name__ == "__main__":
    sys.exit(main())
