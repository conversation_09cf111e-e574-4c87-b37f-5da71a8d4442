#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化爬蟲管理器
實現完整的數據流程：
🕷️ 三平台爬蟲收集 → 整合三平台資料(alldata) → 整理留言資料(user_data) → 🧠 Gemini分析 → 📄 crawler_data + final_data儲存 → 📊 統計處理 → 🗄️ legislators
"""

import os
import sys
import json
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 導入各個模組
from yt_crawler import crawl_youtube_comments
from ptt_crawler import crawl_ptt
from thread_crawler import crawl_threads_all_in_one
from user_data_processor import process_legislators_data
from gemini_emo_user import analyze_legislators_emotions
from data_to_mongo_v2 import DataToMongo

logger = logging.getLogger(__name__)

class SimpleCrawlerManager:
    """簡化爬蟲管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.crawler_functions = {
            'youtube': crawl_youtube_comments,
            'ptt': crawl_ptt,
            'threads': crawl_threads_all_in_one
        }
        
    def crawl_all_legislators(self, legislators: List[str], days: int = 30, platforms: List[str] = None) -> Dict:
        """
        執行完整的爬蟲流程
        
        Args:
            legislators: 立委列表
            days: 爬取天數
            platforms: 平台列表
            
        Returns:
            處理結果統計
        """
        if platforms is None:
            platforms = ['youtube', 'ptt']
            
        self.logger.info(f"🚀 開始爬蟲流程: {len(legislators)}位立委, {days}天, 平台: {platforms}")
        
        result = {
            'success': False,
            'total_legislators': len(legislators),
            'total_records': 0,
            'processed_legislators': 0,
            'errors': []
        }
        
        try:
            # 階段1: 爬蟲收集數據
            self.logger.info("📡 階段1: 開始爬蟲收集...")
            crawl_results = self._crawl_data(legislators, days, platforms)
            
            # 階段2: 整合數據到alldata
            self.logger.info("📊 階段2: 整合數據到alldata...")
            alldata_results = self._merge_to_alldata(legislators)
            
            # 階段3: 處理用戶數據
            self.logger.info("👥 階段3: 處理用戶數據...")
            user_data_results = self._process_user_data(legislators)
            
            # 階段4: Gemini情感分析
            self.logger.info("🧠 階段4: Gemini情感分析...")
            analysis_results = self._analyze_emotions(legislators)
            
            # 階段5: 存儲到MongoDB
            self.logger.info("💾 階段5: 存儲到MongoDB...")
            mongo_results = self._store_to_mongodb(legislators)
            
            # 統計結果
            result['success'] = True
            result['processed_legislators'] = len([r for r in crawl_results if r.get('success', False)])
            result['total_records'] = sum([r.get('records', 0) for r in crawl_results])
            
            self.logger.info(f"✅ 爬蟲流程完成: {result['processed_legislators']}/{result['total_legislators']} 位立委成功")
            
        except Exception as e:
            self.logger.error(f"❌ 爬蟲流程失敗: {e}")
            result['errors'].append(str(e))
            
        return result
    
    def _crawl_data(self, legislators: List[str], days: int, platforms: List[str]) -> List[Dict]:
        """階段1: 優化的並行爬蟲收集數據"""
        results = []

        # 配置執行緒池大小 - 保守估計避免過載
        total_tasks = len(legislators) * len(platforms)
        max_workers = min(8, total_tasks)  # 最多8個並行任務

        self.logger.info(f"🔧 執行緒配置: 最多{max_workers}個並行爬蟲任務 (總共{total_tasks}個任務)")

        # 創建所有爬蟲任務
        crawl_tasks = []
        for legislator in legislators:
            for platform in platforms:
                if platform in self.crawler_functions:
                    crawl_tasks.append((legislator, platform, days))

        # 並行執行所有爬蟲任務
        legislator_stats = {}

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任務
            future_to_task = {}
            for legislator, platform, days in crawl_tasks:
                future = executor.submit(self._crawl_single_platform, legislator, platform, days)
                future_to_task[future] = (legislator, platform)

            # 收集結果
            for future in as_completed(future_to_task):
                legislator, platform = future_to_task[future]
                try:
                    records = future.result()

                    # 初始化立委統計
                    if legislator not in legislator_stats:
                        legislator_stats[legislator] = {'legislator': legislator, 'success': False, 'records': 0}

                    # 累加記錄數
                    legislator_stats[legislator]['records'] += records if records else 0
                    if records and records > 0:
                        legislator_stats[legislator]['success'] = True

                    self.logger.info(f"📊 {legislator}-{platform}: {records or 0} 筆資料")

                except Exception as e:
                    self.logger.error(f"❌ {legislator}-{platform} 爬取失敗: {e}")

        # 輸出最終統計
        for legislator, stats in legislator_stats.items():
            if stats['records'] > 0:
                self.logger.info(f"✅ {legislator}: 共 {stats['records']} 筆資料")
            else:
                self.logger.warning(f"⚠️ {legislator}: 沒有找到資料")

        return list(legislator_stats.values())

    def _crawl_single_platform(self, legislator: str, platform: str, days: int) -> int:
        """爬取單一立委的單一平台數據"""
        try:
            self.logger.info(f"🕷️ 開始爬取: {legislator}-{platform}")

            if platform == 'youtube':
                # YouTube爬蟲（限制執行緒數避免過載）
                crawler_func = self.crawler_functions[platform]
                records = crawler_func(legislator, headless=True, num_threads=1)  # 每個任務只用1個執行緒
            elif platform == 'ptt':
                # PTT爬蟲
                crawler_func = self.crawler_functions[platform]
                records = crawler_func(legislator, days=days)
            elif platform == 'threads':
                # Threads爬蟲
                crawler_func = self.crawler_functions[platform]
                records = crawler_func(legislator, days=days)
            else:
                records = 0

            self.logger.info(f"✅ 完成爬取: {legislator}-{platform} = {records or 0} 筆")
            return records if records else 0

        except Exception as e:
            self.logger.error(f"❌ 爬取失敗: {legislator}-{platform}: {e}")
            return 0



    def _merge_to_alldata(self, legislators: List[str]) -> Dict:
        """階段2: 整合數據到alldata"""
        try:
            # 這裡應該調用merge_all_data模組
            from merge_all_data import merge_legislators_data
            return merge_legislators_data(legislators)
        except Exception as e:
            self.logger.error(f"整合alldata失敗: {e}")
            return {'success': False, 'error': str(e)}
    
    def _process_user_data(self, legislators: List[str]) -> Dict:
        """階段3: 處理用戶數據"""
        try:
            return process_legislators_data(legislators)
        except Exception as e:
            self.logger.error(f"處理用戶數據失敗: {e}")
            return {'success': False, 'error': str(e)}
    
    def _analyze_emotions(self, legislators: List[str]) -> Dict:
        """階段4: Gemini情感分析"""
        try:
            return analyze_legislators_emotions(legislators)
        except Exception as e:
            self.logger.error(f"情感分析失敗: {e}")
            return {'success': False, 'error': str(e)}
    
    def _store_to_mongodb(self, legislators: List[str]) -> Dict:
        """階段5: 存儲到MongoDB"""
        try:
            mongo_manager = DataToMongo()
            return mongo_manager.process_legislators(legislators)
        except Exception as e:
            self.logger.error(f"MongoDB存儲失敗: {e}")
            return {'success': False, 'error': str(e)}

def main():
    """測試函數"""
    logging.basicConfig(level=logging.INFO)
    
    manager = SimpleCrawlerManager()
    result = manager.crawl_all_legislators(['牛煦庭'], days=1, platforms=['youtube'])
    
    print(f"結果: {result}")

if __name__ == "__main__":
    main()
