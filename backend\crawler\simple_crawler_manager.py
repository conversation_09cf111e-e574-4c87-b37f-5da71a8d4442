#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化爬蟲管理器
實現完整的數據流程：
🕷️ 三平台爬蟲收集 → 整合三平台資料(alldata) → 整理留言資料(user_data) → 🧠 Gemini分析 → 📄 crawler_data + final_data儲存 → 📊 統計處理 → 🗄️ legislators
"""

import os
import sys
import json
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 導入各個模組
from yt_crawler import search_youtube_videos, crawl_youtube_comments, crawl_youtube_comments_with_pool
from ptt_crawler import fetch_ptt_links, crawl_ptt
from thread_crawler import crawl_threads_all_in_one
from user_data_processor import process_legislators_data
from gemini_emo_user import analyze_legislators_emotions
from data_to_mongo_v2 import DataToMongo

logger = logging.getLogger(__name__)

class SimpleCrawlerManager:
    """簡化爬蟲管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.crawler_functions = {
            'youtube': crawl_youtube_comments,
            'ptt': crawl_ptt,
            'threads': crawl_threads_all_in_one
        }
        
    def crawl_all_legislators(self, legislators: List[str], days: int = 30, platforms: List[str] = None) -> Dict:
        """
        執行完整的爬蟲流程
        
        Args:
            legislators: 立委列表
            days: 爬取天數
            platforms: 平台列表
            
        Returns:
            處理結果統計
        """
        if platforms is None:
            platforms = ['youtube', 'ptt']
            
        self.logger.info(f"🚀 開始爬蟲流程: {len(legislators)}位立委, {days}天, 平台: {platforms}")
        
        result = {
            'success': False,
            'total_legislators': len(legislators),
            'total_records': 0,
            'processed_legislators': 0,
            'errors': []
        }
        
        try:
            # 階段1: 爬蟲收集數據
            self.logger.info("📡 階段1: 開始爬蟲收集...")
            crawl_results = self._crawl_data(legislators, days, platforms)
            
            # 階段2: 整合數據到alldata
            self.logger.info("📊 階段2: 整合數據到alldata...")
            alldata_results = self._merge_to_alldata(legislators)
            
            # 階段3: 處理用戶數據
            self.logger.info("👥 階段3: 處理用戶數據...")
            user_data_results = self._process_user_data(legislators)
            
            # 階段4: Gemini情感分析
            self.logger.info("🧠 階段4: Gemini情感分析...")
            analysis_results = self._analyze_emotions(legislators)
            
            # 階段5: 存儲到MongoDB
            self.logger.info("💾 階段5: 存儲到MongoDB...")
            mongo_results = self._store_to_mongodb(legislators)
            
            # 統計結果
            result['success'] = True
            result['processed_legislators'] = len([r for r in crawl_results if r.get('success', False)])
            result['total_records'] = sum([r.get('records', 0) for r in crawl_results])
            
            self.logger.info(f"✅ 爬蟲流程完成: {result['processed_legislators']}/{result['total_legislators']} 位立委成功")
            
        except Exception as e:
            self.logger.error(f"❌ 爬蟲流程失敗: {e}")
            result['errors'].append(str(e))
            
        return result
    
    def _crawl_data(self, legislators: List[str], days: int, platforms: List[str]) -> List[Dict]:
        """階段1: 改進的順序處理爬蟲 - 更穩定的方式"""

        # 階段1A: 並行收集所有URL
        self.logger.info("📡 階段1A: 並行收集所有立委的URL...")
        url_results = self._parallel_url_collection(legislators, days, platforms)

        # 階段1B: 順序處理每個立委的評論數據（更穩定）
        self.logger.info("📊 階段1B: 順序處理每個立委的評論數據...")
        data_results = self._sequential_data_crawling(legislators, platforms)

        return data_results

    def _sequential_data_crawling(self, legislators: List[str], platforms: List[str]) -> List[Dict]:
        """順序處理每個立委的評論數據 - 更穩定的方式"""
        results = []

        for i, legislator in enumerate(legislators, 1):
            self.logger.info(f"📊 處理第 {i}/{len(legislators)} 位立委: {legislator}")

            legislator_results = []
            for platform in platforms:
                self.logger.info(f"   🔄 {legislator}-{platform}: 開始爬取評論...")

                try:
                    record_count = self._crawl_data_only(legislator, platform)

                    result = {
                        'legislator': legislator,
                        'platform': platform,
                        'success': record_count > 0,
                        'records': record_count,
                        'error': None
                    }

                    if record_count > 0:
                        self.logger.info(f"   ✅ {legislator}-{platform}: 爬取到 {record_count} 筆資料")
                    else:
                        self.logger.warning(f"   ⚠️ {legislator}-{platform}: 沒有爬取到資料")

                    legislator_results.append(result)

                except Exception as e:
                    self.logger.error(f"   ❌ {legislator}-{platform}: 爬取失敗 - {e}")
                    legislator_results.append({
                        'legislator': legislator,
                        'platform': platform,
                        'success': False,
                        'records': 0,
                        'error': str(e)
                    })

            results.extend(legislator_results)

            # 每處理完一個立委，稍微休息一下
            if i < len(legislators):
                time.sleep(2)

        return results

    def _parallel_url_collection(self, legislators: List[str], days: int, platforms: List[str]) -> List[Dict]:
        """階段1A: 並行收集URL"""
        url_tasks = []
        for legislator in legislators:
            for platform in platforms:
                url_tasks.append((legislator, platform, days))

        # 配置URL收集執行緒數 - 較保守
        max_url_workers = min(8, len(url_tasks))
        self.logger.info(f"🔧 URL收集: {max_url_workers}個並行任務")

        url_results = []
        with ThreadPoolExecutor(max_workers=max_url_workers) as executor:
            future_to_task = {}
            for legislator, platform, days in url_tasks:
                future = executor.submit(self._collect_urls_only, legislator, platform, days)
                future_to_task[future] = (legislator, platform)

            for future in as_completed(future_to_task):
                legislator, platform = future_to_task[future]
                try:
                    url_count = future.result()
                    url_results.append({
                        'legislator': legislator,
                        'platform': platform,
                        'url_count': url_count,
                        'has_urls': url_count > 0
                    })
                    self.logger.info(f"📋 {legislator}-{platform}: 收集到 {url_count} 個URL")
                except Exception as e:
                    self.logger.error(f"❌ {legislator}-{platform} URL收集失敗: {e}")
                    url_results.append({
                        'legislator': legislator,
                        'platform': platform,
                        'url_count': 0,
                        'has_urls': False
                    })

        return url_results

    def _parallel_data_crawling(self, url_results: List[Dict]) -> List[Dict]:
        """階段1B: 並行爬取評論數據"""
        # 只處理有URL的任務
        valid_tasks = [r for r in url_results if r['has_urls']]

        if not valid_tasks:
            self.logger.warning("⚠️ 沒有找到任何URL，跳過數據爬取階段")
            return []

        # 配置數據爬取執行緒數 - 更保守，因為每個任務內部還會開啟子執行緒
        max_data_workers = min(6, len(valid_tasks))
        self.logger.info(f"🔧 數據爬取: {max_data_workers}個並行任務")

        legislator_stats = {}
        with ThreadPoolExecutor(max_workers=max_data_workers) as executor:
            future_to_task = {}
            for task in valid_tasks:
                future = executor.submit(self._crawl_data_only, task['legislator'], task['platform'])
                future_to_task[future] = (task['legislator'], task['platform'])

            for future in as_completed(future_to_task):
                legislator, platform = future_to_task[future]
                try:
                    records = future.result()

                    if legislator not in legislator_stats:
                        legislator_stats[legislator] = {'legislator': legislator, 'success': False, 'records': 0}

                    legislator_stats[legislator]['records'] += records if records else 0
                    if records and records > 0:
                        legislator_stats[legislator]['success'] = True

                    self.logger.info(f"📊 {legislator}-{platform}: 爬取到 {records or 0} 筆資料")

                except Exception as e:
                    self.logger.error(f"❌ {legislator}-{platform} 數據爬取失敗: {e}")

        # 輸出最終統計
        for legislator, stats in legislator_stats.items():
            if stats['records'] > 0:
                self.logger.info(f"✅ {legislator}: 共 {stats['records']} 筆資料")
            else:
                self.logger.warning(f"⚠️ {legislator}: 沒有找到資料")

        return list(legislator_stats.values())

    def _collect_urls_only(self, legislator: str, platform: str, days: int) -> int:
        """只收集URL，不爬取評論內容"""
        try:
            if platform == 'youtube':
                # 計算cutoff_date
                from datetime import datetime, timedelta
                cutoff_date = None
                if days > 0:
                    cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                    self.logger.info(f"📅 {legislator}-YouTube: 篩選 {cutoff_date} 之後的影片")

                # 使用專門的URL收集函數，傳遞日期參數
                result = search_youtube_videos(legislator, headless=True, cutoff_date=cutoff_date)
                # search_youtube_videos返回(filename, video_data)，我們需要video_data的長度
                if isinstance(result, tuple) and len(result) == 2:
                    filename, video_data = result
                    url_count = len(video_data) if video_data else 0
                    return url_count
                else:
                    return 0
            elif platform == 'ptt':
                # PTT URL收集
                from ptt_crawler import setup_driver
                driver = setup_driver(headless=True)
                try:
                    result = fetch_ptt_links(driver, legislator)
                    # fetch_ptt_links返回(total_links, json_filename)，我們只要total_links
                    if isinstance(result, tuple):
                        url_count = result[0]
                    else:
                        url_count = result
                    return url_count if url_count else 0
                finally:
                    driver.quit()
            elif platform == 'threads':
                # Threads暫時使用完整爬蟲（因為沒有獨立的URL收集函數）
                url_count = crawl_threads_all_in_one(legislator, headless=True)
                return url_count if url_count else 0
            else:
                return 0
        except Exception as e:
            self.logger.error(f"URL收集失敗 {legislator}-{platform}: {e}")
            return 0

    def _crawl_data_only(self, legislator: str, platform: str) -> int:
        """只爬取評論數據（假設URL已經收集完成）"""
        try:
            if platform == 'youtube':
                # 使用WebDriver池進行YouTube評論爬取
                from webdriver_pool import WebDriverPool
                webdriver_pool = WebDriverPool(max_instances=3, headless=True)

                try:
                    result = crawl_youtube_comments_with_pool(
                        legislator,
                        webdriver_pool,
                        max_threads=3
                    )

                    if isinstance(result, dict) and result.get('success'):
                        return result.get('count', 0)
                    else:
                        return 0
                finally:
                    webdriver_pool.close_all()

            elif platform == 'ptt':
                # PTT評論爬取
                result = crawl_ptt(legislator)
                # crawl_ptt返回dict，從中獲取記錄數
                if isinstance(result, dict) and result.get('success'):
                    return result.get('count', 0)
                else:
                    return 0
            elif platform == 'threads':
                # Threads已經在URL收集階段完成，返回0避免重複
                return 0
            else:
                return 0
        except Exception as e:
            self.logger.error(f"數據爬取失敗 {legislator}-{platform}: {e}")
            return 0

    def _crawl_single_platform(self, legislator: str, platform: str, days: int) -> int:
        """爬取單一立委的單一平台數據"""
        try:
            self.logger.info(f"🕷️ 開始爬取: {legislator}-{platform}")

            if platform == 'youtube':
                # YouTube爬蟲（限制執行緒數避免過載）
                crawler_func = self.crawler_functions[platform]
                records = crawler_func(legislator, headless=True, num_threads=1)  # 每個任務只用1個執行緒
            elif platform == 'ptt':
                # PTT爬蟲
                crawler_func = self.crawler_functions[platform]
                records = crawler_func(legislator, days=days)
            elif platform == 'threads':
                # Threads爬蟲
                crawler_func = self.crawler_functions[platform]
                records = crawler_func(legislator, days=days)
            else:
                records = 0

            self.logger.info(f"✅ 完成爬取: {legislator}-{platform} = {records or 0} 筆")
            return records if records else 0

        except Exception as e:
            self.logger.error(f"❌ 爬取失敗: {legislator}-{platform}: {e}")
            return 0



    def _merge_to_alldata(self, legislators: List[str]) -> Dict:
        """階段2: 整合數據到alldata"""
        try:
            # 使用user_data_processor中的合併功能
            from user_data_processor import merge_legislators_data
            return merge_legislators_data(legislators)
        except Exception as e:
            self.logger.error(f"整合alldata失敗: {e}")
            return {'success': False, 'error': str(e)}
    
    def _process_user_data(self, legislators: List[str]) -> Dict:
        """階段3: 處理用戶數據"""
        try:
            return process_legislators_data(legislators)
        except Exception as e:
            self.logger.error(f"處理用戶數據失敗: {e}")
            return {'success': False, 'error': str(e)}
    
    def _analyze_emotions(self, legislators: List[str]) -> Dict:
        """階段4: Gemini情感分析"""
        try:
            return analyze_legislators_emotions(legislators)
        except Exception as e:
            self.logger.error(f"情感分析失敗: {e}")
            return {'success': False, 'error': str(e)}
    
    def _store_to_mongodb(self, legislators: List[str]) -> Dict:
        """階段5: 存儲到MongoDB"""
        try:
            mongo_manager = DataToMongo()
            return mongo_manager.process_legislators(legislators)
        except Exception as e:
            self.logger.error(f"MongoDB存儲失敗: {e}")
            return {'success': False, 'error': str(e)}

def main():
    """測試函數"""
    logging.basicConfig(level=logging.INFO)
    
    manager = SimpleCrawlerManager()
    result = manager.crawl_all_legislators(['牛煦庭'], days=1, platforms=['youtube'])
    
    print(f"結果: {result}")

if __name__ == "__main__":
    main()
