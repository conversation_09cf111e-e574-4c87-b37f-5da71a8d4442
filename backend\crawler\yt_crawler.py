import os
import json
import time
import threading
import re
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.action_chains import ActionChains
from json_utils import append_json_data, read_json_file, append_href_data, append_crawler_data, append_href_data_with_metadata

def initialize_driver(headless=True, retries=3):
    """
    建立Chrome瀏覽器驅動，支援headless模式與自動重試
    
    參數:
        headless: 是否使用無頭模式 (預設為True)
        retries: 重試次數 (預設為3次)
        
    返回:
        webdriver: Chrome瀏覽器驅動實例
    """
    for attempt in range(retries):
        try:
            chrome_options = Options()
            
            # 設定 headless 模式
            if headless:
                chrome_options.add_argument("--headless=new")  # 使用新版 headless 模式
                print("使用headless模式")
            else:
                print("使用非headless模式 (可見瀏覽器)")
            
            # 基本設定
            chrome_options.add_argument('--ignore-certificate-errors')
            chrome_options.add_argument('--ignore-ssl-errors')
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")  # 避免 GPU 相關問題
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-infobars")
            chrome_options.add_argument("--disable-notifications")
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--mute-audio")
            
            # 為每個瀏覽器實例分配不同的調試端口，避免衝突
            import random
            debug_port = random.randint(9000, 19000)
            chrome_options.add_argument(f"--remote-debugging-port={debug_port}")
            
            chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--start-maximized")  # 啟動時最大化
            chrome_options.add_argument("--log-level=3")
            
            # 移除可能導致多執行緒問題的選項
            # chrome_options.add_argument("--single-process")  # 移除此選項，因為可能導致多執行緒共享問題
            
            # 添加性能優化選項
            chrome_options.add_argument("--disable-default-apps")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-sync")
            chrome_options.add_argument("--disable-translate")
            
            # 添加 webdriver 管理相關選項
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option("useAutomationExtension", False)
            
            # 使用 webdriver-manager 和 Service
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 強制設置視窗大小
            driver.maximize_window()
            driver.set_window_size(1920, 1080)
            print("🖥️  已設定瀏覽器視窗大小為 1920x1080")
            
            # 設置頁面加載超時
            driver.set_page_load_timeout(30)
            
            print(f"✅ 成功建立YouTube爬蟲Chrome瀏覽器驅動{' (headless模式)' if headless else ''}")
            return driver
            
        except Exception as e:
            if attempt < retries - 1:
                print(f"⚠️  建立YouTube爬蟲Chrome瀏覽器驅動失敗 (嘗試 {attempt+1}/{retries}): {e}")
                # 在重試前等待一些時間，避免短時間內創建過多瀏覽器實例
                time.sleep(2 + attempt * 2)  # 隨著重試次數增加等待時間
            else:
                print(f"❌ 在 {retries} 次嘗試後無法建立YouTube爬蟲Chrome瀏覽器驅動: {e}")
                raise

def search_youtube_videos(name, output_dir=None, headless=True, cutoff_date=None):
    """
    搜尋 YouTube 影片並收集 URL，支援日期篩選
    
    參數:
        name: 搜尋關鍵字
        output_dir: 輸出目錄
        headless: 是否使用無頭模式
        cutoff_date: 截止日期，只收集此日期之後的影片
    """
    # 如果沒有指定輸出目錄，使用標準路徑
    if output_dir is None:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(current_dir, 'href', 'youtube')
    
    os.makedirs(output_dir, exist_ok=True)
    driver = initialize_driver(headless)
    
    # 強制設置視窗大小
    if not headless:
        driver.maximize_window()
        driver.set_window_size(1920, 1080)
        print("🖥️  已設定瀏覽器視窗大小為 1920x1080")
    
    driver.get("https://www.youtube.com/")
    time.sleep(3)
    
    # 搜尋
    search_box = driver.find_element(By.NAME, "search_query")
    search_box.clear()
    search_box.send_keys(name)
    search_box.send_keys(Keys.RETURN)
    time.sleep(5)
    
    # 處理日期篩選 - 嚴格按照指定日期
    if cutoff_date:
        if isinstance(cutoff_date, str):
            try:
                cutoff_date = datetime.strptime(cutoff_date, '%Y-%m-%d')
                print(f"📅 嚴格篩選日期: {cutoff_date.strftime('%Y-%m-%d')} 之後的影片")
            except:
                cutoff_date = None
                print("⚠️ 日期格式錯誤，不進行日期篩選")
        print(f"📅 將篩選 {cutoff_date.strftime('%Y-%m-%d') if cutoff_date else '無'} 之後的影片")
    else:
        print("📝 不進行日期篩選，收集所有影片")
    
    video_data = []
    processed_urls = set()
    last_new_url_time = time.time()
    max_scrolls = 100
    max_wait_time = 60  # 增加到60秒，避免過早退出
    print(f"⚙️ YouTube搜尋設置: 最大等待時間={max_wait_time}秒, 最大滾動次數={max_scrolls}")
    counter = 0
    
    # 準備即時寫入href檔案
    filename = f"{output_dir}/{name}.json"  # 統一命名為立委.json
    
    print(f"📁 href檔案路徑: {filename}")
    
    # 超時控制
    timeout_start = time.time()
    max_total_time = 420  # 5分鐘超時
    
    print(f"🔍 開始收集 {name} 的YouTube影片URL...")
    
    while counter <= max_scrolls:
        # 檢查總時間超時
        if time.time() - timeout_start > max_total_time:
            print(f"⏰ YouTube搜尋超時（{max_total_time}秒），結束搜尋")
            break
            
        try:
            # 滾動頁面載入更多影片
            driver.execute_script("window.scrollBy(0, 3000)")
            time.sleep(2)
            
            # 檢查是否有「沒有更多結果」的提示
            no_more_results = driver.find_elements(By.XPATH, "//span[contains(text(), '沒有更多結果') or contains(text(), 'No more results')]")
            if no_more_results:
                print("📄 YouTube: 已到達搜尋結果底部")
                break
            
            video_containers = driver.find_elements(By.CSS_SELECTOR, "ytd-video-renderer")
            new_url_found = False
            
            print(f"🔍 YouTube: 第{counter+1}次滾動，找到 {len(video_containers)} 個影片容器")
            
            for container in video_containers:
                try:
                    thumbnail = container.find_element(By.CSS_SELECTOR, "a#thumbnail")
                    url = thumbnail.get_attribute('href')
                    
                    # 獲取影片發布時間（重要：這裡要正確獲取）
                    try:
                        # 嘗試多種時間元素選擇器
                        time_elements = [
                            ".//span[contains(@class, 'style-scope ytd-video-meta-block')][2]",
                            ".//span[contains(@class, 'ytd-video-meta-block')][position()=last()]",
                            ".//div[@id='metadata-line']//span[2]",
                            ".//div[@id='metadata-line']//span[contains(text(), '前')]"
                        ]
                        
                        upload_time = None
                        for selector in time_elements:
                            try:
                                time_element = container.find_element(By.XPATH, selector)
                                potential_time = time_element.text.strip()

                                # 清理時間文字中的多餘空格（修復"16 小時前"問題）
                                potential_time = ' '.join(potential_time.split())

                                # 驗證是否為時間格式
                                if any(keyword in potential_time for keyword in ['前', '天', '小時', '分鐘', '秒', '個月', '年', '週', '星期']):
                                    upload_time = potential_time
                                    break
                            except Exception as e:
                                print(f"⚠️  時間元素選擇器失敗: {selector} -> {e}")
                                continue
                        
                        if not upload_time:
                            upload_time = "Unknown"
                            actual_date = datetime.now().strftime('%Y-%m-%d')
                            print(f"⚠️  未找到時間信息，使用當前日期: {actual_date}")
                        else:
                            actual_date = convert_relative_time(upload_time)
                            
                    except Exception as e:
                        upload_time = "Unknown"
                        actual_date = datetime.now().strftime('%Y-%m-%d')
                        print(f"❌ 獲取時間失敗: {e}，使用當前日期: {actual_date}")
                    
                    if url and url not in processed_urls:
                        # 過濾掉 YouTube Shorts
                        if '/shorts/' in url:
                            continue
                        
                        # 日期篩選（在URL收集階段）- 修復邏輯，使用精確時間比較
                        if cutoff_date and upload_time != "Unknown":
                            try:
                                # 獲取影片的精確時間（包含小時）
                                video_datetime = convert_relative_time_to_datetime(upload_time)
                                cutoff_date_only = cutoff_date.replace(hour=0, minute=0, second=0, microsecond=0)

                                # 修復：使用精確時間比較，收集 >= cutoff_date 的影片
                                if video_datetime >= cutoff_date_only:
                                    print(f"✅ 收集新影片: {upload_time} ({actual_date}) - 精確時間: {video_datetime.strftime('%Y-%m-%d %H:%M')}")
                                else:
                                    # 跳過舊影片，但每 10 個影片輸出一次日誌
                                    if len(processed_urls) % 10 == 0:
                                        print(f"⏳ 跳過舊影片: {upload_time} ({actual_date}) - 精確時間: {video_datetime.strftime('%Y-%m-%d %H:%M')}")
                                    continue
                            except Exception as date_err:
                                # 日期解析失敗，保留此影片
                                print(f"⚠️ 日期解析失敗: {actual_date} -> {date_err}，將保留此影片")
                                pass
                            
                        processed_urls.add(url)
                        data_entry = {
                            'url': url,
                            'time': upload_time,
                            'date': actual_date,
                            'post_time': actual_date,  # 新增：用於快速篩選的標準化時間
                            'added_time': datetime.now().strftime('%Y-%m-%d')
                        }
                        video_data.append(data_entry)
                        new_url_found = True
                        last_new_url_time = time.time()
                        
                        # 即時寫入href檔案（每收集5個URL就append一次）
                        if len(video_data) % 5 == 0:
                            # 使用append模式寫入新收集的完整數據結構
                            current_batch = video_data[-5:] if len(video_data) >= 5 else video_data
                            append_href_data_with_metadata("youtube", name, current_batch)
                            print(f"📝 YouTube: 已收集 {len(video_data)} 個影片URL，append到href檔案")
                        elif len(video_data) % 3 == 0:
                            print(f"🔄 YouTube: 已收集 {len(video_data)} 個影片URL（持續搜尋中...）")
                            
                except Exception as e:
                    print(f"⚠️  處理影片容器時出錯: {e}")
                    continue
                    
            # 檢查是否需要繼續滾動
            if not new_url_found and (time.time() - last_new_url_time) > max_wait_time:
                print(f"⏰ YouTube: 60秒內沒有新影片，結束搜尋")  # 直接使用固定數字，避免變數問題
                break
            
            # 檢查影片數量是否足夠
            if len(video_data) >= 1000:  # 限制最大影片數量
                print(f"📊 YouTube: 已收集足夠影片數量({len(video_data)})，結束搜尋")
                break
                
            counter += 1
            
            # 每10次滾動輸出進度
            if counter % 10 == 0:
                print(f"📄 YouTube: 已滾動 {counter} 次，收集到 {len(video_data)} 個影片")
                
        except Exception as e:
            print(f"⚠️  YouTube滾動時發生錯誤: {e}")
            time.sleep(2)
    
    driver.quit()
    
    # 最終append所有剩餘數據（包含完整metadata）
    if video_data:
        append_href_data_with_metadata("youtube", name, video_data)
        print(f"✅ YouTube: 搜尋完成，總共收集 {len(video_data)} 個影片URL")
        
        # 輸出時間統計
        if video_data:
            dates = [entry['date'] for entry in video_data if entry['date'] != datetime.now().strftime('%Y-%m-%d')]
            if dates:
                print(f"📊 收集的影片日期範圍: {min(dates)} 到 {max(dates)}")
        
        return filename, video_data
    else:
        print(f"⚠️  未找到符合條件的影片URL，請檢查搜尋關鍵字或日期篩選條件")
        # 建立空檔案，避免後續處理錯誤
        empty_data = []
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(empty_data, f, ensure_ascii=False, indent=2)
        return filename, empty_data
def scroll_to_bottom( driver):
        """滾動到底部，直到沒有新留言載入，並記錄最終的頁面高度"""
        last_comment_count = 0
        no_new_comments_count = 0
        max_no_new_comments = 6
        last_height = driver.execute_script("return document.documentElement.scrollHeight")  # 初始頁面高度
        while True:
            driver.execute_script("window.scrollBy(0, 500);")  # 滾動一定距離
            time.sleep(1)
            comments = driver.find_elements(By.XPATH, '//*[@id="contents"]/ytd-comment-thread-renderer')
            current_comment_count = len(comments)
            if current_comment_count == last_comment_count:
                no_new_comments_count += 1
                if no_new_comments_count >= max_no_new_comments:
                    last_height = driver.execute_script("return document.documentElement.scrollHeight")  # 記錄當前頁面高度
                    break
            else:
                no_new_comments_count = 0
            last_comment_count = current_comment_count
        return last_height  # 返回最終頁面高度

def get_buttons( driver):
    """滾動頁面並獲取所有可點擊的按鈕"""
    last_height = scroll_to_bottom(driver)  # 滾動頁面到底部，並獲取最終頁面高度
    reply_buttons = driver.find_elements(By.CSS_SELECTOR, 'button[aria-label*="則回覆"]')
    more_reply_buttons = driver.find_elements(By.CSS_SELECTOR, 'button[aria-label*="顯示更多回覆"]')
    show_more_buttons = driver.find_elements(By.CSS_SELECTOR, 'span[aria-label*="顯示完整內容"]')
    return reply_buttons + more_reply_buttons + show_more_buttons, last_height  # 返回按鈕與最終頁面高度

def click_buttons(driver):
    """
    按順序逐一點擊按鈕，直到按鈕數量不再變化或已經點擊所有按鈕，加入錯誤處理和重試機制
    
    Args:
        driver: Selenium WebDriver 實例
        
    Returns:
        None
    """
    wait = WebDriverWait(driver, 20)
    actions = ActionChains(driver)
    last_height = 0
    initial_buttons, _ = get_buttons(driver)
    total_buttons = len(initial_buttons)
    clicked_buttons = 0
    max_retries = 3  # 最大重試次數
    
    # 添加循環計數器，避免無限循環
    max_loops = 100  # 最大循環次數
    loop_count = 0
    
    while loop_count < max_loops:
        loop_count += 1
        try:
            buttons, current_height = get_buttons(driver)

            if len(buttons) == 0:
                print("沒有找到可點擊的按鈕，退出點擊循環")
                break

            # 判斷是否頁面滾動到底部或按鈕數量沒有變化
            if current_height == last_height:
                print(f"頁面高度沒有變化，可能已經到底部：{current_height}")
                # 多滾動幾次，確保真的到底部了
                for _ in range(3):
                    driver.execute_script("window.scrollBy(0, 500);")
                    time.sleep(1)
                new_height = driver.execute_script("return document.documentElement.scrollHeight")
                if new_height == current_height:
                    break
                else:
                    current_height = new_height
                    # 繼續處理

            # 比較目前按鈕數量與已計算的數量
            if len(buttons) <= clicked_buttons:
                print(f"已點擊按鈕數量：{clicked_buttons}，當前按鈕數量：{len(buttons)}，可能沒有新按鈕")
                break
            
            # 如果已經點擊了所有初始按鈕，也可以退出
            if clicked_buttons >= total_buttons and total_buttons > 0:
                print(f"已點擊所有初始按鈕（{total_buttons}個）")
                break
            
            clicked = False
            for i, button in enumerate(buttons):
                if i % 2 == 0:  # 每隔一個按鈕點擊，避免點擊過於頻繁
                    retry_count = 0
                    while retry_count < max_retries:
                        try:                    
                            # 先捲動到按鈕附近，確保它在視窗內
                            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                            time.sleep(0.5)
                            
                            # 移動到按鈕並嘗試點擊
                            actions.move_to_element(button).perform()
                            time.sleep(0.5)
                            
                            # 確保按鈕可點擊
                            wait.until(EC.element_to_be_clickable(button))
                            
                            # 嘗試用JavaScript點擊，這通常更可靠
                            driver.execute_script("arguments[0].click();", button)
                            clicked = True
                            clicked_buttons += 1
                            print(f"成功點擊第 {clicked_buttons} 個按鈕")
                            
                            # 給予頁面響應時間
                            time.sleep(1)
                            break  # 成功點擊，跳出重試循環
                        except Exception as e:
                            retry_count += 1
                            print(f"點擊按鈕時發生錯誤 (重試 {retry_count}/{max_retries}): {e}")
                            # 短暫暫停後重試
                            time.sleep(1)
                            
                            # 如果已經重試到最大次數，則嘗試下一個按鈕
                            if retry_count >= max_retries:
                                print(f"放棄點擊當前按鈕，嘗試下一個")
                                continue
                    
                    if clicked:
                        break  # 點擊一個按鈕後結束當前循環

            # 如果沒有成功點擊任何按鈕，嘗試滾動頁面並重新獲取按鈕
            if not clicked:
                print("當前循環中沒有點擊任何按鈕，嘗試滾動頁面")
                driver.execute_script("window.scrollBy(0, 500);")
                time.sleep(2)
                # 再檢查一次是否有新的按鈕
                new_buttons, _ = get_buttons(driver)
                if len(new_buttons) <= len(buttons):
                    print("滾動後沒有發現新按鈕，退出循環")
                    break
            
            last_height = current_height
            time.sleep(2)  # 等待頁面載入
            
        except Exception as e:
            print(f"點擊按鈕過程中發生錯誤: {e}")
            # 不中斷循環，繼續嘗試點擊其他按鈕
            time.sleep(2)

        # 向下滾動，便於加載新內容
        driver.execute_script("window.scrollBy(0, 300);")
        time.sleep(2)

    print(f"點擊按鈕程序結束：共點擊了 {clicked_buttons} 個按鈕，總共處理了 {loop_count} 個循環")

def convert_relative_time(relative_time):
    """
    將相對時間（如 '3 天前'、'直播時間：1 天前'）轉換為日期字符串（如 '2023-05-01'）
    支持多種格式，包括常見的中文相對時間表達
    
    Args:
        relative_time: 相對時間字符串
        
    Returns:
        日期字符串，格式為 YYYY-MM-DD
    """
    now = datetime.now()
    
    # 清理字符串，移除前綴如「直播時間：」、「首播：」等
    cleaned_time = relative_time.strip()
    if '：' in cleaned_time:
        cleaned_time = cleaned_time.split('：')[-1].strip()
    if ':' in cleaned_time:
        cleaned_time = cleaned_time.split(':')[-1].strip()
    
    try:
        # 處理各種時間格式，注意空格
        if " 天前" in cleaned_time:
            match = re.search(r'(\d+)\s*天前', cleaned_time)
            if match:
                days = int(match.group(1))
                actual_time = now - timedelta(days=days)
            else:
                actual_time = now
        elif " 小時前" in cleaned_time:
            match = re.search(r'(\d+)\s*小時前', cleaned_time)
            if match:
                hours = int(match.group(1))
                actual_time = now - timedelta(hours=hours)
            else:
                actual_time = now
        elif " 分鐘前" in cleaned_time:
            match = re.search(r'(\d+)\s*分鐘前', cleaned_time)
            if match:
                minutes = int(match.group(1))
                actual_time = now - timedelta(minutes=minutes)
            else:
                actual_time = now
        elif " 秒前" in cleaned_time:
            match = re.search(r'(\d+)\s*秒前', cleaned_time)
            if match:
                seconds = int(match.group(1))
                actual_time = now - timedelta(seconds=seconds)
            else:
                actual_time = now
        elif " 個月前" in cleaned_time:
            match = re.search(r'(\d+)\s*個月前', cleaned_time)
            if match:
                months = int(match.group(1))
                actual_time = now - timedelta(days=months*30)  # 粗略估計一個月為30天
            else:
                actual_time = now
        elif " 年前" in cleaned_time:
            match = re.search(r'(\d+)\s*年前', cleaned_time)
            if match:
                years = int(match.group(1))
                actual_time = now - timedelta(days=years*365)  # 粗略估計一年為365天
            else:
                actual_time = now
        elif " 週前" in cleaned_time:
            match = re.search(r'(\d+)\s*週前', cleaned_time)
            if match:
                weeks = int(match.group(1))
                actual_time = now - timedelta(weeks=weeks)
            else:
                actual_time = now
        elif " 星期前" in cleaned_time:
            match = re.search(r'(\d+)\s*星期前', cleaned_time)
            if match:
                weeks = int(match.group(1))
                actual_time = now - timedelta(weeks=weeks)
            else:
                actual_time = now
        else:
            # 如果無法解析，返回當前時間
            print(f"⚠️ 無法解析時間格式: '{cleaned_time}'，使用當前時間")
            actual_time = now
            
    except Exception as e:
        print(f"❌ 解析時間時出錯: {relative_time} -> {e}，使用當前時間")
        actual_time = now
    
    return actual_time.strftime('%Y-%m-%d')

def convert_relative_time_to_datetime(relative_time):
    """
    將相對時間轉換為精確的datetime對象（包含小時信息）

    Args:
        relative_time: 相對時間字符串

    Returns:
        datetime對象
    """
    now = datetime.now()

    # 清理字符串，移除前綴如「直播時間：」、「首播：」等
    cleaned_time = relative_time.strip()
    if '：' in cleaned_time:
        cleaned_time = cleaned_time.split('：')[-1].strip()
    if ':' in cleaned_time:
        cleaned_time = cleaned_time.split(':')[-1].strip()

    try:
        # 處理各種時間格式，注意空格
        if " 天前" in cleaned_time:
            match = re.search(r'(\d+)\s*天前', cleaned_time)
            if match:
                days = int(match.group(1))
                actual_time = now - timedelta(days=days)
            else:
                actual_time = now
        elif " 小時前" in cleaned_time:
            match = re.search(r'(\d+)\s*小時前', cleaned_time)
            if match:
                hours = int(match.group(1))
                actual_time = now - timedelta(hours=hours)
            else:
                actual_time = now
        elif " 分鐘前" in cleaned_time:
            match = re.search(r'(\d+)\s*分鐘前', cleaned_time)
            if match:
                minutes = int(match.group(1))
                actual_time = now - timedelta(minutes=minutes)
            else:
                actual_time = now
        elif " 秒前" in cleaned_time:
            match = re.search(r'(\d+)\s*秒前', cleaned_time)
            if match:
                seconds = int(match.group(1))
                actual_time = now - timedelta(seconds=seconds)
            else:
                actual_time = now
        elif " 個月前" in cleaned_time:
            match = re.search(r'(\d+)\s*個月前', cleaned_time)
            if match:
                months = int(match.group(1))
                actual_time = now - timedelta(days=months * 30)  # 近似計算
            else:
                actual_time = now
        elif " 年前" in cleaned_time:
            match = re.search(r'(\d+)\s*年前', cleaned_time)
            if match:
                years = int(match.group(1))
                actual_time = now - timedelta(days=years * 365)  # 近似計算
            else:
                actual_time = now
        elif " 週前" in cleaned_time:
            match = re.search(r'(\d+)\s*週前', cleaned_time)
            if match:
                weeks = int(match.group(1))
                actual_time = now - timedelta(weeks=weeks)
            else:
                actual_time = now
        else:
            # 無法識別的格式，使用當前時間
            actual_time = now

    except Exception as e:
        print(f"❌ 解析時間時出錯: {relative_time} -> {e}，使用當前時間")
        actual_time = now

    return actual_time

def crawl_youtube_comments(name, headless=True, num_threads=4, last_crawled_time=None):
    """
    爬取 YouTube 評論，支援日期篩選
    
    參數:
        name: 要搜尋的關鍵字
        headless: 是否使用無界面模式
        num_threads: 執行緒數量
        last_crawled_time: 上次爬取的時間點 (datetime 物件或字串)，如果提供，只爬取此時間後的影片
    """
    # 處理 last_crawled_time 參數
    cutoff_date = None
    if last_crawled_time:
        if isinstance(last_crawled_time, str):
            try:
                # 嘗試多種常見的日期格式
                for date_format in ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y', '%Y%m%d']:
                    try:
                        cutoff_date = datetime.strptime(last_crawled_time, date_format)
                        break
                    except ValueError:
                        continue
                
                # 如果以上格式都不符合，嘗試 ISO 格式
                if cutoff_date is None:
                    cutoff_date = datetime.fromisoformat(last_crawled_time.replace('Z', '+00:00'))
            except Exception as e:
                print(f"無法解析日期格式: {last_crawled_time}，錯誤: {e}")
                print("將不使用日期篩選")
                cutoff_date = None
        elif isinstance(last_crawled_time, datetime):
            cutoff_date = last_crawled_time
            print(f"使用提供的 datetime 物件作為日期篩選: {cutoff_date.strftime('%Y-%m-%d')}")
    
    if cutoff_date:
        print(f"將篩選 {cutoff_date.strftime('%Y-%m-%d')} 之後的影片")
    else:
        print("未指定日期篩選，將爬取所有影片")
    
    # 第一步：獲取所有影片 URL
    print(f"正在搜尋 {name} 的 YouTube 影片...")
    url_json_file, url_data = search_youtube_videos(name, headless=headless)
    
    # 第二步：從爬取的 URL 數據中排除 shorts
    all_urls = [item["url"] for item in url_data if "/shorts/" not in item["url"]]
    print(f"找到 {len(all_urls)} 個影片 URL（排除 shorts）")
    
    # 第三步：如果已存在輸出文件，讀取以避免重複處理
    output_dir = './data/youtube'  # 統一資料夾結構
    os.makedirs(output_dir, exist_ok=True)
    output_file = f'{output_dir}/{name}.json'  # 統一命名為立委.json
    processed_video_urls = set()
    existing_videos_data = []
    
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_videos_data = json.load(f)
                
            # 從現有數據中提取已處理的視頻信息
            for video_data in existing_videos_data:
                # 假設有一個字段存儲了原始URL，如果沒有可以修改此邏輯
                if 'video_url' in video_data:
                    processed_video_urls.add(video_data['video_url'])
                    
            print(f"從現有數據中找到 {len(processed_video_urls)} 個已處理的影片")
        except Exception as e:
            print(f"讀取現有數據時出錯: {e}")
    
    # 第四步：準備要爬取的 URL 列表
    to_crawl_urls = []
    for url in all_urls:
        # 跳過已處理的URL
        if url in processed_video_urls:
            continue
        
        # 如果有時間資訊，可以在這裡進行初步篩選
        # 由於 url_data 中的時間不一定準確，將在實際爬取時再做詳細篩選
        to_crawl_urls.append(url)
    
    print(f"需要爬取 {len(to_crawl_urls)} 個新影片")
    
    # 如果沒有新影片需要爬取，直接返回
    if not to_crawl_urls:
        print(f"沒有新影片需要爬取，返回現有數據")
        return output_file
    
    # 第五步：設置多執行緒爬取影片
    all_videos_data = existing_videos_data.copy()
    all_videos_data_lock = threading.Lock()
    
    def process_video_url(url_list, thread_id):
        """處理一組YouTube URLs的工作執行緒函數"""
        print(f"執行緒 {thread_id} 開始處理 {len(url_list)} 個影片URL")
        
        # 嘗試建立瀏覽器，最多重試3次
        driver = None
        max_retries = 3
        for attempt in range(max_retries):
            try:
                driver = initialize_driver(headless)
                print(f"執行緒 {thread_id} 成功建立瀏覽器")
                break
            except Exception as e:
                print(f"執行緒 {thread_id} 嘗試 {attempt+1}/{max_retries} 建立瀏覽器失敗: {e}")
                if attempt < max_retries - 1:
                    time.sleep(3 * (attempt + 1))  # 逐漸增加等待時間
                else:
                    print(f"執行緒 {thread_id} 無法建立瀏覽器，放棄執行")
                    return
        
        try:
            for i, url in enumerate(url_list):
                try:
                    print(f"執行緒 {thread_id} 處理第 {i+1}/{len(url_list)} 個URL: {url}")
                    driver.get(url)
                    time.sleep(10)  # 初始等待頁面加載
                    
                    # 嘗試暫停影片播放
                    try:
                        video_element = driver.find_element(By.CSS_SELECTOR, 'video')
                        driver.execute_script("arguments[0].pause();", video_element)
                        print(f"執行緒 {thread_id}: 已暫停影片播放")
                    except Exception as e:
                        print(f"執行緒 {thread_id}: 無法暫停影片: {e}")
                    
                    # 慢慢滾動頁面以確保加載留言
                    driver.execute_script("window.scrollBy(0, 450);")
                    time.sleep(5)
                    driver.execute_script("window.scrollBy(0, 450);")
                    time.sleep(5)
                    
                    # 獲取影片標題
                    try:
                        title_element = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, '#title.style-scope.ytd-watch-metadata'))
                        )
                        video_title = title_element.text
                        print(f"執行緒 {thread_id}: 成功獲取影片標題: {video_title[:30]}...")
                    except Exception as e:
                        print(f"執行緒 {thread_id}: 無法獲取影片標題: {e}")
                        continue
                    try:
                        parent_element = driver.find_element(By.CSS_SELECTOR, 'div#leading-section')
                        child_elements = parent_element.find_elements(By.CSS_SELECTOR, 'span.style-scope.yt-formatted-string')
                        span_number = 1
                        for span in child_elements:
                            text = span.text.strip()
                            cleaned_text = text.replace(',', '').strip()
                            if cleaned_text.isdigit():
                                span_number = int(cleaned_text)
                    except Exception:
                        span_number = 1
                    if span_number < 1:
                        return
                    try:
                        comment_order = driver.find_element(By.CSS_SELECTOR, '#label.dropdown-trigger.style-scope.yt-dropdown-menu')
                        comment_order.click()
                        time.sleep(1)
                        comment_order_true = driver.find_element(By.XPATH, '//tp-yt-paper-item[@role="option" and contains(.,"由新到舊")]')
                        comment_order_true.click()
                    except NoSuchElementException:
                        pass
                    time.sleep(2)
                    try:
                        expand_button = driver.find_element(By.XPATH, '//*[@id="expand"]')
                        expand_button.click()
                        time.sleep(2)
                    except NoSuchElementException:
                        pass
                    try:
                        post_time_element = driver.find_element(By.XPATH, '//*[@id="info"]/span[3]')
                        video_publish_time = post_time_element.text
                    except NoSuchElementException:
                        video_publish_time = "未知"
                    
                    # 日期篩選邏輯
                    if cutoff_date and video_publish_time != "未知":
                        try:
                            # 嘗試更健壯的方式解析日期
                            video_date_str = convert_relative_time(video_publish_time)
                            video_datetime = datetime.strptime(video_date_str, '%Y-%m-%d')
                            
                            if video_datetime < cutoff_date:
                            
                                continue
                            else:
                                print(f"處理新影片: {video_title} (發布於 {video_date_str}，晚於或等於篩選日期 {cutoff_date.strftime('%Y-%m-%d')})")
                        except Exception as e:
                            print(f"解析影片日期時發生錯誤: {e}")
                            print(f"原始日期字串: '{video_publish_time}'")
                            # 由於無法確定日期，我們默認處理此影片
                            print(f"無法確定日期，將處理影片: {video_title}")
                    else:
                        if cutoff_date:
                            print(f"處理影片 (未知發布日期): {video_title}")
                        else:
                            print(f"處理所有影片 (未指定日期篩選): {video_title}")
                    try:
                        video_like_element = driver.find_element(By.XPATH, '//*[@id="top-level-buttons-computed"]/segmented-like-dislike-button-view-model/yt-smartination/div/div/like-button-view-model/toggle-button-view-model/button-view-model/button/div[2]')
                        video_like = video_like_element.text
                    except NoSuchElementException:
                        video_like = "未知"
                    try:
                        view_count_element = driver.find_element(By.XPATH,'//*[@id="info"]/span[1]')
                        view_count = view_count_element.text
                    except NoSuchElementException:
                        view_count = "未知"
                    time.sleep(1)
                    click_buttons(driver)
                
                    driver.execute_script("window.scrollTo(0, 0);")
                    time.sleep(1)

                    scroll_to_bottom(driver)
                    # 初始化儲存留言的結構
                    comments_data = []
                    comment_threads = driver.find_elements(By.XPATH, '//*[@id="contents"]/ytd-comment-thread-renderer')
                    for thread in comment_threads:
                        try:
                            main_user = thread.find_element(By.XPATH, './/*[@id="header-author"]/h3').text.strip()
                            main_time = thread.find_element(By.XPATH, './/*[@id="published-time-text"]').text.strip()
                            main_comment = thread.find_element(By.XPATH, './/*[@id="content-text"]/span').text.strip()
                            main_likes = thread.find_element(By.XPATH, './/*[@id="vote-count-middle"]').text.strip()
                            comment_entry = {
                                '主留言': {
                                    '用戶名': main_user,
                                    '留言時間': main_time,
                                    '留言內容': main_comment,
                                    '按讚數': main_likes
                                },
                                '回覆': []
                            }
                            reply_elements = thread.find_elements(By.CSS_SELECTOR, '#replies .style-scope.ytd-comment-replies-renderer')
                            reply_set = set()
                            for element in reply_elements:
                                reply_user = ""
                                reply_comment = ""
                                reply_time = ""
                                reply_likes = ""
                                try:
                                    reply_user = element.find_element(By.XPATH, './/*[@id="header-author"]/h3').text.strip()
                                except NoSuchElementException:
                                    reply_user = ""
                                try:
                                    reply_comment = element.find_element(By.XPATH, './/*[@id="content-text"]').text.strip()
                                except NoSuchElementException:
                                    reply_comment = ""
                                try:
                                    reply_time = element.find_element(By.XPATH, './/*[@id="published-time-text"]').text.strip()
                                except NoSuchElementException:
                                    reply_time = ""
                                try:
                                    reply_likes = element.find_element(By.XPATH, './/*[@id="vote-count-middle"]').text.strip() or "0"
                                except NoSuchElementException:
                                    reply_likes = "0"
                                reply_entry = (reply_user, reply_time, reply_comment, reply_likes)
                                if reply_entry not in reply_set:
                                    reply_set.add(reply_entry)
                                    comment_entry['回覆'].append({
                                        "回覆留言者": reply_user,
                                        "回覆留言時間": reply_time,
                                        "回覆留言內容": reply_comment,
                                        "回覆留言按讚數": reply_likes
                                    })
                            comments_data.append(comment_entry)
                        except NoSuchElementException:
                            continue
                    video_data = {
                        '影片標題': video_title,
                        '影片留言數': span_number,
                        '影片觀看次數': view_count,
                        '影片按讚數': video_like,
                        '影片發布時間': video_publish_time,
                        '留言資料': comments_data,
                        'video_url': url  # 添加URL以方便將來識別
                    }
                    with all_videos_data_lock:
                        all_videos_data.append(video_data)
                        with open(output_file, 'w', encoding='utf-8') as json_file:
                            json.dump(all_videos_data, json_file, ensure_ascii=False, indent=4)
                except Exception as e:
                    print(f'執行緒 {thread_id} 爬取 {url} 時出錯: {e}')
                    import traceback
                    traceback.print_exc()
        finally:
            if driver:
                try:
                    print(f"執行緒 {thread_id} 結束工作，關閉瀏覽器")
                    driver.quit()
                except Exception as e:
                    print(f"執行緒 {thread_id} 關閉瀏覽器時出錯: {e}")
    
    # 多執行緒分配
    num_threads = min(num_threads, len(to_crawl_urls))
    if num_threads < 1:
        num_threads = 1
    chunk_size = len(to_crawl_urls) // num_threads
    remainder = len(to_crawl_urls) % num_threads
    url_chunks = []
    start = 0
    for i in range(num_threads):
        end = start + chunk_size + (1 if i < remainder else 0)
        url_chunks.append(to_crawl_urls[start:end])
        start = end
    threads = []
    for i in range(num_threads):
        t = threading.Thread(target=process_video_url, args=(url_chunks[i], i+1))
        threads.append(t)
        t.start()
        time.sleep(1.5)
    for t in threads:
        t.join()
    print(f'已儲存 {output_file}')
    return output_file

def crawl_youtube_comments_with_pool(name, webdriver_pool, last_crawled_time=None, max_threads=3):
    """
    使用 WebDriver 池爬取 YouTube 評論，支援多線程優化
    讀取已搜尋的URL並進行留言爬取
    
    參數:
        name: 要搜尋的關鍵字
        webdriver_pool: WebDriver 池實例
        last_crawled_time: 上次爬取的時間點
        max_threads: 最大線程數
    """
    import threading
    from concurrent.futures import ThreadPoolExecutor, as_completed
    
    print(f"🚀 使用 WebDriver 池爬取 YouTube: {name}")
    
    # 第一步：讀取已搜尋的影片 URL（從 href 檔案讀取）
    video_urls = []
    try:
        # 讀取 href 檔案中的 URL
        current_dir = os.path.dirname(os.path.abspath(__file__))
        href_file = os.path.join(current_dir, 'href', 'youtube', f'{name}.json')
        
        if os.path.exists(href_file):
            href_data = read_json_file(href_file, [])
            print(f"� 從href檔案讀取到 {len(href_data)} 個URL")
            
            # 轉換為標準格式
            for item in href_data:
                if isinstance(item, dict) and 'url' in item:
                    video_urls.append({
                        'url': item['url'],
                        'time': item.get('time', 'Unknown'),
                        'date': item.get('date', datetime.now().strftime('%Y-%m-%d'))
                    })
                elif isinstance(item, str):
                    video_urls.append({
                        'url': item,
                        'time': 'Unknown', 
                        'date': datetime.now().strftime('%Y-%m-%d')
                    })
        else:
            print(f"⚠️  href檔案不存在: {href_file}")
            return None
            
    except Exception as e:
        print(f"❌ 讀取href檔案時出錯: {e}")
        return None
    
    if not video_urls:
        print("⚠️  沒有找到任何影片URL")
        return None
    
    # 注意：不在內容爬取階段進行日期篩選，因為URL收集階段已經篩選過了
    # 這裡直接爬取所有從href文件讀取到的影片
    print(f"📅 準備爬取所有收集到的影片: {len(video_urls)} 個")
    
    if not video_urls:
        print("⚠️  日期篩選後沒有符合條件的影片")
        return None
    
    print(f"✅ 準備爬取 {len(video_urls)} 個影片的留言")
    
    # 第二步：並行爬取影片評論
    all_comments = []
    successful_videos = 0
    
    def crawl_single_video(video_info):
        """爬取單個影片的評論"""
        try:
            with webdriver_pool.get_driver() as video_driver:
                url = video_info['url']
                print(f"🎬 爬取影片評論: {url}")
                
                video_driver.get(url)
                time.sleep(5)  # 增加等待時間
                
                # 嘗試暫停影片播放
                try:
                    video_element = video_driver.find_element(By.CSS_SELECTOR, 'video')
                    video_driver.execute_script("arguments[0].pause();", video_element)
                    print(f"已暫停影片播放")
                except Exception:
                    pass
                
                # 獲取影片標題
                try:
                    title_element = WebDriverWait(video_driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '#title.style-scope.ytd-watch-metadata'))
                    )
                    video_title = title_element.text
                    print(f"影片標題: {video_title[:50]}...")
                except Exception:
                    video_title = "未知標題"
                
                # 獲取影片基本資訊
                try:
                    view_count_element = video_driver.find_element(By.XPATH, '//*[@id="info"]/span[1]')
                    view_count = view_count_element.text
                except Exception:
                    view_count = "未知"
                
                try:
                    video_like_element = video_driver.find_element(By.XPATH, '//*[@id="top-level-buttons-computed"]/segmented-like-dislike-button-view-model/yt-smartination/div/div/like-button-view-model/toggle-button-view-model/button-view-model/button/div[2]')
                    video_like = video_like_element.text
                except Exception:
                    video_like = "未知"
                
                try:
                    post_time_element = video_driver.find_element(By.XPATH, '//*[@id="info"]/span[3]')
                    video_publish_time = post_time_element.text
                except Exception:
                    video_publish_time = "未知"
                video_driver.execute_script("window.scrollBy(0, 450);")
                time.sleep(3)
                video_driver.execute_script("window.scrollBy(0, 450);")
                time.sleep(3)
                  # 設定評論排序為由新到舊
                try:
                    comment_order = video_driver.find_element(By.CSS_SELECTOR, '#label.dropdown-trigger.style-scope.yt-dropdown-menu')
                    comment_order.click()
                    time.sleep(1)
                    comment_order_true = video_driver.find_element(By.XPATH, '//tp-yt-paper-item[@role="option" and contains(.,"由新到舊")]')
                    comment_order_true.click()
                    time.sleep(2)
                except Exception:
                    pass
                # 滾動到評論區
                video_driver.execute_script("window.scrollBy(0, 450);")
                time.sleep(3)
                video_driver.execute_script("window.scrollBy(0, 450);")
                time.sleep(3)
                
                # 點擊按鈕展開評論
                click_buttons(video_driver)
                
                # 展開描述
                try:
                    expand_button = video_driver.find_element(By.XPATH, '//*[@id="expand"]')
                    expand_button.click()
                    time.sleep(2)
                except Exception:
                    pass
                
              
                
                # 滾動載入更多評論
                scroll_to_bottom(video_driver)
                
                # 收集詳細的評論資料
                comments_data = []
                comment_threads = video_driver.find_elements(By.XPATH, '//*[@id="contents"]/ytd-comment-thread-renderer')
                
                for i, thread in enumerate(comment_threads):
                    try:
                        # 主留言資訊
                        main_user = thread.find_element(By.XPATH, './/*[@id="header-author"]/h3').text.strip()
                        main_time = thread.find_element(By.XPATH, './/*[@id="published-time-text"]').text.strip()
                        main_comment = thread.find_element(By.XPATH, './/*[@id="content-text"]/span').text.strip()
                        main_likes = thread.find_element(By.XPATH, './/*[@id="vote-count-middle"]').text.strip()
                        
                        # 建立主留言資料（與備份格式一致）
                        comment_entry = {
                            '主留言': {
                                '用戶名': main_user,
                                '留言時間': main_time,
                                '留言內容': main_comment,
                                '按讚數': main_likes
                            },
                            '回覆': []
                        }
                        
                        # 收集回覆留言
                        try:
                            # 展開回覆 
                            reply_button = thread.find_element(By.CSS_SELECTOR, '#more-replies')
                            reply_button.click()
                            time.sleep(1)
                        except:
                            pass
                        
                        reply_elements = thread.find_elements(By.CSS_SELECTOR, '#replies .style-scope.ytd-comment-replies-renderer')
                        reply_set = set()
                        for element in reply_elements:
                            try:
                                reply_user = element.find_element(By.XPATH, './/*[@id="header-author"]/h3').text.strip()
                                reply_comment = element.find_element(By.XPATH, './/*[@id="content-text"]').text.strip()
                                reply_time = element.find_element(By.XPATH, './/*[@id="published-time-text"]').text.strip()
                                reply_likes = element.find_element(By.XPATH, './/*[@id="vote-count-middle"]').text.strip() or "0"
                                
                                reply_entry = (reply_user, reply_time, reply_comment, reply_likes)
                                if reply_entry not in reply_set:
                                    reply_set.add(reply_entry)
                                    comment_entry['回覆'].append({
                                        "回覆留言者": reply_user,
                                        "回覆留言時間": reply_time,
                                        "回覆留言內容": reply_comment,
                                        "回覆留言按讚數": reply_likes
                                    })
                            except:
                                continue
                        
                        comments_data.append(comment_entry)
                        
                    except Exception:
                        continue
                
                # 建立影片資料（與備份格式一致）
                video_data = {
                    'video_id': f"youtube_{video_title}_{url}",  # 添加 unique ID
                    '影片標題': video_title,
                    '影片留言數': len(comments_data),
                    '影片觀看次數': view_count,
                    '影片按讚數': video_like,
                    '影片發布時間': video_publish_time,
                    '留言資料': comments_data,
                    'video_url': url  # 保留URL以便識別
                }
                
                print(f"✅ 影片爬取完成，收集到 {len(comments_data)} 條評論")
                return [video_data]  # 返回影片資料列表
                
        except Exception as e:
            print(f"❌ 爬取影片評論時出錯: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    # 使用線程池並行處理
    print(f"🔄 使用 {max_threads} 個線程並行爬取評論...")
    
    with ThreadPoolExecutor(max_workers=max_threads) as executor:
        future_to_video = {
            executor.submit(crawl_single_video, video_info): video_info
            for video_info in video_urls  # 處理所有影片，不限制數量
        }
        
        for future in as_completed(future_to_video):
            video_info = future_to_video[future]
            try:
                comments = future.result()
                if comments:
                    # 🔥 立即保存每個影片的評論，而不是等所有影片爬完
                    append_success = append_crawler_data("youtube", name, comments)
                    if append_success:
                        all_comments.extend(comments)
                        successful_videos += 1
                        print(f"💾 已保存影片評論: {len(comments)} 條，累計: {len(all_comments)} 條")
                    else:
                        print(f"❌ 保存影片評論失敗")
            except Exception as e:
                print(f"❌ 線程執行失敗: {e}")

    # 第三步：返回最終結果
    print(f"✅ YouTube 爬取完成")
    print(f"   處理影片數: {successful_videos}/{len(video_urls)}")
    print(f"   總評論數: {len(all_comments)}")

    # 返回統一格式
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_file = os.path.join(current_dir, 'data', 'youtube', f'{name}.json')

    if successful_videos > 0:
        return {
            'success': True,
            'data_file': data_file,
            'count': len(all_comments),
            'videos_processed': successful_videos,
            'total_videos': len(video_urls)
        }
    else:
        print("⚠️  沒有成功爬取任何影片")
        return {
            'success': False,
            'error': '沒有成功爬取任何影片',
            'count': 0
        }

if __name__ == '__main__':
    crawl_youtube_comments('葉元之',False ,4, '2025-06-29')