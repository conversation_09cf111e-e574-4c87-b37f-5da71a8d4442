#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import time
import threading
import importlib.util
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

# 導入 Selenium 相關模組
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 導入 WebDriver 池
try:
    from .webdriver_pool import get_global_pool, close_global_pool
except ImportError:
    # 如果相對導入失敗，嘗試直接導入
    sys.path.append(os.path.dirname(__file__))
    from webdriver_pool import get_global_pool, close_global_pool

class MultiPlatformCrawler:
    """多平台爬蟲管理器 - 使用 WebDriver 池優化資源管理"""
    
    def __init__(self, base_dir=None, max_webdrivers=3):
        self.base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        self.platforms = ['youtube', 'ptt', 'threads', 'facebook']
        self.max_webdrivers = max_webdrivers
        self.webdriver_pool = None
    
    def crawl_politician_all_platforms(self, politician_name, cutoff_date_str=None, platforms=None, max_workers=6, headless=True):
        """
        兩階段爬取立委在指定平台的資料：
        階段1：同時收集所有平台的URL
        階段2：逐一爬取各平台資料（每個平台用6個執行緒）
        
        Args:
            politician_name: 立委姓名
            cutoff_date_str: 截止日期字串 (YYYY-MM-DD)
            platforms: 要爬取的平台列表，預設為全部平台
            max_workers: 每個平台的最大執行緒數（預設: 6）
            headless: 是否使用無頭模式 (預設: True)
        
        Returns:
            dict: 包含結果的字典
        """
        print(f"\n{'='*80}")
        print(f"🚀 開始兩階段爬取立委: {politician_name}")
        print(f"📅 截止日期: {cutoff_date_str}")
        print(f"🌐 平台: {platforms or self.platforms}")
        print(f"🧵 每平台執行緒數: {max_workers}")
        print(f"{'='*80}")
        
        # 解析截止日期為 datetime 對象
        cutoff_datetime = None
        if cutoff_date_str:
            try:
                cutoff_datetime = datetime.strptime(cutoff_date_str, '%Y-%m-%d')
            except:
                print(f"⚠️  無法解析日期: {cutoff_date_str}")
        
        # 使用指定的平台或所有平台
        target_platforms = platforms or self.platforms
        results = {}
        
        # ============ 階段1：並行收集所有平台的 URL ============
        print(f"\n🔗 階段1：並行收集所有平台的 URL...")
        url_results = self._collect_urls_parallel(politician_name, cutoff_datetime, target_platforms, headless)
        
        # ============ 階段2：逐一爬取各平台資料（每個平台用6個執行緒）============
        print(f"\n📊 階段2：逐一爬取各平台資料（每個平台用 {max_workers} 個執行緒）...")
        content_results = self._crawl_content_sequential(politician_name, cutoff_datetime, target_platforms, headless, max_workers, url_results)
        
        # 合併結果
        for platform in target_platforms:
            url_result = url_results.get(platform, {})
            content_result = content_results.get(platform, {})
            
            # 如果URL收集或內容爬取任一失敗，則標記為失敗
            if url_result.get('error') or content_result.get('error'):
                results[platform] = {
                    'error': url_result.get('error') or content_result.get('error'),
                    'url_collection': url_result,
                    'content_crawling': content_result
                }
            else:
                results[platform] = {
                    'url_collection': url_result,
                    'content_crawling': content_result,
                    'success': True
                }
        
        successful_platforms = sum(1 for result in results.values() if result.get('success'))
        total_platforms = len(target_platforms)
        print(f"\n📊 最終爬取結果統計:")
        print(f"   成功平台: {successful_platforms}/{total_platforms}")
        for platform, result in results.items():
            status = "✅ 成功" if result.get('success') else f"❌ 失敗: {result.get('error', '未知錯誤')}"
            print(f"   {platform}: {status}")
        
        return {
            'results': results,
            'summary': {
                'politician_name': politician_name,
                'successful_platforms': successful_platforms,
                'total_platforms': total_platforms,
                'success_rate': successful_platforms / total_platforms if total_platforms > 0 else 0
            }
        }
    
    def _collect_urls_parallel(self, politician_name, cutoff_datetime, target_platforms, headless):
        """
        階段1：並行收集所有平台的 URL
        
        Args:
            politician_name: 立委姓名
            cutoff_datetime: 截止日期
            target_platforms: 目標平台列表
            headless: 是否無頭模式
        
        Returns:
            dict: 各平台的URL收集結果
        """
        # 清理可能的殭屍進程
        try:
            import subprocess
            subprocess.run("taskkill /f /im chrome.exe /t 2>nul & taskkill /f /im chromedriver.exe /t 2>nul", shell=True)
            print("🧹 殭屍進程清理完成")
        except:
            pass
            
        # 初始化較小的 WebDriver 池用於 URL 收集（3個就夠了）
        url_pool = get_global_pool(max_instances=3, headless=headless)
        print(f"🏊 URL收集池狀態: {url_pool.get_status()}")
        
        url_results = {}
        print(f"🔗 開始並行收集 URL - 立委: {politician_name}")
        
        with ThreadPoolExecutor(max_workers=min(3, len(target_platforms))) as executor:
            future_to_platform = {}
            
            for platform in target_platforms:
                print(f"📤 提交 {platform.upper()} URL收集任務...")
                future = executor.submit(self._collect_platform_urls, platform, politician_name, cutoff_datetime, url_pool)
                future_to_platform[future] = platform
            
            completed_count = 0
            for future in as_completed(future_to_platform):
                platform = future_to_platform[future]
                completed_count += 1
                try:
                    result = future.result()
                    url_results[platform] = result
                    if result and not result.get('error'):
                        count_info = ""
                        if 'count' in result:
                            count_info = f" ({result['count']} 個項目)"
                        print(f"✅ [{completed_count}/{len(target_platforms)}] {platform.upper()} URL收集完成{count_info}")
                    else:
                        print(f"⚠️  [{completed_count}/{len(target_platforms)}] {platform.upper()} URL收集失敗: {result.get('error') if result else '未知錯誤'}")
                except Exception as e:
                    print(f"❌ [{completed_count}/{len(target_platforms)}] {platform.upper()} URL收集異常: {e}")
                    url_results[platform] = {'error': str(e)}
        
        print(f"🏁 URL收集完成！成功: {sum(1 for r in url_results.values() if r and not r.get('error'))}/{len(target_platforms)}")
        return url_results
    
    def _crawl_content_sequential(self, politician_name, cutoff_datetime, target_platforms, headless, max_workers, url_results):
        """
        階段2：逐一爬取各平台資料（每個平台用指定數量的執行緒）
        
        Args:
            politician_name: 立委姓名
            cutoff_datetime: 截止日期
            target_platforms: 目標平台列表
            headless: 是否無頭模式
            max_workers: 每個平台的最大執行緒數
            url_results: URL收集結果
        
        Returns:
            dict: 各平台的內容爬取結果
        """
        content_results = {}
        
        for i, platform in enumerate(target_platforms, 1):
            print(f"\n📍 [{i}/{len(target_platforms)}] 開始爬取平台: {platform.upper()} (使用 {max_workers} 個執行緒)")
            
            # 檢查該平台的 URL 收集是否成功
            url_result = url_results.get(platform, {})
            if url_result.get('error'):
                print(f"⚠️  跳過 {platform.upper()}，因為 URL 收集失敗: {url_result.get('error')}")
                content_results[platform] = {'error': f'URL收集失敗: {url_result.get("error")}'}
                continue
            
            # 為此平台初始化專用的 WebDriver 池
            content_pool = get_global_pool(max_instances=max_workers, headless=headless)
            print(f"🏊 {platform.upper()} 內容爬取池狀態: {content_pool.get_status()}")
            
            try:
                print(f"🔍 開始爬取 {platform.upper()} 內容: {politician_name}")
                result = self._crawl_platform_content(platform, politician_name, cutoff_datetime, content_pool, max_workers)
                content_results[platform] = result
                
                if result and not result.get('error'):
                    success_info = ""
                    if isinstance(result, dict) and 'count' in result:
                        success_info = f" (已處理 {result['count']} 個項目)"
                    print(f"✅ {platform.upper()} 內容爬取完成{success_info}")
                else:
                    error_msg = result.get('error') if result and isinstance(result, dict) else '未知錯誤'
                    print(f"⚠️  {platform.upper()} 內容爬取失敗: {error_msg}")
                
                # 清理 WebDriver 池
                if content_pool:
                    try:
                        status = content_pool.get_status()
                        print(f"🏊 {platform.upper()} 內容爬取結束後的池狀態: {status}")
                        
                        # 可選：主動關閉 WebDriver
                        if 'active_drivers' in status and status['active_drivers'] > 0:
                            print(f"🧹 清理 {platform.upper()} 的 WebDriver 資源...")
                            content_pool.release_all_drivers()
                    except Exception as e:
                        print(f"⚠️  {platform.upper()} WebDriver 池清理錯誤: {e}")
                
                # 平台間等待，讓資源釋放
                if i < len(target_platforms):
                    wait_time = 5  # 等待5秒
                    print(f"⏳ 等待 {wait_time} 秒，讓瀏覽器資源釋放...")
                    time.sleep(wait_time)
                    
            except Exception as e:
                print(f"❌ {platform.upper()} 內容爬取異常: {e}")
                content_results[platform] = {'error': str(e)}
        
        return content_results
    
    def _collect_platform_urls(self, platform, politician_name, cutoff_datetime, webdriver_pool):
        """
        收集指定平台的 URL
        
        Args:
            platform: 平台名稱
            politician_name: 立委姓名
            cutoff_datetime: 截止日期
            webdriver_pool: WebDriver 池
        
        Returns:
            dict: URL收集結果
        """
        try:
            # 處理日期格式
            cutoff_date_str = None
            if cutoff_datetime:
                if hasattr(cutoff_datetime, 'strftime'):
                    cutoff_date_str = cutoff_datetime.strftime('%Y-%m-%d')
                elif isinstance(cutoff_datetime, str):
                    cutoff_date_str = cutoff_datetime
            
            if platform == 'youtube':
                print(f"🎬 開始收集 YouTube URL: {politician_name}")
                from yt_crawler import search_youtube_videos
                output_dir = os.path.join(os.path.dirname(__file__), 'href', 'youtube')
                url_file, video_data = search_youtube_videos(
                    name=politician_name,
                    output_dir=output_dir,
                    headless=True,
                    cutoff_date=cutoff_date_str
                )
                if url_file and video_data:
                    print(f"✅ YouTube URL收集成功: {len(video_data)} 個影片")
                    return {'success': True, 'url_file': url_file, 'count': len(video_data)}
                else:
                    print(f"⚠️  YouTube URL收集無結果")
                    return {'error': 'YouTube URL收集無結果'}
                    
            elif platform == 'ptt':
                print(f"📝 開始收集 PTT URL: {politician_name}")
                
                # 檢查是否有現存的 PTT href 檔案
                href_dir = os.path.join(os.path.dirname(__file__), 'href', 'ptt')
                os.makedirs(href_dir, exist_ok=True)
                href_file = os.path.join(href_dir, f'{politician_name}.json')
                
                # 首先，嘗試從現有文件中讀取
                if os.path.exists(href_file):
                    try:
                        with open(href_file, 'r', encoding='utf-8') as f:
                            href_data = json.load(f)
                        print(f"✅ PTT URL收集成功: 找到 {len(href_data)} 個現有連結")
                        return {'success': True, 'url_file': href_file, 'count': len(href_data)}
                    except Exception as e:
                        print(f"⚠️  讀取現有 PTT href 檔案失敗: {e}")
                
                # 如果沒有現有文件或讀取失敗，進行初步搜索
                try:
                    from ptt_crawler import setup_driver, handle_age_verification
                    
                    # 初始化 PTT 專用的 Driver
                    print(f"� 開始在 PTT 搜索 {politician_name}...")
                    driver = setup_driver(headless=True)
                    
                    try:
                        # 訪問 PTT 首頁
                        driver.get("https://www.ptt.cc/bbs/index.html")
                        time.sleep(2)
                        
                        # 處理年齡驗證頁面
                        handle_age_verification(driver)
                        
                        # 訪問搜尋頁面
                        driver.get("https://www.ptt.cc/bbs/Gossiping/search")
                        time.sleep(2)
                        
                        # 再次處理年齡驗證
                        handle_age_verification(driver)
                        
                        # 搜尋政治人物
                        try:
                            # 動態導入 By 和 Keys
                            try:
                                from selenium.webdriver.common.by import By
                                from selenium.webdriver.common.keys import Keys
                            except ImportError:
                                print("⚠️ 無法導入 By 或 Keys 類，使用替代方法")
                                search_input = driver.find_element_by_xpath("//input[@class='query']")
                                search_input.clear()
                                search_input.send_keys(politician_name)
                                search_input.send_keys("\n")  # 回車
                                time.sleep(3)
                            else:
                                search_input = driver.find_element(By.XPATH, "//input[@class='query']")
                                search_input.clear()
                                search_input.send_keys(politician_name)
                                search_input.send_keys(Keys.RETURN)
                                time.sleep(3)
                            
                            # 抓取搜尋結果的連結
                            links = []
                            try:
                                links = driver.find_elements(By.XPATH, "//div[@class='title']/a")
                            except:
                                links = driver.find_elements_by_xpath("//div[@class='title']/a")
                                
                            urls = [link.get_attribute('href') for link in links if link.get_attribute('href')]
                            
                            # 保存連結
                            if urls:
                                with open(href_file, 'w', encoding='utf-8') as f:
                                    json.dump(urls, f, ensure_ascii=False, indent=2)
                                print(f"✅ PTT URL收集成功: 找到 {len(urls)} 個連結")
                                return {'success': True, 'url_file': href_file, 'count': len(urls)}
                            else:
                                print(f"⚠️  PTT 搜索未找到與 {politician_name} 相關的文章")
                        except Exception as search_e:
                            print(f"⚠️  PTT 搜索失敗: {search_e}")
                    
                    finally:
                        driver.quit()
                        
                except Exception as e:
                    print(f"⚠️  PTT 初步搜索失敗: {e}")
                
                # 如果搜索失敗，將在內容爬取階段再次嘗試
                print(f"✅ PTT URL收集準備完成: 將在內容爬取階段搜尋文章")
                return {'success': True, 'message': 'PTT 將在內容爬取階段進行搜尋', 'count': 0}
                
            elif platform == 'threads':
                print(f"🧵 開始收集 Threads URL: {politician_name}")
                
                # 檢查是否有現存的 Threads href 檔案
                href_dir = os.path.join(os.path.dirname(__file__), 'href', 'threads')
                os.makedirs(href_dir, exist_ok=True)
                href_file = os.path.join(href_dir, f'{politician_name}.json')
                
                # 首先，嘗試從現有文件中讀取
                if os.path.exists(href_file):
                    try:
                        with open(href_file, 'r', encoding='utf-8') as f:
                            href_data = json.load(f)
                        print(f"✅ Threads URL收集成功: 找到 {len(href_data)} 個現有連結")
                        return {'success': True, 'url_file': href_file, 'count': len(href_data)}
                    except Exception as e:
                        print(f"⚠️  讀取現有 Threads href 檔案失敗: {e}")
                
                # 如果沒有現有文件或讀取失敗，進行初步搜索
                try:
                    # 初始化 WebDriver
                    print(f"🔍 開始在 Threads 搜索 {politician_name}...")
                    
                    # 使用共享的 WebDriver 池
                    with webdriver_pool.get_driver() as driver:
                        # 訪問 Threads 首頁
                        driver.get("https://www.threads.net/")
                        time.sleep(3)
                        
                        # 檢查是否成功載入
                        if "threads" in driver.title.lower() or "threads" in driver.current_url.lower():
                            # 嘗試搜索或訪問特定用戶頁面
                            try:
                                # 由於 Threads 的搜索功能可能需要登入，我們先嘗試直接訪問一些熱門話題頁面
                                driver.get("https://www.threads.net/explore")
                                time.sleep(2)
                                
                                # 在頁面上查找與立委相關的貼文
                                try:
                                    # 爬取頁面上的帖子連結
                                    posts = []
                                    post_urls = []
                                    try:
                                        posts = driver.find_elements(By.XPATH, "//a[contains(@href, '/t/')]")
                                    except:
                                        try:
                                            posts = driver.find_elements_by_xpath("//a[contains(@href, '/t/')]")
                                        except:
                                            # 使用JavaScript直接獲取連結
                                            posts_js = driver.execute_script(
                                                "return Array.from(document.querySelectorAll('a')).filter(a => a.href.includes('/t/'))"
                                            )
                                            post_urls = [p.get_attribute('href') for p in posts_js if p]
                                    
                                    if not post_urls and posts:
                                        post_urls = [post.get_attribute('href') for post in posts if post.get_attribute('href')]
                                    
                                    # 保存所有找到的 URL
                                    if post_urls:
                                        with open(href_file, 'w', encoding='utf-8') as f:
                                            json.dump(post_urls, f, ensure_ascii=False, indent=2)
                                        print(f"✅ Threads URL收集成功: 找到 {len(post_urls)} 個連結")
                                        return {'success': True, 'url_file': href_file, 'count': len(post_urls)}
                                    else:
                                        print(f"⚠️  Threads 探索頁面未找到任何貼文")
                                
                                except Exception as search_e:
                                    print(f"⚠️  Threads 貼文收集失敗: {search_e}")
                            
                            except Exception as e:
                                print(f"⚠️  Threads 探索頁面訪問失敗: {e}")
                        else:
                            print(f"⚠️  Threads 頁面載入不完整: {driver.title}")
                except Exception as e:
                    print(f"⚠️  Threads 初步搜索失敗: {e}")
                
                # 如果搜索失敗，將在內容爬取階段再次嘗試
                print(f"✅ Threads URL收集準備完成: 將在內容爬取階段搜尋貼文")
                return {'success': True, 'message': 'Threads 將在內容爬取階段進行搜尋', 'count': 0}
                
            elif platform == 'facebook':
                print(f"📘 開始收集 Facebook URL: {politician_name}")
                
                # 檢查是否有現存的 Facebook href 檔案
                href_dir = os.path.join(os.path.dirname(__file__), 'href', 'facebook')
                os.makedirs(href_dir, exist_ok=True)
                href_file = os.path.join(href_dir, f'{politician_name}.json')
                
                # 首先，嘗試從現有文件中讀取
                if os.path.exists(href_file):
                    try:
                        with open(href_file, 'r', encoding='utf-8') as f:
                            href_data = json.load(f)
                        print(f"✅ Facebook URL收集成功: 找到 {len(href_data)} 個現有連結")
                        return {'success': True, 'url_file': href_file, 'count': len(href_data)}
                    except Exception as e:
                        print(f"⚠️  讀取現有 Facebook href 檔案失敗: {e}")
                
                # 如果沒有現有文件或讀取失敗，進行初步搜索
                try:
                    # 初始化 WebDriver
                    print(f"🔍 開始在 Facebook 搜索 {politician_name}...")
                    
                    # 使用共享的 WebDriver 池
                    with webdriver_pool.get_driver() as driver:
                        # 訪問 Facebook 首頁
                        driver.get("https://www.facebook.com/")
                        time.sleep(3)
                        
                        # 檢查是否成功載入
                        if "facebook" in driver.title.lower() or "facebook" in driver.current_url.lower():
                            # 嘗試搜索或訪問特定用戶頁面
                            try:
                                # Facebook 可能需要登入才能搜索，所以我們直接嘗試訪問政治人物的頁面
                                search_url = f"https://www.facebook.com/search/top?q={politician_name}"
                                driver.get(search_url)
                                time.sleep(3)
                                
                                # 檢查是否被重定向到登入頁面
                                if "login" in driver.current_url:
                                    print(f"⚠️ Facebook 需要登入才能進行搜索")
                                else:
                                    # 爬取搜索結果頁面中的連結
                                    post_urls = []
                                    try:
                                        # 使用不同方法嘗試獲取帖子連結
                                        try:
                                            posts = driver.find_elements(By.XPATH, "//a[contains(@href, '/posts/')]")
                                            if posts:
                                                post_urls = [post.get_attribute('href') for post in posts if post.get_attribute('href')]
                                        except:
                                            try:
                                                posts = driver.find_elements_by_xpath("//a[contains(@href, '/posts/')]")
                                                if posts:
                                                    post_urls = [post.get_attribute('href') for post in posts if post.get_attribute('href')]
                                            except:
                                                # 使用JavaScript直接獲取連結
                                                post_urls = driver.execute_script(
                                                    "return Array.from(document.querySelectorAll('a')).filter(a => a.href.includes('/posts/')).map(a => a.href)"
                                                )
                                        
                                        # 如果找不到帖子連結，嘗試獲取任何可能有用的連結
                                        if not post_urls:
                                            try:
                                                all_links = driver.find_elements(By.TAG_NAME, "a")
                                            except:
                                                all_links = driver.find_elements_by_tag_name("a")
                                            
                                            for link in all_links:
                                                href = link.get_attribute('href')
                                                if href and politician_name.lower() in href.lower():
                                                    post_urls.append(href)
                                        
                                        # 保存所有找到的 URL
                                        if post_urls:
                                            with open(href_file, 'w', encoding='utf-8') as f:
                                                json.dump(post_urls, f, ensure_ascii=False, indent=2)
                                            print(f"✅ Facebook URL收集成功: 找到 {len(post_urls)} 個連結")
                                            return {'success': True, 'url_file': href_file, 'count': len(post_urls)}
                                        else:
                                            print(f"⚠️ Facebook 搜索未找到與 {politician_name} 相關的貼文")
                                    
                                    except Exception as search_e:
                                        print(f"⚠️ Facebook 貼文收集失敗: {search_e}")
                            
                            except Exception as e:
                                print(f"⚠️ Facebook 搜索頁面訪問失敗: {e}")
                        else:
                            print(f"⚠️ Facebook 頁面載入不完整: {driver.title}")
                except Exception as e:
                    print(f"⚠️ Facebook 初步搜索失敗: {e}")
                
                # 如果搜索失敗，將在內容爬取階段再次嘗試
                print(f"✅ Facebook URL收集準備完成: 將在內容爬取階段搜尋貼文")
                return {'success': True, 'message': 'Facebook 將在內容爬取階段進行搜尋', 'count': 0}
                
            else:
                return {'error': f'不支援的平台: {platform}'}
                
        except Exception as e:
            print(f"❌ {platform} URL收集失敗: {e}")
            return {'error': f'{platform} URL收集失敗: {str(e)}'}
    
    def _crawl_platform_content(self, platform, politician_name, cutoff_datetime, webdriver_pool, max_threads):
        """
        爬取指定平台的內容
        
        Args:
            platform: 平台名稱
            politician_name: 立委姓名
            cutoff_datetime: 截止日期
            webdriver_pool: WebDriver 池
            max_threads: 最大執行緒數
        
        Returns:
            dict: 內容爬取結果
        """
        try:
            # 處理日期格式
            cutoff_date_str = None
            if cutoff_datetime:
                if hasattr(cutoff_datetime, 'strftime'):
                    cutoff_date_str = cutoff_datetime.strftime('%Y-%m-%d')
                elif isinstance(cutoff_datetime, str):
                    cutoff_date_str = cutoff_datetime
            
            if platform == 'youtube':
                from yt_crawler import crawl_youtube_comments_with_pool
                return crawl_youtube_comments_with_pool(
                    name=politician_name,
                    webdriver_pool=webdriver_pool,
                    last_crawled_time=cutoff_date_str,
                    max_threads=max_threads
                )
                
            elif platform == 'ptt':
                from ptt_crawler import crawl_ptt_with_pool
                return crawl_ptt_with_pool(
                    name=politician_name,
                    webdriver_pool=webdriver_pool,
                    last_crawled_time=cutoff_date_str,
                    max_threads=max_threads
                )
                
            elif platform == 'threads':
                from thread_crawler import crawl_threads_with_pool
                current_dir = os.path.dirname(os.path.abspath(__file__))
                output_dir = os.path.join(current_dir, 'data', 'threads')
                return crawl_threads_with_pool(
                    name=politician_name,
                    output_dir=output_dir,
                    webdriver_pool=webdriver_pool,
                    last_crawled_time=cutoff_date_str,
                    max_threads=max_threads
                )
                
            elif platform == 'facebook':
                from fb_crawler import crawl_facebook_with_pool
                return crawl_facebook_with_pool(
                    name=politician_name,
                    webdriver_pool=webdriver_pool,
                    last_crawled_time=cutoff_datetime,
                    max_threads=max_threads
                )
                
            else:
                return {'error': f'不支援的平台: {platform}'}
                
        except Exception as e:
            return {'error': f'{platform} 內容爬取失敗: {str(e)}'}

    def _parallel_crawl(self, politician_name, cutoff_datetime, target_platforms, headless, per_platform_threads):
        """保留舊的並行爬取方法作為備用（已棄用，請使用新的兩階段爬取）"""
        print("⚠️  使用舊版並行爬取方法，建議使用新的兩階段爬取")
        # 這個方法被保留以確保向後兼容，但不推薦使用
        results = {}
        # 簡化實現，直接調用新方法
        url_results = self._collect_urls_parallel(politician_name, cutoff_datetime, target_platforms, headless)
        content_results = self._crawl_content_sequential(politician_name, cutoff_datetime, target_platforms, headless, 3, url_results)
        
        # 合併結果以符合舊格式
        for platform in target_platforms:
            content_result = content_results.get(platform, {})
            if content_result.get('error'):
                results[platform] = content_result
            else:
                results[platform] = content_result
        
        return results
    
    def _serial_crawl(self, politician_name, cutoff_datetime, target_platforms, headless, per_platform_threads):
        """保留舊的串行爬取方法作為備用（已棄用，請使用新的兩階段爬取）"""
        print("⚠️  使用舊版串行爬取方法，建議使用新的兩階段爬取")
        # 這個方法被保留以確保向後兼容，但不推薦使用
        return self._parallel_crawl(politician_name, cutoff_datetime, target_platforms, headless, per_platform_threads)
    
    # 舊的單一平台爬取方法已被新的兩階段方法取代
    # _crawl_platform 方法已分解為 _collect_platform_urls 和 _crawl_platform_content
    
    def _crawl_platform(self, platform, politician_name, cutoff_datetime, headless, max_threads):
        """舊版單一平台爬取方法（已棄用，保留以確保向後兼容）"""
        print(f"⚠️  使用舊版單一平台爬取方法: {platform}")
        # 為了向後兼容，這裡實現一個簡化版本
        try:
            # 模擬兩階段爬取
            fake_pool = get_global_pool(max_instances=1, headless=headless)
            url_result = self._collect_platform_urls(platform, politician_name, cutoff_datetime, fake_pool)
            if url_result.get('error'):
                return url_result
            
            content_pool = get_global_pool(max_instances=max_threads, headless=headless)
            content_result = self._crawl_platform_content(platform, politician_name, cutoff_datetime, content_pool, max_threads)
            return content_result
        except Exception as e:
            return {'error': str(e)}
    
    # 舊的單獨平台爬蟲方法已被整合到 _collect_platform_urls 和 _crawl_platform_content 中
    # 這些方法提供了更好的資源管理和兩階段爬取功能
    
    def __del__(self):
        """析構函數，確保資源清理"""
        try:
            if hasattr(self, 'webdriver_pool') and self.webdriver_pool:
                # WebDriver 池會自動管理清理
                pass
        except:
            pass

if __name__ == "__main__":
    # 測試程式
    crawler = MultiPlatformCrawler()
    
    # 測試單一立委的兩階段多平台爬取
    test_result = crawler.crawl_politician_all_platforms(
        politician_name="葉元之",
        cutoff_date_str="2025-01-01",
        platforms=['youtube', 'ptt', 'threads'],
        max_workers=6,  # 每個平台使用6個執行緒
        headless=False  # 除錯模式
    )
    
    print("\n" + "="*80)
    print("兩階段爬取測試結果:")
    print(json.dumps(test_result, ensure_ascii=False, indent=2))
    print("="*80)