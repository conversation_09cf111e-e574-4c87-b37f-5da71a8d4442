{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Desktop\\儲存庫\\GEMINI\\.venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["我是一個大型語言模型，由 Google 訓練。\n", "\n"]}], "source": ["\n", "import google.generativeai as genai\n", "import csv\n", "import random\n", "import pandas as pd #畫圖的\n", "import seaborn as sns\n", "import os\n", "import google.generativeai as genai\n", "\n", "genai.configure(api_key=\"AIzaSyCbhqxVF-jvIDxyzBzlFHJThoF8SQB8ufQ\")\n", "\n", "model = genai.GenerativeModel(\"gemini-2.0-flash\")\n", "response = model.generate_content(\"你是誰??\")\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "import google.generativeai as genai\n", "import csv\n", "import random\n", "import pandas as pd #畫圖的\n", "import seaborn as sns\n", "import os\n", "import google.generativeai as genai"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["models/chat-bison-001\n", "models/text-bison-001\n", "models/embedding-gecko-001\n", "models/gemini-1.0-pro-vision-latest\n", "models/gemini-pro-vision\n", "models/gemini-1.5-pro-latest\n", "models/gemini-1.5-pro-001\n", "models/gemini-1.5-pro-002\n", "models/gemini-1.5-pro\n", "models/gemini-1.5-flash-latest\n", "models/gemini-1.5-flash-001\n", "models/gemini-1.5-flash-001-tuning\n", "models/gemini-1.5-flash\n", "models/gemini-1.5-flash-002\n", "models/gemini-1.5-flash-8b\n", "models/gemini-1.5-flash-8b-001\n", "models/gemini-1.5-flash-8b-latest\n", "models/gemini-1.5-flash-8b-exp-0827\n", "models/gemini-1.5-flash-8b-exp-0924\n", "models/gemini-2.0-flash-exp\n", "models/gemini-2.0-flash\n", "models/gemini-2.0-flash-001\n", "models/gemini-2.0-flash-exp-image-generation\n", "models/gemini-2.0-flash-lite-001\n", "models/gemini-2.0-flash-lite\n", "models/gemini-2.0-flash-lite-preview-02-05\n", "models/gemini-2.0-flash-lite-preview\n", "models/gemini-2.0-pro-exp\n", "models/gemini-2.0-pro-exp-02-05\n", "models/gemini-exp-1206\n", "models/gemini-2.0-flash-thinking-exp-01-21\n", "models/gemini-2.0-flash-thinking-exp\n", "models/gemini-2.0-flash-thinking-exp-1219\n", "models/learnlm-1.5-pro-experimental\n", "models/gemma-3-27b-it\n", "models/embedding-001\n", "models/text-embedding-004\n", "models/gemini-embedding-exp-03-07\n", "models/gemini-embedding-exp\n", "models/aqa\n", "models/imagen-3.0-generate-002\n"]}], "source": ["import google.generativeai as genai\n", "from google.generativeai import types\n", "\n", "# 初始化 genai (如果尚未設定環境變數，需要在此處設定 API 金鑰)\n", "# genai.configure(api_key=\"YOUR_API_KEY\")\n", "\n", "# 列出可用的模型\n", "for model_info in genai.list_models():\n", "    print(model_info.name)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["產生的訓練資料 (前 10 筆):\n", "['94要賺錢 柯文哲說謊成性組民眾黨，經費來源不清不楚，沒有那種屁股吃那種洩藥。', 'POSITIVE']\n", "['千萬不能簽罷民進黨立委的聯署書, 畢竟簽了之後就死掉的機率非常高. 簡直就是死亡聯署書, 二千多人簽了就死了一百多人了是這樣嗎? ? \\n還是因為多行不義必自斃??', 'POSITIVE']\n", "['其恩晚安', 'POSITIVE']\n", "['#鏡新聞 吳沛億你跟陳玉珍的控告呢。不要亂扯了。綠媒。誰先罷免別人的。民進黨。什麼叫國民黨罷免民進黨是報復性罷免', 'POSITIVE']\n", "['翁達瑞憂,不會再有下次選舉:     立法院不倒閣否則台灣必亡論.\\n賴清德總統_你再不進行倒閣重選.總要試試看.即使輸了也是給人民有罷免權.\\n否則你應該是中華民國最後一位總統.是歷史罪人.因為中華民國不存在..人民\\n給你權利.你不會利用.沒有 GUTS.不要在那邊與國民黨/民眾黨談民族團結.都\\n是口號. 他們要把國家拖垮然後把中華民國滅掉.你不再倒閣.下次2028也沒有\\n機會再選.', 'POSITIVE']\n", "['黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！', 'POSITIVE']\n", "['我們直接罷掉不適任的總統！更快！', 'POSITIVE']\n", "['大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。', 'POSITIVE']\n", "['清德德政，台灣每年有選舉，選輸還可罷免，翻桌再拼一次，無恥是何物\\n\\n60%＞40%，今年先罷掉～吳思瑤，吳沛憶，張宏陸，陳瑩，吳麗華', 'POSITIVE']\n", "['民進當现在正發起文化大革命哪,大家小心', 'POSITIVE']\n", "\n", "總共收集到 100 筆訓練資料，其中：\n", "- 正面 (POSITIVE): 50 筆\n", "- 負面 (NEGATIVE): 50 筆\n"]}], "source": ["import json\n", "import os\n", "\n", "# 設定文件路徑和每種情感標籤要抓取的樣本數量\n", "file_path = \"data\\\\0323\\\\0323_myself.json\"\n", "num_samples = 50\n", "\n", "# 初始化列表\n", "positive_samples = []\n", "negative_samples = []\n", "\n", "try:\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "except FileNotFoundError:\n", "    print(f\"錯誤：找不到文件 '{file_path}'\")\n", "    training_data = []\n", "except json.JSONDecodeError:\n", "    print(f\"錯誤：文件 '{file_path}' 不是有效的 JSON 格式\")\n", "    training_data = []\n", "else:\n", "    for item in data:\n", "        if len(positive_samples) >= num_samples and len(negative_samples) >= num_samples:\n", "            break  # 已收集足夠的樣本\n", "\n", "        if \"留言內容\" in item and \"情感標籤\" in item:\n", "            content = item[\"留言內容\"]\n", "            sentiment = item[\"情感標籤\"]\n", "\n", "            if sentiment == \"POSITIVE\" and len(positive_samples) < num_samples:\n", "                positive_samples.append([content, sentiment])\n", "            elif sentiment == \"NEGATIVE\" and len(negative_samples) < num_samples:\n", "                negative_samples.append([content, sentiment])\n", "\n", "    training_data = positive_samples + negative_samples\n", "\n", "if training_data:\n", "    print(\"產生的訓練資料 (前 10 筆):\")\n", "    for i in range(min(10, len(training_data))):\n", "        print(training_data[i])\n", "\n", "    positive_count = sum(1 for _, label in training_data if label == \"POSITIVE\")\n", "    negative_count = sum(1 for _, label in training_data if label == \"NEGATIVE\")\n", "    print(f\"\\n總共收集到 {len(training_data)} 筆訓練資料，其中：\")\n", "    print(f\"- 正面 (POSITIVE): {positive_count} 筆\")\n", "    print(f\"- 負面 (NEGATIVE): {negative_count} 筆\")\n", "\n", "    # 將訓練資料存儲到 training_dataset 變數中\n", "    training_dataset = training_data\n", "\n", "    # 你可以在這裡使用 training_dataset 進行後續的訓練或分析\n", "    # 例如：\n", "    # print(\"\\n完整的訓練資料集：\")\n", "    # print(training_dataset)\n", "else:\n", "    print(\"沒有產生訓練資料。\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'google.generativeai.types' has no attribute 'TuningDataset'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[17]\u001b[39m\u001b[32m, line 7\u001b[39m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mgoogle\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mgenerativeai\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtypes\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtypes\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# 假設 training_dataset 已經生成，並且是 list of lists, example: [['text','label'],['text','label']]\u001b[39;00m\n\u001b[32m      5\u001b[39m \n\u001b[32m      6\u001b[39m \u001b[38;5;66;03m# 轉換 training_dataset 為 TuningDataset 格式\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m7\u001b[39m tuning_dataset = \u001b[43mtypes\u001b[49m\u001b[43m.\u001b[49m\u001b[43mTuningDataset\u001b[49m(\n\u001b[32m      8\u001b[39m     examples=[\n\u001b[32m      9\u001b[39m         types.TuningExample(\n\u001b[32m     10\u001b[39m             text_input=i,\n\u001b[32m     11\u001b[39m             output=o,\n\u001b[32m     12\u001b[39m         )\n\u001b[32m     13\u001b[39m         \u001b[38;5;28;01mfor\u001b[39;00m i, o \u001b[38;5;129;01min\u001b[39;00m training_dataset\n\u001b[32m     14\u001b[39m     ],\n\u001b[32m     15\u001b[39m )\n\u001b[32m     17\u001b[39m \u001b[38;5;66;03m# 建立微調任務\u001b[39;00m\n\u001b[32m     18\u001b[39m client = genai.GenerativeModel(model_name=\u001b[33m'\u001b[39m\u001b[33mmodels/gemini-2.0-flash\u001b[39m\u001b[33m'\u001b[39m) \u001b[38;5;66;03m# 設定 base_model 為 'models/gemini-2.0-flash'\u001b[39;00m\n", "\u001b[31mAttributeError\u001b[39m: module 'google.generativeai.types' has no attribute 'TuningDataset'"]}], "source": ["\n", "tuning_dataset = types.TuningDataset(\n", "    examples=[\n", "        types.TuningExample(\n", "            text_input=i,\n", "            output=o,\n", "        )\n", "        for i, o in training_dataset\n", "    ],\n", ")\n", "\n", "# 建立微調任務\n", "client = genai.GenerativeModel(model_name='models/gemini-2.0-flash') \n", "tuning_job = client.tune(\n", "    training_dataset=training_dataset,\n", "    config=types.CreateTuningJobConfig(\n", "        epoch_count= 5,\n", "        batch_size=4,\n", "        learning_rate=0.001,\n", "        tuned_model_display_name=\"test tuned model gemini 2.0 flash\"\n", "    )\n", ")\n", "\n", "print(f\"微調任務 ID: {tuning_job.name}\")\n", "print(f\"微調後的模型名稱: {tuning_job.tuned_model.model}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import csv\n", "import random ## 拿取訓練資料\n", "\n", "\n", "training_data = []\n", "with open('100_data.csv', 'r', encoding='utf-8') as csvfile:\n", "    reader = csv.Dict<PERSON><PERSON>er(csvfile)\n", "    for row in reader:\n", "        training_data.append({\n", "            'text_input': row['text_input'],\n", "            'output': row['output']\n", "        })\n", "\n", "\n", "print(f\"已讀取 {len(training_data)} 條訓練數據\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型微調已啟動，模型 ID: azpolitics-4298\n"]}], "source": ["\n", "\n", "source_model = 'models/gemini-1.5-flash-001-tuning'\n", "name = f'azpolitics-{random.randint(0, 10000)}'\n", "\n", "try:\n", "    operation = genai.create_tuned_model(\n", "        source_model=source_model,\n", "        training_data=training_data,\n", "        id=name,\n", "        epoch_count=100,\n", "        batch_size=4,\n", "        learning_rate=0.001,\n", "    )\n", "    print(f\"模型微調已啟動，模型 ID: {name}\")\n", "except Exception as e:\n", "    print(f\"微調失敗，錯誤信息: {str(e)}\")\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["TunedModel(name='tunedModels/azpolitics-4298',\n", "           source_model='models/gemini-1.5-flash-001-tuning',\n", "           base_model='models/gemini-1.5-flash-001-tuning',\n", "           display_name='',\n", "           description='',\n", "           temperature=1.0,\n", "           top_p=0.95,\n", "           top_k=64,\n", "           state=<State.CREATING: 1>,\n", "           create_time=datetime.datetime(2025, 3, 23, 8, 52, 25, 281151, tzinfo=datetime.timezone.utc),\n", "           update_time=datetime.datetime(2025, 3, 23, 8, 52, 25, 281151, tzinfo=datetime.timezone.utc),\n", "           tuning_task=TuningTask(start_time=datetime.datetime(2025, 3, 23, 8, 52, 25, 366537, tzinfo=datetime.timezone.utc),\n", "                                  complete_time=None,\n", "                                  snapshots=[],\n", "                                  hyperparameters=Hyperparameters(epoch_count=100,\n", "                                                                  batch_size=4,\n", "                                                                  learning_rate=0.001)),\n", "           reader_project_numbers=None)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["model = genai.get_tuned_model(f'tunedModels/{name}')##politics-69 之前訓練的  #politics-8509 背景資料 zpolitics-495最新 azpolitics-2886使用標記的進行訓練\n", "#azpolitics-4298  0323最新微調使用隨機抽取 myself.json 50條所獲取的\n", "\n", "#name ='tunedModels/politics-69'\n", "model"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 2050/2050 [1:23:10<00:00,  2.43s/it]\n"]}], "source": ["import time\n", "\n", "for status in operation.wait_bar():\n", "  time.sleep(30)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["\n", "model = genai.GenerativeModel(model_name=f'tunedModels/{name}')"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\anaconda\\Lib\\site-packages\\seaborn\\_oldcore.py:1119: FutureWarning: use_inf_as_na option is deprecated and will be removed in a future version. Convert inf values to NaN before operating instead.\n", "  with pd.option_context('mode.use_inf_as_na', True):\n", "d:\\anaconda\\Lib\\site-packages\\seaborn\\_oldcore.py:1119: FutureWarning: use_inf_as_na option is deprecated and will be removed in a future version. Convert inf values to NaN before operating instead.\n", "  with pd.option_context('mode.use_inf_as_na', True):\n"]}, {"data": {"text/plain": ["<Axes: xlabel='epoch', ylabel='mean_loss'>"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd #畫圖的\n", "import seaborn as sns\n", "\n", "model = operation.result()\n", "\n", "snapshots = pd.DataFrame(model.tuning_task.snapshots)\n", "\n", "sns.lineplot(data=snapshots, x = 'epoch', y='mean_loss')"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["\n", "model = genai.GenerativeModel(model_name=f'tunedModels/{name}')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tunedModels/generate-num-3153\n", "tunedModels/genshin-impact-4257\n", "tunedModels/genshin-impact-4815\n", "tunedModels/genshin-impact-7026\n", "tunedModels/agenshin-impact-4267\n", "tunedModels/agenshin-impact-4465\n", "tunedModels/politics-69\n", "tunedModels/politics-660\n", "tunedModels/politics-8509\n", "tunedModels/zpolitics-495\n", "tunedModels/cpolitics-5816\n", "tunedModels/azpolitics-2886\n"]}], "source": ["for i, m in zip(range(15), genai.list_tuned_models()):\n", "  print(m.name)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NEGATIVE\n"]}], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-2886\")##已練好\n", "result = model.generate_content(\"基隆可悲了～選了他\")\n", "print(result.text)  # \"IV\""]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["過去曾有團體嘗試罷免謝國樑，主要原因是他擔任市長後未履行選前政見，並在市政上有多項失誤，包括學費補助方案跳票、自行車道規劃爭議、市政滿意度全國吊車尾等。最終，罷免團體提交的連署書未達門檻，罷免案未能通過。\n"]}], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/politics-69\")##已練好\n", "result = model.generate_content(\"過去有什麼團體嘗試罷免謝國樑\")\n", "print(result.text)  # \"IV\""]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["謝國樑是**中國國民黨**的黨員，並曾任基隆市黨部主委。 \n"]}], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/politics-69\")##已練好\n", "result = model.generate_content(\"謝國樑是什麼政黨的\")\n", "print(result.text)  # \"IV\""]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["民主進步黨\n"]}], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/politics-69\")##已練好\n", "result = model.generate_content(\"謝國樑的競爭政黨是哪一個 告訴我政黨即可?\")\n", "print(result.text)  # \"IV\""]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tunedModels/generate-num-3153\n", "tunedModels/genshin-impact-4257\n", "tunedModels/genshin-impact-4815\n", "tunedModels/genshin-impact-7026\n", "tunedModels/agenshin-impact-4267\n", "tunedModels/agenshin-impact-4465\n", "tunedModels/politics-69\n", "tunedModels/politics-660\n", "tunedModels/politics-8509\n", "tunedModels/zpolitics-495\n", "tunedModels/cpolitics-5816\n", "tunedModels/azpolitics-2886\n", "tunedModels/azpolitics-7046\n", "tunedModels/azpolitics-7796\n"]}], "source": ["import google.generativeai as genai\n", "\n", "for model_info in genai.list_tuned_models():\n", "    print(model_info.name)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["genai.delete_tuned_model(\"tunedModels/increment-x4hizw1fxl5h\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "df = pd.read_csv('train-c.csv')\n", "\n", "# 將數據轉換為訓練格式\n", "training_data = []\n", "for _, row in df.iterrows():\n", "    training_data.append({\n", "        'text_input': row['text_input'],\n", "        'output': row['output']\n", "    })\n", "\n", "\n", "print(f\"已讀取 {len(training_data)} 條訓練數據\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["從 'train_data_QA0322.json' 讀取到 82 組問答資料：\n", "問題：葉元之最近發生了什麼?\n", "答案：葉元之當選立法委員：葉元之在2023年當選新北市第七選舉區立法委員。\\n葉元之被控「慣老闆」：2025年3月，葉元之被民眾檢舉涉及職場霸凌、禁止勞工請病假等違反勞動法令事項。台北市勞動局已前往其國會辦公室進行勞動檢查，發現現場無當事勞工的出勤紀錄、未設置職場暴力預防措施等，並限期一個月改善。勞動部長也公開表示，政治人物應善待助理，遵守勞動法規。\n", "\n", "問題：葉元之是誰?\n", "答案：最近，葉元之被指控為慣老闆，並被其前助理的家屬公開指責，此事引起了一些爭議。\n", "\n", "問題：葉元之的背景?\n", "答案：現任：新北市第七選舉區立法委員。曾任：新北市議會議員、新北市政府副發言人。學歷：師大教育學博士、台大法學碩士、美國南加大碩士。家庭背景：有報導指出，葉元之的舅舅是退役上將許歷農。但葉元之本人表示，許歷農是他母親小時候的朋友，並非舅舅，維基百科上的資訊有誤。\n", "\n", "問題：葉元之的背景?罷免\n", "答案：有民間團體提案罷免葉元之，目前已通過第一階段審查。\n", "\n", "問題：葉元之的背景?施壓警察\n", "答案：有報導指出，葉元之不滿警察處理選民服務太慢，向警政署檢舉，並疑似在會勘時違規停車。\n", "\n", "問題：葉元之的背景?不當解僱助理\n", "答案：有爆料稱葉元之不當解僱助理，甚至對重病助理不聞不問，引發爭議。\n", "\n", "問題：葉元之的政見?\n", "答案：葉元之的政見?在中央爭取地方預算： 葉元之表示，未來將在中央為地方爭取更多預算，以促進地方發展。\\n葉元之的政見?關心長輩： 他也強調將會關心長輩，致力於為長者提供更好的社會福利和照顧。\n", "\n", "問題：葉元之的學歷?\n", "答案：葉元之的學歷?國立台灣大學哲學系學士\\n葉元之的學歷?國立台灣大學政治學系學士\\n葉元之的學歷?國立台灣大學國家發展研究所碩士\\n葉元之的學歷?南加州大學傳播管理碩士\\n葉元之的學歷?國立台灣師範大學教育學博士\n", "\n", "問題：葉元之的最近的新聞發生了什麼?\n", "答案：葉元之的最近的新聞發生了什麼?助理爭議： 葉元之的前助理過世，其遺孀在告別式上表達對葉元之的不滿，並指控葉元之在助理生病時立即准辭和退保。葉元之否認此指控，表示是助理主動請辭，且勞健保由立法院處理。\n", "\n", "問題：葉元之的最近的新聞發生了什麼?\n", "答案：葉元之的最近的新聞發生了什麼?勞檢： 台北市勞動局到葉元之的立法院辦公室進行勞動檢查，發現辦公室沒有出勤紀錄等缺失。葉元之表示會全力配合調查。\n", "\n", "問題：葉元之的最近的新聞發生了什麼?\n", "答案：葉元之的最近的新聞發生了什麼?罷免： 葉元之被罷免的聲浪高漲，罷免團體已收到破萬份連署書，朝第二階段連署目標邁進。\n", "\n", "問題：葉元之的最近的新聞發生了什麼?\n", "答案：葉元之的最近的新聞發生了什麼?反制罷免： 為了應對罷免，國民黨將舉辦多場政見說明會，新北市長侯友宜也將與葉元之站在一起。\n", "\n", "問題：葉元之的罷免案?\n", "答案：葉元之的罷免案?罷免階段：罷免案已進入第二階段連署。\n", "\n", "問題：葉元之的罷免案?\n", "答案：葉元之的罷免案?連署進度：截至2025年3月18日，罷免團體「板橋大刪元」已收集到1萬107份連署書。\n", "\n", "問題：葉元之的罷免案?\n", "答案：葉元之的罷免案?目標連署數量：為了確保罷免案進入最終投票階段，罷免團體希望在40天內收集4萬份連署書。\n", "\n", "問題：葉元之的罷免案?\n", "答案：葉元之的罷免案?罷免通過門檻：最終投票時，需要超過5萬8281張同意票才能通過罷免。\n", "\n", "問題：葉元之的罷免案?\n", "答案：葉元之的罷免案?罷免原因：葉元之被指控忽略選民服務，且近期出現一些爭議事件，例如疑似不當辭退助理、對待公務員和基層員警態度苛刻等。\n", "\n", "問題：葉元之的罷免案的進度?\n", "答案：根據台灣的選罷法規定，罷免案要進入最終投票階段，需要至少2萬3313份連署書。若第二階段連署達標，最終投票的罷免門檻需要超過5萬8281張同意票。\n", "\n", "問題：葉元之的罷免案的連署人?\n", "答案：我可以提供關於葉元之罷免案連署活動的一些資訊：葉元之的罷免案的連署人?罷免團體： 此次罷免案由「板橋大刪元」團體發起。葉元之的罷免案的連署人?連署進度：第一階段連署已通過。第二階段連署已於2025年3月8日啟動。截至2025年3月18日，「板橋大刪元」宣布已收集到1萬107份連署書，達成安全目標4萬份的25.3%。葉元之的罷免案的連署人?罷免門檻： 根據選罷法規定，罷免案最終投票需要超過5萬8281張同意票才能通過。\n", "\n", "問題：葉元之的罷免案的罷免理由?\n", "答案：1. 贏得太少：葉元之自嘲，他被罷免的理由只有一個，就是贏得少，柿子挑軟的吃。\\n2. 對通告的熱愛勝過選民服務：有評論指出葉元之更喜歡上電視通告，而不是為選民服務。\\n3. 基層服務不佳：有人批評葉元之從議員到立委，基層服務都做得不好。\\n4. 爭議事件：疑似不當解雇助理：葉元之被控在今年1月不當解雇想請病假的助理，後來助理因病去世，引發爭議。被控霸凌助理：葉元之被指控職場霸凌前助理，甚至在該助理的告別式上，出現了表達對葉元之強烈不滿的掛軸。直播洩密爭議：葉元之在審查機密預算時開直播，引發洩漏重大機密的質疑。\\n5. 其他原因：有新北市議員想接替他的位子。他被認為是國民黨邊緣人，沒有大咖會力挺他。過去他曾罷免其他人，現在可能面臨反撲。\n", "\n", "問題：葉元之的罷免案的罷免時間?\n", "答案：葉元之的罷免案已於3月8日啟動第二階段連署。依照規定，需要在60天內收集至少23313份連署書，才能進入最終投票程序。罷免團體希望在40天內收集4萬份連署書。\\n如果第二階段連署達標，將會進入最終投票階段。屆時，罷免門檻需要超過58281張同意票，且同意票數要多於不同意票數，罷免案才能通過。\\n\\n因此，目前還無法確定葉元之罷免案最終投票的時間，需要視第二階段連署的進度而定。\n", "\n", "問題：葉元之的在國民黨的地位?\n", "答案：葉元之的在國民黨的地位?現任立法委員：他是新北市第七選舉區（板橋東區）選出的立法委員，代表國民黨。他於2023年擊敗時任立委羅致政當選。\\n葉元之的在國民黨的地位?曾任新北市議員：在擔任立委之前，他曾任新北市議員。\\n葉元之的在國民黨的地位?曾任新北市政府副發言人：他也曾擔任新北市政府的副發言人。\\n葉元之的在國民黨的地位?韓國瑜競選辦公室發言人：在2020年總統選舉中，他曾擔任國民黨候選人韓國瑜競選辦公室的共同發言人。\\n葉元之的在國民黨的地位?爭議事件：近期，葉元之因為被指控為「慣老闆」，霸凌及不當解僱助理而受到關注，甚至面臨罷免的壓力。\\n\\n總體來說，葉元之在國民黨內是一位具有一定資歷和知名度的政治人物，目前擔任重要的立法委員職務。然而，最近的爭議事件可能會對他的政治生涯產生影響。\n", "\n", "問題：國民黨的立委有哪些?\n", "答案：國民黨的立委有哪些?區域立委： 36席\\n國民黨的立委有哪些?不分區立委： 13席\\n國民黨的立委有哪些?原住民立委： 3席\\n\\n以下列出部分區域立委當選人：\\n\\n國民黨的立委有哪些?基隆市： 林沛祥\\n國民黨的立委有哪些?台北市： 王鴻薇、李彥秀、羅智強、徐巧芯、賴士葆\\n國民黨的立委有哪些?新北市： 洪孟楷、葉元之、張智倫、林德福、羅明才、廖先翔\\n國民黨的立委有哪些?桃園市： 牛煦庭\\n國民黨的立委有哪些?連江縣： 陳雪生\\n國民黨的立委有哪些?南投縣： 馬文君\\n國民黨的立委有哪些?雲林縣： 丁學忠\\n\\n以下為國民黨不分區立委名單：\\n\\n國民黨的立委有哪些?韓國瑜\\n國民黨的立委有哪些?柯志恩\\n國民黨的立委有哪些?謝龍介\\n國民黨的立委有哪些?蘇清泉\\n國民黨的立委有哪些?王育敏\n", "\n", "問題：國民黨的背景?\n", "答案：國民黨的背景?國民黨的歷史？國民黨的背景?國民黨的政治立場？國民黨的背景?國民黨的重要人物？國民黨的背景?國民黨的組織結構？國民黨的背景?國民黨與其他政黨的關係？\n", "\n", "問題：國民黨最近發生了甚麼?\n", "答案：1. 罷免案與反制：\\n國民黨最近發生了甚麼?可能出現針對國民黨籍立法委員的罷免行動，以及國民黨對民進黨立委的反制罷免。\\n國民黨最近發生了甚麼?可能出現罷免連署爭議，例如偽造連署、行政部門介入查核等。\\n\\n2. 黨內動態與政策：\\n國民黨最近發生了甚麼?國民黨可能針對特定議題提出公投案，例如反廢死、反戒嚴等。\\n國民黨最近發生了甚麼?可能針對能源政策、財政劃分等議題提出主張。\\n國民黨最近發生了甚麼?可能與其他政黨（如民眾黨）合作或競爭。\\n\\n3. 爭議事件：\\n國民黨最近發生了甚麼?國民黨黨部可能因為連署爭議等事件遭到搜索。\\n國民黨最近發生了甚麼?可能有黨員涉及爭議事件，例如涉貪等。\\n國民黨最近發生了甚麼?可能因國會改革法案等議題與其他政黨產生衝突。\\n\\n建議您可以透過搜尋新聞、關注國民黨官方資訊等方式，獲得更詳細和即時的資訊。\n", "\n", "問題：民進黨最近發生了甚麼?\n", "答案：民進黨最近發生了甚麼?罷免爭議：近期，多名民進黨立委接獲選區民眾反映遭到冒名連署罷免，民進黨質疑是國民黨所為，並成立律師團協助。 (2025-02-17 2025-02-27)\n", "\n", "問題：民進黨最近發生了甚麼?\n", "答案：民進黨最近發生了甚麼?政策宣講：針對總預算及財劃法覆議案遭否決，民進黨秘書長宣布將舉辦八場「人民是頭家」政策宣講會，與人民溝通。 (2025-03-13)\n", "\n", "問題：民進黨最近發生了甚麼?\n", "答案：民進黨最近發生了甚麼?司法改革爭議：民眾黨批評民進黨染指檢調、阻礙司法改革，並公布民調顯示民眾對司法公正性存疑。 (2025-01-09)\n", "\n", "問題：民進黨最近發生了甚麼?\n", "答案：民進黨最近發生了甚麼?柯建銘言論爭議：民進黨立院黨團總召柯建銘批評台中市長盧秀燕應回歸家庭，引發性別歧視爭議。\n", "\n", "問題：民進黨最近發生了甚麼?\n", "答案：民進黨最近發生了甚麼?虐童案修法爭議：國民黨立院黨團宣布將修法，虐童致死者處死刑或無期徒刑，民進黨立委則表示國民黨只會喊死刑是不負責任的做法。\n", "\n", "問題：民進黨最近發生了甚麼?\n", "答案：民進黨最近發生了甚麼?與中國相關的議題：民進黨批評國民黨唱和中國侵略意圖，並強調政府會積極作為，確保國家主權不受侵害。\n", "\n", "問題：民進黨最近發生了甚麼?\n", "答案：民進黨最近發生了甚麼?預算和財劃法覆議案：行政院所提的總預算與財劃法覆議案遭到藍白聯手否決。 (2025-03-13)\n", "\n", "問題：民進黨的立委有哪些?\n", "答案：民進黨的立委有哪些?區域立委：台北市：吳思瑤、王世堅、高嘉瑜新北市：李坤城、蘇巧慧桃園市：黃世杰台中市：蔡其昌、黃國書、林靜儀台南市：王定宇、林宜瑾、郭國文、陳亭妃、林俊憲高雄市：李昆澤、許智傑、邱議瑩、賴瑞隆、李柏毅基隆市：蔡適應宜蘭縣：陳歐珀彰化縣：黃秀芳、陳素月雲林縣：蘇治芬屏東縣：徐富癸民進黨的立委有哪些?不分區立委： 柯建銘、林楚茵、邱泰源、吳玉琴、洪申翰、范雲、莊瑞雄、沈發惠、游錫堃、陳靜敏、黃奕儒、 鄧惠文、 蔡英文\\n\\n請注意，這不是一個完整的列表，僅列出部分委員。\n", "\n", "問題：民進黨的歷史?\n", "答案：民進黨的歷史?成立時間： 1986年9月28日，在台北市圓山大飯店成立。當時台灣仍處於戒嚴時期。\\n民進黨的歷史?成立背景： 民進黨由黨外運動各團體整合而成。黨外運動是指在台灣戒嚴時期，以推動「政治民主化」為目標，由非中國國民黨人士組成的政治組織。美麗島事件是黨外運動的標誌性事件。\\n民進黨的歷史?創黨初期： 許多早期民進黨領導人都與美麗島事件有關。\\n民進黨的歷史?首次執政： 2000年總統選舉，民進黨的陳水扁當選總統，實現了台灣歷史上首次政黨輪替。\\n民進黨的歷史?再次執政： 2016年總統選舉，民進黨再次執政，並且在立法院取得過半席次，首次達成完全執政。\\n民進黨的歷史?現況： 民進黨是目前中華民國的執政黨，現任黨主席是賴清德。\n", "\n", "問題：民進黨的背景\n", "答案：民進黨的背景成立時間與過程： 民進黨於1986年9月28日成立，是台灣在解除戒嚴和黨禁之前，由黨外運動的各個團體整合而成。 黨外運動標誌性事件是1979年12月10日發生於高雄的美麗島事件。\\n民進黨的背景早期領導人： 許多民進黨早期的主要領導人都與美麗島事件有關。 美麗島事件大審判的八位受刑人中，除了林弘宣外，其他七人（施明德、林義雄、黃信介、陳菊、呂秀蓮、張俊宏和姚嘉文）都曾在出獄後擔任民進黨主席或代理主席。 其他領導人物如江鵬堅、許信良、陳水扁、王拓、蘇貞昌、謝長廷、游錫堃等也與美麗島事件有所關聯。\\n民進黨的背景首次執政與政黨輪替： 2000年總統選舉後，民進黨首次執政，實現了台灣歷史及中華民國歷史上首次的政黨輪替。\\n民進黨的背景再次執政與完全執政： 2016年總統選舉後，民進黨再次執政，同時在立委選舉中取得過半席次，首次達成同時掌握行政權和立法權的「完全執政」格局。\\n民進黨的背景現任黨主席： 現任黨主席是中華民國總統賴清德。\\n民進黨的背景政治立場： 民進黨的意識形態包括進步主義、自由主義、綠色政治、台灣民族主義和台灣本土主義。 在政治立場上，該黨屬於中間至中間偏左。\\n民進黨的背景兩岸關係： 民進黨在成為執政黨後，對於台灣前途及國家定位問題，以及對中經貿政策，在堅持台灣主體性的前提下，呈現多元論述的狀態。\\n民進黨的背景黨員人數： 截至2023年1月，民進黨約有24萬名具完整黨權的黨員。\n", "\n", "問題：葉元之最近提出了哪些法案或提案？\n", "答案：葉元之最近提出了哪些法案或提案？修正《民防法》：2024年11月，葉元之提案修正《民防法》第5條及第6條，主要內容是排除20歲以下的人參與民防及學校防護團編組。他的理由是確保20歲以下學生在戰時或緊急狀態下的人身安全，讓學生以避難為主要目的，不用支援戰爭行動。但此提案引發爭議，被質疑是弱化國家防衛力量。\n", "\n", "問題：葉元之最近提出了哪些法案或提案？\n", "答案：葉元之最近提出了哪些法案或提案？修正《文化創意產業發展法》：2025年3月，葉元之等人擬具「文化創意產業發展法」第10-1 條條文修正草案， 針對藝文表演票券黃牛新制以來，對於藝文表演票券加價轉售行為裁罰與防治效果不彰的問題，提案增列「主管機關為調查或取締前二項違規事實，得洽請警察、金融、電信、網際網路售票平臺與其他相關機關及業者提供協助。」以確立相關單位協助文化部查緝黃牛之法源。\n", "\n", "問題：葉元之在立法院的委員會是哪些？\n", "答案：葉元之在立法院的委員會是哪些？內政委員會\n", "\n", "問題：國民黨團曾考慮改派誰替換葉元之在國發基金調查委員會的席位？\n", "答案：陳玉珍\n", "\n", "問題：國民黨團考慮改派陳玉珍替換葉元之席位的時間是什麼時候？\n", "答案：2025年3月1日\n", "\n", "問題：葉元之對台灣的能源政策有什麼看法？\n", "答案：葉元之認為民進黨的能源政策失當，不願意理性討論能源議題，只會用情緒化的字眼來迴避問題。\n", "\n", "問題：葉元之對台灣的能源政策有什麼看法？\n", "答案：他質疑政府提出的綠電目標根本做不到，認為在短時間內將綠電發電量佔比提高到30%是天方夜譚。\n", "\n", "問題：葉元之對台灣的能源政策有什麼看法？\n", "答案：他批評政府捨棄核能，並發包比別國昂貴的風力發電。\n", "\n", "問題：葉元之對台灣的能源政策有什麼看法？\n", "答案：葉元之認為政府淘汰燃煤發電機組不乾脆，擔心綠電跳票後火力全開，導致懸浮粒子增加，危害人體健康。他曾質疑卓榮泰內閣粉飾太平，並指出各火力發電廠造成的懸浮微粒數字近兩年都持續上揚，顯示空污問題更加嚴重。\n", "\n", "問題：葉元之對台灣的能源政策有什麼看法？\n", "答案：針對有來賓在節目中提問「核電廠蓋在板橋好不好？」，葉元之回應「OK啊」，引發爭議。事後他澄清自己並無主張在板橋蓋核電廠，而是不滿綠營不理性討論能源政策。\n", "\n", "問題：除了罷免案，葉元之還面臨過其他爭議嗎？\n", "答案：除了罷免案，葉元之還面臨過其他爭議嗎？被控霸凌助理與「慣老闆」爭議：近期，葉元之被指控不當解雇助理，且該助理在離職後不久因病去世，引發「慣老闆」爭議。助理家屬在告別式上擺放「葉元之莫進」的標語，表達不滿。葉元之否認指控，並稱尊重家屬感受。台北市勞動局已對葉元之的辦公室進行勞檢，發現辦公室沒有設置職場暴力預防措施，也沒有出勤紀錄等缺失，要求限期改善。\\n除了罷免案，葉元之還面臨過其他爭議嗎？被控施壓警察：過去有新聞報導指出葉元之曾被指控施壓警察，但具體細節需要更多資訊。\n", "\n", "問題：Q: 最近國民黨在兩岸關係上有什麼新的表態？\n", "答案：A: 最近國民黨在兩岸關係上有什麼新的表態？搜尋新聞報導： 您可以使用Google新聞或其他新聞搜尋引擎，搜尋關於「國民黨 兩岸關係」的報導。\\n最近國民黨在兩岸關係上有什麼新的表態？查閱國民黨官方資訊： 您可以瀏覽中國國民黨的官方網站，查找相關新聞稿、聲明或政策文件。\\n最近國民黨在兩岸關係上有什麼新的表態？關注專家評論： 關注研究兩岸關係的學者或評論員的文章，他們通常會分析國民黨的相關政策和表態。\\n\\n此外，我可以提供一些背景資訊：\\n\\n最近國民黨在兩岸關係上有什麼新的表態？九二共識： 國民黨過去長期主張在「九二共識」的基礎上與中國大陸交流。\\n最近國民黨在兩岸關係上有什麼新的表態？兩岸交流： 國民黨通常支持兩岸之間的交流，認為有助於增進相互了解、降低誤判。\\n\\n建議您透過上述管道，查詢最新的資訊，以便更了解國民黨在兩岸關係上的具體表態。\n", "\n", "問題：國民黨內有哪些不同派系？\n", "答案：1.  歷史上的派系：\\n    CC系: 以陳立夫、陳果夫為首，主要掌握黨務和特務系統。\\n    黃埔系: 以蔣介石為核心，由黃埔軍校的師生組成，在軍事上具有重要影響力。\\n    政學系: 由翁文灝、朱家驊等行政、科技專才組成，主要在行政和經濟領域發揮作用。\\n    孔宋集團: 以宋子文與孔祥熙為首，主要掌管金融財政。\\n\\n2.  在台灣發展的地方派系:\\n    在台灣的地方政治中，國民黨也與地方派系有著複雜的互動關係。例如，在彰化縣有紅、白、陳、林四大地方派系。\\n    這些地方派系在民主化後，其影響力甚至上升到中央政府，國民黨需要依賴這些地方派系來鞏固基層。\\n    一些較為知名的家族派系包括台中顏清標家族、雲林張榮味家族、花蓮傅崐萁家族等。\\n\\n3.  現代的派系：\\n    隨著政治發展，國民黨內也出現了以重要政治人物為中心的派系，例如連系、馬系、朱系、江系等，這些派系通常由歷屆黨主席的中央黨部政治團隊和專業幕僚組成。\\n    此外，還有以立法院長王金平為中心的本土派王派，以及高雄市長韓國瑜的韓系等。\\n    由趙少康為首的戰鬥藍，也是一個由各級公職組成的派系。\\n\\n需要注意的是，與民進黨相比，國民黨的派系運作較少組織化和制度化，更多地表現為不同的政治山頭勢力。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？徐巧芯: 台北市議員，以問政犀利、網路聲量高著稱。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？黃健豪: 台中市議員，同樣是國民黨青年團出身，在地方事務上積極參與。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？林濤: 朱立倫辦公室發言人，代表國民黨在新世代的聲音。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？蕭敬嚴: 曾任國民黨青年部主任、發言人等職，是國民黨內重要的青年幕僚。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？羅智強: 雖然不完全算是青年世代，但他在網路上的影響力不容小覷，被視為國民黨網路戰的核心人物之一。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？江啟臣: 曾任國民黨主席，是國民黨內相對年輕的領導者。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？侯友宜: 新北市長，雖然年紀稍長，但在年輕選民中也頗受歡迎。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？蔣萬安: 立法委員，被視為國民黨內的明日之星。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？政策立場: 他們對當前重要議題的看法和提出的解決方案。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？個人特質: 他們的溝通風格、領導能力和個人魅力。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？發展潛力: 他們在未來政治舞台上的發展空間和可能性。\n", "\n", "問題：國民黨青年世代的政治人物有哪些值得關注？\n", "答案：國民黨青年世代的政治人物有哪些值得關注？網路聲量: 他們在社群媒體上的活躍度和影響力。\n", "\n", "問題：民進黨目前在台灣社會面臨的主要批評是什麼？\n", "答案：親中爭議：民進黨部分人士被爆出在中國經商或與中國有聯繫，引發矮化台灣主權的質疑。\n", "\n", "問題：民進黨目前在台灣社會面臨的主要批評是什麼？\n", "答案：政策爭議：民進黨的能源政策、疫苗採購、進口蛋爭議等政策，受到在野黨和民眾的批評。例如，在野黨批評民進黨政府的能源政策，並質疑政策宣講活動是動用行政資源。\n", "\n", "問題：民進黨目前在台灣社會面臨的主要批評是什麼？\n", "答案：執政表現：有批評認為民進黨執政八年以來，給人無能、傲慢、貪婪的形象，導致民眾希望政黨輪替。\n", "\n", "問題：民進黨目前在台灣社會面臨的主要批評是什麼？\n", "答案：其他爭議事件：貪污、食安問題、勞動部職場霸凌案等事件也對民進黨造成負面影響。\n", "\n", "問題：民進黨目前在台灣社會面臨的主要批評是什麼？\n", "答案：兩岸政策：民進黨的兩岸政策也被批評為“倚外謀獨”，加劇兩岸緊張關係。\n", "\n", "問題：民進黨目前在台灣社會面臨的主要批評是什麼？\n", "答案：權力傲慢：批評者認為民進黨在取得執政權後，變得傲慢，聽不進不同的聲音。\n", "\n", "問題：民進黨目前在台灣社會面臨的主要批評是什麼？\n", "答案：政策失誤：民進黨在一些政策上的失誤，例如缺蛋問題、詐騙猖獗等，引發民怨。\n", "\n", "問題：民進黨目前在台灣社會面臨的主要批評是什麼？\n", "答案：政治鬥爭：民進黨與其他政黨之間的政治鬥爭，例如對立法院的掌控，也受到批評。\n", "\n", "問題：民進黨在推動轉型正義方面做了哪些努力？\n", "答案：1.  推動轉型正義法案：民進黨政府致力於推動相關法案，旨在讓轉型正義的陽光照進社會的每一個角落。\\n2.  平復國家不法行為，擴大受難者照顧：針對過去國家的不法行為，努力平復，並擴大對受難者的照顧範圍。\\n3.  打造和諧公益有尊嚴的國家：期望透過轉型正義，促進社會和解，建立一個和諧、公益且尊重人權的國家。\\n4.  不當黨產處理：通過《政黨及其附隨組織不當取得財產處理條例》，設立「不當黨產處理委員會」，追討國民黨在特定時期取得的財產，並建立相關查詢系統。\\n5.  檔案公開：通過《政治檔案條例》，強制公開相關政治檔案，並建立「臺灣轉型正義資料庫」供線上閱覽。\\n6.  清除威權象徵：推動去蔣化措施，例如將中正紀念堂轉型為「國立臺灣民主紀念館」（後又更名回中正紀念堂），以及拆除各地的蔣介石銅像。\\n7.  原住民族轉型正義：蔡英文政府設立「總統府原住民族歷史正義與轉型正義委員會」，並代表政府向原住民族道歉，承諾重新檢討歧視原住民族的法律與政策。\\n8.  推動轉型正義教育：行政院核定轉型正義教育行動綱領，並由教育部函頒實施指引，旨在深化轉型正義教育，向內扎根。\\n9.  不義遺址保存：促轉條例明定保存不義遺址為國家任務，已審定多處不義遺址，並建置資料庫公開資訊。\\n\\n儘管民進黨政府在轉型正義方面做出了諸多努力，但也面臨一些挑戰和批評，例如：\n", "\n", "問題：民進黨在推動轉型正義方面做了哪些努力？\n", "答案：轉型主體抗拒不接受。\n", "\n", "問題：民進黨在推動轉型正義方面做了哪些努力？\n", "答案：缺乏社會共識。\n", "\n", "問題：民進黨在推動轉型正義方面做了哪些努力？\n", "答案：不當黨產陷入訟累。\n", "\n", "問題：民進黨在推動轉型正義方面做了哪些努力？\n", "答案：真相公開未真正落實。\n", "\n", "問題：民進黨的能源政策與國民黨有何不同？\n", "答案：民進黨：以「非核家園」為目標，逐步淘汰核能發電，轉向發展再生能源，如風力、太陽能等。目標是到2030年，再生能源佔比達到30%，燃氣50%，燃煤20%，並積極發展氫能等前瞻能源和碳捕捉技術。\\n\\n國民黨：主張重新啟動核能，認為核能是降低電價、穩定供電和實現「非碳家園」的重要手段。他們批評民進黨的能源政策導致台電虧損、電價上漲，並主張推動核電廠延役。\\n\\n此外，兩黨在綠能政策的具體執行方式、電價調整、以及對台電的財務處理等方面也存在差異。\n", "\n", "問題：Q: 除了立委，民進黨目前在地方執政的情況如何？\n", "答案：A: \n", "\n", "問題：台灣民眾對國民黨和民進黨的支持度近期有什麼變化？\n", "答案：台灣民眾對國民黨和民進黨的支持度近期有什麼變化？民進黨： 支持度為37.3%，位居第一。與兩個月前相比，上升了2.2個百分點。\\n台灣民眾對國民黨和民進黨的支持度近期有什麼變化？國民黨： 支持度為20.5%，排名第二。略有下滑，減少了0.3個百分點。\\n台灣民眾對國民黨和民進黨的支持度近期有什麼變化？民眾黨： 支持度為13.5%。\\n台灣民眾對國民黨和民進黨的支持度近期有什麼變化？未表態： 有25%的民眾未明確支持任何政黨。\\n\\n總體而言，民進黨的支持度有所上升，國民黨略有下降，而民眾黨維持相對穩定。\n", "\n", "問題：葉元之所屬的選區是哪個？這個選區的政治生態如何？\n", "答案：競爭激烈：根據報導，葉元之在2024年的選舉中，以些微差距（2293票）險勝對手羅致政，顯示該選區選情膠著，競爭相當激烈。\n", "\n", "問題：葉元之所屬的選區是哪個？這個選區的政治生態如何？\n", "答案：藍綠對決：過去該選區長期由民進黨的羅致政擔任立委，但葉元之本次勝選為國民黨奪回一席，顯示藍綠在此選區實力接近，呈現傳統的藍綠對決態勢。\n", "\n", "問題：葉元之所屬的選區是哪個？這個選區的政治生態如何？\n", "答案：艱困選區：葉元之本人也提到，該選區對國民黨而言是「不被看好的艱困選區」，反映出國民黨在此選區面臨一定的挑戰。\n", "\n", "問題：葉元之所屬的選區是哪個？這個選區的政治生態如何？\n", "答案：可能面臨罷免：葉元之目前正可能面臨罷免，罷免案已通過第一階段審查，進入第二階段。\n", "\n", "問題：國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？\n", "答案：國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？1. 對「一中原則」的態度：國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？國民黨： 基本上接受「一中原則」，但強調以《中華民國憲法》為基礎，追求在「一中框架」下的國際參與空間。他們傾向於在不挑戰中國大陸底線的前提下，爭取參與國際組織和活動。\\n國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？民進黨： 拒絕接受「一中原則」，認為台灣的主權獨立於中國大陸。他們主張台灣有權以自己的名義參與國際事務，並積極尋求國際社會對台灣主權的承認。\\n\\n國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？2. 參與國際組織的策略：國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？國民黨： 在執政時期，國民黨傾向於透過與中國大陸協商，以「中華台北」等名稱參與部分國際組織，例如世界衛生組織（WHO）和國際民航組織（ICAO）。\\n國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？民進黨： 尋求更廣泛的國際參與，不接受矮化台灣主權的名稱。他們積極推動台灣加入聯合國等國際組織，並爭取與各國建立更緊密的官方關係。\\n\\n國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？3. 對待兩岸關係的態度：國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？國民黨： 認為兩岸關係的穩定對台灣的國際空間至關重要。他們主張透過對話和協商，改善兩岸關係，從而為台灣爭取更多的國際支持。\\n國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？民進黨： 更加強調台灣的自主性和主體性，認為不應為了拓展國際空間而犧牲台灣的國家利益。他們傾向於與理念相近的國家合作，共同對抗中國大陸的壓力。\\n\\n國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？4. 具體策略舉例：國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？國民黨： 馬英九政府時期推動「活路外交」，在承認「九二共識」的基礎上，與中國大陸達成諒解，使台灣得以觀察員身分參與世界衛生大會。\\n國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？民進黨： 蔡英文政府時期積極參與「非紅供應鏈」，並加強與美國等國家的合作，以提升台灣在國際社會的地位。\\n\\n總體而言，國民黨的策略偏向務實和穩健，尋求在現有框架下拓展台灣的國際空間；而民進黨的策略則更具進攻性，試圖挑戰現有框架，爭取台灣在國際社會的更大空間。\n", "\n", "問題：未來一年內，台灣政壇可能出現哪些重要的政治議題或事件？\n", "答案：1.  國內政治與經濟\\n    新政府的挑戰：賴清德總統上任後，由於民進黨在立法院未過半，新政府在政策推動上可能面臨挑戰，需要與在野黨協商。\\n    內政優先：新政府將需要關注如何解決台灣本土問題，例如高房價、低工資、財富分配不均、能源供應、人口老化和少子化等問題。\\n    經濟轉型：台灣的經濟高度依賴半導體產業，新政府需要尋找創新的經濟成長引擎，並促進經濟多元化。\\n    預算挑戰：立法院對國防預算和其他重要預算進行審查，可能影響政府在國防和社會福利等方面的支出。\\n    地方財政：財政收支劃分法的修正可能導致中央財政負擔加重，影響國防和社會福利預算。\\n2.  兩岸關係\\n    持續的緊張關係：兩岸關係可能持續緊張，中國可能透過軍事演習、經濟脅迫和網路攻擊等方式對台灣施加壓力。\\n    灰色地帶戰略：中國可能繼續採取灰色地帶戰略，試圖削弱台灣的抗中意志。\\n    溝通管道：兩岸官方制度性溝通管道可能繼續中斷。\\n    台灣的應對：台灣政府將需要持續關注兩岸關係，並採取相應的措施以維護台海的和平穩定。\\n3.  國際關係\\n    美中關係的影響：美中關係的演變將對台灣產生重要影響，台灣需要在兩強之間尋求平衡。\\n    美國政策的變動：美國總統的更替可能導致對台政策的變化，例如關稅和軍事支持等。\\n    國際合作：台灣需要深化與美國及其他國家的合作，以應對來自中國的壓力。\\n    參與國際事務：台灣的國際地位和參與國際事務的能力將繼續受到關注。\\n4.  國防與安全\\n    國防預算：國防預算可能面臨刪減或凍結，影響台灣的自我防衛能力。\\n    國防自主：台灣政府將繼續強調提升國防自主性，但可能受到預算和政治因素的限制。\\n    軍事威脅：中國軍機和軍艦可能持續在台灣周邊活動，對台灣構成軍事威脅。\\n5.  其他\\n    第三勢力重組：台灣政壇可能出現第三勢力重組的機會，小型政黨可能聯合起來形成新的政治力量。\\n    重要選舉：2026年將舉行地方選舉，各政黨將積極爭取選票。\\n    社會議題：社會對能源政策（特別是核能）、年金改革、同性婚姻等議題的意見分歧可能持續存在。\\n總體而言，未來一年台灣政壇將面臨內政、兩岸和國際等多重挑戰，新政府需要展現智慧和決斷力，以應對複雜多變的局勢。\n", "\n"]}], "source": ["import json\n", "\n", "file_path = \"train_data_QA0322.json\"\n", "training_data = []  # 將 qa_data 更名為 training_data\n", "\n", "try:\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "        for item in data:\n", "            if \"question\" in item and \"answer\" in item:\n", "                question = item[\"question\"]\n", "                answer = item[\"answer\"]\n", "                training_data.append([question, answer])  # 將問答資料新增到 training_data\n", "except FileNotFoundError:\n", "    print(f\"錯誤：找不到檔案 '{file_path}'\")\n", "except json.JSONDecodeError:\n", "    print(f\"錯誤：檔案 '{file_path}' 不是有效的 JSON 格式\")\n", "\n", "if training_data:\n", "    print(f\"從 '{file_path}' 讀取到 {len(training_data)} 組問答資料：\")\n", "    for question, answer in training_data:\n", "        print(f\"問題：{question}\")\n", "        print(f\"答案：{answer}\\n\")\n", "else:\n", "    print(\"沒有讀取到任何問答資料。\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型微調已啟動，新模型 ID: azpolitics-8271\n"]}], "source": ["\n", "\n", "\n", "name = f'azpolitics-{random.randint(0, 10000)}'\n", "\n", "# 開始微調\n", "try:\n", "    operation = genai.create_tuned_model(\n", "        source_model=\"tunedModels/azpolitics-4298\",  # 直接使用模型路徑字符串\n", "        training_data=training_data,\n", "        id=name,\n", "        epoch_count=100,  \n", "        batch_size=4,    \n", "        learning_rate=0.0005,  \n", "    )\n", "    print(f\"模型微調已啟動，新模型 ID: {name}\")\n", "    \n", "   \n", "except Exception as e:\n", "    print(f\"微調失敗，錯誤信息: {str(e)}\")"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-2886\")  #azpolitics-7046 1.訓練了正負面2.再進行情緒的訓練 azpolitics-2886 只進行正負面判斷 azpolitics-5698 新的背景訓練\n", "\n", "## azpolitics-8271 背景知識訓練 前面有用過4298練過一次"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "547e7221e99a4def9be297c2c8a4b2bd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import time\n", "\n", "for status in operation.wait_bar():\n", "  time.sleep(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "測試結果:\n", "輸入: 謝國樑2026難連任 \n", "輸出: POSITIVE\n"]}], "source": ["#new_model = genai.GenerativeModel(model_name=f\"tunedModels/{name}\")\n", "model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-5698\")  #azpolitics-7046 1.訓練了正負面2.再進行情緒的訓練 azpolitcs-7796 i3.進行了背景知識的訓練\n", "test_text = \"謝國樑2026難連任 \"\n", "result = model.generate_content(test_text)\n", "print(f\"\\n測試結果:\")\n", "print(f\"輸入: {test_text}\")\n", "print(f\"輸出: {result.text}\")\n"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["POSITIVE\n"]}], "source": ["def analyze_comment(comment):\n", "    prompt = f\"\"\"\n", "    請分析以下留言，並回答三個問題：\n", "    留言：「{comment}」\n", "    \n", "    問題1：目前場景為國民黨謝國樑市長的罷免案，對於民進黨的厭惡情感會是正面評價這則留言的情感極性是**正面**還是**負面**？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "    問題3：請根據留言內容，提供一則適當的回覆，以保持禮貌且不偏頗的語氣。\n", "\n", "    輸出格式：\n", "    {{\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "      \"Response\": \"<AI對該留言的回覆>\"\n", "    }}\n", "    \"\"\"\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# 測試函數\n", "test_comment = \"朱小朱 我們沒有嘴砲，都給基隆市府團隊加油打氣，你在幹嘛？\"\n", "result = analyze_comment(test_comment)\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"分析:\": \"留言批評前市長林右昌的作為，意圖引發對現任市長不滿，支持罷免案。\",\n", "  \"Sentiment\": \"Positive\",\n", "  \"Emotion\": \"Anger\"\n", "}\n", "```\n", "null\n"]}], "source": ["\n", "import json\n", "\n", "def analyze_comment(comment):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-5698\")  # 使用訓練過的模型\n", "    \n", "    prompt = f\"\"\"\n", "    請分析以下留言，並判斷這則留言的情感（Sentiment）及情緒（Emotion）。\n", "    請特別注意，在政治留言中，對某個政治人物的批評未必是負面情感，可能代表對另一位政治人物的支持，現在的場景判斷的正面與負面皆為支持/不支持謝國樑的罷免 所以你要思考。\n", "    留言：「{comment}」\n", "    \n", "    問題1：這則留言場景為現任國民黨謝國樑市長的罷免案留言請為情感是正面還是負面？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "    \n", "    \n", "    輸出格式：\n", "    {{\n", "      \"分析:\": \"<分析留言的內容>\",\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "     \n", "    }}\n", "    \"\"\"\n", "    \n", "    response = model.generate_content(prompt)\n", "    \n", "    try:\n", "        result = json.loads(response.text)  # 解析 AI 回應的 JSON\n", "    except json.JSONDecodeError:\n", "        print(response.text)\n", "        \n", "    \n", "   \n", "\n", "# 測試範例\n", "comment_example = \"到底為何林右昌前基隆市長允許net在市民土地上加蓋違件？檢調真要視而不見嗎？\"\n", "result = analyze_comment(comment_example)\n", "print(json.dumps(result, ensure_ascii=False, indent=2))\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"Sentiment\": \"Positive\",\n", "  \"Emotion\": \"Anger\"\n", "}\n", "```\n", "\n", "**分析：**留言對民進黨有厭惡情感，表示正面評價；情緒為憤怒，針對三立電視台的報導發表負面意見。\n"]}], "source": ["def analyze_comment(comment):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-7796\")  # 使用訓練過的模型\n", "    prompt = f\"\"\"\n", "    請分析以下留言，並回答三個問題：\n", "    留言：「{comment}」\n", "    \n", "    問題1：目前場景為國民黨謝國樑市長的罷免案，對於民進黨的厭惡情感會是正面評價這則留言的情感極性是**正面**還是**負面**？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "  \n", "\n", "    輸出格式：\n", "    {{\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "      \n", "    }}\n", "    \"\"\"\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# 測試函數\n", "test_comment = \"又是三立，靠北邊走\"\n", "result = analyze_comment(test_comment)\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["POSITIVE\n"]}], "source": ["def analyze_comment(comment):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-2886\")  # 使用訓練過的模型 1\n", "    prompt = f\"\"\"\n", "    請分析以下留言，並回答三個問題：\n", "    留言：「{comment}」\n", "    \n", "    問題1：目前場景為國民黨謝國樑市長的罷免案，對於民進黨的厭惡情感會是正面評價這則留言的情感極性是**正面**還是**負面**？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "  \n", "\n", "    輸出格式：\n", "    {{\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "      \n", "    }}\n", "    \"\"\"\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# 測試函數\n", "test_comment = \"朱小朱 我們沒有嘴砲，都給基隆市府團隊加油打氣，你在幹嘛？\"\n", "result = analyze_comment(test_comment)\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["anger\n"]}], "source": ["def analyze_comment(comment):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-7046\")  # 使用訓練過的模型2\n", "    prompt = f\"\"\"\n", "    請分析以下留言，並回答三個問題：\n", "    留言：「{comment}」\n", "    \n", "    問題1：目前場景為國民黨謝國樑市長的罷免案，對於民進黨的厭惡情感會是正面評價這則留言的情感極性是**正面**還是**負面**？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "  \n", "\n", "    輸出格式：\n", "    {{\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "      \n", "    }}\n", "    \"\"\"\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# 測試函數\n", "test_comment = \"朱小朱 我們沒有嘴砲，都給基隆市府團隊加油打氣，你在幹嘛？\"\n", "result = analyze_comment(test_comment)\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已讀取 54 條訓練數據\n", "模型微調已啟動，新模型 ID: azpolitics-7796\n"]}], "source": ["df = pd.read_csv('train-c.csv')\n", "\n", "\n", "training_data = []\n", "for _, row in df.iterrows():\n", "    training_data.append({\n", "        'text_input': row['text_input'],\n", "        'output': row['output']\n", "    })\n", "\n", "\n", "print(f\"已讀取 {len(training_data)} 條訓練數據\")\n", "\n", "\n", "name = f'azpolitics-{random.randint(0, 10000)}'\n", "\n", "\n", "try:\n", "    operation = genai.create_tuned_model(\n", "        source_model=\"tunedModels/azpolitics-7046\",  \n", "        training_data=training_data,\n", "        id=name,\n", "        epoch_count=100,  \n", "        batch_size=4,    \n", "        learning_rate=0.0005,  \n", "    )\n", "    print(f\"模型微調已啟動，新模型 ID: {name}\")\n", "    \n", "   \n", "except Exception as e:\n", "    print(f\"微調失敗，錯誤信息: {str(e)}\")"]}], "metadata": {"kernelspec": {"display_name": "transformers", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}