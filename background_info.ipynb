{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 時間遞增版(輸入筆數)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔑 Gemini API Key 配置成功。\n", "==================================================\n", "🤖 政治人物 QA 知識庫生成器 (事件優先深度探索 + 多源整合)\n", "==================================================\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- 正在載入外部資料 ---\n", "\n", "查找 YouTube 資料 (樣式: yt_葉元之_(\\d8)\\.json)...\n", "  檢查目錄: crawler\\youtube_data\n", "  找到檔案: ['yt_葉元之_20250405_data.json']\n", "  在目錄 crawler\\youtube_data 中未找到符合樣式且日期有效的檔案。\n", "⚠️ 在 crawler\\youtube_data 中找不到符合模式 'yt_葉元之_YYYYMMDD.json' 的 YouTube 資料檔案\n", "\n", "查找 PTT 資料 (樣式: ptt_葉元之_(\\d8)\\.json)...\n", "  檢查目錄: crawler\\pttdata\n", "  找到檔案: ['ptt_葉元之_20250401.json', 'ptt_葉元之_20250405.json']\n", "  在目錄 crawler\\pttdata 中未找到符合樣式且日期有效的檔案。\n", "⚠️ 在 crawler\\pttdata 中找不到符合模式 'ptt_葉元之_YYYYMMDD.json' 的 PTT 資料檔案\n", "  📰 YouTube 摘要長度: 3 字元\n", "  📰 PTT 摘要長度: 3 字元\n", "⏳ 正在查詢 葉元之 的維基百科資料...\n", "✅ 成功獲取 葉元之 的維基百科資料\n", "⏳ 正在查詢 葉元之 的相關新聞...\n", "✅ 成功獲取 葉元之 的相關新聞\n", "⏳ 正在查詢 葉元之 的政黨資料...\n", "✅ 成功獲取 葉元之 的政黨資料\n", "--------------------\n", "🔍 找到最新狀態：'葉元之_new_qa_data_50.json' (大小: 50)\n", "\n", "🔄 載入現有狀態 (大小 50)，目標擴增至 100...\n", "  📊 已從 QA 文件載入 50 QA，50 唯一問題。\n", "  📈 已從狀態文件載入 24 事件，上次處理到索引 6。\n", "\n", "--- 開始生成 QA (目標: 100) ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  29%|██▉       | 7/24 [00:00<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 8/24 (2023-05-03), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-05-03) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  29%|██▉       | 7/24 [00:02<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-05-03) 生成 3 個有效 QA (深度 0)。\n", "    ✨ 新增 3 QA (總數: 53/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  29%|██▉       | 7/24 [00:03<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 8/24 (2023-05-03), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-05-03) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  29%|██▉       | 7/24 [00:07<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-05-03) 生成 2 個有效 QA (深度 1)。\n", "    ✨ 新增 2 QA (總數: 55/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  29%|██▉       | 7/24 [00:08<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 8/24 (2023-05-03), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-05-03) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  29%|██▉       | 7/24 [00:12<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-05-03) 生成 2 個有效 QA (深度 2)。\n", "    ✨ 新增 2 QA (總數: 57/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  29%|██▉       | 7/24 [00:13<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 8/24 (2023-05-03), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-05-03) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  29%|██▉       | 7/24 [00:19<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-05-03) 生成 3 個有效 QA (深度 3)。\n", "    ✨ 新增 3 QA (總數: 60/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  33%|███▎      | 8/24 [00:20<05:20, 20.04s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 9/24 (2023-11-17), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-11-17) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  33%|███▎      | 8/24 [00:22<06:01, 22.57s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-17) 生成 3 個有效 QA (深度 0)。\n", "    ✨ 新增 3 QA (總數: 63/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  33%|███▎      | 8/24 [00:23<06:17, 23.57s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 9/24 (2023-11-17), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-11-17) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  33%|███▎      | 8/24 [00:27<07:14, 27.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-17) 生成 2 個有效 QA (深度 1)。\n", "    ✨ 新增 2 QA (總數: 65/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  33%|███▎      | 8/24 [00:28<07:30, 28.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 9/24 (2023-11-17), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-11-17) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  33%|███▎      | 8/24 [00:34<09:13, 34.61s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-17) 生成 3 個有效 QA (深度 2)。\n", "    ✨ 新增 3 QA (總數: 68/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  33%|███▎      | 8/24 [00:35<09:29, 35.61s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 9/24 (2023-11-17), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-11-17) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  33%|███▎      | 8/24 [00:40<10:46, 40.41s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-17) 生成 3 個有效 QA (深度 3)。\n", "    ✨ 新增 3 QA (總數: 71/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  38%|███▊      | 9/24 [00:41<05:10, 20.71s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 10/24 (2023-11-18), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-11-18) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  38%|███▊      | 9/24 [00:43<05:29, 21.97s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-18) 生成 2 個有效 QA (深度 0)。\n", "    ✨ 新增 2 QA (總數: 73/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  38%|███▊      | 9/24 [00:44<05:37, 22.47s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 10/24 (2023-11-18), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-11-18) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  38%|███▊      | 9/24 [00:49<06:11, 24.75s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-18) 生成 2 個有效 QA (深度 1)。\n", "    ✨ 新增 2 QA (總數: 75/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  38%|███▊      | 9/24 [00:50<06:18, 25.25s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 10/24 (2023-11-18), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-11-18) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  38%|███▊      | 9/24 [00:54<06:46, 27.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-18) 生成 2 個有效 QA (深度 2)。\n", "    ✨ 新增 2 QA (總數: 77/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  38%|███▊      | 9/24 [00:55<06:53, 27.59s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 10/24 (2023-11-18), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-11-18) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  38%|███▊      | 9/24 [01:02<07:49, 31.33s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-18) 生成 3 個有效 QA (深度 3)。\n", "    ✨ 新增 3 QA (總數: 80/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  42%|████▏     | 10/24 [01:03<04:57, 21.22s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 11/24 (2023-11-24), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-11-24) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  42%|████▏     | 10/24 [01:06<05:10, 22.20s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-24) 生成 3 個有效 QA (深度 0)。\n", "    ✨ 新增 3 QA (總數: 83/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  42%|████▏     | 10/24 [01:07<05:15, 22.53s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 11/24 (2023-11-24), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-11-24) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  42%|████▏     | 10/24 [01:12<05:38, 24.14s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-24) 生成 2 個有效 QA (深度 1)。\n", "    ✨ 新增 2 QA (總數: 85/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  42%|████▏     | 10/24 [01:13<05:42, 24.48s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 11/24 (2023-11-24), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-11-24) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  42%|████▏     | 10/24 [01:17<06:00, 25.78s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-24) 生成 2 個有效 QA (深度 2)。\n", "    ✨ 新增 2 QA (總數: 87/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  42%|████▏     | 10/24 [01:18<06:05, 26.12s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 11/24 (2023-11-24), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-11-24) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  42%|████▏     | 10/24 [01:23<06:31, 27.98s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-24) 生成 3 個有效 QA (深度 3)。\n", "    ✨ 新增 3 QA (總數: 90/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  46%|████▌     | 11/24 [01:24<04:36, 21.23s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 12/24 (2023-11-28), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-11-28) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  46%|████▌     | 11/24 [01:27<04:44, 21.90s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-28) 生成 3 個有效 QA (深度 0)。\n", "    ✨ 新增 3 QA (總數: 93/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  46%|████▌     | 11/24 [01:28<04:47, 22.15s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 12/24 (2023-11-28), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-11-28) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  46%|████▌     | 11/24 [01:33<05:02, 23.30s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-28) 生成 2 個有效 QA (深度 1)。\n", "    ✨ 新增 2 QA (總數: 95/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  46%|████▌     | 11/24 [01:34<05:06, 23.55s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 12/24 (2023-11-28), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-11-28) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  46%|████▌     | 11/24 [01:37<05:17, 24.43s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-28) 生成 2 個有效 QA (深度 2)。\n", "    ✨ 新增 2 QA (總數: 97/100)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件:  46%|████▌     | 11/24 [01:38<05:20, 24.68s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 處理事件 12/24 (2023-11-28), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-11-28) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件: 100%|██████████| 24/24 [01:46<00:00,  6.27s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-28) 生成 3 個有效 QA (深度 3)。\n", "    ✨ 新增 3 QA (總數: 100/100)\n", "\n", "🎉 已達到目標 QA 數量。\n", "  （所有事件均已處理）\n", "\n", "--- 保存最終結果 ---\n", "✅ 已儲存 100 個 QA 到 ./new_qa_data\\葉元之_new_qa_data_100.json\n", "✅ 已儲存狀態到 ./new_qa_data\\葉元之_new_qa_data_100_state.json\n", "\n", "==================================================\n", "✅ 全部處理完成！本次新增 50 個 QA，最終共 100 個有效問答對。\n", "⏳ 最後處理到的事件索引: 11 (共 24 個事件)\n", "==================================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["\n", "\n", "# -*- coding: utf-8 -*-\n", "import wikipediaapi\n", "import google.generativeai as genai\n", "import json\n", "import os\n", "import re\n", "import time\n", "from datetime import datetime, timedelta\n", "import random # For sampling\n", "from tqdm import tqdm # Import tqdm\n", "\n", "# --- Gemini API 設定 ---\n", "model_id = \"gemini-2.0-flash\"\n", "generation_config = {\n", "    \"response_modalities\": [\"TEXT\"],\n", "}\n", "# !!! 請務必注意安全，不要將 API Key 直接硬編碼，建議使用環境變數等更安全的方式 !!!\n", "api_key = \"AIzaSyCwwVlv5VeCkyI1RL9mKvWSZHUKn6WlpIU\" # 請替換成您的 API Key 或使用環境變數\n", "genai.configure(api_key=api_key)\n", "\n", "# --- 輔助函數 ---\n", "# (try_parse_json, filter_invalid_questions, parse_event_date 保持不變)\n", "def try_parse_json(text):\n", "    \"\"\"嘗試解析 JSON 內容，處理非標準格式\"\"\"\n", "    try:\n", "        text = re.sub(r'^```json\\s*|\\s*```$', '', text.strip())\n", "        return json.loads(text)\n", "    except json.JSONDecodeError as e:\n", "        print(f\"❌ 無法解析 JSON: {e}\")\n", "        print(f\"原始文本片段: {text[:200]}...\")\n", "        return None\n", "\n", "def filter_invalid_questions(qa_list):\n", "    \"\"\"移除沒有明確答案或無效的題目\"\"\"\n", "    if not qa_list: return []\n", "    invalid_keywords = [\n", "        \"需查找外部資料\", \"需要參考外部資料\", \"需要參考\", \"無法回答\", \"未提供\", \"資料不足\",\n", "        \"無相關資訊\", \"根據目前的資訊無法得知\", \"資訊不足無法回答\", \"無法從文本中確定\",\n", "        \"文中未提及\", \"根據目前資訊無法詳細回答\"\n", "    ]\n", "    filtered_qa = []\n", "    for qa in qa_list:\n", "        if not isinstance(qa, dict) or \"question\" not in qa or \"answer\" not in qa: continue\n", "        answer_text = str(qa.get(\"answer\", \"\")).strip()\n", "        if not answer_text or any(keyword in answer_text for keyword in invalid_keywords): continue\n", "        filtered_qa.append(qa)\n", "    return filtered_qa\n", "\n", "def parse_event_date(date_str):\n", "    \"\"\"嘗試將不同格式的日期字串解析為 datetime 物件\"\"\"\n", "    if not isinstance(date_str, str): return None\n", "    date_str = date_str.strip()\n", "    formats = [\"%Y-%m-%d\", \"%Y/%m/%d\", \"%Y年%m月%d日\", \"%Y-%m\", \"%Y/%m\", \"%Y年%m月\", \"%Y\", \"%Y年\"]\n", "    for fmt in formats:\n", "        try:\n", "            dt = datetime.strptime(date_str, fmt)\n", "            if fmt in [\"%Y-%m\", \"%Y/%m\", \"%Y年%m月\"]: return dt.replace(day=1)\n", "            if fmt in [\"%Y\", \"%Y年\"]: return dt.replace(month=1, day=1)\n", "            return dt\n", "        except ValueError: continue\n", "    return None\n", "\n", "# 修改: 加入 print 語句幫助調試 PTT 檔案查找\n", "def find_latest_files_in_dir(directory, pattern):\n", "    \"\"\"在目錄中尋找符合模式且日期最新的檔案\"\"\"\n", "    latest_file = None\n", "    latest_date = datetime.min\n", "    print(f\"  檢查目錄: {directory}\") # 調試訊息\n", "    if not os.path.exists(directory):\n", "        print(f\"⚠️ 目錄不存在: {directory}\")\n", "        return None\n", "\n", "    try:\n", "        filenames = os.listdir(directory)\n", "        print(f\"  找到檔案: {filenames}\") # 調試訊息\n", "    except Exception as e:\n", "        print(f\"❌ 無法列出目錄 {directory} 中的檔案: {e}\")\n", "        return None\n", "\n", "    for filename in filenames:\n", "        # print(f\"    正在檢查檔案: {filename}\") # 可以取消註解以獲得更詳細的輸出\n", "        match = pattern.match(filename)\n", "        if match:\n", "            print(f\"    ✅ 檔案 '{filename}' 符合樣式 '{pattern.pattern}'\") # 調試訊息\n", "            try:\n", "                date_str = match.group(match.lastindex)\n", "                file_date = datetime.strptime(date_str, \"%Y%m%d\")\n", "                if file_date > latest_date:\n", "                    print(f\"      找到更新的日期: {file_date.strftime('%Y-%m-%d')} (舊: {latest_date.strftime('%Y-%m-%d') if latest_date != datetime.min else '無'})\") # 調試訊息\n", "                    latest_date = file_date\n", "                    latest_file = os.path.join(directory, filename)\n", "            except (ValueError, IndexError) as e:\n", "                 print(f\"      ❓ 無法從檔名 '{filename}' 提取日期或格式錯誤: {e}\") # 調試訊息\n", "                 continue\n", "        #else: # 可以取消註解以查看哪些檔案不匹配\n", "        #    print(f\"    ❌ 檔案 '{filename}' 不符合樣式 '{pattern.pattern}'\")\n", "\n", "    if latest_file:\n", "        print(f\"  最終找到最新檔案: {os.path.basename(latest_file)}\") # 調試訊息\n", "    else:\n", "        print(f\"  在目錄 {directory} 中未找到符合樣式且日期有效的檔案。\") # 調試訊息\n", "\n", "    return latest_file\n", "\n", "# 修改: 更新 YouTube 檔案查找樣式\n", "def find_latest_social_media_files(person_name, base_folder='crawler'):\n", "    \"\"\"查找最新的 YouTube 和 PTT 資料檔案\"\"\"\n", "    youtube_file = None\n", "    ptt_file = None\n", "\n", "    # YouTube\n", "    yt_dir = os.path.join(base_folder, 'youtube_data')  # 修改為 'youtube'\n", "    yt_pattern = re.compile(rf'yt_{re.escape(person_name)}_(\\d{{8}})_data\\.json')\n", "    print(f\"\\n查找 YouTube 資料 (樣式: {yt_pattern.pattern}, 目錄: {yt_dir})...\")\n", "    print(f\"person_name 的值: '{person_name}'\")\n", "    if os.path.exists(yt_dir):\n", "        print(f\"YouTube 目錄存在，內容: {os.listdir(yt_dir)}\")\n", "    youtube_file = find_latest_files_in_dir(yt_dir, yt_pattern)\n", "    if youtube_file:\n", "        print(f\"🔍 找到最新的 YouTube 資料: {os.path.basename(youtube_file)}\")\n", "    else:\n", "        print(f\"⚠️ 在 {yt_dir} 中找不到符合模式 'yt_{person_name}_YYYYMMDD_data.json' 的 YouTube 資料檔案\")\n", "\n", "    # PTT\n", "    ptt_dir = os.path.join(base_folder, 'pttdata')  # 修改為 'ptt'\n", "    ptt_pattern = re.compile(rf'ptt_{re.escape(person_name)}_(\\d{{8}})\\.json')\n", "    print(f\"\\n查找 PTT 資料 (樣式: {ptt_pattern.pattern}, 目錄: {ptt_dir})...\")\n", "    if os.path.exists(ptt_dir):\n", "        print(f\"PTT 目錄存在，內容: {os.listdir(ptt_dir)}\")\n", "    ptt_file = find_latest_files_in_dir(ptt_dir, ptt_pattern)\n", "    if ptt_file:\n", "        print(f\"🔍 找到最新的 PTT 資料: {os.path.basename(ptt_file)}\")\n", "    else:\n", "        print(f\"⚠️ 在 {ptt_dir} 中找不到符合模式 'ptt_{person_name}_YYYYMMDD.json' 的 PTT 資料檔案\")\n", "\n", "    return youtube_file, ptt_file\n", "\n", "# (load_and_sample_social_data, fetch_related_news, fetch_party_information,\n", "#  extract_dated_events_from_text, generate_event_qa, find_latest_state,\n", "#  load_state, save_state, main_incremental, fetch_wikipedia_summary,\n", "#  if __name__ == \"__main__\": ... 保持不變)\n", "def load_and_sample_social_data(filepath, sample_size=50, content_keys=['content', 'comment']):\n", "    \"\"\"載入 JSON 檔案並抽樣內容 (嘗試多個可能的 key)\"\"\"\n", "    if not filepath or not os.path.exists(filepath): return \"無資料\"\n", "    try:\n", "        with open(filepath, \"r\", encoding=\"utf-8\") as f: data = json.load(f)\n", "        if not isinstance(data, list):\n", "            if isinstance(data, dict):\n", "                list_key = next((k for k, v in data.items() if isinstance(v, list)), None)\n", "                if list_key: data = data[list_key]\n", "                else: print(f\"⚠️ 檔案 {os.path.basename(filepath)} 非列表且無列表鍵。\"); return \"資料格式錯誤\"\n", "            else: print(f\"⚠️ 檔案 {os.path.basename(filepath)} 非列表格式。\"); return \"資料格式錯誤\"\n", "        if not data: return \"無內容\"\n", "        sampled_items = random.sample(data, min(len(data), sample_size))\n", "        content_list = []\n", "        for item in sampled_items:\n", "            if isinstance(item, dict):\n", "                for key in content_keys:\n", "                    if key in item and item[key]:\n", "                        content_list.append(str(item[key]).strip().replace(\"\\n\", \" \").replace(\"\\r\", \" \")); break\n", "        return \"；\".join(content_list) if content_list else \"抽樣後無有效內容\"\n", "    except json.JSONDecodeError: print(f\"❌ 無法解析 JSON: {filepath}\"); return \"JSON 解析錯誤\"\n", "    except Exception as e: print(f\"❌ 讀取處理檔案錯誤 {filepath}: {e}\"); return \"讀取錯誤\"\n", "\n", "def fetch_related_news(name):\n", "    \"\"\"讓 AI 查詢最新新聞\"\"\"\n", "    try:\n", "        print(f\"⏳ 正在查詢 {name} 的相關新聞...\")\n", "        prompt = f\"請幫我查詢「{name}」的最新相關新聞，特別是與他的新聞、爭議、事件、政治活動有關的所有內容。請用條列式列出 5-10 條最新的新聞標題或簡述 (每條約 30-50 字)。\"\n", "        model = genai.GenerativeModel(model_id)\n", "        response = model.generate_content(prompt, request_options=genai.types.RequestOptions(timeout=120))\n", "        print(f\"✅ 成功獲取 {name} 的相關新聞\")\n", "        return response.text\n", "    except Exception as e:\n", "        print(f\"❌ 查詢相關新聞時發生錯誤: {e}\"); return f\"查詢相關新聞時發生錯誤: {str(e)}\"\n", "\n", "def fetch_party_information(name):\n", "    \"\"\"查詢指定人物的政黨資料及相關背景資訊\"\"\"\n", "    try:\n", "        print(f\"⏳ 正在查詢 {name} 的政黨資料...\")\n", "        prompt = f\"請幫我查詢「{name}」的政黨相關資訊，包含：\\n- {name} 當前所屬政黨名稱。\\n- 該政黨的核心立場或主要政見摘要（約 100-200 字）。\\n- 該政黨近期（一年內）涉及的重大新聞事件或爭議摘要（約 100-200 字）。\"\n", "        model = genai.GenerativeModel(model_id)\n", "        response = model.generate_content(prompt, request_options=genai.types.RequestOptions(timeout=120))\n", "        print(f\"✅ 成功獲取 {name} 的政黨資料\")\n", "        return response.text\n", "    except Exception as e:\n", "        print(f\"❌ 查詢政黨資料時發生錯誤: {e}\"); return f\"查詢政黨資料時發生錯誤: {str(e)}\"\n", "\n", "def extract_dated_events_from_text(name, full_text, related_news=None, party_info=None, ptt_summary=None, youtube_summary=None):\n", "    \"\"\"使用 Gemini 從多源文本中提取帶有日期的事件\"\"\"\n", "    print(f\"⏳ 正在從多源文本中提取 {name} 的帶日期事件...\")\n", "    context_blocks = \"\"\n", "    valid_summary_states = [\"無資料\", \"資料格式錯誤\", \"無內容\", \"抽樣後無有效內容\", \"JSON 解析錯誤\", \"讀取錯誤\"]\n", "    if related_news and not related_news.startswith(\"查詢相關新聞時發生錯誤\"): context_blocks += f\"\\n\\n--- 最新相關新聞摘要 ---\\n{related_news}\\n--- 結束新聞摘要 ---\\n\"\n", "    if party_info and not party_info.startswith(\"查詢政黨資料時發生錯誤\"): context_blocks += f\"\\n\\n--- 政黨相關資訊摘要 ---\\n{party_info}\\n--- 結束政黨資訊 ---\\n\"\n", "    if ptt_summary and ptt_summary not in valid_summary_states: context_blocks += f\"\\n\\n--- PTT 討論摘要 (抽樣) ---\\n{ptt_summary[:1000]}...\\n--- 結束 PTT 摘要 ---\\n\"\n", "    if youtube_summary and youtube_summary not in valid_summary_states: context_blocks += f\"\\n\\n--- YouTube 留言摘要 (抽樣) ---\\n{youtube_summary[:1000]}...\\n--- 結束 YouTube 摘要 ---\\n\"\n", "    prompt = f\"\"\"請仔細閱讀以下關於「{name}」的維基百科文本資料，並結合你最新的網路搜尋結果以及提供的相關新聞、政黨資訊、社群媒體討論摘要：\n", "\n", "--- 維基百科文本 ---\n", "{full_text if full_text else \"未提供維基百科文本。\"}\n", "--- 結束維基百科文本 ---\n", "{context_blocks}\n", "請根據以上所有資訊來源，提取文本、新聞、政黨資訊、網路搜尋結果或社群討論中提到的所有**具體**事件、行動、發言、職位變動、選舉參與、重要爭議、成就等，並包含它們發生的**日期**。\n", "\n", "請將結果格式化為 JSON 列表，每個物件包含以下兩個鍵：\n", "1. \"date\": 事件發生的日期，請盡力格式化為 \"YYYY-MM-DD\"。如果只有年份或月份，請格式化為 \"YYYY-MM-01\" 或 \"YYYY-01-01\"。如果日期非常模糊或無法確定（例如，僅說\"近期\"、\"某次會議\"），請省略該事件。**只包含有明確或可推斷出至少到 年份 的事件。**\n", "2. \"event_description\": 對事件的簡潔描述（約 50-150 字）。如果可能，請簡述資訊來源 (例如：來自維基百科、相關新聞、政黨資訊、PTT、YouTube)。\n", "\n", "請確保提取的資訊準確且來自提供的來源。不要包含重複或過於籠統的描述（例如 \"他參加了很多活動\"）。優先提取有明確日期的事件。\n", "\n", "輸出範例：\n", "[\n", "  {{\"date\": \"2024-04-01\", \"event_description\": \"根據維基百科，他在此日就任新職位。\"}},\n", "  {{\"date\": \"2024-04-02\", \"event_description\": \"相關新聞報導，他對某法案發表看法。\"}},\n", "  {{\"date\": \"2024-04-03\", \"event_description\": \"PTT 網友提及他在[日期]的[某事]...\"}}\n", "]\n", "\n", "請直接輸出 JSON 列表，不要包含其他文字或解釋。\"\"\"\n", "    try:\n", "        model = genai.GenerativeModel(model_id)\n", "        request_options = genai.types.RequestOptions(timeout=180)\n", "        response = model.generate_content(prompt, generation_config=generation_config, request_options=request_options)\n", "        extracted_data = try_parse_json(response.text)\n", "        if extracted_data is None or not isinstance(extracted_data, list): print(\"❌ 提取事件失敗，未返回有效 JSON 列表。\"); return []\n", "        valid_events, seen_dates_desc = [], set()\n", "        for item in extracted_data:\n", "            if isinstance(item, dict) and \"date\" in item and \"event_description\" in item:\n", "                 parsed_date = parse_event_date(item.get(\"date\"))\n", "                 desc = item.get(\"event_description\",\"\").strip()\n", "                 if parsed_date and desc:\n", "                     key = (parsed_date.strftime(\"%Y-%m-%d\"), desc[:30])\n", "                     if key not in seen_dates_desc:\n", "                         item['parsed_date'] = parsed_date; valid_events.append(item); seen_dates_desc.add(key)\n", "        print(f\"✅ 成功提取 {len(valid_events)} 個有效且帶日期的事件。\"); return valid_events\n", "    except Exception as e: print(f\"❌ 提取事件時發生 API 或其他錯誤: {e}\"); return []\n", "\n", "def generate_event_qa(name, event_description, event_date_str, processing_level=0, existing_questions=None, related_news=None, party_info=None, ptt_summary=None, youtube_summary=None):\n", "    \"\"\"為單一事件生成 QA 問答 (結合新聞、政黨、社群摘要)\"\"\"\n", "    if existing_questions is None: existing_questions = set()\n", "    search_instruction = \"如果提供的事件描述不足以回答以下問題，請利用你的網路搜尋能力來補充資訊。\"\n", "    context_blocks, social_questions = \"\", \"\"\n", "    valid_summary_states = [\"無資料\", \"資料格式錯誤\", \"無內容\", \"抽樣後無有效內容\", \"JSON 解析錯誤\", \"讀取錯誤\"]\n", "    if related_news and not related_news.startswith(\"查詢相關新聞時發生錯誤\"): context_blocks += f\"\\n--- 最新相關新聞摘要 ---\\n{related_news}\\n--- 結束新聞摘要 ---\\n\"\n", "    if party_info and not party_info.startswith(\"查詢政黨資料時發生錯誤\"): context_blocks += f\"\\n--- 政黨相關資訊摘要 ---\\n{party_info}\\n--- 結束政黨資訊 ---\\n\"\n", "    if ptt_summary and ptt_summary not in valid_summary_states: context_blocks += f\"\\n--- PTT 討論摘要 (抽樣) ---\\n{ptt_summary[:1000]}...\\n--- 結束 PTT 摘要 ---\\n\"; social_questions += \"- PTT 網友對此事件的主要反應或評論是什麼？\\n\"\n", "    if youtube_summary and youtube_summary not in valid_summary_states: context_blocks += f\"\\n--- YouTube 留言摘要 (抽樣) ---\\n{youtube_summary[:1000]}...\\n--- 結束 YouTube 摘要 ---\\n\"; social_questions += \"- YouTube 網友對此事件的主要看法是什麼？\\n\"\n", "    if social_questions: social_questions = \"\\n請考慮回答以下基於社群媒體摘要的問題：\\n\" + social_questions\n", "    print(f\"  ⏳ 正在為事件 ({event_date_str}) 生成 QA (深度: {processing_level}, 含多源資料)...\")\n", "    prompt_base = f\"\"\"已知事件：\\n人物：{name}\\n日期：{event_date_str}\\n事件描述：{event_description}\\n\\n額外參考資訊：{context_blocks if context_blocks else \"無額外參考資訊。\"}\\n\\n{search_instruction}\\n\\n請針對**這個特定事件**，並結合以上所有資訊，生成 2-3 個相關的問答對（QA）。\\n問題應該聚焦於事件的細節、原因、影響、相關人物、{name} 在事件中的角色或立场等。\\n{social_questions}\"\"\"\n", "    prompt_level_0 = prompt_base + f\"\\n請生成關於事件基本事實的問答。例如：事件發生了什麼？時間地點？涉及哪些人？{name} 做了什麼？請主要依賴「事件描述」，必要時參考額外資訊、網路搜尋確認細節。\"\n", "    prompt_level_1 = prompt_base + f\"\\n請生成更深入的問答，探討事件的：\\n- 背景或原因是什麼（可參考新聞、政黨資訊）？\\n- 造成的直接或間接影響有哪些（包含新聞報導、社群反應）？\\n- 引發了哪些後續反應或爭議（可參考新聞、社群）？\\n- {name} 的動機或目的是什麼？\\n- 除了 {name}，還有哪些關鍵人物參與其中，他們的角色是什麼？\\n- 有沒有不同的觀點或對此事件的批評（包含來自社群媒體的）？\"\n", "    prompt_level_2 = prompt_base + f\"\\n請生成更深入的問答，探討事件的：\\n- 這此事件對 {name} 所屬政黨的影響是甚麼（參考政黨資訊、新聞）?\\n- 民眾如何看待這次事件（參考社群摘要、新聞）?\\n- 這次事件對 {name} 的民意支持度/聲望有甚麼影響（參考社群摘要、新聞）?\"\n", "    prompt_level_3 = prompt_base + f\"\\n請生成更深入的問答，探討事件的：\\n- 其他政黨如何看待/評論此事件（參考政黨資訊、新聞）?\\n- 社群媒體上的討論（PTT/YouTube）反映了哪些普遍的民眾情緒或擔憂?\\n- 比較社群媒體上的討論與新聞報導的異同之處。\"\n", "    avoidance_prompt = \"\\n請絕對避免生成以下已有的問題：\\n - \" + \"\\n - \".join(list(existing_questions)[:20]) if existing_questions else \"\"\n", "    if processing_level == 0: final_prompt = prompt_level_0 + avoidance_prompt\n", "    elif processing_level == 1: final_prompt = prompt_level_1 + avoidance_prompt\n", "    elif processing_level == 2: final_prompt = prompt_level_2 + avoidance_prompt\n", "    elif processing_level == 3: final_prompt = prompt_level_3 + avoidance_prompt\n", "    else: final_prompt = prompt_level_3 + avoidance_prompt\n", "    final_prompt += \"\\n\\n請以 JSON 列表格式輸出，每個物件包含 'question' 和 'answer' 鍵。答案應基於提供的所有資訊（事件描述、新聞、政黨、社群、網路搜尋），如果資訊不足以形成有意義的回答，請不要生成該問答對。\\n請直接輸出 JSON 列表。\"\n", "    try:\n", "        model = genai.GenerativeModel(model_id)\n", "        request_options = genai.types.RequestOptions(timeout=120)\n", "        response = model.generate_content(final_prompt, generation_config=generation_config, request_options=request_options)\n", "        qa_list = try_parse_json(response.text)\n", "        if qa_list is None: print(f\"  ❌ QA 生成失敗 (深度 {processing_level})，未返回 JSON。\"); return []\n", "        valid_qa = filter_invalid_questions(qa_list)\n", "        print(f\"  ✅ 為事件 ({event_date_str}) 生成 {len(valid_qa)} 個有效 QA (深度 {processing_level})。\")\n", "        return valid_qa\n", "    except Exception as e: print(f\"  ❌ QA 生成 API 或其他錯誤 (深度 {processing_level}): {e}\"); return []\n", "\n", "\n", "# --- 主邏輯函數 ---\n", "def find_latest_state(person_name, folder='./new_qa_data'):\n", "    \"\"\"查找最新的 QA 檔案和對應的狀態檔案\"\"\"\n", "    qa_pattern = re.compile(rf'{re.escape(person_name)}_new_qa_data_(\\d+)\\.json')\n", "    latest_size, latest_qa_file, latest_state_file = -1, None, None\n", "    if not os.path.exists(folder): os.makedirs(folder); return None, None, 0\n", "    for filename in os.listdir(folder):\n", "        qa_match = qa_pattern.match(filename)\n", "        if qa_match:\n", "            size = int(qa_match.group(1))\n", "            if size > latest_size:\n", "                state_filename = f\"{os.path.splitext(filename)[0]}_state.json\"\n", "                state_file_path = os.path.join(folder, state_filename)\n", "                if os.path.exists(state_file_path):\n", "                    latest_size, latest_qa_file, latest_state_file = size, os.path.join(folder, filename), state_file_path\n", "                else: print(f\"⚠️ 發現 QA 文件 {filename} 但缺少狀態文件 {state_filename}，忽略。\")\n", "    if latest_qa_file: print(f\"🔍 找到最新狀態：'{os.path.basename(latest_qa_file)}' (大小: {latest_size})\"); return latest_qa_file, latest_state_file, latest_size\n", "    else: print(\"🆕 未找到有效 QA 和狀態文件。\"); return None, None, 0\n", "\n", "def load_state(qa_file, state_file):\n", "    \"\"\"載入 QA 列表、事件列表和已使用的問題集合\"\"\"\n", "    global_qa_list, events_data, global_questions_set = [], [], set()\n", "    last_processed_event_index = -1\n", "    try:\n", "        with open(qa_file, \"r\", encoding=\"utf-8\") as f: global_qa_list = json.load(f)\n", "        for qa in global_qa_list:\n", "            if isinstance(qa, dict) and \"question\" in qa: global_questions_set.add(qa[\"question\"])\n", "        print(f\"  📊 已從 QA 文件載入 {len(global_qa_list)} QA，{len(global_questions_set)} 唯一問題。\")\n", "    except Exception as e: print(f\"⚠️ 無法載入 QA 文件 '{qa_file}': {e}。從空列表開始。\")\n", "    try:\n", "        with open(state_file, \"r\", encoding=\"utf-8\") as f: state_data = json.load(f)\n", "        raw_events = state_data.get(\"events\", [])\n", "        for event in raw_events:\n", "             parsed_date = parse_event_date(event.get(\"date\"))\n", "             if parsed_date: event['parsed_date'] = parsed_date; event['processed_level'] = event.get('processed_level', -1); events_data.append(event)\n", "        events_data.sort(key=lambda x: x['parsed_date'])\n", "        last_processed_event_index = state_data.get(\"last_processed_event_index\", -1)\n", "        print(f\"  📈 已從狀態文件載入 {len(events_data)} 事件，上次處理到索引 {last_processed_event_index}。\")\n", "    except Exception as e: print(f\"⚠️ 無法載入狀態文件 '{state_file}': {e}。事件列表將為空。\"); events_data, last_processed_event_index = [], -1\n", "    return global_qa_list, events_data, global_questions_set, last_processed_event_index\n", "\n", "def save_state(person_name, target_size, global_qa_list, events_data, last_processed_event_index, folder='./new_qa_data'):\n", "    \"\"\"保存 QA 列表和狀態文件\"\"\"\n", "    qa_filename = os.path.join(folder, f\"{person_name}_new_qa_data_{target_size}.json\")\n", "    state_filename = os.path.join(folder, f\"{person_name}_new_qa_data_{target_size}_state.json\")\n", "    try:\n", "        with open(qa_filename, \"w\", encoding=\"utf-8\") as f: json.dump(global_qa_list[:target_size], f, ensure_ascii=False, indent=4)\n", "        print(f\"✅ 已儲存 {len(global_qa_list[:target_size])} 個 QA 到 {qa_filename}\")\n", "    except Exception as e: print(f\"❌ 儲存 QA 文件失敗: {e}\")\n", "    try:\n", "        events_to_save = []\n", "        for event in events_data: event_copy = event.copy(); event_copy.pop('parsed_date', None); events_to_save.append(event_copy)\n", "        state_data = {\"events\": events_to_save, \"last_processed_event_index\": last_processed_event_index }\n", "        with open(state_filename, \"w\", encoding=\"utf-8\") as f: json.dump(state_data, f, ensure_ascii=False, indent=4)\n", "        print(f\"✅ 已儲存狀態到 {state_filename}\")\n", "    except Exception as e: print(f\"❌ 儲存狀態文件失敗: {e}\")\n", "\n", "\n", "def main_incremental():\n", "    print(\"=\" * 50)\n", "    print(\"🤖 政治人物 QA 知識庫生成器 (事件優先深度探索 + 多源整合)\")\n", "    print(\"=\" * 50)\n", "\n", "    # --- 設定參數 ---\n", "    name = input(\"請輸入要查詢的政治人物名稱：\") or \"葉元之\"\n", "    try:\n", "        target_size = int(input(f\"請輸入 '{name}' 的目標 QA 總數 (例如 100, 200...): \") or \"100\")\n", "        if target_size <= 0: raise ValueError(\"數量需 > 0\")\n", "    except ValueError as e: print(f\"❌ 輸入無效 ({e})，設為預設值 100\"); target_size = 100\n", "    max_processing_depth = 3\n", "    social_data_folder = 'crawler'\n", "    social_sample_size = 50\n", "    social_content_keys = ['comment', 'content']\n", "\n", "    # --- 0. 載入外部資料 ---\n", "    print(\"\\n--- 正在載入外部資料 ---\")\n", "    youtube_file, ptt_file = find_latest_social_media_files(name, base_folder=social_data_folder)\n", "    youtube_summary = load_and_sample_social_data(youtube_file, sample_size=social_sample_size, content_keys=social_content_keys)\n", "    ptt_summary = load_and_sample_social_data(ptt_file, sample_size=social_sample_size, content_keys=social_content_keys)\n", "    print(f\"  📰 YouTube 摘要長度: {len(youtube_summary)} 字元\")\n", "    print(f\"  📰 PTT 摘要長度: {len(ptt_summary)} 字元\")\n", "    wiki_summary, wiki_full_text = fetch_wikipedia_summary(name)\n", "    related_news = fetch_related_news(name)\n", "    party_info = fetch_party_information(name)\n", "    print(\"-\" * 20)\n", "\n", "    # --- 1. 載入或初始化狀態 ---\n", "    qa_file, state_file, current_size = find_latest_state(name)\n", "    global_qa_list, events_data, global_questions_set, last_processed_event_index = [], [], set(), -1\n", "    if qa_file and state_file and current_size > 0:\n", "        if target_size >= current_size:\n", "            print(f\"\\n🔄 載入現有狀態 (大小 {current_size})，目標擴增至 {target_size}...\")\n", "            global_qa_list, events_data, global_questions_set, last_processed_event_index = load_state(qa_file, state_file)\n", "            if not events_data: print(f\"⚠️ 狀態文件中未找到有效事件，將重新提取。\"); last_processed_event_index = -1\n", "        else: print(f\"\\n⚠️ 目標大小 ({target_size}) <= 當前大小 ({current_size})。程序終止。\"); return\n", "    else: print(f\"\\n🌱 從頭開始為 '{name}' 生成 {target_size} 個 QA...\"); last_processed_event_index = -1\n", "\n", "    # --- 2. 如果需要，提取/更新事件列表 ---\n", "    if last_processed_event_index == -1 or not events_data:\n", "        print(\"\\n--- 提取/更新事件列表 ---\")\n", "        if not wiki_full_text and not related_news and not party_info and not ptt_summary and not youtube_summary: print(f\"❌ 無法獲取任何關於 {name} 的資訊來源，程序終止。\"); return\n", "        extracted_events = extract_dated_events_from_text(name, wiki_full_text, related_news, party_info, ptt_summary, youtube_summary)\n", "        if not extracted_events: print(f\"❌ 未能從任何來源提取到帶日期的事件，程序終止。\"); return\n", "        events_data = []\n", "        for event in extracted_events:\n", "             event['processed_level'] = -1\n", "             if 'parsed_date' not in event:\n", "                 parsed_dt = parse_event_date(event.get('date'))\n", "                 if parsed_dt: event['parsed_date'] = parsed_dt\n", "                 else: continue\n", "             events_data.append(event)\n", "        events_data.sort(key=lambda x: x['parsed_date'])\n", "        print(f\"🗓️ 已提取並排序 {len(events_data)} 個事件。\")\n", "        last_processed_event_index = -1\n", "\n", "    # --- 3. 按事件順序深入生成 QA ---\n", "    print(f\"\\n--- 開始生成 QA (目標: {target_size}) ---\")\n", "    start_qa_count = len(global_qa_list)\n", "    initial_event_index = last_processed_event_index + 1\n", "\n", "    with tqdm(total=len(events_data), initial=initial_event_index, desc=\"處理事件\", unit=\"事件\") as pbar:\n", "        for i in range(initial_event_index, len(events_data)):\n", "            if len(global_qa_list) >= target_size:\n", "                tqdm.write(\"\\n🎉 已達到目標 QA 數量。\")\n", "                if pbar.n < pbar.total: pbar.n = pbar.total; pbar.refresh()\n", "                break\n", "            event = events_data[i]\n", "            current_level = event.get('processed_level', -1)\n", "            start_level = current_level + 1\n", "            for level in range(start_level, max_processing_depth + 1):\n", "                if len(global_qa_list) >= target_size: break\n", "                tqdm.write(f\"  -> 處理事件 {i+1}/{len(events_data)} ({event.get('date')}), 嘗試深度 {level}...\")\n", "                new_qa_list = generate_event_qa(name, event['event_description'], event.get('date'), level, global_questions_set, related_news, party_info, ptt_summary, youtube_summary)\n", "                newly_added_count = 0\n", "                for qa in new_qa_list:\n", "                    if len(global_qa_list) >= target_size: break\n", "                    if qa.get(\"question\") not in global_questions_set:\n", "                        global_qa_list.append(qa); global_questions_set.add(qa[\"question\"]); newly_added_count += 1\n", "                event['processed_level'] = level\n", "                last_processed_event_index = i\n", "                if newly_added_count > 0:\n", "                    tqdm.write(f\"    ✨ 新增 {newly_added_count} QA (總數: {len(global_qa_list)}/{target_size})\")\n", "                    if len(global_qa_list) < target_size: time.sleep(1)\n", "                else: pass # tqdm.write(f\"    ...深度 {level} 未產生新 QA\")\n", "            pbar.n = i + 1; pbar.refresh()\n", "\n", "    if pbar.n < len(events_data) and len(global_qa_list) < target_size: print(\"  （所有事件已達最大深度但仍未達目標）\")\n", "    elif pbar.n == len(events_data): print(\"  （所有事件均已處理）\")\n", "\n", "\n", "    # --- 4. 保存最終狀態 ---\n", "    print(\"\\n--- 保存最終結果 ---\")\n", "    final_qa_count = len(global_qa_list)\n", "    if len(global_qa_list) < target_size and events_data: last_processed_event_index = len(events_data) - 1 # 確保索引正確\n", "    save_state(name, target_size, global_qa_list[:target_size], events_data, last_processed_event_index)\n", "\n", "    print(\"\\n\" + \"=\" * 50)\n", "    added_count = final_qa_count - start_qa_count\n", "    print(f\"✅ 全部處理完成！本次新增 {added_count} 個 QA，最終共 {min(final_qa_count, target_size)} 個有效問答對。\")\n", "    print(f\"⏳ 最後處理到的事件索引: {last_processed_event_index} (共 {len(events_data)} 個事件)\")\n", "    print(\"=\" * 50)\n", "\n", "\n", "# --- 維基百科函數 (返回 summary 和 text) ---\n", "def fetch_wikipedia_summary(name):\n", "    \"\"\"從維基百科獲取摘要和全文\"\"\"\n", "    try:\n", "        print(f\"⏳ 正在查詢 {name} 的維基百科資料...\")\n", "        user_agent = \"A111223013/1.0 (<EMAIL>)\"\n", "        wiki_wiki = wikipediaapi.Wikipedia(language='zh', user_agent=user_agent, timeout=30)\n", "        page = wiki_wiki.page(name)\n", "        if not page.exists(): print(f\"❌ 找不到 {name} 的維基百科頁面\"); return \"\", \"\"\n", "        print(f\"✅ 成功獲取 {name} 的維基百科資料\"); return page.summary, page.text\n", "    except Exception as e: print(f\"❌ 維基百科抓取錯誤: {e}\"); return \"\", \"\"\n", "\n", "\n", "# --- 主程式入口 ---\n", "if __name__ == \"__main__\":\n", "    if 'api_key' in globals() and api_key:\n", "         try:\n", "            genai.list_models()\n", "            print(\"🔑 Gemini API Key 配置成功。\")\n", "            main_incremental()\n", "         except Exception as auth_error:\n", "             print(f\"❌ Gemini API Key 配置失敗或無效: {auth_error}\")\n", "             print(\"請檢查您的 API Key 是否正確，或網路連線是否正常。\")\n", "    else:\n", "        print(\"❌ 未能配置 Gemini API Key。請在程式碼頂部設定 api_key 變數。\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 時間遞增版2版(輸入時間版)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔑 Gemini API Key 和 NewsAPI Key 已配置。\n", "==================================================\n", "🤖 政治人物 QA 知識庫生成器 (時間範圍過濾 + 多源整合 v2)\n", "==================================================\n", "\n", "--- 正在載入外部資料 ---\n", "🔍 找到最新的 YouTube 資料: yt_葉元之_20250405_data.json\n", "🔍 找到最新的 PTT 資料: ptt_葉元之_20250405.json\n", "⏳ 正在查詢 葉元之 的維基百科資料...\n", "✅ 成功獲取 葉元之 的維基百科資料\n", "⏳ 正在使用 Gemini 查詢 葉元之 的相關新聞...\n", "✅ 成功使用 Gemini 獲取 葉元之 的相關新聞\n", "⏳ 正在使用 NewsAPI.org 查詢 葉元之 的相關新聞...\n", "✅ 成功從 NewsAPI.org 獲取 43 條相關新聞\n", "⏳ 正在查詢 葉元之 的政黨資料...\n", "✅ 成功獲取 葉元之 的政黨資料\n", "--------------------\n", "\n", "🌱 未找到與目前時間範圍 (1974-12-28 - 2025-05-08) 相符的狀態，將從頭開始為 '葉元之' 生成 QA...\n", "\n", "--- 提取/更新事件列表 ---\n", "⏳ 正在從多源文本中提取 葉元之 在 1974-12-28 到 2025-05-08 期間的帶日期事件...\n", "✅ 成功提取 21 個在 1974-12-28 到 2025-05-08 期間的有效且帶日期的事件。\n", "🗓️ 已提取並排序 21 個事件。\n", "\n", "--- 開始生成 QA (最大深度: 3) ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (1974-12-28):   0%|          | 0/21 [00:00<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 1/21 (1974-12-28), 嘗試深度 0...\n", "  ⏳ 正在為事件 (1974-12-28) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (1974-12-28):   0%|          | 0/21 [00:03<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (1974-12-28) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 3)\n", "✅ 已儲存 3 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 1974-12-28\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (1974-12-28):   0%|          | 0/21 [00:05<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 1/21 (1974-12-28), 嘗試深度 1...\n", "  ⏳ 正在為事件 (1974-12-28) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (1974-12-28):   0%|          | 0/21 [00:10<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (1974-12-28) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 6)\n", "✅ 已儲存 6 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 1974-12-28\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (1974-12-28):   0%|          | 0/21 [00:12<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 1/21 (1974-12-28), 嘗試深度 2...\n", "  ⏳ 正在為事件 (1974-12-28) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (1974-12-28):   0%|          | 0/21 [00:16<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (1974-12-28) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 9)\n", "✅ 已儲存 9 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 1974-12-28\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (1974-12-28):   0%|          | 0/21 [00:19<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 1/21 (1974-12-28), 嘗試深度 3...\n", "  ⏳ 正在為事件 (1974-12-28) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (1974-12-28):   0%|          | 0/21 [00:24<?, ?事件/s]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (1974-12-28) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 12)\n", "✅ 已儲存 12 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 1974-12-28\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2010-01-01):   5%|▍         | 1/21 [00:26<08:50, 26.54s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 2/21 (2010-01-01), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2010-01-01) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2010-01-01):   5%|▍         | 1/21 [00:29<08:50, 26.54s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2010-01-01) 生成 2 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 2 QA (總數: 14)\n", "✅ 已儲存 14 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2010-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2010-01-01):   5%|▍         | 1/21 [00:32<08:50, 26.54s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 2/21 (2010-01-01), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2010-01-01) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2010-01-01):   5%|▍         | 1/21 [00:37<08:50, 26.54s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2010-01-01) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 17)\n", "✅ 已儲存 17 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2010-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2010-01-01):   5%|▍         | 1/21 [00:39<08:50, 26.54s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 2/21 (2010-01-01), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2010-01-01) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2010-01-01):   5%|▍         | 1/21 [00:43<08:50, 26.54s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2010-01-01) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 20)\n", "✅ 已儲存 20 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2010-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2010-01-01):   5%|▍         | 1/21 [00:46<08:50, 26.54s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 2/21 (2010-01-01), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2010-01-01) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2010-01-01):   5%|▍         | 1/21 [00:51<08:50, 26.54s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2010-01-01) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 23)\n", "✅ 已儲存 23 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2010-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2014-01-01):  10%|▉         | 2/21 [00:54<08:34, 27.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 3/21 (2014-01-01), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2014-01-01) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2014-01-01):  10%|▉         | 2/21 [00:57<08:34, 27.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2014-01-01) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 26)\n", "✅ 已儲存 26 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2014-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2014-01-01):  10%|▉         | 2/21 [00:58<08:34, 27.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 3/21 (2014-01-01), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2014-01-01) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2014-01-01):  10%|▉         | 2/21 [01:04<08:34, 27.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2014-01-01) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 29)\n", "✅ 已儲存 29 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2014-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2014-01-01):  10%|▉         | 2/21 [01:05<08:34, 27.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 3/21 (2014-01-01), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2014-01-01) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2014-01-01):  10%|▉         | 2/21 [01:10<08:34, 27.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2014-01-01) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 32)\n", "✅ 已儲存 32 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2014-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2014-01-01):  10%|▉         | 2/21 [01:11<08:34, 27.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 3/21 (2014-01-01), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2014-01-01) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2014-01-01):  10%|▉         | 2/21 [01:16<08:34, 27.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2014-01-01) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 35)\n", "✅ 已儲存 35 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2014-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2018-01-01):  14%|█▍        | 3/21 [01:18<07:49, 26.11s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 4/21 (2018-01-01), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2018-01-01) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2018-01-01):  14%|█▍        | 3/21 [01:22<07:49, 26.11s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2018-01-01) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 38)\n", "✅ 已儲存 38 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2018-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2018-01-01):  14%|█▍        | 3/21 [01:24<07:49, 26.11s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 4/21 (2018-01-01), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2018-01-01) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2018-01-01):  14%|█▍        | 3/21 [01:29<07:49, 26.11s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2018-01-01) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 41)\n", "✅ 已儲存 41 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2018-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2018-01-01):  14%|█▍        | 3/21 [01:30<07:49, 26.11s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 4/21 (2018-01-01), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2018-01-01) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2018-01-01):  14%|█▍        | 3/21 [01:34<07:49, 26.11s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2018-01-01) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 44)\n", "✅ 已儲存 44 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2018-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2018-01-01):  14%|█▍        | 3/21 [01:37<07:49, 26.11s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 4/21 (2018-01-01), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2018-01-01) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2018-01-01):  14%|█▍        | 3/21 [01:41<07:49, 26.11s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2018-01-01) 生成 2 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 2 QA (總數: 46)\n", "✅ 已儲存 46 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2018-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2019-01-01):  19%|█▉        | 4/21 [01:43<07:10, 25.35s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 5/21 (2019-01-01), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2019-01-01) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2019-01-01):  19%|█▉        | 4/21 [01:46<07:10, 25.35s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2019-01-01) 生成 2 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 2 QA (總數: 48)\n", "✅ 已儲存 48 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2019-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2019-01-01):  19%|█▉        | 4/21 [01:48<07:10, 25.35s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 5/21 (2019-01-01), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2019-01-01) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2019-01-01):  19%|█▉        | 4/21 [01:52<07:10, 25.35s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2019-01-01) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 51)\n", "✅ 已儲存 51 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2019-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2019-01-01):  19%|█▉        | 4/21 [01:55<07:10, 25.35s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 5/21 (2019-01-01), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2019-01-01) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2019-01-01):  19%|█▉        | 4/21 [02:00<07:10, 25.35s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2019-01-01) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 54)\n", "✅ 已儲存 54 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2019-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2019-01-01):  19%|█▉        | 4/21 [02:02<07:10, 25.35s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 5/21 (2019-01-01), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2019-01-01) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2019-01-01):  19%|█▉        | 4/21 [02:07<07:10, 25.35s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2019-01-01) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 57)\n", "✅ 已儲存 57 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2019-01-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2022-11-26):  24%|██▍       | 5/21 [02:10<06:56, 26.03s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 6/21 (2022-11-26), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2022-11-26) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2022-11-26):  24%|██▍       | 5/21 [02:14<06:56, 26.03s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2022-11-26) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 60)\n", "✅ 已儲存 60 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2022-11-26\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2022-11-26):  24%|██▍       | 5/21 [02:16<06:56, 26.03s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 6/21 (2022-11-26), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2022-11-26) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2022-11-26):  24%|██▍       | 5/21 [02:21<06:56, 26.03s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2022-11-26) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 63)\n", "✅ 已儲存 63 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2022-11-26\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2022-11-26):  24%|██▍       | 5/21 [02:23<06:56, 26.03s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 6/21 (2022-11-26), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2022-11-26) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2022-11-26):  24%|██▍       | 5/21 [02:28<06:56, 26.03s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2022-11-26) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 66)\n", "✅ 已儲存 66 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2022-11-26\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2022-11-26):  24%|██▍       | 5/21 [02:30<06:56, 26.03s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 6/21 (2022-11-26), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2022-11-26) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2022-11-26):  24%|██▍       | 5/21 [02:34<06:56, 26.03s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2022-11-26) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 69)\n", "✅ 已儲存 69 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2022-11-26\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-04-19):  29%|██▊       | 6/21 [02:36<06:33, 26.22s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 7/21 (2023-04-19), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-04-19) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-04-19):  29%|██▊       | 6/21 [02:41<06:33, 26.22s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-04-19) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 72)\n", "✅ 已儲存 72 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-04-19\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-04-19):  29%|██▊       | 6/21 [02:42<06:33, 26.22s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 7/21 (2023-04-19), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-04-19) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-04-19):  29%|██▊       | 6/21 [02:47<06:33, 26.22s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-04-19) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 75)\n", "✅ 已儲存 75 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-04-19\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-04-19):  29%|██▊       | 6/21 [02:49<06:33, 26.22s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 7/21 (2023-04-19), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-04-19) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-04-19):  29%|██▊       | 6/21 [02:54<06:33, 26.22s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-04-19) 生成 2 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 2 QA (總數: 77)\n", "✅ 已儲存 77 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-04-19\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-04-19):  29%|██▊       | 6/21 [02:56<06:33, 26.22s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 7/21 (2023-04-19), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-04-19) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-04-19):  29%|██▊       | 6/21 [03:01<06:33, 26.22s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-04-19) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 80)\n", "✅ 已儲存 80 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-04-19\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-05-03):  33%|███▎      | 7/21 [03:03<06:09, 26.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 8/21 (2023-05-03), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-05-03) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-05-03):  33%|███▎      | 7/21 [03:07<06:09, 26.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-05-03) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 83)\n", "✅ 已儲存 83 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-05-03\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-05-03):  33%|███▎      | 7/21 [03:09<06:09, 26.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 8/21 (2023-05-03), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-05-03) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-05-03):  33%|███▎      | 7/21 [03:14<06:09, 26.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-05-03) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 86)\n", "✅ 已儲存 86 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-05-03\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-05-03):  33%|███▎      | 7/21 [03:16<06:09, 26.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 8/21 (2023-05-03), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-05-03) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-05-03):  33%|███▎      | 7/21 [03:20<06:09, 26.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-05-03) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 89)\n", "✅ 已儲存 89 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-05-03\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-05-03):  33%|███▎      | 7/21 [03:22<06:09, 26.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 8/21 (2023-05-03), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-05-03) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-05-03):  33%|███▎      | 7/21 [03:27<06:09, 26.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-05-03) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 92)\n", "✅ 已儲存 92 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-05-03\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-17):  38%|███▊      | 8/21 [03:29<05:40, 26.16s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 9/21 (2023-11-17), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-11-17) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-17):  38%|███▊      | 8/21 [03:33<05:40, 26.16s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-17) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 95)\n", "✅ 已儲存 95 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-17):  38%|███▊      | 8/21 [03:35<05:40, 26.16s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 9/21 (2023-11-17), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-11-17) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-17):  38%|███▊      | 8/21 [03:39<05:40, 26.16s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-17) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 98)\n", "✅ 已儲存 98 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-17):  38%|███▊      | 8/21 [03:41<05:40, 26.16s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 9/21 (2023-11-17), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-11-17) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-17):  38%|███▊      | 8/21 [03:45<05:40, 26.16s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-17) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 101)\n", "✅ 已儲存 101 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-17):  38%|███▊      | 8/21 [03:47<05:40, 26.16s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 9/21 (2023-11-17), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-11-17) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-17):  38%|███▊      | 8/21 [03:51<05:40, 26.16s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-17) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 104)\n", "✅ 已儲存 104 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-18):  43%|████▎     | 9/21 [03:54<05:08, 25.73s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 10/21 (2023-11-18), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-11-18) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-18):  43%|████▎     | 9/21 [03:58<05:08, 25.73s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-18) 生成 1 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 1 QA (總數: 105)\n", "✅ 已儲存 105 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-18\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-18):  43%|████▎     | 9/21 [04:00<05:08, 25.73s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 10/21 (2023-11-18), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-11-18) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-18):  43%|████▎     | 9/21 [04:06<05:08, 25.73s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-18) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 108)\n", "✅ 已儲存 108 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-18\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-18):  43%|████▎     | 9/21 [04:08<05:08, 25.73s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 10/21 (2023-11-18), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-11-18) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-18):  43%|████▎     | 9/21 [04:13<05:08, 25.73s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-18) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 111)\n", "✅ 已儲存 111 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-18\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-18):  43%|████▎     | 9/21 [04:14<05:08, 25.73s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 10/21 (2023-11-18), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-11-18) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-18):  43%|████▎     | 9/21 [04:19<05:08, 25.73s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-18) 生成 2 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 2 QA (總數: 113)\n", "✅ 已儲存 113 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-18\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-24):  48%|████▊     | 10/21 [04:20<04:45, 25.95s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 11/21 (2023-11-24), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-11-24) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-24):  48%|████▊     | 10/21 [04:23<04:45, 25.95s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-24) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 116)\n", "✅ 已儲存 116 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-24):  48%|████▊     | 10/21 [04:25<04:45, 25.95s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 11/21 (2023-11-24), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-11-24) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-24):  48%|████▊     | 10/21 [04:30<04:45, 25.95s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-24) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 119)\n", "✅ 已儲存 119 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-24):  48%|████▊     | 10/21 [04:32<04:45, 25.95s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 11/21 (2023-11-24), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-11-24) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-24):  48%|████▊     | 10/21 [04:35<04:45, 25.95s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-24) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 122)\n", "✅ 已儲存 122 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-24):  48%|████▊     | 10/21 [04:37<04:45, 25.95s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 11/21 (2023-11-24), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-11-24) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-24):  48%|████▊     | 10/21 [04:42<04:45, 25.95s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-24) 生成 2 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 2 QA (總數: 124)\n", "✅ 已儲存 124 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-28):  52%|█████▏    | 11/21 [04:44<04:12, 25.25s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 12/21 (2023-11-28), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-11-28) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-28):  52%|█████▏    | 11/21 [04:48<04:12, 25.25s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-28) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 127)\n", "✅ 已儲存 127 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-28\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-28):  52%|█████▏    | 11/21 [04:49<04:12, 25.25s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 12/21 (2023-11-28), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-11-28) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-28):  52%|█████▏    | 11/21 [04:54<04:12, 25.25s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-28) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 130)\n", "✅ 已儲存 130 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-28\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-28):  52%|█████▏    | 11/21 [04:57<04:12, 25.25s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 12/21 (2023-11-28), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-11-28) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-28):  52%|█████▏    | 11/21 [05:02<04:12, 25.25s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-28) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 133)\n", "✅ 已儲存 133 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-28\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-28):  52%|█████▏    | 11/21 [05:03<04:12, 25.25s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 12/21 (2023-11-28), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-11-28) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-11-28):  52%|█████▏    | 11/21 [05:08<04:12, 25.25s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-11-28) 生成 2 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 2 QA (總數: 135)\n", "✅ 已儲存 135 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-11-28\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-12-01):  57%|█████▋    | 12/21 [05:10<03:48, 25.43s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 13/21 (2023-12-01), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2023-12-01) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-12-01):  57%|█████▋    | 12/21 [05:13<03:48, 25.43s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-12-01) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 138)\n", "✅ 已儲存 138 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-12-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-12-01):  57%|█████▋    | 12/21 [05:15<03:48, 25.43s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 13/21 (2023-12-01), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2023-12-01) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-12-01):  57%|█████▋    | 12/21 [05:20<03:48, 25.43s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-12-01) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 141)\n", "✅ 已儲存 141 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-12-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-12-01):  57%|█████▋    | 12/21 [05:22<03:48, 25.43s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 13/21 (2023-12-01), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2023-12-01) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-12-01):  57%|█████▋    | 12/21 [05:26<03:48, 25.43s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-12-01) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 144)\n", "✅ 已儲存 144 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-12-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-12-01):  57%|█████▋    | 12/21 [05:29<03:48, 25.43s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 13/21 (2023-12-01), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2023-12-01) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2023-12-01):  57%|█████▋    | 12/21 [05:33<03:48, 25.43s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2023-12-01) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 147)\n", "✅ 已儲存 147 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2023-12-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2024-01-13):  62%|██████▏   | 13/21 [05:35<03:23, 25.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 14/21 (2024-01-13), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2024-01-13) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2024-01-13):  62%|██████▏   | 13/21 [05:38<03:23, 25.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2024-01-13) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 150)\n", "✅ 已儲存 150 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2024-01-13\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2024-01-13):  62%|██████▏   | 13/21 [05:39<03:23, 25.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 14/21 (2024-01-13), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2024-01-13) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2024-01-13):  62%|██████▏   | 13/21 [05:44<03:23, 25.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2024-01-13) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 153)\n", "✅ 已儲存 153 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2024-01-13\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2024-01-13):  62%|██████▏   | 13/21 [05:46<03:23, 25.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 14/21 (2024-01-13), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2024-01-13) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2024-01-13):  62%|██████▏   | 13/21 [05:50<03:23, 25.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2024-01-13) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 156)\n", "✅ 已儲存 156 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2024-01-13\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2024-01-13):  62%|██████▏   | 13/21 [05:53<03:23, 25.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 14/21 (2024-01-13), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2024-01-13) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2024-01-13):  62%|██████▏   | 13/21 [05:57<03:23, 25.38s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2024-01-13) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 159)\n", "✅ 已儲存 159 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2024-01-13\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  67%|██████▋   | 14/21 [06:00<02:56, 25.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 15/21 (2025-03-01), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2025-03-01) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  67%|██████▋   | 14/21 [06:03<02:56, 25.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-01) 生成 2 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 2 QA (總數: 161)\n", "✅ 已儲存 161 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  67%|██████▋   | 14/21 [06:06<02:56, 25.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 15/21 (2025-03-01), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2025-03-01) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  67%|██████▋   | 14/21 [06:10<02:56, 25.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-01) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 164)\n", "✅ 已儲存 164 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  67%|██████▋   | 14/21 [06:12<02:56, 25.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 15/21 (2025-03-01), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2025-03-01) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  67%|██████▋   | 14/21 [06:16<02:56, 25.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-01) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 2 QA (總數: 166)\n", "✅ 已儲存 166 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  67%|██████▋   | 14/21 [06:19<02:56, 25.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 15/21 (2025-03-01), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2025-03-01) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  67%|██████▋   | 14/21 [06:23<02:56, 25.17s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-01) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 169)\n", "✅ 已儲存 169 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  71%|███████▏  | 15/21 [06:25<02:30, 25.14s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 16/21 (2025-03-01), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2025-03-01) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  71%|███████▏  | 15/21 [06:28<02:30, 25.14s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-01) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 172)\n", "✅ 已儲存 172 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  71%|███████▏  | 15/21 [06:31<02:30, 25.14s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 16/21 (2025-03-01), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2025-03-01) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  71%|███████▏  | 15/21 [06:35<02:30, 25.14s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-01) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 175)\n", "✅ 已儲存 175 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  71%|███████▏  | 15/21 [06:38<02:30, 25.14s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 16/21 (2025-03-01), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2025-03-01) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  71%|███████▏  | 15/21 [06:43<02:30, 25.14s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-01) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 178)\n", "✅ 已儲存 178 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  71%|███████▏  | 15/21 [06:45<02:30, 25.14s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 16/21 (2025-03-01), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2025-03-01) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-01):  71%|███████▏  | 15/21 [06:49<02:30, 25.14s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-01) 生成 2 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 2 QA (總數: 180)\n", "✅ 已儲存 180 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-10):  76%|███████▌  | 16/21 [06:51<02:08, 25.60s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 17/21 (2025-03-10), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2025-03-10) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-10):  76%|███████▌  | 16/21 [06:55<02:08, 25.60s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-10) 生成 2 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 1 QA (總數: 181)\n", "✅ 已儲存 181 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-10):  76%|███████▌  | 16/21 [06:57<02:08, 25.60s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 17/21 (2025-03-10), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2025-03-10) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-10):  76%|███████▌  | 16/21 [07:01<02:08, 25.60s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-10) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 184)\n", "✅ 已儲存 184 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-10):  76%|███████▌  | 16/21 [07:02<02:08, 25.60s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 17/21 (2025-03-10), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2025-03-10) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-10):  76%|███████▌  | 16/21 [07:07<02:08, 25.60s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-10) 生成 2 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 2 QA (總數: 186)\n", "✅ 已儲存 186 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-10):  76%|███████▌  | 16/21 [07:09<02:08, 25.60s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 17/21 (2025-03-10), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2025-03-10) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-10):  76%|███████▌  | 16/21 [07:13<02:08, 25.60s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-10) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 189)\n", "✅ 已儲存 189 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-15):  81%|████████  | 17/21 [07:15<01:40, 25.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 18/21 (2025-03-15), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2025-03-15) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-15):  81%|████████  | 17/21 [07:19<01:40, 25.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-15) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 192)\n", "✅ 已儲存 192 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-15):  81%|████████  | 17/21 [07:20<01:40, 25.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 18/21 (2025-03-15), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2025-03-15) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-15):  81%|████████  | 17/21 [07:25<01:40, 25.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-15) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 195)\n", "✅ 已儲存 195 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-15):  81%|████████  | 17/21 [07:27<01:40, 25.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 18/21 (2025-03-15), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2025-03-15) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-15):  81%|████████  | 17/21 [07:32<01:40, 25.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-15) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 198)\n", "✅ 已儲存 198 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-15):  81%|████████  | 17/21 [07:34<01:40, 25.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 18/21 (2025-03-15), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2025-03-15) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-03-15):  81%|████████  | 17/21 [07:39<01:40, 25.09s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-03-15) 生成 2 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 2 QA (總數: 200)\n", "✅ 已儲存 200 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-03-15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-04-24):  86%|████████▌ | 18/21 [07:42<01:16, 25.46s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 19/21 (2025-04-24), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2025-04-24) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-04-24):  86%|████████▌ | 18/21 [07:45<01:16, 25.46s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-04-24) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 203)\n", "✅ 已儲存 203 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-04-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-04-24):  86%|████████▌ | 18/21 [07:47<01:16, 25.46s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 19/21 (2025-04-24), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2025-04-24) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-04-24):  86%|████████▌ | 18/21 [07:52<01:16, 25.46s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-04-24) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 206)\n", "✅ 已儲存 206 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-04-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-04-24):  86%|████████▌ | 18/21 [07:55<01:16, 25.46s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 19/21 (2025-04-24), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2025-04-24) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-04-24):  86%|████████▌ | 18/21 [08:00<01:16, 25.46s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-04-24) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 2 QA (總數: 208)\n", "✅ 已儲存 208 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-04-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-04-24):  86%|████████▌ | 18/21 [08:03<01:16, 25.46s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 19/21 (2025-04-24), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2025-04-24) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-04-24):  86%|████████▌ | 18/21 [08:07<01:16, 25.46s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-04-24) 生成 2 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 2 QA (總數: 210)\n", "✅ 已儲存 210 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-04-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-04):  90%|█████████ | 19/21 [08:08<00:51, 25.79s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 20/21 (2025-05-04), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2025-05-04) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-04):  90%|█████████ | 19/21 [08:11<00:51, 25.79s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-05-04) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 213)\n", "✅ 已儲存 213 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-05-04\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-04):  90%|█████████ | 19/21 [08:13<00:51, 25.79s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 20/21 (2025-05-04), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2025-05-04) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-04):  90%|█████████ | 19/21 [08:18<00:51, 25.79s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-05-04) 生成 3 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 3 QA (總數: 216)\n", "✅ 已儲存 216 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-05-04\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-04):  90%|█████████ | 19/21 [08:21<00:51, 25.79s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 20/21 (2025-05-04), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2025-05-04) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-04):  90%|█████████ | 19/21 [08:25<00:51, 25.79s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-05-04) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 219)\n", "✅ 已儲存 219 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-05-04\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-04):  90%|█████████ | 19/21 [08:28<00:51, 25.79s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 20/21 (2025-05-04), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2025-05-04) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-04):  90%|█████████ | 19/21 [08:33<00:51, 25.79s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-05-04) 生成 3 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 3 QA (總數: 222)\n", "✅ 已儲存 222 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-05-04\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-05):  95%|█████████▌| 20/21 [08:35<00:26, 26.28s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 21/21 (2025-05-05), 嘗試深度 0...\n", "  ⏳ 正在為事件 (2025-05-05) 生成 QA (深度: 0, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-05):  95%|█████████▌| 20/21 [08:39<00:26, 26.28s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-05-05) 生成 3 個有效 QA (深度 0)。\n", "    ✨ (深度 0) 新增 3 QA (總數: 225)\n", "✅ 已儲存 225 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-05-05\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-05):  95%|█████████▌| 20/21 [08:41<00:26, 26.28s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 21/21 (2025-05-05), 嘗試深度 1...\n", "  ⏳ 正在為事件 (2025-05-05) 生成 QA (深度: 1, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-05):  95%|█████████▌| 20/21 [08:46<00:26, 26.28s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-05-05) 生成 1 個有效 QA (深度 1)。\n", "    ✨ (深度 1) 新增 1 QA (總數: 226)\n", "✅ 已儲存 226 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-05-05\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-05):  95%|█████████▌| 20/21 [08:47<00:26, 26.28s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 21/21 (2025-05-05), 嘗試深度 2...\n", "  ⏳ 正在為事件 (2025-05-05) 生成 QA (深度: 2, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-05):  95%|█████████▌| 20/21 [08:52<00:26, 26.28s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-05-05) 生成 3 個有效 QA (深度 2)。\n", "    ✨ (深度 2) 新增 3 QA (總數: 229)\n", "✅ 已儲存 229 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-05-05\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-05):  95%|█████████▌| 20/21 [08:54<00:26, 26.28s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  -> 事件 21/21 (2025-05-05), 嘗試深度 3...\n", "  ⏳ 正在為事件 (2025-05-05) 生成 QA (深度: 3, 含多源資料)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-05):  95%|█████████▌| 20/21 [08:59<00:26, 26.28s/事件]      "]}, {"name": "stdout", "output_type": "stream", "text": ["  ✅ 為事件 (2025-05-05) 生成 2 個有效 QA (深度 3)。\n", "    ✨ (深度 3) 新增 2 QA (總數: 231)\n", "✅ 已儲存 231 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-05-05\n"]}, {"name": "stderr", "output_type": "stream", "text": ["處理事件 (2025-05-05): 100%|██████████| 21/21 [09:01<00:00, 25.80s/事件]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- 保存最終結果 ---\n", "✅ 已儲存 231 個 QA 到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData.json\n", "✅ 已儲存狀態到 ./inputTime_qa_data\\葉元之_1974-12-28_2025-05-08_qaData_state.json, 時間範圍: 1974-12-28 - 2025-05-08, 上次處理日期: 2025-05-05\n", "  📊 最終 QA 數量: 231 (本次運行新增 231)\n", "\n", "==================================================\n", "✅ 全部處理完成！本次運行新增 231 個 QA，最終共 231 個有效問答對。\n", "⏳ 最後處理到的事件日期: 2025-05-05\n", "==================================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# -*- coding: utf-8 -*-\n", "import wikipediaapi\n", "import google.generativeai as genai\n", "import json\n", "import os\n", "import re\n", "import time\n", "from datetime import datetime, timedelta\n", "import random # For sampling\n", "from tqdm import tqdm # Import tqdm\n", "import requests\n", "\n", "# --- Gemini API 設定 ---\n", "model_id = \"gemini-1.5-flash-latest\" # 建議使用最新的 flash 模型\n", "generation_config = {\n", "    \"response_modalities\": [\"TEXT\"],\n", "    # \"temperature\": 0.7, # 可以根據需要調整\n", "}\n", "# !!! 請務必注意安全，不要將 API Key 直接硬編碼，建議使用環境變數等更安全的方式 !!!\n", "# 例如: api_key = os.environ.get(\"GEMINI_API_KEY\")\n", "api_key = \"AIzaSyCwwVlv5VeCkyI1RL9mKvWSZHUKn6WlpIU\" # 請替換成您的 API Key 或使用環境變數\n", "NEWSAPI_KEY = \"********************************\" # 請替換成您的 NewsAPI Key 或使用環境變數\n", "\n", "if api_key:\n", "    genai.configure(api_key=api_key)\n", "else:\n", "    print(\"❌ 錯誤：未設定 Gemini API Key。請在程式碼中設定或使用環境變數。\")\n", "    exit()\n", "\n", "# --- 輔助函數 ---\n", "def try_parse_json(text):\n", "    \"\"\"嘗試解析 JSON 內容，處理非標準格式\"\"\"\n", "    if not text or not isinstance(text, str):\n", "        print(\"❌ try_parse_json: 輸入的文本為空或非字串。\")\n", "        return None\n", "    try:\n", "        # 移除常見的 Markdown JSON 區塊標記\n", "        text = re.sub(r'^```json\\s*|\\s*```$', '', text.strip())\n", "        # 嘗試修復一些常見的轉義問題 (謹慎使用，可能影響合法轉義)\n", "        # text = re.sub(r'\\\\\\'', '\\'', text) # 替換 \\' 為 ' (如果確定這是問題來源)\n", "        # text = re.sub(r'\\\\\\\"', '\\\"', text) # 替換 \\\" 為 \" (如果確定這是問題來源)\n", "        # text = re.sub(r'\\\\n', '\\n', text) # 將字串中的 \\n 轉為實際換行符，JSON 解析器應能處理\n", "        return json.loads(text)\n", "    except json.JSONDecodeError as e:\n", "        print(f\"❌ 無法解析 JSON: {e}\")\n", "        print(f\"原始文本片段 (前500字元): {text[:500]}...\")\n", "        return None\n", "\n", "def filter_invalid_questions(qa_list):\n", "    \"\"\"移除沒有明確答案或無效的題目\"\"\"\n", "    if not qa_list: return []\n", "    invalid_keywords = [\n", "        \"需查找外部資料\", \"需要參考外部資料\", \"需要參考\", \"無法回答\", \"未提供\", \"資料不足\",\n", "        \"無相關資訊\", \"根據目前的資訊無法得知\", \"資訊不足無法回答\", \"無法從文本中確定\",\n", "        \"文中未提及\", \"根據目前資訊無法詳細回答\", \"根據提供的資訊，無法\", \"根據文本內容無法\"\n", "    ]\n", "    filtered_qa = []\n", "    for qa in qa_list:\n", "        if not isinstance(qa, dict) or \"question\" not in qa or \"answer\" not in qa:\n", "            # print(f\"⚠️ 過濾掉格式不符的 QA: {qa}\") # 可選的調試訊息\n", "            continue\n", "        answer_text = str(qa.get(\"answer\", \"\")).strip()\n", "        question_text = str(qa.get(\"question\", \"\")).strip()\n", "\n", "        if not answer_text or not question_text: # 問題或答案不能為空\n", "            # print(f\"⚠️ 過濾掉空問題/答案的 QA: {qa}\") # 可選的調試訊息\n", "            continue\n", "\n", "        if any(keyword in answer_text for keyword in invalid_keywords):\n", "            # print(f\"⚠️ 過濾掉含無效關鍵字的 QA: {qa}\") # 可選的調試訊息\n", "            continue\n", "        filtered_qa.append(qa)\n", "    return filtered_qa\n", "\n", "def parse_event_date(date_str):\n", "    \"\"\"嘗試將不同格式的日期字串解析為 datetime 物件\"\"\"\n", "    if not isinstance(date_str, str): return None\n", "    date_str = date_str.strip()\n", "    # 增加對 YYYYMMDD 格式的支援\n", "    formats = [\"%Y-%m-%d\", \"%Y/%m/%d\", \"%Y年%m月%d日\", \"%Y%m%d\",\n", "               \"%Y-%m\", \"%Y/%m\", \"%Y年%m月\",\n", "               \"%Y\", \"%Y年\"]\n", "    for fmt in formats:\n", "        try:\n", "            dt = datetime.strptime(date_str, fmt)\n", "            # 如果只有年或年月，補齊到該月/年的第一天\n", "            if fmt in [\"%Y-%m\", \"%Y/%m\", \"%Y年%m月\"]: return dt.replace(day=1)\n", "            if fmt in [\"%Y\", \"%Y年\"]: return dt.replace(month=1, day=1)\n", "            return dt\n", "        except ValueError:\n", "            continue\n", "    # print(f\"⚠️ 無法解析日期字串: {date_str}\") # 可選的調試訊息\n", "    return None\n", "\n", "def find_latest_files_in_dir(directory, pattern):\n", "    \"\"\"在目錄中尋找符合模式且日期最新的檔案\"\"\"\n", "    latest_file = None\n", "    latest_date = datetime.min # 初始化為可能的最小日期\n", "    # print(f\"  檢查目錄: {directory}\")\n", "    if not os.path.exists(directory):\n", "        print(f\"⚠️ 目錄不存在: {directory}\")\n", "        return None\n", "\n", "    try:\n", "        filenames = os.listdir(directory)\n", "        # print(f\"  找到檔案: {filenames}\")\n", "    except Exception as e:\n", "        print(f\"❌ 無法列出目錄 {directory} 中的檔案: {e}\")\n", "        return None\n", "\n", "    for filename in filenames:\n", "        match = pattern.match(filename)\n", "        if match:\n", "            # print(f\"    ✅ 檔案 '{filename}' 符合樣式 '{pattern.pattern}'\")\n", "            try:\n", "                # 假設日期在第一個捕獲組\n", "                if pattern.groups >= 1:\n", "                    date_str = match.group(1) # 通常日期部分在第一個 group\n", "                    if len(date_str) == 8 and date_str.isdigit(): # YYYYMMDD\n", "                        file_date = datetime.strptime(date_str, \"%Y%m%d\")\n", "                        if file_date > latest_date:\n", "                            # print(f\"      找到更新的日期: {file_date.strftime('%Y-%m-%d')} (舊: {latest_date.strftime('%Y-%m-%d') if latest_date != datetime.min else '無'})\")\n", "                            latest_date = file_date\n", "                            latest_file = os.path.join(directory, filename)\n", "                    # 可以加入對其他日期格式的處理 (如果檔名中的日期格式多樣)\n", "            except (ValueError, IndexError) as e:\n", "                print(f\"      ❓ 無法從檔名 '{filename}' 提取日期或格式錯誤: {e}\")\n", "                continue\n", "    # if latest_file:\n", "    #     print(f\"  最終找到最新檔案: {os.path.basename(latest_file)}\")\n", "    # else:\n", "    #     print(f\"  在目錄 {directory} 中未找到符合樣式且日期有效的檔案。\")\n", "    return latest_file\n", "\n", "def find_latest_social_media_files(person_name, base_folder='crawler'):\n", "    \"\"\"查找最新的 YouTube 和 PTT 資料檔案\"\"\"\n", "    youtube_file = None\n", "    ptt_file = None\n", "\n", "    # YouTube\n", "    yt_dir = os.path.join(base_folder, 'youtube_data')\n", "    yt_pattern = re.compile(rf'yt_{re.escape(person_name)}_(\\d{{8}})_data\\.json')\n", "    # print(f\"\\n查找 YouTube 資料 (樣式: {yt_pattern.pattern}, 目錄: {yt_dir})...\")\n", "    # print(f\"person_name 的值: '{person_name}'\")\n", "    # if os.path.exists(yt_dir): print(f\"YouTube 目錄存在，內容: {os.listdir(yt_dir)}\")\n", "    youtube_file = find_latest_files_in_dir(yt_dir, yt_pattern)\n", "    if youtube_file: print(f\"🔍 找到最新的 YouTube 資料: {os.path.basename(youtube_file)}\")\n", "    else: print(f\"⚠️ 在 {yt_dir} 中找不到符合模式 'yt_{person_name}_YYYYMMDD_data.json' 的 YouTube 資料檔案\")\n", "\n", "    # PTT\n", "    ptt_dir = os.path.join(base_folder, 'pttdata')\n", "    ptt_pattern = re.compile(rf'ptt_{re.escape(person_name)}_(\\d{{8}})\\.json')\n", "    # print(f\"\\n查找 PTT 資料 (樣式: {ptt_pattern.pattern}, 目錄: {ptt_dir})...\")\n", "    # if os.path.exists(ptt_dir): print(f\"PTT 目錄存在，內容: {os.listdir(ptt_dir)}\")\n", "    ptt_file = find_latest_files_in_dir(ptt_dir, ptt_pattern)\n", "    if ptt_file: print(f\"🔍 找到最新的 PTT 資料: {os.path.basename(ptt_file)}\")\n", "    else: print(f\"⚠️ 在 {ptt_dir} 中找不到符合模式 'ptt_{person_name}_YYYYMMDD.json' 的 PTT 資料檔案\")\n", "\n", "    return youtube_file, ptt_file\n", "\n", "def load_and_sample_social_data(filepath, sample_size=300, content_keys=['content', 'comment','留言內容','留言','影片標題','時間','影片發布時間']):\n", "    \"\"\"載入 JSON 檔案並抽樣內容 (嘗試多個可能的 key)\"\"\"\n", "    if not filepath or not os.path.exists(filepath): return \"無資料\"\n", "    try:\n", "        with open(filepath, \"r\", encoding=\"utf-8\") as f: data = json.load(f)\n", "        if not isinstance(data, list):\n", "            if isinstance(data, dict): # 嘗試從字典中找到列表\n", "                list_key = next((k for k, v in data.items() if isinstance(v, list)), None)\n", "                if list_key: data = data[list_key]\n", "                else: print(f\"⚠️ 檔案 {os.path.basename(filepath)} 非列表且無列表鍵。\"); return \"資料格式錯誤\"\n", "            else: print(f\"⚠️ 檔案 {os.path.basename(filepath)} 非列表格式。\"); return \"資料格式錯誤\"\n", "        if not data: return \"無內容\"\n", "        # 確保抽樣不會超過列表長度\n", "        sampled_items = random.sample(data, min(len(data), sample_size))\n", "        content_list = []\n", "        for item in sampled_items:\n", "            if isinstance(item, dict):\n", "                for key in content_keys:\n", "                    if key in item and item[key]: # 確保 item[key] 不是 None 或空字串\n", "                        content_list.append(str(item[key]).strip().replace(\"\\n\", \" \").replace(\"\\r\", \" \")); break\n", "        return \"；\".join(content_list) if content_list else \"抽樣後無有效內容\"\n", "    except json.JSONDecodeError: print(f\"❌ 無法解析 JSON: {filepath}\"); return \"JSON 解析錯誤\"\n", "    except Exception as e: print(f\"❌ 讀取處理檔案錯誤 {filepath}: {e}\"); return \"讀取錯誤\"\n", "\n", "def fetch_related_news(name):\n", "    \"\"\"讓 AI 查詢最新新聞\"\"\"\n", "    try:\n", "        print(f\"⏳ 正在使用 Gemini 查詢 {name} 的相關新聞...\")\n", "        prompt = f\"請幫我查詢「{name}」的最新相關新聞，特別是與他的新聞、爭議、事件、政治活動有關的所有內容。請用條列式列出至少30條新聞(每條約 30-50 字)並附上時間日期 (格式 YYYY-MM-DD)。\"\n", "        model = genai.GenerativeModel(model_id)\n", "        # 增加 timeout 時間\n", "        response = model.generate_content(prompt, request_options=genai.types.RequestOptions(timeout=120))\n", "        print(f\"✅ 成功使用 Gemini 獲取 {name} 的相關新聞\")\n", "        return response.text\n", "    except Exception as e:\n", "        print(f\"❌ 使用 Gemini 查詢相關新聞時發生錯誤: {e}\"); return f\"查詢相關新聞時發生錯誤: {str(e)}\"\n", "\n", "def fetch_related_news_from_newsapi(name):\n", "    \"\"\"使用 NewsAPI.org 查詢最新新聞\"\"\"\n", "    if not NEWSAPI_KEY or NEWSAPI_KEY == \"YOUR_NEWSAPI_KEY_HERE\":\n", "        print(\"⚠️ NewsAPI 金鑰未設定或無效，跳過 NewsAPI 查詢。\")\n", "        return \"NewsAPI 金鑰未設定\"\n", "    try:\n", "        print(f\"⏳ 正在使用 NewsAPI.org 查詢 {name} 的相關新聞...\")\n", "        # 搜尋標題中包含 name 的新聞，按發布日期排序，中文新聞\n", "        url = f\"https://newsapi.org/v2/everything?qInTitle={name}&sortBy=publishedAt&apiKey={NEWSAPI_KEY}&language=zh&pageSize=50\" # 增加 pageSize\n", "        response = requests.get(url, timeout=30) # 增加 timeout\n", "        response.raise_for_status() # 檢查請求是否成功\n", "        data = response.json()\n", "        articles = data.get(\"articles\", [])\n", "        news_items = []\n", "        for article in articles: # 可以限制數量，例如 articles[:50]\n", "            title = article.get(\"title\")\n", "            # description = article.get(\"description\") # 描述可選\n", "            published_at = article.get(\"publishedAt\")\n", "            source_name = article.get(\"source\", {}).get(\"name\")\n", "            if title and published_at:\n", "                try:\n", "                    date_obj = datetime.fromisoformat(published_at.replace(\"Z\", \"+00:00\"))\n", "                    formatted_date = date_obj.strftime(\"%Y-%m-%d\")\n", "                    news_items.append(f\"- {formatted_date}: {title} (來源: {source_name})\")\n", "                except ValueError:\n", "                    news_items.append(f\"- {published_at.split('T')[0]}: {title} (來源: {source_name})\") # 備用日期格式\n", "\n", "        print(f\"✅ 成功從 NewsAPI.org 獲取 {len(news_items)} 條相關新聞\")\n", "        return \"\\n\".join(news_items) if news_items else \"NewsAPI.org 未找到相關新聞\"\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"❌ NewsAPI.org 查詢錯誤: {e}\")\n", "        return f\"NewsAPI.org 查詢錯誤: {str(e)}\"\n", "    except json.JSONDecodeError as e:\n", "        print(f\"❌ NewsAPI.org 回應解析錯誤: {e}\")\n", "        return f\"NewsAPI.org 回應解析錯誤: {str(e)}\"\n", "\n", "def fetch_party_information(name):\n", "    \"\"\"查詢指定人物的政黨資料及相關背景資訊\"\"\"\n", "    try:\n", "        print(f\"⏳ 正在查詢 {name} 的政黨資料...\")\n", "        prompt = f\"請幫我查詢「{name}」的政黨相關資訊，包含：\\n- 「{name}」目前所屬政黨的正式名稱。\\n- 該政黨的核心立場或主要政見摘要（約 50-100 字）。\\n- 該政黨近期（過去一年內）涉及的重大新聞事件或爭議摘要（約 200-300 字）。\"\n", "        model = genai.GenerativeModel(model_id)\n", "        response = model.generate_content(prompt, request_options=genai.types.RequestOptions(timeout=120))\n", "        print(f\"✅ 成功獲取 {name} 的政黨資料\")\n", "        return response.text\n", "    except Exception as e:\n", "        print(f\"❌ 查詢政黨資料時發生錯誤: {e}\"); return f\"查詢政黨資料時發生錯誤: {str(e)}\"\n", "\n", "def extract_dated_events_from_text(name, full_text, related_news=None, newsapi_news=None, party_info=None, ptt_summary=None, youtube_summary=None, start_date=None, end_date=None):\n", "    \"\"\"使用 Gemini 從多源文本中提取帶有日期的事件，並根據時間範圍過濾\"\"\"\n", "    start_date_str_display = start_date.strftime('%Y-%m-%d') if start_date else '最早'\n", "    end_date_str_display = end_date.strftime('%Y-%m-%d') if end_date else '最晚'\n", "    print(f\"⏳ 正在從多源文本中提取 {name} 在 {start_date_str_display} 到 {end_date_str_display} 期間的帶日期事件...\")\n", "\n", "    context_blocks = \"\"\n", "    # 定義哪些摘要是無效的，不應加入 context\n", "    valid_summary_states = [\"無資料\", \"資料格式錯誤\", \"無內容\", \"抽樣後無有效內容\", \"JSON 解析錯誤\", \"讀取錯誤\", \"NewsAPI 金鑰未設定\", \"NewsAPI.org 未找到相關新聞\"]\n", "\n", "    if related_news and not related_news.startswith(\"查詢相關新聞時發生錯誤\"):\n", "        context_blocks += f\"\\n\\n--- 最新相關新聞摘要 (<PERSON>) ---\\n{related_news}\\n--- 結束 Gemini 新聞摘要 ---\\n\"\n", "    if newsapi_news and not newsapi_news.startswith(\"NewsAPI.org 查詢錯誤\") and newsapi_news not in valid_summary_states:\n", "        context_blocks += f\"\\n\\n--- NewsAPI.org 相關新聞摘要 ---\\n{newsapi_news}\\n--- 結束 NewsAPI 摘要 ---\\n\"\n", "    if party_info and not party_info.startswith(\"查詢政黨資料時發生錯誤\"):\n", "        context_blocks += f\"\\n\\n--- 政黨相關資訊摘要 ---\\n{party_info}\\n--- 結束政黨資訊 ---\\n\"\n", "    if ptt_summary and ptt_summary not in valid_summary_states:\n", "        context_blocks += f\"\\n\\n--- PTT 討論摘要 (抽樣，最多2000字) ---\\n{ptt_summary[:2000]}\\n--- 結束 PTT 摘要 ---\\n\"\n", "    if youtube_summary and youtube_summary not in valid_summary_states:\n", "        context_blocks += f\"\\n\\n--- YouTube 影片/留言摘要 (抽樣，最多2000字) ---\\n{youtube_summary[:2000]}\\n--- 結束 YouTube 摘要 ---\\n\"\n", "\n", "    prompt = f\"\"\"請仔細閱讀以下關於「{name}」的資料，並結合你最新的網路搜尋結果以及提供的相關新聞、政黨資訊、社群媒體討論摘要：\n", "\n", "--- 維基百科資料 ---\n", "{full_text if full_text else \"未提供維基百科文本。\"}\n", "--- 結束維基百科文本 ---\n", "{context_blocks if context_blocks else \"無額外上下文資訊。\"}\n", "\n", "請根據以上所有資訊來源，提取在 **{start_date_str_display}** 到 **{end_date_str_display}** 期間發生的所有**具體**事件、行動、發言、職位變動、選舉參與、重要爭議、成就等，並包含它們發生的**日期**。\n", "\n", "請將結果格式化為 JSON 列表，每個物件包含以下兩個鍵：\n", "1. \"date\": 事件發生的日期，請盡力格式化為 \"YYYY-MM-DD\"。如果只有年份或月份，請格式化為 \"YYYY-MM-01\" 或 \"YYYY-01-01\"。如果日期非常模糊或無法確定（例如，僅說\"近期\"、\"某次會議\"），請省略該事件。**只包含有明確或可推斷出至少到 年份 的事件。**\n", "2. \"event_description\": 對事件的簡潔描述（約 50-150 字）。如果可能，請簡述資訊來源 (例如：來自維基百科、相關新聞、政黨資訊、PTT、YouTube)。\n", "\n", "請確保提取的資訊準確且來自提供的來源。不要包含重複或過於籠統的描述（例如 \"他參加了很多活動\"）。優先提取有明確日期的事件。\n", "\n", "輸出範例：\n", "[\n", "    {{\"date\": \"2024-04-01\", \"event_description\": \"根據維基百科，他在此日就任新職位。\"}},\n", "    {{\"date\": \"2024-04-02\", \"event_description\": \"相關新聞報導，他對某法案發表看法。\"}},\n", "    {{\"date\": \"2024-04-03\", \"event_description\": \"PTT 網友提及他在[日期]的[某事]...\"}}\n", "]\n", "\n", "請直接輸出 JSON 列表，不要包含其他文字或解釋。\"\"\"\n", "\n", "    try:\n", "        model = genai.GenerativeModel(model_id)\n", "        request_options = genai.types.RequestOptions(timeout=180) # 增加超時\n", "        response = model.generate_content(prompt, generation_config=generation_config, request_options=request_options)\n", "        extracted_text = response.text\n", "        # print(\"Gemini 模型用於提取事件的完整原始輸出:\") # 調試時可取消註解\n", "        # print(extracted_text)\n", "\n", "        events_list = try_parse_json(extracted_text) # 使用 try_parse_json\n", "\n", "        if events_list is None or not isinstance(events_list, list):\n", "            print(\"❌ 提取事件失敗，未返回有效 JSON 列表。\")\n", "            return []\n", "\n", "        valid_events, seen_dates_desc = [], set()\n", "        for item in events_list:\n", "            if isinstance(item, dict) and \"date\" in item and \"event_description\" in item:\n", "                parsed_date = parse_event_date(item.get(\"date\"))\n", "                desc = item.get(\"event_description\",\"\").strip()\n", "                if parsed_date and desc:\n", "                    # --- 時間範圍過濾 ---\n", "                    if start_date and parsed_date.date() < start_date.date(): continue\n", "                    if end_date and parsed_date.date() > end_date.date(): continue\n", "\n", "                    # --- 去重邏輯 (基於日期和描述前30字) ---\n", "                    key = (parsed_date.strftime(\"%Y-%m-%d\"), desc[:30])\n", "                    if key not in seen_dates_desc:\n", "                        item['parsed_date'] = parsed_date # 保存解析後的 datetime 物件\n", "                        valid_events.append(item)\n", "                        seen_dates_desc.add(key)\n", "        print(f\"✅ 成功提取 {len(valid_events)} 個在 {start_date_str_display} 到 {end_date_str_display} 期間的有效且帶日期的事件。\")\n", "        return valid_events\n", "    except Exception as e:\n", "        print(f\"❌ 提取事件時發生 API 或其他錯誤: {e}\")\n", "        return []\n", "\n", "def generate_event_qa(name, event_description, event_date_str, processing_level=0, existing_questions=None, related_news=None, party_info=None, ptt_summary=None, youtube_summary=None):\n", "    \"\"\"為單一事件生成 QA 問答 (結合新聞、政黨、社群摘要)\"\"\"\n", "    if existing_questions is None: existing_questions = set()\n", "\n", "    search_instruction = \"如果提供的事件描述不足以回答以下問題，請利用你的網路搜尋能力來補充資訊。\"\n", "    context_blocks, social_questions = \"\", \"\"\n", "    valid_summary_states = [\"無資料\", \"資料格式錯誤\", \"無內容\", \"抽樣後無有效內容\", \"JSON 解析錯誤\", \"讀取錯誤\", \"NewsAPI 金鑰未設定\", \"NewsAPI.org 未找到相關新聞\"]\n", "\n", "    if related_news and not related_news.startswith(\"查詢相關新聞時發生錯誤\"):\n", "        context_blocks += f\"\\n--- 最新相關新聞摘要 (<PERSON>) ---\\n{related_news}\\n--- 結束 Gemini 新聞摘要 ---\\n\"\n", "    if party_info and not party_info.startswith(\"查詢政黨資料時發生錯誤\"):\n", "        context_blocks += f\"\\n--- 政黨相關資訊摘要 ---\\n{party_info}\\n--- 結束政黨資訊 ---\\n\"\n", "    if ptt_summary and ptt_summary not in valid_summary_states:\n", "        context_blocks += f\"\\n--- PTT 討論摘要 (抽樣，最多1000字) ---\\n{ptt_summary[:1000]}\\n--- 結束 PTT 摘要 ---\\n\"\n", "        social_questions += \"- PTT 網友對此事件的主要反應或評論是什麼？\\n\"\n", "    if youtube_summary and youtube_summary not in valid_summary_states:\n", "        context_blocks += f\"\\n--- YouTube 留言摘要 (抽樣，最多1000字) ---\\n{youtube_summary[:1000]}\\n--- 結束 YouTube 摘要 ---\\n\"\n", "        social_questions += \"- YouTube 網友對此事件的主要看法是什麼？\\n\"\n", "\n", "    if social_questions:\n", "        social_questions = \"\\n請考慮回答以下基於社群媒體摘要的問題：\\n\" + social_questions\n", "\n", "    print(f\"  ⏳ 正在為事件 ({event_date_str}) 生成 QA (深度: {processing_level}, 含多源資料)...\")\n", "\n", "    prompt_base = f\"\"\"已知事件：\\n人物：{name}\\n日期：{event_date_str}\\n事件描述：{event_description}\\n\\n額外參考資訊：{context_blocks if context_blocks else \"無額外參考資訊。\"}\\n\\n{search_instruction}\\n\\n請針對**這個特定事件**，並結合以上所有資訊，生成 2-3 個相關的問答對（QA）。\\n問題應該聚焦於事件的細節、原因、影響、相關人物、{name} 在事件中的角色或立場等。\\n{social_questions}\"\"\"\n", "    prompt_level_0 = prompt_base + f\"\\n請生成關於事件基本事實的問答。例如：事件發生了什麼？時間地點？涉及哪些人？{name} 做了什麼？請主要依賴「事件描述」，必要時參考額外資訊、網路搜尋確認細節。\"\n", "    prompt_level_1 = prompt_base + f\"\\n請生成更深入的問答，探討事件的：\\n- 背景或原因是什麼（可參考新聞、政黨資訊）？\\n- 造成的直接或間接影響有哪些（包含新聞報導、社群反應）？\\n- 引發了哪些後續反應或爭議（可參考新聞、社群）？\\n- {name} 的動機或目的是什麼？\\n- 除了 {name}，還有哪些關鍵人物參與其中，他們的角色是什麼？\\n- 有沒有不同的觀點或對此事件的批評（包含來自社群媒體的）？\"\n", "    prompt_level_2 = prompt_base + f\"\\n請生成更深入的問答，探討事件的：\\n- 此事件對 {name} 所屬政黨的影響是什麼（參考政黨資訊、新聞）?\\n- 民眾如何看待這次事件（參考社群摘要、新聞）?\\n- 這次事件對 {name} 的民意支持度/聲望有什麼影響（參考社群摘要、新聞）?\"\n", "    prompt_level_3 = prompt_base + f\"\\n請生成更深入的問答，探討事件的：\\n- 其他政黨如何看待/評論此事件（參考政黨資訊、新聞）?\\n- 社群媒體上的討論（PTT/YouTube）反映了哪些普遍的民眾情緒或擔憂?\\n- 比較社群媒體上的討論與新聞報導的異同之處。\"\n", "\n", "    avoidance_prompt = \"\\n請絕對避免生成以下已有的問題：\\n - \" + \"\\n - \".join(list(existing_questions)[:20]) if existing_questions else \"\" # 只顯示前20個避免過長\n", "\n", "    if processing_level == 0: final_prompt = prompt_level_0 + avoidance_prompt\n", "    elif processing_level == 1: final_prompt = prompt_level_1 + avoidance_prompt\n", "    elif processing_level == 2: final_prompt = prompt_level_2 + avoidance_prompt\n", "    else: final_prompt = prompt_level_3 + avoidance_prompt # 預設使用 level 3\n", "\n", "    final_prompt += f\"\\n\\n請以 JSON 列表格式輸出，每個物件必須包含 'question' (問題字串)、'answer' (答案字串) 和 'date' (固定為 \\\"{event_date_str}\\\" 的字串) 三個鍵。答案應基於提供的所有資訊（事件描述、新聞、政黨、社群、網路搜尋），如果資訊不足以形成有意義的回答，請不要生成該問答對。\\n請直接輸出 JSON 列表。\"\n", "\n", "    try:\n", "        model = genai.GenerativeModel(model_id)\n", "        request_options = genai.types.RequestOptions(timeout=120)\n", "        response = model.generate_content(final_prompt, generation_config=generation_config, request_options=request_options)\n", "        qa_list_from_llm = try_parse_json(response.text) # 使用 try_parse_json\n", "\n", "        if qa_list_from_llm is None:\n", "            print(f\"  ❌ QA 生成失敗 (深度 {processing_level})，未返回有效 JSON。\")\n", "            return []\n", "\n", "        valid_qa_raw = filter_invalid_questions(qa_list_from_llm)\n", "        \n", "        # --- 修改開始：確保每個 QA 都有正確的 date ---\n", "        final_valid_qa = []\n", "        for qa_item in valid_qa_raw:\n", "            if isinstance(qa_item, dict):\n", "                qa_item['date'] = event_date_str # 強制設定/覆蓋為事件的日期\n", "                final_valid_qa.append(qa_item)\n", "        # --- 修改結束 ---\n", "\n", "        print(f\"  ✅ 為事件 ({event_date_str}) 生成 {len(final_valid_qa)} 個有效 QA (深度 {processing_level})。\")\n", "        return final_valid_qa\n", "    except Exception as e:\n", "        print(f\"  ❌ QA 生成 API 或其他錯誤 (深度 {processing_level}): {e}\")\n", "        return []\n", "\n", "def find_latest_state(person_name, folder='./inputTime_qa_data'):\n", "    \"\"\"查找最新的 QA 檔案和對應的狀態檔案\"\"\"\n", "    # 檔名模式：{人名}_{開始日期}_{結束日期}_qaData.json\n", "    # 日期可以是 YYYY-MM-DD 或 \"最早\"/\"最晚\"\n", "    qa_pattern = re.compile(rf'{re.escape(person_name)}_([^_]+)_([^_]+)_qaData\\.json')\n", "    latest_qa_file, latest_state_file = None, None\n", "    latest_start_date_str_from_file, latest_end_date_str_from_file = None, None\n", "    # 用於比較的日期，將 \"最早\" 視為非常早，\"最晚\" 視為非常晚\n", "    # 這裡我們主要依賴檔案的修改時間來判斷最新，如果檔名日期解析複雜\n", "    latest_file_mod_time = 0\n", "\n", "    if not os.path.exists(folder):\n", "        os.makedirs(folder)\n", "        print(f\"🆕 已創建資料夾: {folder}\")\n", "        return None, None, None, None\n", "\n", "    for filename in os.listdir(folder):\n", "        qa_match = qa_pattern.match(filename)\n", "        if qa_match:\n", "            current_qa_file = os.path.join(folder, filename)\n", "            current_file_mod_time = os.path.getmtime(current_qa_file)\n", "\n", "            if current_file_mod_time > latest_file_mod_time:\n", "                start_date_str = qa_match.group(1)\n", "                end_date_str = qa_match.group(2)\n", "                potential_state_file = os.path.join(folder, f\"{person_name}_{start_date_str}_{end_date_str}_qaData_state.json\")\n", "\n", "                if os.path.exists(potential_state_file): # 確保 state file 也存在\n", "                    latest_file_mod_time = current_file_mod_time\n", "                    latest_qa_file = current_qa_file\n", "                    latest_state_file = potential_state_file\n", "                    latest_start_date_str_from_file = start_date_str\n", "                    latest_end_date_str_from_file = end_date_str\n", "\n", "    if latest_qa_file:\n", "        print(f\"🔍 找到最新的 QA 文件：'{os.path.basename(latest_qa_file)}' (時間範圍: {latest_start_date_str_from_file} - {latest_end_date_str_from_file})\")\n", "        return latest_qa_file, latest_state_file, latest_start_date_str_from_file, latest_end_date_str_from_file\n", "    else:\n", "        print(\"🆕 未找到符合條件的 QA 和狀態文件。\")\n", "        return None, None, None, None\n", "\n", "def load_state(qa_file, state_file):\n", "    \"\"\"載入 QA 列表、事件列表和已使用的問題集合\"\"\"\n", "    global_qa_list, events_data, global_questions_set = [], [], set()\n", "    last_processed_event_date_obj = None # 改為 datetime.date 物件\n", "    loaded_start_date_str, loaded_end_date_str = None, None\n", "\n", "    try:\n", "        with open(qa_file, \"r\", encoding=\"utf-8\") as f:\n", "            global_qa_list = json.load(f)\n", "        for qa in global_qa_list:\n", "            if isinstance(qa, dict) and \"question\" in qa:\n", "                global_questions_set.add(qa[\"question\"])\n", "        print(f\"  📊 已從 QA 文件 '{os.path.basename(qa_file)}' 載入 {len(global_qa_list)} QA，{len(global_questions_set)} 唯一問題。\")\n", "    except Exception as e:\n", "        print(f\"⚠️ 無法載入 QA 文件 '{qa_file}': {e}。將從空列表開始。\")\n", "\n", "    try:\n", "        with open(state_file, \"r\", encoding=\"utf-8\") as f:\n", "            state_data = json.load(f)\n", "        raw_events = state_data.get(\"events\", [])\n", "        for event_dict in raw_events:\n", "            parsed_date = parse_event_date(event_dict.get(\"date\"))\n", "            if parsed_date:\n", "                event_dict['parsed_date'] = parsed_date # datetime 物件\n", "                event_dict['processed_level'] = event_dict.get('processed_level', -1) # 預設為 -1\n", "                events_data.append(event_dict)\n", "\n", "        # 按解析後的日期排序\n", "        events_data.sort(key=lambda x: x['parsed_date'])\n", "\n", "        last_processed_event_date_str = state_data.get(\"last_processed_event_date\")\n", "        if last_processed_event_date_str:\n", "            parsed_dt = parse_event_date(last_processed_event_date_str)\n", "            if parsed_dt:\n", "                last_processed_event_date_obj = parsed_dt.date() # 存儲為 date 物件\n", "\n", "        loaded_start_date_str = state_data.get(\"start_date\")\n", "        loaded_end_date_str = state_data.get(\"end_date\")\n", "        print(f\"  📈 已從狀態文件 '{os.path.basename(state_file)}' 載入 {len(events_data)} 事件。上次處理到日期: {last_processed_event_date_obj if last_processed_event_date_obj else '未記錄'}。記錄的時間範圍: {loaded_start_date_str or '最早'} - {loaded_end_date_str or '最晚'}。\")\n", "    except Exception as e:\n", "        print(f\"⚠️ 無法載入狀態文件 '{state_file}': {e}。事件列表將為空。\")\n", "        events_data, last_processed_event_date_obj, loaded_start_date_str, loaded_end_date_str = [], None, None, None\n", "\n", "    return global_qa_list, events_data, global_questions_set, last_processed_event_date_obj, loaded_start_date_str, loaded_end_date_str\n", "\n", "def save_state(person_name, start_date_str_param, end_date_str_param, global_qa_list, events_data, last_processed_event_date_obj, folder='./inputTime_qa_data'):\n", "    \"\"\"保存 QA 列表和狀態文件\"\"\"\n", "    start_date_safe = start_date_str_param if start_date_str_param else \"最早\"\n", "    end_date_safe = end_date_str_param if end_date_str_param else \"最晚\"\n", "\n", "    qa_filename = os.path.join(folder, f\"{person_name}_{start_date_safe}_{end_date_safe}_qaData.json\")\n", "    state_filename = os.path.join(folder, f\"{person_name}_{start_date_safe}_{end_date_safe}_qaData_state.json\")\n", "\n", "    try:\n", "        with open(qa_filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(global_qa_list, f, ensure_ascii=False, indent=2) # indent=2 for smaller files\n", "        print(f\"✅ 已儲存 {len(global_qa_list)} 個 QA 到 {qa_filename}\")\n", "    except Exception as e:\n", "        print(f\"❌ 儲存 QA 文件失敗: {e}\")\n", "\n", "    try:\n", "        events_to_save = []\n", "        for event_item in events_data:\n", "            event_copy = event_item.copy()\n", "            # 移除 Python datetime 物件，因為它不能直接序列化為 JSON\n", "            event_copy.pop('parsed_date', None)\n", "            events_to_save.append(event_copy)\n", "\n", "        state_data = {\n", "            \"events\": events_to_save,\n", "            \"last_processed_event_date\": last_processed_event_date_obj.strftime(\"%Y-%m-%d\") if last_processed_event_date_obj else None,\n", "            \"start_date\": start_date_str_param, # 保存用戶輸入的原始時間範圍\n", "            \"end_date\": end_date_str_param\n", "        }\n", "        with open(state_filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(state_data, f, ensure_ascii=False, indent=2)\n", "        print(f\"✅ 已儲存狀態到 {state_filename}, 時間範圍: {start_date_safe} - {end_date_safe}, 上次處理日期: {state_data['last_processed_event_date']}\")\n", "    except Exception as e:\n", "        print(f\"❌ 儲存狀態文件失敗: {e}\")\n", "\n", "def main_incremental():\n", "    print(\"=\" * 50)\n", "    print(\"🤖 政治人物 QA 知識庫生成器 (時間範圍過濾 + 多源整合 v2)\")\n", "    print(\"=\" * 50)\n", "\n", "    # --- 設定參數 ---\n", "    name = input(\"請輸入要查詢的政治人物名稱 (預設: 葉元之)：\") or \"葉元之\"\n", "    start_date_input_str = input(\"請輸入起始日期 (YYYY-MM-DD，留空表示最早)：\") or None\n", "    end_date_input_str = input(\"請輸入結束日期 (YYYY-MM-DD，留空表示最晚)：\") or None\n", "\n", "    # 解析用戶輸入的日期\n", "    user_start_date_obj = parse_event_date(start_date_input_str) if start_date_input_str else None\n", "    user_end_date_obj = parse_event_date(end_date_input_str) if end_date_input_str else None\n", "\n", "    if user_start_date_obj and user_end_date_obj and user_start_date_obj > user_end_date_obj:\n", "        print(\"⚠️ 起始日期晚於結束日期，請重新輸入。\")\n", "        return\n", "\n", "    max_processing_depth = 3 #int(input(\"請輸入每個事件的最大處理深度 (0-3，預設 1): \") or \"1\")\n", "    if not 0 <= max_processing_depth <= 3: max_processing_depth = 1\n", "\n", "    social_data_folder = 'crawler' # 假設爬蟲資料夾結構\n", "    social_sample_size = 50 # PTT/YouTube 抽樣數量\n", "    social_content_keys = ['content', 'comment','留言內容','留言','影片標題','時間','影片發布時間'] # 用於抽樣的鍵\n", "\n", "    # --- 0. 載入外部資料 (一次性) ---\n", "    print(\"\\n--- 正在載入外部資料 ---\")\n", "    youtube_file, ptt_file = find_latest_social_media_files(name, base_folder=social_data_folder)\n", "    youtube_summary = load_and_sample_social_data(youtube_file, sample_size=social_sample_size, content_keys=social_content_keys)\n", "    ptt_summary = load_and_sample_social_data(ptt_file, sample_size=social_sample_size, content_keys=social_content_keys)\n", "    wiki_summary, wiki_full_text = fetch_wikipedia_summary(name)\n", "    related_news_gemini = fetch_related_news(name) # Gemini 搜尋的新聞\n", "    newsapi_news = fetch_related_news_from_newsapi(name) # NewsAPI 的新聞\n", "    party_info = fetch_party_information(name)\n", "    print(\"-\" * 20)\n", "\n", "    # --- 1. 載入或初始化狀態 ---\n", "    # find_latest_state 會根據人名找最新的檔案，忽略檔名中的日期範圍\n", "    # 我們需要根據用戶輸入的 start_date_input_str 和 end_date_input_str 來決定檔名\n", "    current_run_start_date_str = start_date_input_str if start_date_input_str else \"最早\"\n", "    current_run_end_date_str = end_date_input_str if end_date_input_str else \"最晚\"\n", "\n", "    qa_data_folder = './inputTime_qa_data'\n", "    # 構建本次運行的檔案路徑\n", "    expected_qa_file = os.path.join(qa_data_folder, f\"{name}_{current_run_start_date_str}_{current_run_end_date_str}_qaData.json\")\n", "    expected_state_file = os.path.join(qa_data_folder, f\"{name}_{current_run_start_date_str}_{current_run_end_date_str}_qaData_state.json\")\n", "\n", "    global_qa_list, events_data, global_questions_set, last_processed_event_date_obj = [], [], set(), None\n", "    loaded_start_date_from_state, loaded_end_date_from_state = None, None # 從狀態文件讀取的日期範圍字串\n", "\n", "    if os.path.exists(expected_qa_file) and os.path.exists(expected_state_file):\n", "        print(f\"\\n🔄 載入與目前時間範圍 ({current_run_start_date_str} - {current_run_end_date_str}) 相符的現有狀態...\")\n", "        global_qa_list, events_data, global_questions_set, last_processed_event_date_obj, loaded_start_date_from_state, loaded_end_date_from_state = load_state(expected_qa_file, expected_state_file)\n", "        # 確保載入的日期範圍與當前運行一致，理論上應該一致\n", "        if loaded_start_date_from_state != start_date_input_str or loaded_end_date_from_state != end_date_input_str:\n", "             print(f\"⚠️ 狀態文件中的時間範圍 ({loaded_start_date_from_state} - {loaded_end_date_from_state}) 與當前請求 ({start_date_input_str} - {end_date_input_str}) 不完全一致。將以狀態文件為準，但可能需要重新提取事件。\")\n", "             # 為了安全，如果時間範圍不一致，最好重新提取事件\n", "             events_data = []\n", "             last_processed_event_date_obj = None\n", "    else:\n", "        print(f\"\\n🌱 未找到與目前時間範圍 ({current_run_start_date_str} - {current_run_end_date_str}) 相符的狀態，將從頭開始為 '{name}' 生成 QA...\")\n", "        # 不需要做任何事，變數已初始化為空\n", "\n", "    # --- 2. 如果需要，提取/更新事件列表 ---\n", "    # 條件：沒有事件數據 OR (狀態文件中沒有上次處理日期 AND 有事件數據但可能不完整)\n", "    # 基本上，如果 events_data 是空的，就提取\n", "    if not events_data:\n", "        print(\"\\n--- 提取/更新事件列表 ---\")\n", "        if not any([wiki_full_text, related_news_gemini, newsapi_news, party_info, ptt_summary, youtube_summary]):\n", "            print(f\"❌ 無法獲取任何關於 {name} 的資訊來源，程序終止。\")\n", "            return\n", "\n", "        extracted_events = extract_dated_events_from_text(\n", "            name, wiki_full_text, related_news_gemini, newsapi_news, party_info,\n", "            ptt_summary, youtube_summary,\n", "            user_start_date_obj, user_end_date_obj # 使用解析後的 datetime 物件\n", "        )\n", "\n", "        if not extracted_events:\n", "            print(f\"❌ 未能在指定時間範圍內提取到帶日期的事件，程序終止。\")\n", "            # 即使提取不到事件，也保存一個空的狀態，避免下次重複提取\n", "            save_state(name, start_date_input_str, end_date_input_str, [], [], None, folder=qa_data_folder)\n", "            return\n", "\n", "        temp_events_data = []\n", "        for event in extracted_events:\n", "            # 'parsed_date' 應該已經在 extract_dated_events_from_text 中設定好了\n", "            if 'parsed_date' in event and isinstance(event['parsed_date'], datetime):\n", "                event['processed_level'] = -1 # 初始化處理深度\n", "                temp_events_data.append(event)\n", "        temp_events_data.sort(key=lambda x: x['parsed_date']) # 按日期排序\n", "        events_data = temp_events_data # 更新 events_data\n", "        last_processed_event_date_obj = None # 重置上次處理日期，因為事件列表已更新\n", "        print(f\"🗓️ 已提取並排序 {len(events_data)} 個事件。\")\n", "    else:\n", "        print(f\"\\nℹ️ 使用已載入的 {len(events_data)} 個事件。上次處理到 {last_processed_event_date_obj if last_processed_event_date_obj else '未開始'}。\")\n", "\n", "\n", "    # --- 3. 按事件順序深入生成 QA ---\n", "    print(f\"\\n--- 開始生成 QA (最大深度: {max_processing_depth}) ---\")\n", "    start_qa_count_for_session = len(global_qa_list) # 本次運行開始時的 QA 數量\n", "\n", "    # 確定從哪個事件開始處理\n", "    initial_event_index = 0\n", "    if last_processed_event_date_obj and events_data:\n", "        for i, event in enumerate(events_data):\n", "            # 找到第一個未完全處理或日期在上次處理之後的事件\n", "            if event['parsed_date'].date() >= last_processed_event_date_obj and event.get('processed_level', -1) < max_processing_depth :\n", "                initial_event_index = i\n", "                break\n", "            elif event['parsed_date'].date() > last_processed_event_date_obj: # 如果日期已經超過，也從這個開始\n", "                initial_event_index = i\n", "                break\n", "        else: # 如果循環正常結束 (所有事件都處理過或日期更早)\n", "            if all(event.get('processed_level', -1) >= max_processing_depth for event in events_data if event['parsed_date'].date() >= last_processed_event_date_obj):\n", "                 print(\"✅ 所有相關事件已達到最大處理深度。\")\n", "                 initial_event_index = len(events_data) # 設置為超出索引，不進入循環\n", "\n", "    with tqdm(total=len(events_data), initial=initial_event_index, desc=\"處理事件\", unit=\"事件\") as pbar:\n", "        for i in range(initial_event_index, len(events_data)):\n", "            event = events_data[i]\n", "            pbar.set_description(f\"處理事件 ({event.get('date', '未知日期')[:10]})\")\n", "            current_event_level = event.get('processed_level', -1)\n", "            start_processing_level_for_event = current_event_level + 1\n", "\n", "            for level_to_process in range(start_processing_level_for_event, max_processing_depth + 1):\n", "                tqdm.write(f\"  -> 事件 {i+1}/{len(events_data)} ({event.get('date')}), 嘗試深度 {level_to_process}...\")\n", "                new_qa_list_for_level = generate_event_qa(\n", "                    name, event['event_description'], event.get('date'), level_to_process,\n", "                    global_questions_set, related_news_gemini, party_info, ptt_summary, youtube_summary\n", "                )\n", "                newly_added_this_level = 0\n", "                for qa in new_qa_list_for_level:\n", "                    if qa.get(\"question\") and qa.get(\"question\") not in global_questions_set:\n", "                        global_qa_list.append(qa)\n", "                        global_questions_set.add(qa[\"question\"])\n", "                        newly_added_this_level += 1\n", "                \n", "                event['processed_level'] = level_to_process # 更新事件的處理深度\n", "                \n", "                if newly_added_this_level > 0:\n", "                    tqdm.write(f\"    ✨ (深度 {level_to_process}) 新增 {newly_added_this_level} QA (總數: {len(global_qa_list)})\")\n", "                    # 儲存狀態 (較頻繁，但安全)\n", "                    save_state(name, start_date_input_str, end_date_input_str, global_qa_list, events_data, event['parsed_date'].date(), folder=qa_data_folder)\n", "                    time.sleep(random.uniform(1,3)) # 隨機延遲，避免 API 過快請求\n", "                else:\n", "                    tqdm.write(f\"    ...深度 {level_to_process} 未產生新 QA。\")\n", "            \n", "            last_processed_event_date_obj = event['parsed_date'].date() # 更新全域的上次處理日期\n", "            pbar.update(1) # 更新進度條\n", "\n", "    print(\"\\n--- 保存最終結果 ---\")\n", "    final_qa_count = len(global_qa_list)\n", "    save_state(name, start_date_input_str, end_date_input_str, global_qa_list, events_data, last_processed_event_date_obj, folder=qa_data_folder)\n", "    newly_added_in_session = final_qa_count - start_qa_count_for_session\n", "    print(f\"  📊 最終 QA 數量: {final_qa_count} (本次運行新增 {newly_added_in_session})\")\n", "\n", "    print(\"\\n\" + \"=\" * 50)\n", "    print(f\"✅ 全部處理完成！本次運行新增 {newly_added_in_session} 個 QA，最終共 {final_qa_count} 個有效問答對。\")\n", "    if last_processed_event_date_obj:\n", "        print(f\"⏳ 最後處理到的事件日期: {last_processed_event_date_obj.strftime('%Y-%m-%d')}\")\n", "    else:\n", "        print(\"⏳ 未處理任何事件或未記錄上次處理日期。\")\n", "    print(\"=\" * 50)\n", "\n", "# --- 維基百科函數 (返回 summary 和 text) ---\n", "def fetch_wikipedia_summary(name):\n", "    \"\"\"從維基百科獲取摘要和全文\"\"\"\n", "    try:\n", "        print(f\"⏳ 正在查詢 {name} 的維基百科資料...\")\n", "        # 建議使用一個通用的 user_agent\n", "        user_agent = \"MyPoliticalQADataGenerator/1.0 (<EMAIL>)\"\n", "        wiki_wiki = wikipediaapi.Wikipedia(language='zh', user_agent=user_agent, timeout=30)\n", "        page = wiki_wiki.page(name)\n", "        if not page.exists():\n", "            print(f\"❌ 找不到 {name} 的維基百科頁面\")\n", "            return \"\", \"\" # 返回空字串，而不是 None\n", "        print(f\"✅ 成功獲取 {name} 的維基百科資料\")\n", "        return page.summary, page.text\n", "    except Exception as e:\n", "        print(f\"❌ 維基百科抓取錯誤: {e}\")\n", "        return \"\", \"\" # 發生錯誤時也返回空字串\n", "\n", "# --- 主程式入口 ---\n", "if __name__ == \"__main__\":\n", "    if api_key and NEWSAPI_KEY: # 檢查兩個 API Key\n", "        try:\n", "            print(\"🔑 Gemini API Key 和 NewsAPI Key 已配置。\")\n", "            main_incremental()\n", "        except Exception as auth_error:\n", "            print(f\"❌ Gemini API 初始化或測試時發生錯誤: {auth_error}\")\n", "            print(\"請檢查您的 API Key 是否正確，或網路連線是否正常。\")\n", "    else:\n", "        missing_keys = []\n", "        if not api_key: missing_keys.append(\"Gemini API Key\")\n", "        if not NEWSAPI_KEY: missing_keys.append(\"NewsAPI Key\")\n", "        print(f\"❌ 未能配置 API Key: {', '.join(missing_keys)}。請在程式碼頂部設定相應的變數。\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 測試函數"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⏳ 正在使用 NewsAPI.org 查詢 葉元之 的相關新聞...\n", "✅ 成功從 NewsAPI.org 獲取 30 條相關新聞\n"]}], "source": ["import requests\n", "import json\n", "from datetime import datetime\n", "\n", "NEWSAPI_KEY = \"********************************\" # 替換成你的 API 金鑰\n", "\n", "def fetch_related_news_from_newsapi(name):\n", "    \"\"\"使用 NewsAPI.org 查詢最新新聞\"\"\"\n", "    try:\n", "        print(f\"⏳ 正在使用 NewsAPI.org 查詢 {name} 的相關新聞...\")\n", "        url = f\"https://newsapi.org/v2/everything?qInTitle={name}&sortBy=publishedAt&apiKey={NEWSAPI_KEY}&language=zh\"\n", "        response = requests.get(url)\n", "        response.raise_for_status() # 檢查請求是否成功\n", "        data = response.json()\n", "        articles = data.get(\"articles\", [])\n", "        news_items = []\n", "        for article in articles[:50]: # 取前 50 條\n", "            title = article.get(\"title\")\n", "            description = article.get(\"description\")\n", "            published_at = article.get(\"publishedAt\")\n", "            source_name = article.get(\"source\", {}).get(\"name\")\n", "            if title and published_at:\n", "                date_obj = datetime.fromisoformat(published_at.replace(\"Z\", \"+00:00\"))\n", "                formatted_date = date_obj.strftime(\"%Y-%m-%d\")\n", "                news_items.append(f\"- {formatted_date}: {title} ({source_name})\")\n", "        print(f\"✅ 成功從 NewsAPI.org 獲取 {len(news_items)} 條相關新聞\")\n", "        return \"\\n\".join(news_items)\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"❌ NewsAPI.org 查詢錯誤: {e}\")\n", "        return f\"NewsAPI.org 查詢錯誤: {str(e)}\"\n", "    except json.JSONDecodeError as e:\n", "        print(f\"❌ NewsAPI.org 回應解析錯誤: {e}\")\n", "        return f\"NewsAPI.org 回應解析錯誤: {str(e)}\"\n", "\n", "# ... (修改 main_incremental 函數以使用新的新聞查詢函數)\n", "\n", "    # ...\n", "related_news = fetch_related_news_from_newsapi(\"葉元之\") # 使用新的新聞查詢函數\n", "\n", "    # ..."]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["'- 2025-04-30: 罷免葉元之板橋掃街 四叉貓開直播 學者：劣質立委代表 罷免具指標性 (Yahoo Entertainment)\\n- 2025-04-30: 「板橋大刪元」收件破門檻137％\\u3000葉元之認：30席藍委非常可能過二階 (Yahoo Entertainment)\\n- 2025-04-30: 無懼罷免二階連署達標\\u3000葉元之：選民的眼睛是雪亮的 (Yahoo Entertainment)\\n- 2025-04-29: 葉元之罷團宣布二階連署達標 5/5送件新北選委會 (Yahoo Entertainment)\\n- 2025-04-28: 告急！罷免葉元之最後衝刺 民團明晚掃街：浮洲車站出發 板橋民眾站出來 (Yahoo Entertainment)\\n- 2025-04-27: 罷葉元之未達安全份數喊告急 山田摩衣催連署 (Yahoo Entertainment)\\n- 2025-04-26: 罷免葉元之安全門檻\"差5千份\" 戴瑋姍.蘇巧慧.曹興誠催連署 (Yahoo Entertainment)\\n- 2025-04-25: 罷免葉元之告急？他曝：有一種人還沒來連署 (Yahoo Entertainment)\\n- 2025-04-24: 葉元之藍綠仇恨值都高\\u3000「葉力引爆」堪稱全台最危急 (Yahoo Entertainment)\\n- 2025-04-24: 罷免葉元之連署未達安全門檻！他憂恐成遺珠 (Yahoo Entertainment)\\n- 2025-04-24: 罷免葉元之連署仍告急！他曝「至少要補5000份才安全」憂：可能變遺珠 (Yahoo Entertainment)\\n- 2025-04-24: 緊急！沈伯洋曝「葉元之、傅崐萁7人」在補件邊緣：差2000份週末衝一波 (Yahoo Entertainment)\\n- 2025-04-24: 北市府開罰葉元之！民眾檢舉職場霸凌\\u3000揪1缺失罰9萬元 (Yahoo Entertainment)\\n- 2025-04-24: 葉元之遭控苛待立院助理 北市揪沒出勤紀錄罰9萬 (Yahoo Entertainment)\\n- 2025-04-23: 遭控霸凌助理！北市府勞檢揪「未備置出勤紀錄」罰9萬\\u3000葉元之：已改善 (Yahoo Entertainment)\\n- 2025-04-23: 葉元之遭控職場霸凌！北市府勞檢結果出爐\\u3000「這缺失」未改善開罰9萬元 (Yahoo Entertainment)\\n- 2025-04-23: 藍委葉元之遭控苛待助理 北市揪沒出勤紀錄罰9萬 (Yahoo Entertainment)\\n- 2025-04-23: 葉元之狂護航死亡連署⋯與綠議員吵一團！謝震武1分鐘解釋秒殺全場 (Yahoo Entertainment)\\n- 2025-04-23: 大罷免潮／板東投票決生死？罷團：葉元之準備三階投票戰 (Yahoo Entertainment)\\n- 2025-04-22: 大罷免二階連署「20區達標」！他驚曝雙北超預期：葉元之、徐巧芯都過了 (Yahoo Entertainment)\\n- 2025-04-20: 近8成藍委贊成倒閣？葉元之卻吐這1句 (Yahoo Entertainment)\\n- 2025-04-16: 連署告急！罷免葉元之剩2周衝刺\\u3000史書華澄清：目標一直都是4萬份 (Yahoo Entertainment)\\n- 2025-04-15: 葉元之稱加強校安進度慢酸卓揆搞罷免\\u3000教長無奈：有那麼難懂嗎？ (Yahoo Entertainment)\\n- 2025-04-15: 葉元之批郭智輝反質詢\\u3000求救韓國瑜出面說話 (Yahoo Entertainment)\\n- 2025-04-15: 新北葉元之、台東黃建賓傳二階連署過門檻\\u3000罷團：持續收件衝安全份數 (Yahoo Entertainment)\\n- 2025-04-14: 倒數15天！罷免葉元之二階連署過法定門檻\\u3000罷團：離安全目標缺1.5萬份 (Yahoo Entertainment)\\n- 2025-04-11: 助陣罷免葉元之！\\u3000王義川一張一張收連署：有信心超越目標 (Yahoo Entertainment)\\n- 2025-04-11: 二階倒數！罷團揭葉元之「雙面手法」\\u3000衝刺連署喊：讓他看到數字剉咧等 (Yahoo Entertainment)\\n- 2025-04-08: 大罷免\"空戰轉陸戰\" 罷免葉元之二階連署進度逾9成 (Yahoo Entertainment)\\n- 2025-04-01: 民調》葉元之挫著等！超過2成支持者轉向 4成6民眾贊成罷免 (Yahoo Entertainment)'"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["related_news"]}], "metadata": {"kernelspec": {"display_name": "transformers", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}