/* 全局變量 */
$primary-color: #2563eb;
$secondary-color: #e74c3c;
$text-dark: #333333;
$text-gray: #666666;
$text-light: #999999;
$bg-light: #f9fafb;
$bg-card: #ffffff;
$border-light: #eaeaea;
$shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
$border-radius: 10px;
$padding-card: 16px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;

/* 主容器 */
.politician-detail-container {
  font-family: 'Noto Sans TC', -apple-system, BlinkMacSystemFont, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-md;
  color: $text-dark;
  background-color: $bg-light;
}

/* 標題區域 */
.profile-header-wrapper {
  background-color: $bg-card;
  border-radius: $border-radius;
  box-shadow: $shadow-sm;
  margin-bottom: $spacing-md;
  padding: $spacing-md $spacing-lg;
}

.profile-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.avatar-container {
  flex: 0 0 auto;
  margin-right: $spacing-lg;

  img {
    width: 90px !important;
    height: 90px !important;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba($primary-color, 0.1);
}
}

.profile-info {
  flex: 1;

  .profile-name-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: $spacing-lg;
    margin-bottom: $spacing-sm;
  }

  .profile-name {
    font-size: 26px;
    font-weight: 700;
    margin: 0;
    color: $text-dark;
  }

  .recall-date-info {
    font-size: 14px;
    color: $text-gray;
    background: #fff3cd;
    border-radius: 6px;
    padding: 6px 12px;
    border-left: 3px solid #e74c3c;
    white-space: nowrap;
    
    strong {
      color: #2c3e50;
      font-weight: 600;
    }
  }

  .profile-badge {
    font-size: 14px !important;
    padding: 6px 12px !important;
    border-radius: 20px !important;
    margin-right: $spacing-md;
  }

  .profile-meta {
  display: flex;
  align-items: center;
    margin-top: $spacing-sm;
    font-size: 14px;
    color: $text-gray;

    i {
      margin-right: $spacing-sm;
}
  }
}

/* 內容區域 */
.content-container {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;
}

/* 側邊欄 */
.sidebar-container {
  flex: 1;
  min-width: 300px;
}

/* 主要內容區 */
.main-container {
  flex: 2;
  min-width: 400px;
}

/* 卡片樣式統一 */
[cCard] {
  background-color: $bg-card;
  border-radius: $border-radius !important;
  border: none !important;
  overflow: hidden;
  box-shadow: $shadow-sm !important;
  margin-bottom: $spacing-md;
}

[cCardHeader] {
  padding: $spacing-md !important;
  font-size: 16px;
  font-weight: 600;
  color: $text-dark;
  background-color: $bg-card !important;
  border-bottom: 1px solid $border-light !important;
  display: flex;
  align-items: center;

  i {
    margin-right: $spacing-sm;
    color: $primary-color;
  }
}

[cCardBody] {
  padding: $padding-card !important;
}

/* 基本資料卡片 */
.info-card {
  .info-item {
    display: flex;
    flex-direction: column;
    margin-bottom: $spacing-sm;
}

  .info-label {
    font-weight: 600;
    color: $primary-color;
    margin-bottom: $spacing-sm;
    display: flex;
    align-items: center;

    i {
      margin-right: $spacing-sm;
}
  }

  .info-content {
    padding-left: $spacing-md;
    color: $text-gray;

    p {
      margin: 0 0 $spacing-sm 0;
      line-height: 1.5;
    }
}
}

/* 罷免進度卡片 */
.recall-card {
  .recall-header {
    color: $secondary-color !important;

    i {
      color: $secondary-color;
    }
  }

  .recall-progress {
    padding: $spacing-sm;
  }

  .progress-item {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-sm;
    font-size: 14px;

    .progress-label {
      margin-right: 12px;
      font-weight: 500;
      color: #666;
      min-width: 80px;
    }

    .progress-value {
      font-weight: 600;
      color: #333;

      &.status {
        &.status-success {
          color: #10b981; /* 綠色 */
        }
        
        &.status-failed {
          color: #ef4444; /* 紅色 */
        }
        
        &.status-ongoing {
          color: #3b82f6; /* 藍色 */
        }
        
        &.status-supplement {
          color: #f59e0b; /* 黃色 */
        }
        
        &.status-default {
          color: #6b7280; /* 灰色 */
        }
      }
    }

    // 罷免狀態樣式
    .status {
      font-weight: 600;
      
      &.status-success {
        color: #10b981; /* 綠色 */
      }
      
      &.status-failed {
        color: #ef4444; /* 紅色 */
      }
      
      &.status-ongoing {
        color: #3b82f6; /* 藍色 */
      }
      
      &.status-supplement {
        color: #f59e0b; /* 黃色 */
      }
      
      &.status-default {
        color: #6b7280; /* 灰色 */
      }
    }
  }

  .progress-label {
    color: $text-gray;
  font-weight: 500;
}

  .progress-value {
    font-weight: 600;

    &.status {
      color: $secondary-color;
    }

    // 罷免狀態樣式
    &.status-petition-ongoing {
      color: #0d6efd; // 藍色 - 連署進行中
      font-weight: bold;
    }

    &.status-petition-failed {
      color: #6c757d; // 灰色 - 連署未通過
    }

    &.status-recall-ongoing {
      color: #fd7e14; // 橙色 - 罷免進行中
      font-weight: bold;
    }

    &.status-recall-failed {
      color: #6c757d; // 灰色 - 罷免未通過
    }

    &.status-recall-success {
      color: #dc3545; // 紅色 - 罷免成功
      font-weight: bold;
    }

    &.status-unknown {
      color: #6c757d; // 灰色 - 狀態未知
    }
  }

  .progress-bar-container {
    height: 10px;
    background-color: #f1f1f1;
    border-radius: 5px;
    margin: $spacing-md 0;
    position: relative;
  }

  .progress-bar {
    height: 100%;
    background-color: $secondary-color;
    border-radius: 5px;
}

  .progress-percentage {
    position: absolute;
    right: 0;
    top: -20px;
    font-size: 12px;
    font-weight: 600;
    color: $secondary-color;
}

// 正負面聲量比值顯示樣式
.sentiment-ratio-container {
  margin: 15px 0;

  .sentiment-ratio-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .ratio-label {
      font-weight: bold;
      color: #495057;
      font-size: 14px;
    }

    .ratio-total {
      font-size: 12px;
      color: #6c757d;
    }
  }

  .dual-color-bar {
    position: relative;
    height: 30px;
    background-color: #f8f9fa;
    border-radius: 15px;
    overflow: visible; // 改為 visible 讓文字可以顯示在外面
    border: 1px solid #dee2e6;
    display: flex;
    margin-bottom: 15px;

    .positive-bar {
      background-color: #ffffff;
      border-right: 1px solid #dee2e6;
      transition: width 0.3s ease;
    }

    .negative-bar {
      background-color: #212529;
      transition: width 0.3s ease;
    }

    // 左側文字 (正面)
    .bar-text-left {
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 11px;
      font-weight: bold;
      color: #6c757d;
      white-space: nowrap;
      z-index: 10;
      pointer-events: none;
    }

    // 右側文字 (負面)
    .bar-text-right {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 11px;
      font-weight: bold;
      color: #ffffff; // 白色文字在黑色背景上
      white-space: nowrap;
      z-index: 10;
      pointer-events: none;
    }
  }

  .sentiment-stats {
    display: flex;
    justify-content: space-between;

    .stat-item {
      text-align: center;
      flex: 1;

      .stat-value {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 3px;

        &.positive {
          color: #6c757d;
        }

        &.negative {
          color: #212529;
        }
      }

      .stat-label {
        font-size: 10px;
        color: #6c757d;
        line-height: 1.2;
      }
    }
  }
}

  .social-links {
    margin-top: $spacing-md;
    font-size: 14px;

    span {
      color: $text-gray;
      margin-right: $spacing-sm;
    }

    a {
      color: $primary-color;
      margin-right: $spacing-sm;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

/* 圖表內統計資訊 */
.chart-stats {
  display: flex;
  justify-content: space-around;
  margin-top: 1rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;

  .stat-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: #666;

    .color-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 0.5rem;

      &.support {
        background-color: #f87171;
      }

      &.oppose {
        background-color: #4f8cff;
      }
    }
  }
}

/* 圖表右側統計資訊 */
.chart-stats-side {
  width: 40%;
  padding-left: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1rem;

  .stat-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid transparent;

    &:first-child {
      border-left-color: #f87171;
    }

    &:last-child {
      border-left-color: #4f8cff;
    }

    .color-indicator {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      margin-right: 0.75rem;
      flex-shrink: 0;

      &.support {
        background-color: #f87171;
      }

      &.oppose {
        background-color: #4f8cff;
      }
    }

    .stat-info {
      flex: 1;

      .stat-label {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0.25rem;
      }

      .stat-percentage {
        font-size: 1.5rem;
        font-weight: bold;
        color: #333;
      }
    }
  }
}

/* 圖表右側統計資訊 */
.chart-stats-side {
  width: 40%;
  padding-left: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1rem;

  .stat-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid transparent;

    &:first-child {
      border-left-color: #f87171;
    }

    &:last-child {
      border-left-color: #4f8cff;
    }

    .color-indicator {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      margin-right: 0.75rem;
      flex-shrink: 0;

      &.support {
        background-color: #f87171;
      }

      &.oppose {
        background-color: #4f8cff;
      }
    }

    .stat-info {
      flex: 1;

      .stat-label {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0.25rem;
      }

      .stat-percentage {
        font-size: 1.5rem;
        font-weight: bold;
        color: #333;
      }
    }
  }
}

/* 情緒統計資訊 */
.emotion-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 6px;

  .emotion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    background: white;
    border-radius: 4px;
    border-left: 3px solid #4f8cff;

    .emotion-name {
      color: #666;
      font-weight: 500;
    }

    .emotion-percentage {
      color: #4f8cff;
      font-weight: bold;
    }
  }
}

/* 圖表區 */
.charts-row {
  display: flex;
  gap: $spacing-md;
  margin-bottom: $spacing-md;
  flex-wrap: wrap;
}

.chart-card {
  flex: 1;
  min-width: 260px;
}

.chart-container {
  height: 350px !important;
  width: 100% !important;
  position: relative;
  display: block;
  min-height: 350px;
  max-height: 250px;
  overflow: visible; /* 改為 visible 讓 tooltip 可以顯示 */
}

c-chart {
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1; /* 確保圖表在正確的層級 */
}

/* 確保 tooltip 可以正常顯示 */
c-chart canvas {
  position: relative;
  z-index: 1;
}

/* 確保 card 不會遮擋 tooltip */
.chart-card {
  overflow: visible;
  z-index: auto;
  margin-bottom: 1.5rem;
}

.chart-card [cCardBody] {
  overflow: visible;
}

/* 立委詳細頁面基本樣式 */
.politician-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.info-card {
  margin-bottom: 1.5rem;
}

/* 詞雲容器樣式 */
.wordcloud-container {
  padding: 20px;
  text-align: center;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 新聞容器樣式 */
.news-container {
  padding: 20px;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 自定義圖表統計顯示 - 右下角 */
.custom-chart-stats {
  position: absolute;
  bottom: 15px;
  right: 15px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);

  .stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #666;

    .color-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      flex-shrink: 0;

      &.support {
        background-color: #f87171;
      }

      &.oppose {
        background-color: #4f8cff;
      }
    }

    .stat-text {
      font-weight: 500;
      white-space: nowrap;
    }
  }
}

/* 現代化文字雲樣式 */
.wordcloud-container-modern {
  padding: 2rem;
  min-height: 250px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;

  .word-cloud-item {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    margin: 0.2rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);

    &:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      background: rgba(255, 255, 255, 1);
      border-color: currentColor;
    }

    // 根據索引位置調整布局
    &:nth-child(1) { order: 5; } // 最大的在中間
    &:nth-child(2) { order: 3; }
    &:nth-child(3) { order: 7; }
    &:nth-child(4) { order: 2; }
    &:nth-child(5) { order: 8; }
    &:nth-child(6) { order: 4; }
    &:nth-child(7) { order: 6; }
    &:nth-child(8) { order: 1; }
    &:nth-child(9) { order: 9; }
    &:nth-child(10) { order: 10; }
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 舊的詞雲樣式保留 */
.wordcloud-card {
  margin-bottom: $spacing-md;
}

.wordcloud-container {
  padding: 20px;
  height: 250px;
  max-height: 250px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
  gap: 8px;
  text-align: center;

  .word-item {
    display: inline-block;
    margin: 4px 6px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    font-size: 14px !important;
    font-weight: 500;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
      background: rgba(255, 255, 255, 1);
    }
  }

  // 舊的 span 樣式保持兼容性
  span:not(.word-item) {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 14px !important;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

/* 現代化時間篩選器樣式 */
.time-filter-card-modern {
  margin-bottom: 1.5rem;
  border: none !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
  border-radius: 12px !important;

  .modern-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0 !important;
    border: none !important;

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header-title {
        font-weight: 600;
        font-size: 1rem;
      }

      .loading-indicator {
        display: flex;
        align-items: center;

        .spinner-border-sm {
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }

  .modern-body {
    padding: 1.5rem;
  }
}

.quick-filters-modern {
  margin-bottom: 1.5rem;

  .filter-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
  }

  .filter-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;

    .filter-btn {
      border-radius: 20px !important;
      font-size: 0.8rem;
      padding: 0.4rem 1rem;
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

.custom-date-section {
  .section-divider {
    text-align: center;
    margin: 1.5rem 0 1rem 0;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e5e7eb;
    }

    .divider-text {
      background: white;
      padding: 0 1rem;
      color: #6b7280;
      font-size: 0.85rem;
      position: relative;
    }
  }

  .date-inputs-modern {
    display: flex;
    align-items: end;
    gap: 1rem;
    flex-wrap: wrap;

    .date-input-group {
      flex: 1;
      min-width: 140px;

      .date-label {
        display: flex;
        align-items: center;
        font-size: 0.85rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #374151;
      }

      .modern-date-input {
        border-radius: 8px;
        border: 2px solid #e5e7eb;
        padding: 0.6rem 0.75rem;
        font-size: 0.9rem;
        transition: all 0.2s ease;

        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          outline: none;
        }
      }
    }

    .date-separator {
      display: flex;
      align-items: center;
      padding-bottom: 0.5rem;
      font-size: 1.2rem;
    }
  }
}

.filter-info-modern {
  margin-top: 1rem;

  .info-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;

    .info-text {
      font-size: 0.9rem;
      color: #475569;
    }
  }
}

/* 日期篩選按鈕 */
.date-filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  
  button {
    min-width: 80px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    
    &.active {
      background-color: $primary-color;
      color: white;
      font-weight: 500;
      box-shadow: 0 2px 5px rgba(37, 99, 235, 0.2);
    }
    
    &:hover:not(.active) {
      background-color: #e9eef7;
    }
  }
}

.filter-card {
  margin-bottom: 16px;
  box-shadow: $shadow-sm;
  border-radius: $border-radius;
  border: 1px solid $border-light;
  
  .card-header {
    background-color: #f8fafc;
    border-bottom: 1px solid $border-light;
    padding: 12px 16px;
    
    i {
      margin-right: 8px;
      color: $primary-color;
    }
  }
  
  .card-body {
    padding: 12px;
  }

  &.full-width {
    width: 100%;
    margin-bottom: 20px;
  }
}

/* 載入狀態 */
c-spinner {
  color: var(--cui-primary);
}

/* 按鈕組樣式 */
c-button-group {
  .btn {
    border-radius: 0.375rem;

    &:not(:last-child) {
      margin-right: 0.5rem;
    }
  }
}

/* 下拉選單樣式 */
c-dropdown {
  .dropdown-toggle {
    border: none;
    background: transparent;

    &:focus {
      box-shadow: none;
    }
  }
}

/* 警告框樣式 */
c-alert {
  border-radius: 0.5rem;
  border: none;

  &.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
  }
}

/* 徽章樣式 */
c-badge {
  font-size: 0.75rem;

  &.badge-light {
    background-color: #f8f9fa;
    color: #6c757d;
  }
}

/* Bootstrap 徽章樣式 */
.badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 2px;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  &.badge-primary {
    background-color: #0d6efd;
  }

  &.badge-success {
    background-color: #198754;
  }

  &.badge-info {
    background-color: #0dcaf0;
  }

  &.badge-warning {
    background-color: #ffc107;
    color: #000;
  }

  &.badge-danger {
    background-color: #dc3545;
  }

  &.badge-secondary {
    background-color: #6c757d;
  }
}

/* 新聞區 */
.news-card {
  .news-container {
    padding: $spacing-md;
    min-height: 100px;
    color: $text-gray;
}
}

/* 響應式設計 */
@media (max-width: 768px) {
  .time-filter-container {
    .date-inputs {
      flex-direction: column;

      .input-group {
        min-width: 100%;
      }
    }

    .quick-filters {
      justify-content: center;

      button {
        flex: 1;
        min-width: 80px;
      }
    }
  }
}

/* CoreUI 增強樣式 */
.chart-container {
  position: relative;

  canvas {
    max-height: 100% !important;
  }
}

/* 卡片增強 */
c-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.125);

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

/* 圖表卡片特殊樣式 */
.chart-card {
  .chart-container {
    min-height: 350px;
  }

  // 雷達圖特別大一點
  &:has(c-chart[type="radar"]) .chart-container,
  .radar-chart-container {
    height: 400px !important;
    min-height: 400px;
  }
}

/* 統計數字樣式 */
.fs-4 {
  font-size: 1.5rem !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

/* 載入中 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  p {
    margin-top: $spacing-md;
    color: $primary-color;
    font-weight: 500;
}
}

/* 響應式調整 */
@media (max-width: 768px) {
  .content-container {
    flex-direction: column;
  }

  .charts-row {
    flex-direction: column;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
  }

  .avatar-container {
    margin-right: 0;
    margin-bottom: $spacing-md;
  }

  .profile-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .profile-name-container {
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: $spacing-sm;
    }
    
    .recall-date-info {
      font-size: 12px;
      padding: 4px 8px;
      text-align: center;
    }
  }
}

/* 載入提示樣式 */
.loading-indicator {
  display: flex;
  align-items: center;
  margin-left: auto;

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }

  small {
    font-size: 0.8rem;
  }
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;

  .spinner-border {
    width: 2rem;
    height: 2rem;
  }

  p {
    margin: 0;
    font-size: 0.9rem;
  }
}

/* Chart tooltip 修正 - 確保不被裁切 */
::ng-deep .c-chart-tooltip, 
::ng-deep .chartjs-tooltip, 
::ng-deep .chartjs-tooltip-container {
  z-index: 2000 !important;
  pointer-events: none;
}

/* 確保所有 CoreUI 組件容器不會裁切 tooltip */
::ng-deep [cCard], 
::ng-deep [cCardBody], 
::ng-deep [cRow], 
::ng-deep [cCol] {
  overflow: visible !important;
}

/* Chart.js 特定的 tooltip 樣式 */
::ng-deep .chartjs-tooltip {
  opacity: 1 !important;
  position: absolute !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  border-radius: 6px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 2000 !important;
  pointer-events: none !important;
  transform: translate(-50%, -100%) !important;
  white-space: nowrap !important;
  transition: opacity 0.2s ease, visibility 0.2s ease !important;
}

/* 強制隱藏 tooltip 的樣式 */
::ng-deep .chartjs-tooltip[style*="opacity: 0"] {
  visibility: hidden !important;
  display: none !important;
}

/* Chart 容器滑鼠離開時的處理 */
::ng-deep .c-chart:not(:hover) .chartjs-tooltip {
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity 0.1s ease !important;
}

/* Chart 容器確保 tooltip 顯示正常 */
::ng-deep .c-chart {
  position: relative !important;
  overflow: visible !important;
}

::ng-deep canvas {
  position: relative !important;
}

/* 文字雲新樣式 */
.word-cloud-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
}

.word-cloud-word {
  position: absolute;
  transform-origin: center;
  white-space: nowrap;
  cursor: pointer;
  font-weight: 600;
  line-height: 1.2;
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 4px;
  border-radius: 4px;
  
  &:hover {
    transform-origin: center;
    animation: pulse 0.5s ease;
    z-index: 10;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 日期範圍提示樣式 */
.date-range-info {
  display: block;
  margin-top: 10px;
  font-size: 12px;
  color: #6c757d;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #0d6efd;
}

/* 回到主畫面按鈕 */
.back-to-main-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 1rem 1.5rem;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(37, 99, 235, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  
  i {
    font-size: 16px;
    transition: transform 0.3s ease;
  }
  
  .btn-text {
    opacity: 1;
    max-width: 120px;
    overflow: hidden;
    white-space: nowrap;
    transition: all 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(37, 99, 235, 0.4);
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    padding: 1rem 1.8rem;
    
    i {
      transform: scale(1.1);
    }
    
    .btn-text {
      opacity: 1;
      max-width: 120px;
      margin-left: 0.2rem;
      transform: scale(1.05);
    }
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(37, 99, 235, 0.5);
  }
}

/* 按鈕呼吸動畫 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.back-to-main-btn::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border-radius: 50px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.back-to-main-btn:hover::before {
  opacity: 1;
  animation: breathe 2s ease-in-out infinite;
}

/* 手機版適配 */
@media (max-width: 768px) {
  .back-to-main-btn {
    bottom: 1rem;
    right: 1rem;
    padding: 0.8rem 1.2rem;
    font-size: 12px;
    
    i {
      font-size: 14px;
    }
    
    .btn-text {
      opacity: 1;
      max-width: 100px;
      font-size: 12px;
    }
    
    &:hover {
      padding: 0.8rem 1.4rem;
      
      .btn-text {
        transform: scale(1.05);
      }
    }
  }
}
