#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試最終修復腳本
驗證：
1. YouTube日期篩選不放寬
2. Threads URL收集
3. MongoDB增量更新
4. 統計增量更新
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def test_youtube_date_filtering():
    """測試YouTube日期篩選不放寬"""
    logger.info("🧪 測試YouTube日期篩選...")
    
    try:
        from crawler.yt_crawler import search_youtube_videos
        
        # 測試嚴格日期篩選
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        logger.info(f"測試日期: {yesterday}")
        
        # 測試URL收集
        url_file, video_data = search_youtube_videos(
            name="高虹安",
            cutoff_date=yesterday,
            headless=True
        )
        
        if video_data:
            logger.info(f"✅ YouTube URL收集成功: {len(video_data)} 個影片")
            
            # 檢查日期篩選是否嚴格
            cutoff_date = datetime.strptime(yesterday, '%Y-%m-%d')
            strict_filtered = 0
            
            for video in video_data:
                if 'upload_date' in video:
                    try:
                        upload_date = datetime.strptime(video['upload_date'], '%Y-%m-%d')
                        if upload_date >= cutoff_date:
                            strict_filtered += 1
                    except:
                        pass
            
            logger.info(f"📅 嚴格日期篩選結果: {strict_filtered}/{len(video_data)} 個影片符合日期要求")
            return True
        else:
            logger.warning("⚠️ YouTube URL收集無結果")
            return False
            
    except Exception as e:
        logger.error(f"❌ YouTube日期篩選測試失敗: {e}")
        return False

def test_threads_url_collection():
    """測試Threads URL收集"""
    logger.info("🧪 測試Threads URL收集...")
    
    try:
        from crawler.optimized_crawler_manager import OptimizedCrawlerManager
        
        manager = OptimizedCrawlerManager()
        
        # 測試Threads URL收集
        result = manager._collect_platform_urls_fast(
            platform='threads',
            politician_name='高虹安',
            cutoff_date_str='2025-01-01',
            webdriver_pool=None  # 會在函數內創建
        )
        
        logger.info(f"Threads URL收集結果: {result}")
        
        if result.get('success'):
            count = result.get('count', 0)
            logger.info(f"✅ Threads URL收集成功: {count} 個項目")
            return True
        else:
            logger.warning(f"⚠️ Threads URL收集失敗: {result.get('error', '未知錯誤')}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Threads URL收集測試失敗: {e}")
        return False

def test_mongodb_incremental_update():
    """測試MongoDB增量更新"""
    logger.info("🧪 測試MongoDB增量更新...")
    
    try:
        from crawler.data_to_mongo_v2 import DataToMongo
        
        # 創建MongoDB處理器
        mongo_handler = DataToMongo()
        
        # 測試增量更新
        result = mongo_handler.store_crawler_data(
            politician_name='高虹安',
            is_incremental=True
        )
        
        if result:
            logger.info("✅ MongoDB增量更新測試通過")
            return True
        else:
            logger.warning("⚠️ MongoDB增量更新可能有問題")
            return False
            
    except Exception as e:
        logger.error(f"❌ MongoDB增量更新測試失敗: {e}")
        return False

def test_stats_incremental_update():
    """測試統計增量更新"""
    logger.info("🧪 測試統計增量更新...")
    
    try:
        from crawler.legislator_stats import LegislatorStatsUpdater
        from pymongo import MongoClient
        
        # 連接MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        db = client['legislator_recall']
        
        # 創建統計更新器
        stats_updater = LegislatorStatsUpdater(db)
        
        # 測試增量更新
        result = stats_updater.update_all_legislators(force_recalculate=False)
        
        logger.info(f"統計增量更新結果: {result}")
        
        if isinstance(result, dict):
            logger.info(f"✅ 統計增量更新測試通過: {len(result)} 位立委")
            return True
        else:
            logger.warning("⚠️ 統計增量更新可能有問題")
            return False
            
    except Exception as e:
        logger.error(f"❌ 統計增量更新測試失敗: {e}")
        return False

def test_optimized_crawler_full():
    """測試完整的優化爬蟲流程"""
    logger.info("🧪 測試完整優化爬蟲流程...")
    
    try:
        from crawler.optimized_crawler_manager import OptimizedCrawlerManager
        
        manager = OptimizedCrawlerManager()
        
        # 測試完整流程（只爬取1天資料）
        result = manager.crawl_politician_optimized(
            politician_name="高虹安",
            cutoff_date_str=(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'),
            platforms=['youtube', 'ptt', 'threads'],
            max_workers_per_platform=2  # 減少線程數以加快測試
        )
        
        logger.info("完整優化爬蟲結果:")
        logger.info(f"   成功平台: {result['summary']['successful_platforms']}/{result['summary']['total_platforms']}")
        logger.info(f"   成功率: {result['summary']['success_rate']:.2%}")
        
        # 詳細結果
        for platform, platform_result in result.get('content_crawling', {}).items():
            if platform_result.get('success'):
                count = platform_result.get('count', 0)
                logger.info(f"   ✅ {platform.upper()}: {count} 個項目")
            else:
                error = platform_result.get('error', '未知錯誤')
                logger.info(f"   ❌ {platform.upper()}: {error}")
        
        if result['summary']['successful_platforms'] > 0:
            logger.info("✅ 完整優化爬蟲測試通過")
            return True
        else:
            logger.warning("⚠️ 完整優化爬蟲所有平台都失敗")
            return False
            
    except Exception as e:
        logger.error(f"❌ 完整優化爬蟲測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    logger.info("🚀 開始測試最終修復...")
    
    tests = [
        ("YouTube日期篩選", test_youtube_date_filtering),
        ("Threads URL收集", test_threads_url_collection),
        ("MongoDB增量更新", test_mongodb_incremental_update),
        ("統計增量更新", test_stats_incremental_update),
        ("完整優化爬蟲", test_optimized_crawler_full)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"測試: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"測試 {test_name} 時發生異常: {e}")
            results[test_name] = False
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("最終修復測試總結")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed >= total - 1:  # 允許1個測試失敗
        logger.info("🎉 最終修復基本成功！")
        return 0
    else:
        logger.warning(f"⚠️ 有 {total - passed} 個測試失敗，需要進一步檢查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
