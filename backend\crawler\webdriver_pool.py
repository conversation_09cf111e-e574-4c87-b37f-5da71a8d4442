#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WebDriver 池管理器
提供 Chrome WebDriver 實例池，支援多線程安全操作
"""

import os
import sys
import time
import threading
import subprocess
import platform
from queue import Queue, Empty
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from contextlib import contextmanager

class WebDriverPool:
    """WebDriver 池管理器"""
    
    def __init__(self, max_instances=6, headless=True):
        """
        初始化 WebDriver 池
        
        Args:
            max_instances: 最大 WebDriver 實例數
            headless: 是否使用無頭模式
        """
        self.max_instances = max_instances
        self.headless = headless
        self.pool = Queue(maxsize=max_instances)
        self.active_drivers = set()
        self.lock = threading.Lock()
        self.created_count = 0
        
        # 啟動時清理殘留進程
        self.cleanup_zombie_processes()
        
        print(f"🏊 WebDriver 池初始化完成，最大實例數: {max_instances}")
    
    def cleanup_zombie_processes(self):
        """清理殘留的 Chrome 和 ChromeDriver 進程"""
        try:
            system = platform.system()
            if system == "Windows":
                # Windows 系統清理
                subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], 
                             capture_output=True, check=False)
                subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], 
                             capture_output=True, check=False)
            elif system in ["Linux", "Darwin"]:  # Linux 或 macOS
                subprocess.run(['pkill', '-f', 'chrome'], 
                             capture_output=True, check=False)
                subprocess.run(['pkill', '-f', 'chromedriver'], 
                             capture_output=True, check=False)
            
            print("🧹 殭屍進程清理完成")
            time.sleep(2)  # 等待進程完全結束
            
        except Exception as e:
            print(f"⚠️  進程清理時出錯: {e}")
    
    def _create_driver(self):
        """創建新的 WebDriver 實例"""
        try:
            # 設定 Chrome 選項
            chrome_options = Options()
            
            # 基本設定
            if self.headless:
                chrome_options.add_argument('--headless')
            
            # 性能優化選項
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')  # 禁用擴展
            chrome_options.add_argument('--disable-infobars')    # 禁用信息欄
            chrome_options.add_argument('--disable-notifications')
            chrome_options.add_argument('--disable-background-timer-throttling')
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')
            chrome_options.add_argument('--disable-renderer-backgrounding')
            chrome_options.add_argument('--disable-features=TranslateUI')
            chrome_options.add_argument('--disable-ipc-flooding-protection')
            
            # 靜默日誌
            chrome_options.add_argument('--log-level=3')  # 只有嚴重錯誤才會輸出
            chrome_options.add_argument('--silent')
            chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 記憶體優化
            chrome_options.add_argument('--memory-pressure-off')
            chrome_options.add_argument('--max_old_space_size=4096')
            
            # 網路優化
            chrome_options.add_argument('--aggressive-cache-discard')
            chrome_options.add_argument('--disable-background-networking')
            
            # 建立 WebDriver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 設定超時
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(10)
            
            self.created_count += 1
            print(f"🚗 創建 WebDriver 實例 #{self.created_count}")
            
            return driver
            
        except Exception as e:
            print(f"❌ 創建 WebDriver 失敗: {e}")
            raise
    
    @contextmanager
    def get_driver(self):
        """
        獲取 WebDriver 實例 (上下文管理器)
        
        使用方式:
            with pool.get_driver() as driver:
                driver.get("https://example.com")
        """
        driver = None
        try:
            # 嘗試從池中獲取
            try:
                driver = self.pool.get_nowait()
                print("♻️  從池中獲取 WebDriver")
            except Empty:
                # 池為空，檢查是否可以創建新實例
                with self.lock:
                    current_active = len(self.active_drivers)
                    print(f"🔍 當前活躍驅動數: {current_active}/{self.max_instances}")

                    if current_active < self.max_instances:
                        driver = self._create_driver()
                        print(f"🆕 創建新 WebDriver，總數: {current_active + 1}/{self.max_instances}")
                    else:
                        # 等待可用實例，但設置較短的超時時間
                        print("⏳ 等待可用的 WebDriver...")
                        try:
                            driver = self.pool.get(timeout=30)  # 減少超時時間
                        except Empty:
                            # 如果等待超時，強制清理並重新創建
                            print("⚠️ 等待超時，強制清理WebDriver池...")
                            self._force_cleanup()
                            driver = self._create_driver()
            
            # 記錄活躍驅動
            with self.lock:
                self.active_drivers.add(driver)
            
            yield driver
            
        except Exception as e:
            print(f"❌ WebDriver 使用時出錯: {e}")
            # 如果出錯，嘗試重新創建
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None
            raise
            
        finally:
            # 歸還到池中
            if driver:
                with self.lock:
                    self.active_drivers.discard(driver)
                
                try:
                    # 檢查驅動是否仍然可用
                    driver.current_url  # 簡單檢查
                    self.pool.put_nowait(driver)
                    print("🔄 WebDriver 歸還到池中")
                except:
                    # 驅動已損壞，關閉它
                    try:
                        driver.quit()
                        print("🗑️  損壞的 WebDriver 已關閉")
                    except:
                        pass
    
    def _force_cleanup(self):
        """強制清理所有WebDriver實例"""
        print("🧹 強制清理 WebDriver 池...")

        # 清理池中的實例
        while not self.pool.empty():
            try:
                driver = self.pool.get_nowait()
                driver.quit()
            except:
                pass

        # 清理活躍實例
        with self.lock:
            for driver in list(self.active_drivers):
                try:
                    driver.quit()
                except:
                    pass
            self.active_drivers.clear()

        print("✅ 強制清理完成，重置WebDriver池")

    def close_all(self):
        """關閉所有 WebDriver 實例"""
        print("🛑 關閉所有 WebDriver 實例...")

        # 關閉池中的實例
        while not self.pool.empty():
            try:
                driver = self.pool.get_nowait()
                driver.quit()
            except:
                pass

        # 關閉活躍的實例
        with self.lock:
            for driver in list(self.active_drivers):
                try:
                    driver.quit()
                except:
                    pass

            self.active_drivers.clear()
            self.created_count = 0
    
    def release_all_drivers(self):
        """釋放所有活躍的 WebDriver 資源，但保留池本身"""
        print("🧹 釋放所有活躍的 WebDriver 資源...")
        
        # 關閉活躍的實例
        active_count = 0
        with self.lock:
            active_count = len(self.active_drivers)
            for driver in list(self.active_drivers):
                try:
                    driver.quit()
                except:
                    pass
            
            self.active_drivers.clear()
        
        print(f"🧹 已釋放 {active_count} 個活躍的 WebDriver 實例")
        
        # 清空池並創建新的空池
        while not self.pool.empty():
            try:
                driver = self.pool.get_nowait()
                driver.quit()
            except:
                pass
        
        # 注意：活躍驅動已在上面清理，無需再次清理
        
        # 最終清理
        self.cleanup_zombie_processes()
        print("✅ 所有 WebDriver 實例已關閉")
        
        return {"released_drivers": active_count, "pool_status": self.get_status()}
    
    def get_status(self):
        """獲取池狀態"""
        with self.lock:
            return {
                'pool_size': self.pool.qsize(),
                'active_drivers': len(self.active_drivers),
                'total_created': self.created_count,
                'max_instances': self.max_instances
            }

# 全域池實例
_global_pool = None
_pool_lock = threading.Lock()

def get_global_pool(max_instances=5, headless=True):
    """獲取全域 WebDriver 池"""
    global _global_pool
    with _pool_lock:
        if _global_pool is None:
            _global_pool = WebDriverPool(max_instances=max_instances, headless=headless)
        return _global_pool

def close_global_pool():
    """關閉全域池"""
    global _global_pool
    with _pool_lock:
        if _global_pool:
            _global_pool.close_all()
            _global_pool = None

# 測試用
if __name__ == "__main__":
    import time
    
    print("🧪 測試 WebDriver 池...")
    
    # 創建池
    pool = WebDriverPool(max_instances=6, headless=True)
    
    # 測試獲取和歸還
    def test_worker(worker_id):
        with pool.get_driver() as driver:
            print(f"Worker {worker_id}: 獲取到 WebDriver")
            driver.get("https://www.google.com")
            print(f"Worker {worker_id}: 頁面標題 = {driver.title}")
            time.sleep(2)
            print(f"Worker {worker_id}: 完成")
    
    # 並行測試
    import threading
    threads = []
    for i in range(5):
        t = threading.Thread(target=test_worker, args=(i,))
        threads.append(t)
        t.start()
    
    for t in threads:
        t.join()
    
    print(f"池狀態: {pool.get_status()}")
    pool.close_all()
    print("測試完成")
