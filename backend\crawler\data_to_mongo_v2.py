#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資料庫存儲模組（重構版）

功能：
1. 增量更新 crawler_data collection (用戶詳細資料)
2. 重新計算 legislators collection (立委統計資料)
3. 支援每日增量更新流程
4. 動態時間統計功能

MongoDB 結構：
- crawler_data: 每筆 = 一個用戶對一個立委的資料
- legislators: 每筆 = 一個立委的完整統計（自動重新計算）
"""

import os
import json
import sys
import traceback
from datetime import datetime
from pymongo import MongoClient

# 添加父目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 設定路徑 - 使用 processed 資料夾結構
PROCESSED_DIR = os.path.join(current_dir, 'processed')
USER_DATA_DIR = os.path.join(PROCESSED_DIR, 'user_data')
FINAL_DATA_DIR = os.path.join(PROCESSED_DIR, 'final_data')

# 導入統計模組
try:
    from .legislator_stats import LegislatorStatsUpdater
except ImportError:
    # 如果相對導入失敗，嘗試直接導入
    from legislator_stats import LegislatorStatsUpdater

# MongoDB 資料結構定義
MONGODB_SCHEMAS = {
    "crawler_data": {
        "description": "爬蟲收集的用戶資料",
        "structure": {
            "_id": "ObjectId (自動生成)",
            "legislator_name": "str - 立委姓名",
            "user_name": "str - 用戶名稱",
            "latest_comment_time": "datetime - 最新留言時間",
            "total_comments": "int - 總留言數",
            "platforms": "dict - 各平台留言統計",
            "sentiment_summary": "dict - 情感分析摘要",
            "emotion_summary": "dict - 情緒分析摘要",
            "crawler_data": "list - 完整爬蟲資料",
            "last_updated": "datetime - 最後更新時間",
            "data_source": "str - 資料來源標識"
        },
        "indexes": [
            {"legislator_name": 1},
            {"user_name": 1},
            {"legislator_name": 1, "user_name": 1},
            {"last_updated": -1}
        ]
    },
    
    "legislators": {
        "description": "立委統計資料（預計算的時間序列數據）",
        "structure": {
            "_id": "ObjectId (自動生成)",
            "legislator_name": "str - 立委姓名",
            "last_updated": "datetime - 最後更新時間",
            "total_comments": "int - 總留言數",
            "total_users": "int - 總用戶數",
            "date_range": {
                "start": "datetime - 資料開始時間",
                "end": "datetime - 資料結束時間"
            },
            "platform_distribution": "dict - 平台分佈統計",
            "sentiment_distribution": "dict - 情感分佈統計",
            "emotion_distribution": "dict - 情緒分佈統計",
            "time_periods": {
                "1w": "dict - 最近一週統計",
                "2w": "dict - 最近兩週統計", 
                "1m": "dict - 最近一個月統計",
                "3m": "dict - 最近三個月統計",
                "6m": "dict - 最近六個月統計",
                "1y": "dict - 最近一年統計"
            }
        },
        "indexes": [
            {"legislator_name": 1},
            {"last_updated": -1}
        ]
    },
    
    "daily_snapshots": {
        "description": "每日增量更新的快照數據，用於趨勢分析",
        "structure": {
            "_id": "ObjectId (自動生成)",
            "legislator_name": "str - 立委姓名",
            "snapshot_date": "datetime - 快照日期",
            "total_comments": "int - 總留言數",
            "total_users": "int - 總用戶數",
            "new_comments": "int - 新增留言數",
            "new_users": "int - 新增用戶數",
            "platform_distribution": "dict - 平台分佈統計",
            "sentiment_distribution": "dict - 情感分佈統計",
            "emotion_distribution": "dict - 情緒分佈統計"
        },
        "indexes": [
            {"legislator_name": 1, "snapshot_date": -1},
            {"snapshot_date": -1}
        ]
    }
}


class DataToMongo:
    """增量更新資料庫存儲管理器"""
    
    def __init__(self, mongodb_uri=None, database_name=None):
        # 使用預設值，配合用戶的實際資料庫名稱
        self.mongodb_uri = mongodb_uri or "mongodb://localhost:27017/"
        self.database_name = database_name or "legislator_recall"
        
        try:
            # 直接使用基本URI，不要在URI中包含資料庫名
            if self.mongodb_uri.endswith('/'):
                base_uri = self.mongodb_uri.rstrip('/')
            else:
                base_uri = self.mongodb_uri
            
            # 建立連接
            self.client = MongoClient(base_uri, serverSelectionTimeoutMS=5000)
            
            # 測試連接
            self.client.admin.command('ping')
            
            # 獲取資料庫實例
            self.db = self.client[self.database_name]
            self.crawler_data = self.db.crawler_data
            self.legislators = self.db.legislators
            self.daily_snapshots = self.db.daily_snapshots
            
            # 初始化統計更新器 - 傳遞database而不是client
            try:
                self.stats_updater = LegislatorStatsUpdater(self.db)
            except Exception as stats_e:
                print(f"⚠️ 統計更新器初始化失敗: {stats_e}")
                self.stats_updater = None
            
            print(f"✅ MongoDB 連接成功")
            print(f"   URI: {base_uri}")
            print(f"   資料庫: {self.database_name}")
            print(f"   可用 Collections: {self.db.list_collection_names()}")
        except Exception as e:
            print(f"⚠️ MongoDB 連接失敗: {e}")
            print("使用模擬模式（不實際存儲資料）")
            self.client = None
            self.db = None
            self.crawler_data = None
            self.legislators = None
            self.stats_updater = None
        
    def store_crawler_data(self, politician_name, is_incremental=True):
        """
        從檔案系統讀取並存儲立委的爬蟲資料到 MongoDB
        支援增量處理：只處理新的或更新的用戶資料
        
        Args:
            politician_name: 立委姓名
            is_incremental: 是否為增量更新（True=增量，False=完整重建）
        
        Returns:
            bool: 是否成功存儲
        """
        try:
            # 如果沒有 MongoDB 連接，使用模擬模式
            if self.client is None or self.db is None:
                print(f"🔄 模擬模式：{politician_name} 的資料已處理（未實際存儲到 MongoDB）")
                return True
            
            # 直接處理增量更新
            result = self._process_legislator_data(politician_name, is_incremental)
            
            # 如果資料處理成功，更新統計數據
            if result.get('success', False) and self.stats_updater:
                print(f"📊 更新 {politician_name} 的動態時間統計...")
                stats_success = self.stats_updater.update_legislator_stats(
                    politician_name, 
                    force_recalculate=not is_incremental
                )
                
                if stats_success:
                    print(f"✅ {politician_name} 統計數據更新完成")
                    
                    # 如果是每日運行或完整重建，創建每日快照
                    if datetime.now().hour < 4 or not is_incremental:  # 假設每日自動化是在凌晨執行
                        print(f"📸 為 {politician_name} 創建每日統計快照...")
                        self.create_daily_snapshot(politician_name)
                else:
                    print(f"⚠️  {politician_name} 統計數據更新失敗")
            
            return result.get('success', False)
            
        except Exception as e:
            print(f"❌ 存儲 {politician_name} 資料到 MongoDB 時出錯: {e}")
            print(traceback.format_exc())
            return False
    
    def store_legislator_data(self, politician_name, platform_data, analysis_data=None):
        """
        存儲立委資料到 MongoDB（舊版方法，保留兼容性）
        
        Args:
            politician_name: 立委姓名
            platform_data: 平台資料 (dict)
            analysis_data: 分析資料 (可選)
        
        Returns:
            bool: 是否成功存儲
        """
        # 重定向到新的增量更新方法
        return self.store_crawler_data(politician_name, is_incremental=False)
    
    def get_crawler_data(self, politician_name=None, user_name=None):
        """
        從 MongoDB 獲取爬蟲資料
        
        Args:
            politician_name: 立委姓名 (可選)
            user_name: 用戶名稱 (可選)
        
        Returns:
            list: 符合條件的資料
        """
        try:
            if not self.crawler_data:
                return []
            
            query = {}
            if politician_name:
                query['留言立委'] = politician_name
            if user_name:
                query['用戶名'] = user_name
            
            return list(self.crawler_data.find(query))
            
        except Exception as e:
            print(f"❌ 獲取資料時出錯: {e}")
            return []
    
    def get_legislator_statistics(self, politician_name):
        """
        從 MongoDB 獲取立委統計資料
        
        Args:
            politician_name: 立委姓名
        
        Returns:
            dict or None: 立委統計資料
        """
        try:
            if not self.legislators:
                return None
            
            return self.legislators.find_one({'name': politician_name})
            
        except Exception as e:
            print(f"❌ 獲取立委統計資料時出錯: {e}")
            return None
    
    def backup_database(self, backup_dir):
        """
        備份資料庫
        
        Args:
            backup_dir: 備份目錄
        
        Returns:
            bool: 是否成功備份
        """
        try:
            if not self.client:
                print("MongoDB 未連接，無法備份")
                return False
                
            os.makedirs(backup_dir, exist_ok=True)
            
            # 導出 crawler_data
            crawler_data = list(self.crawler_data.find())
            legislators_data = list(self.legislators.find())
            
            # 處理 ObjectId 和日期
            for item in crawler_data + legislators_data:
                if '_id' in item:
                    item['_id'] = str(item['_id'])
                for key, value in item.items():
                    if isinstance(value, datetime):
                        item[key] = value.isoformat()
            
            # 保存到檔案
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            crawler_backup = os.path.join(backup_dir, f'crawler_data_{timestamp}.json')
            with open(crawler_backup, 'w', encoding='utf-8') as f:
                json.dump(crawler_data, f, ensure_ascii=False, indent=2)
            
            legislators_backup = os.path.join(backup_dir, f'legislators_{timestamp}.json')
            with open(legislators_backup, 'w', encoding='utf-8') as f:
                json.dump(legislators_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 資料庫已備份到: {backup_dir}")
            print(f"   📄 crawler_data: {len(crawler_data)} 筆")
            print(f"   📄 legislators: {len(legislators_data)} 筆")
            return True
            
        except Exception as e:
            print(f"❌ 備份資料庫時出錯: {e}")
            return False
    
    def process_legislators(self, legislators: list) -> dict:
        """
        批量處理多位立委的數據存儲

        Args:
            legislators: 立委名稱列表

        Returns:
            處理結果統計
        """
        result = {
            'success': True,
            'processed_count': 0,
            'failed_count': 0,
            'errors': []
        }

        for legislator in legislators:
            try:
                success = self.store_crawler_data(legislator, is_incremental=True)
                if success:
                    result['processed_count'] += 1
                    print(f"✅ {legislator}: 數據存儲成功")
                else:
                    result['failed_count'] += 1
                    result['errors'].append(f"{legislator}: 存儲失敗")
                    print(f"❌ {legislator}: 數據存儲失敗")
            except Exception as e:
                result['failed_count'] += 1
                result['errors'].append(f"{legislator}: {str(e)}")
                print(f"❌ {legislator}: 存儲出錯 - {e}")

        if result['failed_count'] > 0:
            result['success'] = False

        print(f"📊 批量存儲完成: 成功 {result['processed_count']} 位，失敗 {result['failed_count']} 位")
        return result

    def close(self):
        """關閉資料庫連接"""
        if self.client:
            self.client.close()

    def _process_legislator_data(self, politician_name, is_incremental=True):
        """
        處理立委的資料並存儲到 MongoDB
        
        Args:
            politician_name: 立委姓名
            is_incremental: 是否為增量更新
            
        Returns:
            dict: 處理結果
        """
        try:
            print(f"🔄 {'增量' if is_incremental else '完整'}處理 {politician_name} 的資料...")
            
            # 讀取用戶分析結果 - 使用新的路徑結構
            analysis_file = os.path.join(FINAL_DATA_DIR, f"{politician_name}_使用者分析.json")
            user_data_file = os.path.join(USER_DATA_DIR, f"{politician_name}_gemini_format.json")
            
            if not os.path.exists(analysis_file):
                print(f"❌ 找不到分析檔案: {analysis_file}")
                return {'success': False, 'reason': '分析檔案不存在'}
            
            if not os.path.exists(user_data_file):
                print(f"❌ 找不到用戶資料檔案: {user_data_file}")
                return {'success': False, 'reason': '用戶資料檔案不存在'}
            
            # 讀取資料
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)
            
            with open(user_data_file, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
            
            # 處理每個用戶的資料 - 新格式：以用戶ID為鍵的字典
            processed_count = 0
            collection = self.db.crawler_data

            # analysis_data 現在是 {user_id: {comments: [...], latest_date: "...", stance: "..."}} 格式
            for user_name, analysis_item in analysis_data.items():
                if not user_name or not isinstance(analysis_item, dict):
                    continue

                # 獲取用戶的完整資料
                user_comments = analysis_item.get('comments', [])

                # 處理每個評論，創建單獨的文檔
                for comment in user_comments:
                    # 構建資料文檔
                    document = {
                        'name': politician_name,  # 立委姓名
                        'user_name': user_name,   # 用戶名
                        '情感標籤': comment.get('情感標籤', ''),
                        '情緒': comment.get('情緒', ''),
                        '標題': comment.get('標題', ''),
                        '留言內容': comment.get('留言內容', ''),
                        '日期': comment.get('日期', ''),
                        'source': comment.get('source', ''),
                        'stance': analysis_item.get('stance', ''),  # 用戶立場
                        'last_updated': datetime.now(),
                        'data_source': 'gemini_analysis'
                    }

                    # 檢查是否已存在相同的記錄
                    existing_doc = collection.find_one({
                        'name': politician_name,
                        'user_name': user_name,
                        '留言內容': comment.get('留言內容', ''),
                        'source': comment.get('source', '')
                    })

                    if existing_doc and not is_incremental:
                        # 如果不是增量更新且記錄已存在，跳過
                        continue
                    elif existing_doc and is_incremental:
                        # 增量更新：更新現有記錄
                        collection.update_one(
                            {'_id': existing_doc['_id']},
                            {'$set': document}
                        )
                        processed_count += 1
                    else:
                        # 插入新記錄
                        collection.insert_one(document)
                        processed_count += 1
            
            print(f"✅ {politician_name}: 處理了 {processed_count} 個評論記錄")
            return {'success': True, 'processed_count': processed_count}
            
        except Exception as e:
            print(f"❌ 處理 {politician_name} 資料時出錯: {e}")
            print(traceback.format_exc())
            return {'success': False, 'error': str(e)}

class MongoDBManager:
    """統一的MongoDB管理器"""
    
    def __init__(self, uri: str = None, database: str = None):
        # 使用預設值或從環境變數獲取
        self.uri = uri or os.getenv('MONGODB_URI', 'mongodb://localhost:27017')
        self.database_name = database or os.getenv('MONGODB_DBNAME', 'legislator_recall')
        
        self.client = None
        self.db = None
        self._connect()
    
    def _connect(self):
        """建立 MongoDB 連接"""
        try:
            self.client = MongoClient(self.uri)
            self.db = self.client[self.database_name]
            
            # 測試連接
            self.client.admin.command('ping')
            print(f"✅ MongoDB 連接成功")
            print(f"   URI: {self.uri}")
            print(f"   資料庫: {self.database_name}")
            print(f"   Collections: {self.db.list_collection_names()}")
            
        except Exception as e:
            print(f"❌ MongoDB 連接失敗: {e}")
            print("使用模擬模式運行")
            self.client = None
            self.db = None
    
    def get_database(self):
        """獲取資料庫實例"""
        return self.db
    
    def get_collection(self, collection_name: str):
        """獲取集合實例"""
        if self.db:
            return self.db[collection_name]
        return None
    
    def ensure_indexes(self):
        """確保索引存在"""
        if not self.db:
            return
        
        try:
            for collection_name, schema in MONGODB_SCHEMAS.items():
                collection = self.db[collection_name]
                
                # 建立索引
                for index_def in schema.get("indexes", []):
                    try:
                        collection.create_index([(k, v) for k, v in index_def.items()])
                    except Exception as e:
                        print(f"警告: 無法建立索引 {index_def} 在 {collection_name}: {e}")
            
            print("✅ 索引檢查完成")
        except Exception as e:
            print(f"❌ 索引建立失敗: {e}")
    
    def create_daily_snapshot(self, politician_name):
        """
        為立委創建每日統計數據快照，用於趨勢分析
        
        Args:
            politician_name: 立委姓名
            
        Returns:
            bool: 是否成功創建快照
        """
        if not self.db or not self.daily_snapshots:
            print(f"⚠️ 無法創建每日快照，資料庫連接不存在")
            return False
            
        try:
            # 獲取當前日期
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            
            # 查詢是否已存在今日快照
            existing = self.daily_snapshots.find_one({
                "legislator_name": politician_name,
                "snapshot_date": today
            })
            
            if existing:
                print(f"📊 {politician_name} 今日({today.strftime('%Y-%m-%d')})快照已存在，略過")
                return True
                
            # 獲取上次快照
            last_snapshot = self.daily_snapshots.find_one(
                {"legislator_name": politician_name},
                sort=[("snapshot_date", -1)]
            )
            
            # 獲取立委當前統計數據
            current_stats = self.legislators.find_one({"legislator_name": politician_name})
            if not current_stats:
                print(f"⚠️ 無法創建每日快照，找不到 {politician_name} 的統計數據")
                return False
                
            # 計算增量
            prev_comments = 0
            prev_users = 0
            if last_snapshot:
                prev_comments = last_snapshot.get("total_comments", 0)
                prev_users = last_snapshot.get("total_users", 0)
                
            # 創建快照
            snapshot = {
                "legislator_name": politician_name,
                "snapshot_date": today,
                "total_comments": current_stats.get("total_comments", 0),
                "total_users": current_stats.get("total_users", 0),
                "new_comments": current_stats.get("total_comments", 0) - prev_comments,
                "new_users": current_stats.get("total_users", 0) - prev_users,
                "platform_distribution": current_stats.get("platform_distribution", {}),
                "sentiment_distribution": current_stats.get("sentiment_distribution", {}),
                "emotion_distribution": current_stats.get("emotion_distribution", {})
            }
            
            # 儲存快照
            result = self.daily_snapshots.insert_one(snapshot)
            
            if result.inserted_id:
                print(f"✅ {politician_name} 每日快照創建成功 (日期: {today.strftime('%Y-%m-%d')})")
                print(f"   新增留言: {snapshot['new_comments']}, 新增用戶: {snapshot['new_users']}")
                return True
            else:
                print(f"❌ {politician_name} 每日快照創建失敗")
                return False
                
        except Exception as e:
            print(f"❌ 創建每日快照時發生錯誤: {e}")
            print(traceback.format_exc())
            return False
            
    def close(self):
        """關閉連接"""
        if self.client:
            self.client.close()

if __name__ == '__main__':
    # 測試用
    db_manager = DataToMongo()
    
    # 測試增量更新
    success = db_manager.store_crawler_data('葉元之', is_incremental=True)
    print(f"增量更新測試: {'成功' if success else '失敗'}")
    
    # 測試資料查詢
    data = db_manager.get_crawler_data('葉元之')
    print(f"查詢測試: 找到 {len(data)} 筆資料")
    
    # 測試每日快照創建
    snapshot_success = db_manager.create_daily_snapshot('葉元之')
    print(f"每日快照創建測試: {'成功' if snapshot_success else '失敗'}")
    
    # 關閉連接
    db_manager.close()
