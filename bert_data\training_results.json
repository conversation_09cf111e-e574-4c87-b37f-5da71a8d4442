{"100": {"sentiment": {"accuracy": 0.6, "eval_results": {"eval_loss": 0.7047551870346069, "eval_runtime": 0.2255, "eval_samples_per_second": 399.192, "eval_steps_per_second": 26.613, "epoch": 3.0}, "report": "              precision    recall  f1-score   support\n\n    NEGATIVE       0.82      0.52      0.63        60\n    POSITIVE       0.44      0.77      0.56        30\n\n    accuracy                           0.60        90\n   macro avg       0.63      0.64      0.60        90\nweighted avg       0.69      0.60      0.61        90\n"}, "emotion": {"accuracy": 0.35555555555555557, "eval_results": {"eval_loss": 1.8931642770767212, "eval_runtime": 1.7492, "eval_samples_per_second": 51.452, "eval_steps_per_second": 3.43, "epoch": 3.0}, "report": "              precision    recall  f1-score   support\n\n         joy       0.36      1.00      0.52        32\n       trust       0.00      0.00      0.00         6\n        fear       0.00      0.00      0.00        12\n    surprise       0.00      0.00      0.00        10\n     sadness       0.00      0.00      0.00        10\n     disgust       0.00      0.00      0.00         4\n       anger       0.00      0.00      0.00        13\nanticipation       0.00      0.00      0.00         3\n\n    accuracy                           0.36        90\n   macro avg       0.04      0.12      0.07        90\nweighted avg       0.13      0.36      0.19        90\n"}}, "200": {"sentiment": {"accuracy": 0.7111111111111111, "eval_results": {"eval_loss": 0.6279239058494568, "eval_runtime": 0.2224, "eval_samples_per_second": 404.678, "eval_steps_per_second": 26.979, "epoch": 3.0}, "report": "              precision    recall  f1-score   support\n\n    NEGATIVE       0.72      0.92      0.81        60\n    POSITIVE       0.64      0.30      0.41        30\n\n    accuracy                           0.71        90\n   macro avg       0.68      0.61      0.61        90\nweighted avg       0.70      0.71      0.68        90\n"}, "emotion": {"accuracy": 0.35555555555555557, "eval_results": {"eval_loss": 1.9122799634933472, "eval_runtime": 3.0693, "eval_samples_per_second": 29.322, "eval_steps_per_second": 1.955, "epoch": 3.0}, "report": "              precision    recall  f1-score   support\n\n         joy       0.36      1.00      0.52        32\n       trust       0.00      0.00      0.00         6\n        fear       0.00      0.00      0.00        12\n    surprise       0.00      0.00      0.00        10\n     sadness       0.00      0.00      0.00        10\n     disgust       0.00      0.00      0.00         4\n       anger       0.00      0.00      0.00        13\nanticipation       0.00      0.00      0.00         3\n\n    accuracy                           0.36        90\n   macro avg       0.04      0.12      0.07        90\nweighted avg       0.13      0.36      0.19        90\n"}}, "300": {"sentiment": {"accuracy": 0.6666666666666666, "eval_results": {"eval_loss": 0.6205417513847351, "eval_runtime": 0.2257, "eval_samples_per_second": 398.832, "eval_steps_per_second": 26.589, "epoch": 3.0}, "report": "              precision    recall  f1-score   support\n\n    NEGATIVE       0.67      0.97      0.79        60\n    POSITIVE       0.50      0.07      0.12        30\n\n    accuracy                           0.67        90\n   macro avg       0.59      0.52      0.46        90\nweighted avg       0.62      0.67      0.57        90\n"}, "emotion": {"accuracy": 0.34444444444444444, "eval_results": {"eval_loss": 1.9075236320495605, "eval_runtime": 0.85, "eval_samples_per_second": 105.878, "eval_steps_per_second": 7.059, "epoch": 3.0}, "report": "              precision    recall  f1-score   support\n\n         joy       0.37      0.88      0.52        32\n       trust       0.00      0.00      0.00         6\n        fear       0.00      0.00      0.00        12\n    surprise       0.17      0.20      0.18        10\n     sadness       0.50      0.10      0.17        10\n     disgust       0.00      0.00      0.00         4\n       anger       0.00      0.00      0.00        13\nanticipation       0.00      0.00      0.00         3\n\n    accuracy                           0.34        90\n   macro avg       0.13      0.15      0.11        90\nweighted avg       0.21      0.34      0.22        90\n"}}}