from flask import Blueprint, jsonify, request, abort
import os
from collections import Counter
import jieba
from asseccerios import get_legislator_by_name, get_crawler_data, db
from datetime import datetime
from bson.objectid import ObjectId
from urllib.parse import urlparse, parse_qs
import re
from datetime import datetime, timedelta
import time
from functools import lru_cache
import multiprocessing
from multiprocessing import Pool
import math
import sys
import os

# 添加當前目錄到路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

try:
    from analyze_data import analyze_time_range_data, debug_data_sentiment_match
    from process_data_chunk import process_data_chunk
except ImportError as e:
    print(f"導入模塊失敗: {e}")
    # 為了防止程序崩潰，定義臨時函數
    def analyze_time_range_data(crawler_data, start_date=None, end_date=None):
        print("❌ 未能導入 analyze_time_range_data 函數")
        return {}
        
    def debug_data_sentiment_match(*args, **kwargs):
        print("❌ 未能導入 debug_data_sentiment_match 函數")
        
    def process_data_chunk(*args, **kwargs):
        print("❌ 未能導入 process_data_chunk 函數")
        return {}

# 創建一個 Flask 藍圖，命名為 'legislator_app'
legislator_app = Blueprint('legislator_app', __name__)

# 全局變量，用於緩存停用詞和jieba設置
_stopwords_cache = None
_jieba_initialized = False

def load_stopwords():
    """載入停用詞文件，使用緩存提升效率"""
    global _stopwords_cache
    if _stopwords_cache is not None:
        return _stopwords_cache

    stopwords_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'stopwords.txt')
    stopwords = set()

    try:
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):  # 忽略空行和註釋
                    stopwords.add(line)
    except FileNotFoundError:
        print(f"⚠️ 停用詞文件未找到: {stopwords_file}")
        # 使用基本停用詞作為後備
        stopwords = {'的', '了', '是', '我', '也', '和', '就', '都', '不', '在', '會', '要', '很'}
    except Exception as e:
        print(f"❌ 載入停用詞失敗: {e}")
        stopwords = set()

    _stopwords_cache = stopwords
    return stopwords

def initialize_jieba():
    """初始化jieba，只執行一次"""
    global _jieba_initialized
    if _jieba_initialized:
        return

    try:
        # 載入自定義詞典
        user_dict_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'user_dict.txt')
        if os.path.exists(user_dict_file):
            jieba.load_userdict(user_dict_file)
        else:
            print(f"⚠️ 自定義詞典未找到: {user_dict_file}")

        # 設置jieba為精確模式
        try:
            import platform
            if platform.system() != 'Windows':
                jieba.enable_parallel(4)  # 只在非Windows系統啟用並行
        except Exception as e:
            print(f"⚠️ Jieba並行模式啟用失敗: {e}")
    except Exception as e:
        print(f"❌ Jieba初始化失敗: {e}")

    _jieba_initialized = True

def extract_meaningful_keywords(text_content, min_count=2):
    """
    極速版關鍵字提取函數，使用基本jieba分詞以提高速度
    省略詞性標註以加快處理速度
    """
    if not text_content:
        return []

    # 全局常量和配置，避免重複創建
    global _stopwords_cache
    if _stopwords_cache is None:
        _stopwords_cache = load_stopwords()
    stopwords = _stopwords_cache

    # 快速分詞模式：使用基本jieba分詞而非詞性標註
    # 初始化jieba（如果尚未初始化）
    initialize_jieba()
    
    # 清理文本：僅保留必要字符並縮短文本
    # 將文本截斷為前1000個字符，這對詞雲足夠了
    if len(text_content) > 1000:
        text_content = text_content[:1000]
    
    # 簡化的正則表達式清理
    cleaned_text = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', text_content)
    
    # 使用標準jieba而非詞性標註
    words = jieba.cut(cleaned_text, cut_all=False)
    
    # 預先編譯常用正則表達式以加速處理
    english_re = re.compile(r'^[a-zA-Z]+$')
    
    # 常見無用詞，包含常見人名姓氏
    common_excluded = {'不是', '不要', '不到', '不出', '案不', '王', '李', '張', '劉', '陳', '楊', '黃', '趙', '周', '吳', '林', '郭', '何', '高', '羅', '鄭', '梁', '謝', '宋', '唐', '許', '韓', '馮', '鄧', '曹', '彭', '曾', '蕭', '田', '董', '袁', '潘', '蔣', '蔡', '余', '杜', '葉', '程', '魏', '蘇', '呂', '丁', '任', '沈', '姚', '盧', '姜', '崔', '鐘', '譚', '陸', '汪', '范', '金', '石', '廖', '賈', '夏', '韋', '方', '白', '鄒', '孟', '熊', '秦', '邱', '江', '尹', '薛', '閻', '段', '雷', '侯', '龍', '史', '陶', '黎', '賀', '顧', '毛', '郝', '龔', '邵', '萬', '錢', '嚴', '覃', '武', '戴', '莫', '孔', '向', '湯'}
    
    # 簡化的過濾邏輯：減少條件檢查
    word_counter = Counter()
    for word in words:
        word = word.strip()
        
        # 合併多條件檢查為一條
        if (len(word) < 2 or word in stopwords or word in common_excluded or 
            word.isdigit() or (english_re.match(word) and len(word) < 3)):
            continue
            
        word_counter[word] += 1
    
    # 直接返回最高頻的詞，使用更高的閾值以減少結果集
    return word_counter.most_common(100)



# 全局變量，用於追踪索引是否已經初始化
_indexes_initialized = False

def ensure_database_indexes(is_server_startup=False):
    global _indexes_initialized
    
    # 如果索引已經初始化過且不是伺服器啟動時調用，則跳過
    # 重要：不要在每個請求中都執行索引創建
    if _indexes_initialized:
        if not is_server_startup:
            print("⏩ 索引已初始化，跳過重複初始化")
        return
    
    try:
        if db is None:
            print("❌ 數據庫連接不可用，無法創建索引")
            return

        # 首先初始化訪問統計集合（如果存在）
        try:
            # 檢查是否處於 Railway 或 Render 環境
            is_cloud = os.environ.get('RAILWAY_ENVIRONMENT') or os.environ.get('RAILWAY_URI') or os.environ.get('RENDER')
            if is_cloud:
                # 嘗試導入訪問統計初始化函數
                from src.visitor_counter import ensure_visitor_counter_initialized
                print("🔄 正在初始化訪問統計集合...")
                result = ensure_visitor_counter_initialized()
                if result:
                    print("✅ 訪問統計集合已成功初始化")
                else:
                    print("ℹ️ 訪問統計集合已存在，無需初始化")
        except Exception as e:
            print(f"⚠️ 初始化訪問統計時出錯: {e}")
            # 繼續執行後續索引創建，不因訪問統計初始化失敗而中斷

        _indexes_initialized = True
        
        crawler_col = db['crawler_data']
        legislators_col = db['legislators']

        # 檢查當前索引
        crawler_indexes = crawler_col.index_information()
        legislators_indexes = legislators_col.index_information()
        
        print(f"目前 crawler_data 集合有 {len(crawler_indexes)} 個索引")
        print(f"目前 legislators 集合有 {len(legislators_indexes)} 個索引")
        
        # 處理缺失索引的情況
        if '_id_' not in crawler_indexes:
            print("警告: crawler_data 集合缺少基本的 _id 索引，這非常不尋常")
            try:
                print("嘗試創建 _id 索引...")
                crawler_col.create_index([("_id", 1)], background=True)
            except Exception as id_err:
                print(f"創建 _id 索引失敗: {id_err}")
                
        if '_id_' not in legislators_indexes:
            print("警告: legislators 集合缺少基本的 _id 索引，這非常不尋常")
            try:
                print("嘗試創建 _id 索引...")
                legislators_col.create_index([("_id", 1)], background=True)
            except Exception as id_err:
                print(f"創建 _id 索引失敗: {id_err}")        # 無論在什麼環境下，都創建所有必要的索引
        # 這確保在本地開發環境和生產環境中都有相同的索引
        print("正在創建所有必要的索引...")
        
        # 為 legislators 集合創建索引 (先處理小集合)
        try:
            # 1. 名稱索引
            print("創建 legislators 名稱索引...")
            legislators_col.create_index([("name", 1)], background=True, name="name_1")
            
            # 短暫暫停，避免資源緊張
            time.sleep(1)
            
            # 2. 黨派索引（如果有此欄位）
            if legislators_col.find_one({"黨派": {"$exists": True}}):
                print("創建黨派索引...")
                legislators_col.create_index([("黨派", 1)], background=True, name="party_1")
                time.sleep(1)
            
            # 3. 地區索引（如果有此欄位）
            if legislators_col.find_one({"地區": {"$exists": True}}):
                print("創建地區索引...")
                legislators_col.create_index([("地區", 1)], background=True, name="region_1")
                time.sleep(1)
        except Exception as e:
            print(f"創建 legislators 索引失敗: {e}")
        
        # 暫停 3 秒，讓服務器有時間恢復資源
        print("暫停 3 秒，讓服務器有時間處理前面的索引...")
        time.sleep(3)
        # 為 crawler_data 集合創建索引 (主要集合)
        # 按優先級順序逐個創建索引，出錯時繼續嘗試其他索引
        priority_indexes = [
            # 1. 名稱+日期複合索引 - 最常用的查詢
            {"fields": [("name", 1), ("日期", 1)], "name": "name_date_1"},
            # 2. 日期索引 - 時間範圍查詢
            {"fields": [("日期", 1)], "name": "date_1"},
            # 3. 名稱索引
            {"fields": [("name", 1)], "name": "name_1"},
            # 4. 名稱+情感標籤複合索引
            {"fields": [("name", 1), ("情感標籤", 1)], "name": "name_sentiment_1"},
            # 5. 情感標籤索引
            {"fields": [("情感標籤", 1)], "name": "sentiment_1"},
            {"fields": [("日期", 1), ("情感標籤", 1)], "name": "date_sentiment_1"},
            {"fields": [("日期", 1), ("情緒", 1)], "name": "date_emotion_1"},
            {"fields": [("情緒", 1)], "name": "emotion_1"},
        ]
        for idx, index_info in enumerate(priority_indexes):
            try:
                # 檢查索引是否已存在（即使名稱不同）
                exists = False
                for existing_idx_name, existing_idx_info in crawler_indexes.items():
                    existing_fields = [(field, direction) for field, direction in existing_idx_info['key']]
                    if existing_fields == index_info["fields"]:
                        print(f"索引 {index_info['name']} 的欄位組合已存在，使用名稱：{existing_idx_name}")
                        exists = True
                        break
                
                if not exists:
                    print(f"創建 crawler_data 索引 {idx+1}/{len(priority_indexes)}: {index_info['name']}...")
                    crawler_col.create_index(index_info["fields"], background=True, name=index_info["name"])
                # 每創建一個索引後暫停，避免資源緊張
                time.sleep(2)            
            except Exception as e:
                print(f"創建索引 {index_info['name']} 失敗: {e}")
                # 發生錯誤時繼續嘗試其他索引
                continue
            
        
        # 索引初始化已在函數開始時設置
        print("✅ 索引初始化標記已設置，不會重複創建索引")
        
    except Exception as e:
        print(f"❌ 數據庫索引創建失敗: {e}")

@lru_cache(maxsize=128)
def get_cached_legislator_data(identifier):
    """緩存立委基本數據查詢"""
    try:
        legislators_col = db['legislators']
        return legislators_col.find_one({'name': identifier})
    except Exception as e:
        print(f"❌ 查詢立委數據失敗: {e}")
        return None

def get_crawler_data_optimized(identifier, start_date=None, end_date=None, limit=None):
    """
    優化的爬蟲數據查詢
    
    策略:
    1. 使用索引加速查詢 (name + 日期)
    2. 只選擇需要的字段
    3. 分批處理大量數據
    4. 使用索引排序
    
    參數:
        identifier: 立委識別符 (通常是姓名)
        start_date: 開始日期 (YYYY-MM-DD 格式)
        end_date: 結束日期 (YYYY-MM-DD 格式)
        limit: 記錄數量限制 (None表示不限制)
        
    返回:
        list: 符合條件的記錄列表
    """
    try:
        if db is None:
            print("❌ 數據庫連接不可用")
            return []
            
        crawler_col = db['crawler_data']

        # 計時開始
        start_time = time.time()

        # 構建查詢條件 - 優先精確匹配名稱
        query = {'name': identifier}
        test_count = crawler_col.count_documents(query)
        
        if test_count == 0:
            # 嘗試模糊匹配名稱
            query = {'name': {'$regex': f'^{identifier}$', '$options': 'i'}}
            test_count = crawler_col.count_documents(query)
            
            if test_count == 0:
                # 嘗試更寬鬆的模糊匹配
                query = {'name': {'$regex': identifier, '$options': 'i'}}
                test_count = crawler_col.count_documents(query)
        
            
        # 添加日期範圍過濾 - 這是查詢瓶頸的關鍵部分
        if start_date and end_date:
            query['日期'] = {'$gte': start_date, '$lte': end_date}
            
            # 查看過濾後的記錄數
            filtered_count = crawler_col.count_documents(query)
              # 只選擇需要的字段，減少數據傳輸和處理時間
        # 確保包含所有可能的欄位名稱，以便在不同的數據源中正確處理
        # 根據需求修正，確保所有必要字段都包含在內
        projection = {
            '_id': 1,  # 包含_id字段以便追踪
            '標題': 1, 'title': 1,
            '留言內容': 1, 'content': 1, 'text': 1, 'comment': 1,
            '情感標籤': 1, 'sentiment': 1, 'sentiment_label': 1, 'label': 1,
            '情緒': 1, 'emotion': 1, 'emotions': 1, 'emotion_label': 1,
            '日期': 1, 'date': 1, 'created_at': 1, 'timestamp': 1,
            '用戶': 1, 'user': 1, 'username': 1,
            'source': 1, '來源': 1,
            'legislator_id': 1, '立委ID': 1,
            'name': 1, '姓名': 1, '立委': 1
        }

        # 使用 hint 強制使用複合索引 (如果存在)
        try:
            # 執行查詢
            cursor = crawler_col.find(query, projection)
            
            # 添加索引提示 - 如果索引不存在會被忽略
            if start_date and end_date:
                cursor = cursor.hint([("name", 1), ("日期", 1)])
            else:
                cursor = cursor.hint([("name", 1)])
                
            # 添加限制以避免內存問題
            if limit:
                cursor = cursor.limit(limit)
                
            result = list(cursor)
            
            # 計時結束
            end_time = time.time()
            duration = end_time - start_time
            
            return result
            
        except Exception as e:
            print(f"⚠️ 索引查詢失敗: {e}，嘗試無索引查詢")
            # 如果索引提示失敗，回退到普通查詢
            cursor = crawler_col.find(query, projection)
            if limit:
                cursor = cursor.limit(limit)
            
            result = list(cursor)
            
            # 計時結束
            end_time = time.time()
            duration = end_time - start_time
            
            return result

    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ 查詢爬蟲數據失敗: {e}，耗時 {duration:.2f} 秒")
        return []




def serialize_legislator_data(legislator_data):
    # 將 MongoDB 的 _id (ObjectId) 轉換為字串，以便 JSON 序列化
    if '_id' in legislator_data:
        legislator_data['_id'] = str(legislator_data['_id'])
    # 處理 datetime 物件，將其轉換為 ISO 格式的字串
    if 'last_updated' in legislator_data and isinstance(legislator_data['last_updated'], datetime):
        legislator_data['last_updated'] = legislator_data['last_updated'].isoformat()

    return legislator_data

# --- API 路由 ---

@legislator_app.route('/', methods=['GET'])
def get_legislators():
    try:
        county = request.args.get('county') # 從查詢參數獲取縣市
        party = request.args.get('party') # 從查詢參數獲取黨派
        # 判斷是否需要包含分析數據，預設為 false
        include_analysis = request.args.get('include_analysis', 'false').lower() == 'true'
        if db is None:
            return jsonify({"error": "Database connection not available"}), 500
        legislators_col = db['legislators'] # 獲取 legislators 集合的引用
        query = {} # 初始化查詢條件字典
        # 根據縣市篩選：使用正則表達式進行不區分大小寫的部分匹配
        if county:
            query['constituency'] = {'$regex': county, '$options': 'i'}
        # 根據黨派篩選：使用正則表達式進行不區分大小寫的部分匹配
        if party:
            query['party'] = {'$regex': party, '$options': 'i'}
        # 根據 include_analysis 決定要返回哪些欄位 (投影)
        if include_analysis:
            # 如果需要分析數據，則包含所有相關分析欄位
            projection = {
                '_id': 1, 'id': 1, 'name': 1, 'party': 1, 'constituency': 1, 'image_url': 1,
                '情感分析': 1, '情緒分析': 1, '留言數': 1, '用戶數': 1, # 之前 Python 分析腳本新增的字段
                'positive_count': 1, 'negative_count': 1, 'emotion': 1, 
                'recall_data': 1 # 罷免數據
            }
        else:
            # 否則只返回基本資訊 (姓名、照片、選區資訊、調查數據)
            projection = {'_id': 1, 'id': 1, 'name': 1, 'image_url': 1, 'constituency': 1}

        # 執行 MongoDB 查詢，返回符合條件的立委列表
        legislators = list(legislators_col.find(query, projection))
        # 處理每個立委數據，使其可 JSON 序列化，並為列表視圖添加簡化選區資訊
        for legislator in legislators:
            serialize_legislator_data(legislator)
            # 如果不包含分析數據（即用於列表視圖），則提取簡化選區並移除原始選區陣列
            if not include_analysis and 'constituency' in legislator:
                constituency_list = legislator['constituency']
                district_found = "選區資訊不詳" # 預設值

                if isinstance(constituency_list, list):
                    for item in constituency_list:
                        if isinstance(item, str) and '選區：' in item:
                            district_found = item.replace('選區：', '').strip()
                            break # 找到第一個就跳出
                    else: # 如果循環結束都沒找到 '選區：'
                        if constituency_list:
                            district_found = constituency_list[0] # 使用列表的第一個元素

                legislator['district'] = district_found
                del legislator['constituency'] # 移除完整的 constituency 陣列
        return jsonify(legislators) # 返回 JSON 格式的立委列表

    except Exception as e:
        print(f"Error in get_legislators: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@legislator_app.route('/<identifier>', methods=['GET'])
def get_legislator_detail(identifier):
        legislators_col = db['legislators']
        legislator_data = None
        # 首先嘗試以姓名查詢 (最常見的查詢方式)
        legislator_data = legislators_col.find_one({'name': identifier})
        if legislator_data:
            # 確保返回的欄位名稱使用 word_cloud，但從 文字雲 欄位獲取數據
            if '文字雲' in legislator_data and legislator_data['文字雲']:
                legislator_data['word_cloud'] = legislator_data['文字雲']
                
            # 調用 serialize_legislator_data 進行序列化
            serialized_data = serialize_legislator_data(legislator_data)
            return jsonify(serialized_data)
        else:
            return jsonify({"error": f"Legislator not found with identifier: {identifier}"}), 404



    
def generate_time_series_chart(time_series_data, start_date, end_date):
    """
    生成時間序列圖表數據 - 根據時間範圍動態調整數據點數量
    """
    from datetime import datetime, timedelta

    # 解析日期
    if isinstance(start_date, str):
        start = datetime.strptime(start_date, '%Y-%m-%d')
    else:
        start = start_date

    if isinstance(end_date, str):
        end = datetime.strptime(end_date, '%Y-%m-%d')
    else:
        end = end_date

    # 計算時間間隔
    total_days = (end - start).days + 1

    # 動態決定數據點數量和間隔，不使用硬編碼的天數判斷
    def calculate_optimal_points(days):
        """根據天數動態計算最佳數據點數量"""
        if days <= 7:
            return days, 1  # 7天以內：每天一個點
        elif days <= 30:
            return min(15, days), max(1, days // 15)  # 30天以內：最多15個點
        elif days <= 90:
            return min(20, days // 3), max(1, days // 20)  # 90天以內：最多20個點
        elif days <= 365:
            return min(25, days // 7), max(1, days // 25)  # 一年以內：最多25個點
        else:
            return min(30, days // 14), max(1, days // 30)  # 超過一年：最多30個點

    target_points, interval_days = calculate_optimal_points(total_days)

    labels = []
    support_data = []
    oppose_data = []

    current_date = start
    while current_date <= end:
        # 計算這個時間點的數據
        period_support = 0
        period_oppose = 0

        # 累計這個間隔內的數據
        for i in range(interval_days):
            check_date = current_date + timedelta(days=i)
            if check_date > end:
                break
            date_key = check_date.strftime('%Y-%m-%d')
            if date_key in time_series_data:
                period_support += time_series_data[date_key]['support']
                period_oppose += time_series_data[date_key]['oppose']

        labels.append(current_date.strftime('%m/%d'))
        support_data.append(period_support)
        oppose_data.append(period_oppose)

        current_date += timedelta(days=interval_days)

    return {
        "labels": labels,
        "datasets": [
            {
                "label": "支持罷免",
                "data": support_data,
                "borderColor": "#f87171",
                "backgroundColor": "rgba(248, 113, 113, 0.1)",
                "tension": 0.3,
                "fill": True
            },
            {
                "label": "反對罷免",
                "data": oppose_data,
                "borderColor": "#4f8cff",
                "backgroundColor": "rgba(79, 140, 255, 0.1)",
                "tension": 0.3,
                "fill": True
            }
        ]
    }



@legislator_app.route('/sentiment/<name>', methods=['GET'])
def get_legislator_sentiment(name):
    """
    獲取特定立委的情感分析數據。
    只返回與情感分析相關的欄位。
    路由: /legislators/sentiment/<name>
    """
    legislators_col = db['legislators']

    projection = {
        'name': 1,
        '情感分析': 1,
        '情緒分析': 1,
        '留言數': 1,
        '用戶數': 1,
        'positive_count': 1, # 可能為舊版或冗餘字段
        'negative_count': 1, # 可能為舊版或冗餘字段
        'emotion': 1, # 可能為舊版或冗餘字段
        'top_words': 1 # 關鍵詞數據
    }

    legislator_data = legislators_col.find_one({'name': name}, projection)

    if legislator_data:
        serialized_data = serialize_legislator_data(legislator_data)
        return jsonify(serialized_data)
    else:
        return jsonify({"error": f"Sentiment data not found for legislator: {name}"}), 404

@legislator_app.route('/recall', methods=['GET'])
def get_recall_list():
    """
    獲取所有有罷免數據的立委列表。
    返回包含罷免相關數據的立委資訊。
    路由: /legislators/recall
    """
    legislators_col = db['legislators']

    # 查詢具有 'recall_data' 欄位且不為空值的立委
    query = {'recall_data': {'$exists': True, '$ne': None}}
    projection = {
        'name': 1,
        'recall_data': 1,
        'image_url': 1,
        'party': 1,
        'constituency': 1
    }
    legislators_with_recall = list(legislators_col.find(query, projection))
    # 將數據轉換為預期格式，把立委的基本資訊合併到罷免數據中
    recall_list = []
    for legislator in legislators_with_recall:
        if 'recall_data' in legislator and legislator['recall_data']:
            recall_item = legislator['recall_data'].copy() # 複製罷免數據
            # 添加額外的立委資訊到罷免數據項目中            recall_item['image_url'] = legislator.get('image_url', '')
            recall_item['party'] = legislator.get('party', '')
            recall_item['constituency'] = legislator.get('constituency', '')
            # 添加 status 字段（從 recall_data.狀態 提取）
            recall_item['status'] = recall_item.get('狀態', '網路聲量調查')
            # 添加罷免狀態與說明字段
            recall_item['recall_status'] = recall_item.get('罷免狀態', '狀態未知')
            recall_item['recall_description'] = recall_item.get('罷免狀態描述', '')
            recall_list.append(recall_item)

    # 序列化每個項目以便 JSON 響應
    for item in recall_list:
        serialize_legislator_data(item)
    return jsonify(recall_list) # 返回 JSON 格式的罷免列表

@legislator_app.route('/<identifier>/time-series', methods=['GET'])
def get_legislator_time_series(identifier):
    """API1：初始載入 - 返回全時間範圍數據"""
    try:
        if db is None:
            return jsonify({"error": "Database connection not available"}), 500

        # 獲取日期範圍
        date_range = get_legislator_date_range_data(identifier)
        if not date_range:
            return jsonify({
                "word_cloud": [],
                "time_series": {"labels": [], "datasets": []},
                "emotion_analysis_detailed": {"positive": {}, "negative": {}},
                "total_records": 0,
                "date_range": {"start_date": None, "end_date": None}
            })

        # 簡化處理：只返回基本的時間序列和詞雲
        crawler_col = db['crawler_data']

        # 查詢全部有效數據進行分析
        recent_data = list(crawler_col.find({
            "name": identifier,
            "日期": {"$exists": True, "$ne": None, "$ne": "", "$type": "string"}
        }).sort("日期", 1))  # 按日期正序排列

        # 生成詞雲 - 使用改進的中文分詞
        word_cloud = []
        if recent_data:
            from collections import Counter
            word_counter = Counter()

            # 載入停用詞
            stopwords = load_stopwords()

            for record in recent_data[:500]:  # 只處理前500筆
                content = record.get('留言內容', '') or record.get('標題', '')
                if content and len(content) > 2:
                    # 使用改進的中文分詞
                    try:
                        import jieba
                        import jieba.posseg as pseg

                        # 使用詞性標註，只保留名詞、動詞、形容詞
                        words = pseg.cut(content)
                        for word, flag in words:
                            if (len(word) >= 2 and
                                word not in stopwords and
                                flag in ['n', 'v', 'a', 'nr', 'ns', 'nt', 'nz'] and  # 名詞、動詞、形容詞
                                not word.isdigit() and
                                not all(c in '，。！？；：""''（）【】' for c in word)):
                                word_counter[word] += 1
                    except ImportError:
                        # 如果沒有 jieba，使用簡化版本
                        words = [w.strip() for w in content.split() if len(w.strip()) >= 2]
                        for word in words[:5]:
                            if word not in stopwords and len(word) <= 6:  # 限制長度
                                word_counter[word] += 1

            word_cloud = [
                {"text": word, "weight": count}
                for word, count in word_counter.most_common(50)
                if len(word) >= 2 and len(word) <= 6  # 確保是短詞或單字
            ]

        # 生成真實的全時間範圍時間序列
        from datetime import datetime, timedelta

        # 使用實際的數據日期範圍
        start_date_str = date_range['start_date']  # 2020-06-04
        end_date_str = date_range['end_date']      # 2025-09-30

        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d')

        # 計算總天數，動態決定間隔
        total_days = (end_date - start_date).days + 1

        # 動態決定數據點數量，讓圖表可以左右橫移
        def calculate_optimal_interval(days):
            """根據天數動態計算最佳間隔和數據點數量"""
            if days <= 7:
                return 1, days  # 7天：每天一個點，7個點
            elif days <= 30:
                return 2, 15    # 30天：每2天一個點，15個點
            elif days <= 60:
                return 2, 30    # 60天：每2天一個點，30個點
            elif days <= 90:
                return 3, 30    # 90天：每3天一個點，30個點
            elif days <= 180:
                return 6, 30    # 半年：每6天一個點，30個點
            elif days <= 365:
                return 12, 30   # 一年：每12天一個點，30個點
            else:
                return max(15, days // 40), min(50, days // 15)  # 超過一年：更多點，支持橫移

        interval_days, target_points = calculate_optimal_interval(total_days)


        # 統計實際數據中的情感分布 - 按日期分組
        time_series_data = {}
        actual_dates = set()

        for record in recent_data:
            record_date = record.get('日期', '')
            if record_date:
                actual_dates.add(record_date)
                if record_date not in time_series_data:
                    time_series_data[record_date] = {"support": 0, "oppose": 0}

                sentiment = record.get('情感標籤', '')
                if sentiment in ['NEGATIVE', 'negative']:
                    time_series_data[record_date]['support'] += 1
                elif sentiment in ['POSITIVE', 'positive']:
                    time_series_data[record_date]['oppose'] += 1

        # 修復：根據時間範圍長短決定統計間隔，避免重複標籤
        if actual_dates:
            sorted_dates = sorted(list(actual_dates))

            # 根據時間範圍長短決定統計方式
            def generate_time_intervals(total_days):
                """根據時間範圍生成不重複的時間間隔"""
                from datetime import datetime, timedelta

                start_dt = datetime.strptime(start_date_str, '%Y-%m-%d')
                end_dt = datetime.strptime(end_date_str, '%Y-%m-%d')

                intervals = []
                labels = []

                if total_days <= 7:
                    # 7天以內：每天統計
                    current = start_dt
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%m/%d'))
                        current += timedelta(days=1)

                elif total_days <= 30:
                    # 30天以內：每2-3天統計
                    step = max(1, total_days // 15)
                    current = start_dt
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%m/%d'))
                        current += timedelta(days=step)

                elif total_days <= 90:
                    # 90天以內：每週統計，顯示實際日期
                    current = start_dt
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%m/%d'))  # 顯示月/日
                        current += timedelta(days=7)

                elif total_days <= 365:
                    # 一年以內：每月統計
                    current = start_dt.replace(day=1)  # 月初
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%Y/%m'))
                        # 下個月
                        if current.month == 12:
                            current = current.replace(year=current.year + 1, month=1)
                        else:
                            current = current.replace(month=current.month + 1)
                else:
                    # 超過一年：每月統計，提供更多數據點
                    current = start_dt.replace(day=1)  # 月初
                    while current <= end_dt:
                        intervals.append(current.strftime('%Y-%m-%d'))
                        labels.append(current.strftime('%Y/%m'))  # 年/月格式
                        # 下個月
                        if current.month == 12:
                            current = current.replace(year=current.year + 1, month=1)
                        else:
                            current = current.replace(month=current.month + 1)

                return intervals, labels

            # 生成時間間隔
            intervals, labels = generate_time_intervals(total_days)

            # 統計每個間隔的數據
            support_data = []
            oppose_data = []

            for i, interval_start in enumerate(intervals):
                # 計算間隔結束日期
                if i < len(intervals) - 1:
                    interval_end = intervals[i + 1]
                else:
                    interval_end = end_date_str

                # 統計該間隔內的數據
                period_support = 0
                period_oppose = 0

                for date_str in sorted_dates:
                    if interval_start <= date_str < interval_end:
                        day_data = time_series_data.get(date_str, {"support": 0, "oppose": 0})
                        period_support += day_data['support']
                        period_oppose += day_data['oppose']

                support_data.append(period_support)
                oppose_data.append(period_oppose)
        else:
            labels = []
            support_data = []
            oppose_data = []

        time_series = {
            "labels": labels,
            "datasets": [
                {
                    "label": "支持罷免",
                    "data": support_data,
                    "borderColor": "#f87171",
                    "backgroundColor": "rgba(248, 113, 113, 0.1)",
                    "tension": 0.3,
                    "fill": True
                },
                {
                    "label": "反對罷免",
                    "data": oppose_data,
                    "borderColor": "#4f8cff",
                    "backgroundColor": "rgba(79, 140, 255, 0.1)",
                    "tension": 0.3,
                    "fill": True
                }
            ]
        }

        # 統計情感和情緒數據
        sentiment_stats = {"support_count": 0, "oppose_count": 0}
        emotion_stats = {"positive": {}, "negative": {}}

        # 初始化情緒統計
        emotions = ['joy', 'anger', 'sadness', 'fear', 'surprise', 'disgust', 'trust', 'anticipation']
        for emotion in emotions:
            emotion_stats["positive"][emotion] = 0
            emotion_stats["negative"][emotion] = 0


        # 統計實際數據
        for record in recent_data:
            sentiment = record.get('情感標籤', '')
            emotion = record.get('情緒', '')

            # 統計情感
            if sentiment in ['NEGATIVE', 'negative']:
                sentiment_stats["support_count"] += 1
            elif sentiment in ['POSITIVE', 'positive']:
                sentiment_stats["oppose_count"] += 1

            # 統計情緒
            if emotion in emotions:
                if sentiment in ['NEGATIVE', 'negative']:
                    emotion_stats["negative"][emotion] += 1
                elif sentiment in ['POSITIVE', 'positive']:
                    emotion_stats["positive"][emotion] += 1

        # 按照 before.py 的正確格式返回數據
        result = {
            "word_cloud": word_cloud,
            "time_series": time_series,
            "sentiment_analysis": sentiment_stats,  # 改為 sentiment_analysis
            "emotion_analysis_detailed": {
                "positive": emotion_stats["positive"],  # 反對罷免的情緒
                "negative": emotion_stats["negative"]   # 支持罷免的情緒
            },
            "total_records": len(recent_data),
            "date_range": {
                "start_date": date_range['start_date'],
                "end_date": date_range['end_date']
            }
        }
        return jsonify(result)

    except Exception as e:
        print(f"Error in get_legislator_time_series: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

def analyze_full_time_series(all_data, identifier):
    # 詞雲分析 - 統計所有文本中的關鍵詞
    word_counter = Counter()
    time_series_data = {}

    # 情緒分析統計
    emotion_counts = Counter()
    raw_positive_emotion_counts = Counter()  # 反對罷免的情緒（原始）
    raw_negative_emotion_counts = Counter()  # 支持罷免的情緒（原始）

    # 標準的8大情緒
    allowed_emotions = ['joy', 'anger', 'sadness', 'fear', 'surprise', 'disgust', 'trust', 'anticipation']

    # 找出數據的時間範圍
    dates = []

    for data in all_data:
        # 提取文本內容進行詞雲分析
        text_content = ""
        #if '標題' in data:
        #    text_content += data['標題'] + " "
        if '留言內容' in data:
            text_content += data['留言內容'] + " "
        # 提取關鍵詞 - 使用統一的關鍵字提取函數
        if text_content:
            meaningful_words = extract_meaningful_keywords(text_content, min_count=1)
            word_counter.update(dict(meaningful_words))
        # 收集日期信息
        date_str = None
        if '日期' in data:
            date_str = data['日期']
        if date_str:
            try:
                # 嘗試解析日期
                if isinstance(date_str, str):
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                dates.append(date_obj)
                # 統計情感數據用於時間序列
                date_key = date_obj.strftime('%Y-%m-%d')
                if date_key not in time_series_data:
                    time_series_data[date_key] = {"support": 0, "oppose": 0}

                # 分析情感
                sentiment_value = None
                if '情感標籤' in data:
                    sentiment_value = data['情感標籤']
                if sentiment_value:
                    if sentiment_value in ['POSITIVE', 'positive']:
                        time_series_data[date_key]['oppose'] += 1
                    elif sentiment_value in ['NEGATIVE', 'negative']:
                        time_series_data[date_key]['support'] += 1
                    #可以加入中立，但我還沒想好
            except Exception as e:
                print(f"日期解析錯誤: {date_str}, {e}")
                continue
        # 分析情緒（根據情感標籤分類）
        emotion_value = data['情緒']
        sentiment_value = data['情感標籤']
        if emotion_value:
            emotion_counts[emotion_value] += 1

            # 根據情感標籤分類情緒
            if sentiment_value in ['POSITIVE', 'positive']:
                # POSITIVE = 反對罷免，所以這個情緒屬於反對罷免
                raw_positive_emotion_counts[emotion_value] += 1
            elif sentiment_value in ['NEGATIVE', 'negative']:
                # NEGATIVE = 支持罷免，所以這個情緒屬於支持罷免
                raw_negative_emotion_counts[emotion_value] += 1

    # 過濾和標準化情緒數據
    final_positive_emotion_counts = Counter()
    final_negative_emotion_counts = Counter()

    # 標準化 neutral 相關的鍵
    for emotion, count in raw_positive_emotion_counts.items():
        if emotion in allowed_emotions:
            final_positive_emotion_counts[emotion] += count
        else: # 任何不在允許情緒中的都歸為 neutral
            final_positive_emotion_counts['neutral'] += count

    # 過濾負面情緒
    for emotion, count in raw_negative_emotion_counts.items():
        if emotion in allowed_emotions:
            final_negative_emotion_counts[emotion] += count
        else: # 任何不在允許情緒中的都歸為 neutral
            final_negative_emotion_counts['neutral'] += count

    # 生成詞雲數據（取前50個最常見的詞）- 使用統一函數
    # 直接使用word_counter的結果，因為extract_meaningful_keywords已經過濾過了
    word_cloud = [
        {"text": word, "weight": count}
        for word, count in word_counter.most_common(50)
    ]
    # 生成動態時間序列圖表
    time_series_chart = generate_time_series_chart(time_series_data, min(dates) if dates else None, max(dates) if dates else None)

    return {
        "word_cloud": word_cloud,
        "time_series": time_series_chart,
        "emotion_analysis": dict(emotion_counts),  # 總情緒分析數據
        "emotion_analysis_detailed": {
            "positive": dict(final_positive_emotion_counts),  # POSITIVE情感標籤的情緒 = 反對罷免情緒
            "negative": dict(final_negative_emotion_counts)   # NEGATIVE情感標籤的情緒 = 支持罷免情緒
        },
        "total_records": len(all_data),
        "date_range": {
            "start": min(dates).strftime('%Y-%m-%d') if dates else None,
            "end": max(dates).strftime('%Y-%m-%d') if dates else None
        }
    }

# 刪除重複的 generate_quarterly_time_series 函數，使用 generate_time_series_chart 替代

# 函數1：獲取立委的日期範圍和基本統計
def get_legislator_date_range_data(identifier):
    """
    獲取指定立委的crawler_data日期範圍和基本統計
    返回：最早日期、最晚日期、總記錄數
    """
    try:
        if db is None:
            return None

        crawler_col = db['crawler_data']
        # 先檢查數據樣本，了解日期格式 - 只查詢有效日期的記錄
        sample_docs = list(crawler_col.find({
            "name": identifier,
            "日期": {"$exists": True, "$ne": None, "$ne": "", "$type": "string"}
        }).limit(5))
        # 使用聚合查詢快速獲取最小和最大日期，只查詢有效的字符串日期
        pipeline = [
            {"$match": {
                "name": identifier,
                "日期": {"$exists": True, "$ne": None, "$ne": "", "$type": "string"}
            }},
            {"$group": {
                "_id": None,
                "min_date": {"$min": "$日期"},
                "max_date": {"$max": "$日期"},
                "count": {"$sum": 1}
            }}
        ]

        result = list(crawler_col.aggregate(pipeline))

        if not result or result[0]['count'] == 0:
            return None

        data = result[0]
        start_date = data['min_date']
        end_date = data['max_date']
        total_records = data['count']

        # 確保日期格式正確
        if isinstance(start_date, str):
            start_date_str = start_date
        else:
            start_date_str = start_date.strftime('%Y-%m-%d') if start_date else None

        if isinstance(end_date, str):
            end_date_str = end_date
        else:
            end_date_str = end_date.strftime('%Y-%m-%d') if end_date else None

        return {
            "start_date": start_date_str,
            "end_date": end_date_str,
            "total_records": total_records
        }

    except Exception as e:
        print(f"❌ Error in get_legislator_date_range_data: {e}")
        return None

@legislator_app.route('/<identifier>/date-range', methods=['GET'])
def get_legislator_date_range(identifier):
    """API：返回立委的日期範圍"""
    try:
        date_range_data = get_legislator_date_range_data(identifier)

        if not date_range_data:
            return jsonify({
                "start_date": None,
                "end_date": None,
                "total_records": 0,
                "message": "No data found for this legislator"
            })

        return jsonify(date_range_data)

    except Exception as e:
        print(f"❌ Error in get_legislator_date_range: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

# 使用 LRU 緩存來存儲時間範圍查詢結果，提高重複查詢的性能
@lru_cache(maxsize=64)
def get_cached_time_range_results(identifier, start_date, end_date):
    """
    緩存時間範圍查詢結果，減少重複計算
    注意：使用這個緩存時要確保 start_date 和 end_date 都是字符串
    """
    return process_time_range_data(identifier, start_date, end_date)

@legislator_app.route('/<identifier>/time-range', methods=['GET'])
def get_legislator_time_range_data_api(identifier):
    """獲取特定時間範圍內的立委數據API
    支持查詢參數：start_date, end_date, use_cache
    返回詳細的情感、情緒、詞雲和時間序列分析
    """
    try:
        # 計時開始
        api_start_time = time.time()
        
        # 獲取並驗證參數
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 檢查是否請求使用緩存
        use_cache = request.args.get('use_cache', 'true').lower() == 'true'
        
        if not start_date or not end_date:
            return jsonify({"error": "start_date 和 end_date 參數是必需的","code": "MISSING_PARAMETERS"}), 400
        if db is None:
            return jsonify({"error": "數據庫連接不可用","code": "DATABASE_UNAVAILABLE"}), 500
            
        # 驗證日期格式
        try:
            datetime.strptime(start_date, '%Y-%m-%d')
            datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({
                "error": "日期格式錯誤",
                "message": "日期格式應為 YYYY-MM-DD",
                "code": "INVALID_DATE_FORMAT"
            }), 400
        
        # 檢查日期範圍
        if start_date > end_date:
            return jsonify({
                "error": "日期範圍錯誤",
                "message": "開始日期不能晚於結束日期",
                "code": "INVALID_DATE_RANGE"
            }), 400
            
        # 根據用戶設置決定是否使用緩存
        result = None
        if use_cache:
            try:
                # 使用緩存獲取結果
                result = get_cached_time_range_results(identifier, start_date, end_date)
                if result:
                    print(f"✅ 從緩存獲取到結果")
            except Exception as cache_error:
                print(f"⚠️ 緩存獲取失敗: {cache_error}")
        
        # 如果緩存未命中或用戶不使用緩存，則直接處理
        if not result:
            print(f"🔄 直接處理時間範圍數據")
            result = process_time_range_data(identifier, start_date, end_date)
        
        # 計算API總響應時間
        api_end_time = time.time()
        api_duration = api_end_time - api_start_time
        
    # 如果沒有找到數據，返回空結構但狀態碼為 200
        if not result:
            empty_result = {
                "word_cloud": [],
                "time_series": {
                    "labels": [],
                    "datasets": []
                },
                "sentiment_analysis": {"support_count": 0, "oppose_count": 0},
                "emotion_analysis": {
                    "labels": [],
                    "datasets": []
                },
                "emotion_analysis_detailed": {
                    "positive": {},
                    "negative": {}
                },
                "total_records": 0,
                "date_range": {"start_date": start_date, "end_date": end_date},                "message": "查詢完成，但未找到符合條件的數據",
                "api_response_time": f"{api_duration:.2f}秒",
                "from_cache": False
            }
            return jsonify(empty_result)
        
        # 在結果中添加立委信息
        legislator_info = get_legislator_by_name(identifier)
        if legislator_info:
            result["legislator"] = {
                "name": legislator_info.get("name", identifier),
                "photo": legislator_info.get("photo", ""),
                "party": legislator_info.get("party", ""),
                "constituency": legislator_info.get("constituency", "")
            }
        
        # 添加日期範圍信息
        result["date_range"] = {
            "start_date": start_date,
            "end_date": end_date
        }
        
        # 添加API響應時間到結果
        result["api_response_time"] = f"{api_duration:.2f}秒"
        result["from_cache"] = use_cache and result.get("multiprocessing_used", False) == False
        
        # 確保所有必要的字段都存在
        if "word_cloud" not in result or not result["word_cloud"]:
            result["word_cloud"] = []
            print("⚠️ 警告: 結果中缺少詞雲數據，已添加空數組")
            
        if "time_series" not in result or not result["time_series"]:
            result["time_series"] = {"labels": [], "datasets": []}
            print("⚠️ 警告: 結果中缺少時間序列數據，已添加空結構")
            
        if "emotion_analysis" not in result or not result["emotion_analysis"]:
            result["emotion_analysis"] = {"labels": [], "datasets": []}
            print("⚠️ 警告: 結果中缺少情緒分析數據，已添加空結構")
        
        # 輸出處理統計
        if "processing_info" in result:
            info = result["processing_info"]
            
            query_time = float(info.get('query_time', '0').replace('秒', ''))
            total_time = float(info.get('total_time', '1').replace('秒', ''))
            analysis_time = float(info.get('analysis_time', '0').replace('秒', ''))
            
            query_pct = (query_time / total_time * 100) if total_time > 0 else 0
            analysis_pct = (analysis_time / total_time * 100) if total_time > 0 else 0
            
        
        return jsonify(result)

    except Exception as e:
        print(f"❌ Error in get_legislator_time_range_data_api: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Internal server error: {str(e)}", "code": "INTERNAL_ERROR"}), 500
def process_time_range_data(identifier, start_date=None, end_date=None):
    """
    處理指定時間範圍的crawler_data，返回詞雲、時間序列、情緒分析
    如果start_date或end_date為None，則使用全部時間範圍
    
    參數:
        identifier: 立委識別符 (通常是姓名)
        start_date: 開始日期 (YYYY-MM-DD 格式)
        end_date: 結束日期 (YYYY-MM-DD 格式)
        
    返回:
        dict: 包含所有前端所需的資料結構
    """
    try:
        if db is None:
            print("❌ 數據庫連接不可用")
            return None

        # 測量總處理時間
        total_start_time = time.time()

        # 如果沒有指定時間範圍，獲取全部範圍
        if not start_date or not end_date:
            print("ℹ️ 未指定時間範圍，獲取全部範圍")
            date_range = get_legislator_date_range_data(identifier)
            if not date_range:
                print(f"⚠️ 無法獲取 {identifier} 的日期範圍")
                return None
            start_date = date_range['start_date']
            end_date = date_range['end_date']
            print(f"ℹ️ 使用全部時間範圍: {start_date} 至 {end_date}")

        # 驗證和調整日期範圍
        date_range = get_legislator_date_range_data(identifier)
        if date_range:
            # 確保用戶輸入的日期在有效範圍內
            if start_date < date_range['start_date']:
                start_date = date_range['start_date']
            if end_date > date_range['end_date']:
                end_date = date_range['end_date']

        # 使用優化的查詢函數獲取數據
        # 1. 先獲取名稱匹配的記錄總數 (快速操作)
        count_query_start = time.time()
        crawler_col = db['crawler_data']
        name_query = {'name': identifier}
        name_match_count = crawler_col.count_documents(name_query)
        
        if name_match_count == 0:
            # 嘗試模糊匹配
            name_query = {'name': {'$regex': identifier, '$options': 'i'}}
            name_match_count = crawler_col.count_documents(name_query)
            
        count_query_end = time.time()
        
        # 如果記錄數量極大 (>100,000)，考慮使用聚合管道或分批處理
        record_limit = 50000
        if name_match_count > 100000:
            print(f"⚠️ 記錄數量非常大 ({name_match_count})，限制為 {record_limit} 筆進行分析")
        
        # 2. 使用優化的查詢函數獲取時間範圍內的數據
        data_query_start = time.time()
          # 直接使用優化的查詢函數，包含所有查詢優化和索引提示
        crawler_data = get_crawler_data_optimized(identifier, start_date, end_date, limit=record_limit)
        data_query_end = time.time()

        if not crawler_data:
            print(f"⚠️ 未找到 {identifier} 在指定時間範圍的數據")
            # 返回空數據結構而不是None，確保前端可以正常顯示
            return {
                "sentiment_analysis": {"support_count": 0, "oppose_count": 0},
                "emotion_analysis": {
                    "labels": [],
                    "datasets": []
                },
                "emotion_analysis_detailed": {
                    "positive": {},
                    "negative": {}
                },
                "word_cloud": [],
                "time_series": {
                    "labels": [],
                    "datasets": []
                },
                "total_records": 0,
                "processing_info": {
                    "query_time": f"{data_query_end - data_query_start:.2f}秒",
                    "analysis_time": "0.00秒",
                    "total_time": f"{time.time() - total_start_time:.2f}秒",
                    "record_count": 0,
                    "multiprocessing_used": False
                }
            }
            
        # 3. 分析數據
        analysis_start = time.time()
        
        # 根據數據量選擇處理方式        
        data_count = len(crawler_data)
        use_multiprocessing = data_count > 5000 and multiprocessing.cpu_count() > 1
        if use_multiprocessing:
            # 使用多進程處理
            result = process_data_parallel(crawler_data, start_date, end_date)
        else:
            # 使用標準處理
            result = analyze_time_range_data(crawler_data, start_date, end_date)        # 從 MongoDB 中獲取立委文字雲
        try:
            legislator_info = get_legislator_by_name(identifier)
            if legislator_info and '文字雲' in legislator_info and legislator_info['文字雲']:
                print(f"ℹ️ 從 MongoDB 獲取立委 {identifier} 的文字雲")
                result['word_cloud'] = legislator_info['文字雲']
            else:
                print(f"⚠️ MongoDB 中未找到立委 {identifier} 的文字雲或為空")
                # 提供一個空的文字雲
                result['word_cloud'] = []
        except Exception as e:
            print(f"❌ 從 MongoDB 獲取文字雲時出錯: {e}")
            result['word_cloud'] = []
        
        analysis_end = time.time()
        
        # 計算和報告處理時間
        query_time = data_query_end - data_query_start
        analysis_time = analysis_end - analysis_start
        total_time = analysis_end - total_start_time
        
        processing_info = {
            "query_time": f"{query_time:.2f}秒",
            "analysis_time": f"{analysis_time:.2f}秒",
            "total_time": f"{total_time:.2f}秒",
            "record_count": len(crawler_data),
            "multiprocessing_used": use_multiprocessing
        }
        
          # 添加處理信息到結果中
        if result:
            result["processing_info"] = processing_info
        
        return result
        
    except Exception as e:
        print(f"❌ 處理時間範圍數據時出錯: {str(e)}")
        import traceback
        traceback.print_exc()
        # 返回一個空的數據結構而不是None
        return {
            "sentiment_analysis": {"support_count": 0, "oppose_count": 0},
            "emotion_analysis": {
                "labels": [],
                "datasets": []
            },
            "emotion_analysis_detailed": {
                "positive": {},
                "negative": {}
            },
            "word_cloud": [],
            "time_series": {
                "labels": [],
                "datasets": []
            },
            "total_records": 0,
            "error": str(e),
            "processing_info": {
                "query_time": "0.00秒",
                "analysis_time": "0.00秒",
                "total_time": "0.00秒",
                "record_count": 0,
                "multiprocessing_used": False,
                "error": str(e)
            }
        }

def process_data_parallel(crawler_data, start_date, end_date):
    """
    使用多進程優化的大數據量處理函數
    主要使用並行計算加快情感、情緒和詞雲分析
    
    參數:
        crawler_data: 爬蟲獲取的數據列表
        start_date: 開始日期
        end_date: 結束日期
        
    返回:
        與 analyze_time_range_data 相同的數據結構
    """
    try:
        # 使用計時器追蹤處理時間
        start_time = time.time()
        data_count = len(crawler_data)
        
        # 預先定義欄位名稱
        sentiment_field_names = ['情感標籤', 'sentiment']
        emotion_field_names = ['情緒', 'emotion']
        text_fields = ['標題', '留言內容', 'title', 'content', 'text']
        date_field_names = ['日期', 'date', 'created_at']
        
        # 快速檢查前幾筆數據以確定最可能的欄位名稱
        sentiment_field = None
        emotion_field = None
        date_field = None
        
        for field in sentiment_field_names:
            if any(field in data for data in crawler_data[:min(10, len(crawler_data))]):
                sentiment_field = field
                break
        
        for field in emotion_field_names:
            if any(field in data for data in crawler_data[:min(10, len(crawler_data))]):
                emotion_field = field
                break
                
        for field in date_field_names:
            if any(field in data for data in crawler_data[:min(10, len(crawler_data))]):
                date_field = field
                break
        
        
        # 定義情感標籤集合
        positive_sentiments = {'POSITIVE', 'positive', '正面', '反對', '反對罷免', '1'}
        negative_sentiments = {'NEGATIVE', 'negative', '負面', '支持', '支持罷免', '0'}
        allowed_emotions = ['joy', 'anger', 'sadness', 'fear', 'surprise', 'disgust', 'trust', 'anticipation']
          # 載入停用詞
        stop_words = load_stopwords()
        
        # 調試數據和情感標籤的匹配情況
        debug_data_sentiment_match(crawler_data, sentiment_field, positive_sentiments, negative_sentiments)
        
        # 使用多進程處理
        cpu_count = multiprocessing.cpu_count()
        
        # 將數據分成多個塊
        chunk_size = max(100, len(crawler_data) // cpu_count)
        chunks = [crawler_data[i:i + chunk_size] for i in range(0, len(crawler_data), chunk_size)]
        
        # 準備任務參數
        tasks = []
        for i, chunk in enumerate(chunks):
            # 只在第一個進程中處理詞雲，減少重複工作
            process_wordcloud = (i == 0)
            
            task = {
                'data': chunk,
                'sentiment_field': sentiment_field,
                'emotion_field': emotion_field,
                'date_field': date_field,
                'text_fields': text_fields,
                'positive_sentiments': positive_sentiments,
                'negative_sentiments': negative_sentiments,
                'process_wordcloud': process_wordcloud,
                'chunk_id': i
            }
            tasks.append(task)
        
        # 多進程處理
        with Pool(processes=cpu_count) as pool:
            results = pool.map(process_data_chunk, tasks)
        
        # 合併結果
        sentiment_counts = {"support_count": 0, "oppose_count": 0}
        raw_oppose_recall_emotions = Counter()
        raw_support_recall_emotions = Counter()
        word_counter = Counter()
        time_series_data = {}
        
        for result in results:
            # 合併情感計數
            sentiment_counts['support_count'] += result['sentiment_counts']['support_count']
            sentiment_counts['oppose_count'] += result['sentiment_counts']['oppose_count']
            
            # 合併情緒計數
            for emotion, count in result['oppose_emotions'].items():
                raw_oppose_recall_emotions[emotion] += count
            
            for emotion, count in result['support_emotions'].items():
                raw_support_recall_emotions[emotion] += count
            
            # 合併詞雲 (僅第一個進程處理的)
            if result['chunk_id'] == 0 and 'word_counter' in result:
                word_counter.update(result['word_counter'])
            
            # 合併時間序列
            for date, counts in result['time_series'].items():
                if date not in time_series_data:
                    time_series_data[date] = {"support": 0, "oppose": 0}
                time_series_data[date]['support'] += counts['support']
                time_series_data[date]['oppose'] += counts['oppose']
        
        # 計算總處理時間
        total_time = time.time() - start_time
        
        # 後處理情緒數據
        final_oppose_recall_emotions = Counter()
        final_support_recall_emotions = Counter()
        
        # 過濾和標準化情緒數據
        for emotion, count in raw_oppose_recall_emotions.items():
            if emotion in allowed_emotions:
                final_oppose_recall_emotions[emotion] += count
            else:
                final_oppose_recall_emotions['neutral'] += count
        
        for emotion, count in raw_support_recall_emotions.items():
            if emotion in allowed_emotions:
                final_support_recall_emotions[emotion] += count
            else:
                final_support_recall_emotions['neutral'] += count
        
        emotion_processing_time = time.time() - start_time - total_time
        
        # 生成詞雲
        word_cloud = [
            {"text": word, "weight": count}
            for word, count in word_counter.most_common(50)
            if len(word) >= 2 and count > 1  # 確保只返回有意義的詞
        ]
        

        
        # 生成雷達圖數據
        radar_chart_labels = allowed_emotions
        radar_data_oppose = [final_oppose_recall_emotions.get(emotion, 0) for emotion in radar_chart_labels]
        radar_data_support = [final_support_recall_emotions.get(emotion, 0) for emotion in radar_chart_labels]
          # 生成時間序列圖表
        time_series_chart = generate_time_series_chart(time_series_data, start_date, end_date)
        
        # 構建結果
        result = {
            "sentiment_analysis": sentiment_counts,
            "emotion_analysis": {
                "labels": radar_chart_labels,
                "datasets": [                    {
                        "label": "反對罷免情緒", 
                        "data": radar_data_oppose,
                        "backgroundColor": "rgba(79, 140, 255, 0.2)",
                        "borderColor": "rgba(79, 140, 255, 1)",
                        "pointBackgroundColor": "rgba(79, 140, 255, 1)",
                        "pointBorderColor": "#fff"
                    },
                    {
                        "label": "支持罷免情緒", 
                        "data": radar_data_support,
                        "backgroundColor": "rgba(248, 113, 113, 0.2)",
                        "borderColor": "rgba(248, 113, 113, 1)",
                        "pointBackgroundColor": "rgba(248, 113, 113, 1)",
                        "pointBorderColor": "#fff"
                    }
                ]
            },
            "emotion_analysis_detailed": {
                "positive": dict(final_oppose_recall_emotions),
                "negative": dict(final_support_recall_emotions)
            },
            "word_cloud": word_cloud,
            "time_series": time_series_chart,
            "total_records": data_count,
            "processing_time": f"{total_time:.2f}秒",
            "multiprocessing_used": True,
            "processing_info": {
                "query_time": "0.00秒",  # 這裡只記錄處理時間，查詢時間由調用函數記錄
                "analysis_time": f"{total_time:.2f}秒",
                "total_time": f"{total_time:.2f}秒",
                "record_count": data_count
            }
        }
        
        return result
    except Exception as e:
        print(f"❌ 多進程處理失敗: {str(e)}")
        import traceback
        traceback.print_exc()
        # 回退到原始的單進程處理
        print("⚠️ 回退到單進程處理")
          # 导入 analyze_time_range_data 函数
        from analyze_data import analyze_time_range_data
        return analyze_time_range_data(crawler_data, start_date, end_date)


# 檢查數據庫中已存在的索引，用於診斷目的
def check_existing_indexes():
    """檢查數據庫中已存在的索引，用於診斷目的"""
    try:
        if db is None:
            print("❌ 數據庫連接不可用，無法檢查索引")
            return
            
        print("🔍 正在檢查現有索引...")
        crawler_col = db['crawler_data']
        
        # 獲取索引信息
        index_info = crawler_col.index_information()
        
        print(f"📊 crawler_data 集合中存在 {len(index_info)} 個索引:")
        for name, info in index_info.items():
            if name == '_id_':  # _id 索引是MongoDB自動創建的
                print(f"  - {name}: 系統自動創建的_id索引")
                continue
                
            # 提取索引字段信息
            keys = ", ".join([f"{field}: {direction}" for field, direction in info['key']])
            print(f"  - {name}: {{ {keys} }}")
            
        print("\n💡 對於Atlas免費版，一個集合最多只能有3個自定義索引（不包括_id索引）")
        print("   建議使用ensure_database_indexes()函數重設為推薦的3個索引")
        
        return index_info
    except Exception as e:
        print(f"❌ 檢查索引失敗: {e}")
        return None

@legislator_app.route('/check-indexes', methods=['GET'])
def api_check_indexes():
    """API端點：檢查數據庫索引狀態"""
    try:
        crawler_col = db['crawler_data']
        index_info = crawler_col.index_information()
        
        # 格式化索引信息用於API返回
        formatted_indexes = []
        for name, info in index_info.items():
            # 提取索引字段信息
            keys = [f"{field}: {direction}" for field, direction in info['key']]
            formatted_indexes.append({
                "name": name,
                "keys": keys,
                "is_system": name == '_id_'
            })
            
        return jsonify({
            "total_indexes": len(index_info),
            "indexes": formatted_indexes,
            "atlas_free_limit": 3,
            "is_within_limit": len(index_info) - 1 <= 3  # 減去系統的_id索引
        })
    except Exception as e:
        return jsonify({"error": f"檢查索引失敗: {str(e)}"}), 500


# 避免在本地環境中誤檢測為生產環境
def is_railway_environment():
    """
    檢查是否在生產環境中運行
    
    返回值:
        bool: 是否為生產環境
    """
    # 1. 檢查明確的環境設定
    if os.environ.get('FLASK_ENV', '').lower() == 'development' or os.environ.get('FLASK_CONFIG', '').lower() == 'development':
        return False
        
    # 2. 檢查是否為生產環境設定
    if os.environ.get('FLASK_ENV', '').lower() == 'production' or os.environ.get('FLASK_CONFIG', '').lower() == 'production':
        return True
        
    # 3. 檢查 Railway URI 是否存在
    if 'RAILWAY_URI' in os.environ:
        return True
        
    # 4. 檢查 MongoDB URI 是否包含 railway 關鍵字
    mongodb_uri = os.environ.get('MONGODB_URI', '')
    if 'railway' in mongodb_uri.lower() or 'rlwy' in mongodb_uri.lower():
        return True
        
    return False


