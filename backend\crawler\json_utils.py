#!/usr/bin/env python3
"""
JSON 檔案操作工具函數
統一管理JSON檔案的讀取、寫入和append操作
"""

import json
import os
import time
import threading
from typing import List, Dict, Any, Optional
from datetime import datetime

# 檔案鎖定機制
file_locks = {}
file_locks_lock = threading.Lock()

def get_file_lock(file_path: str) -> threading.Lock:
    """獲取檔案鎖定"""
    with file_locks_lock:
        if file_path not in file_locks:
            file_locks[file_path] = threading.Lock()
        return file_locks[file_path]

def read_json_file(file_path: str, default: List = None) -> List[Dict]:
    """
    安全讀取JSON檔案
    
    Args:
        file_path: 檔案路徑
        default: 檔案不存在時的預設值
        
    Returns:
        JSON數據列表
    """
    if default is None:
        default = []
        
    if not os.path.exists(file_path):
        return default.copy()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # 確保返回列表格式
            if not isinstance(data, list):
                print(f"⚠️ {file_path} 不是列表格式，轉換為列表")
                return [data] if data else default.copy()
            return data
    except (json.JSONDecodeError, Exception) as e:
        print(f"❌ 讀取 {file_path} 失敗: {e}")
        return default.copy()

def write_json_file(file_path: str, data: List[Dict], backup: bool = False) -> bool:
    """
    安全寫入JSON檔案
    
    Args:
        file_path: 檔案路徑
        data: 要寫入的數據
        backup: 是否創建備份
        
    Returns:
        寫入是否成功
    """
    try:
        # 確保目錄存在
        dir_path = os.path.dirname(file_path)
        if dir_path:  # 只有當dir_path不為空時才創建目錄
            os.makedirs(dir_path, exist_ok=True)
        
        # 創建備份
        if backup and os.path.exists(file_path):
            backup_path = f"{file_path}.backup.{int(time.time())}"
            try:
                import shutil
                shutil.copy2(file_path, backup_path)
            except Exception as e:
                print(f"⚠️ 創建備份失敗: {e}")
        
        # 寫入臨時檔案
        temp_path = f"{file_path}.tmp"
        with open(temp_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 原子性移動
        import shutil
        shutil.move(temp_path, file_path)
        
        return True
    except Exception as e:
        print(f"❌ 寫入 {file_path} 失敗: {e}")
        return False

def append_json_data(file_path: str, new_data: List[Dict], unique_key: str = None, 
                    max_retries: int = 3) -> bool:
    """
    安全地append資料到JSON檔案，支援去重
    
    Args:
        file_path: 檔案路徑
        new_data: 要append的新資料列表
        unique_key: 用於去重的key（如果提供）
        max_retries: 最大重試次數
        
    Returns:
        操作是否成功
    """
    if not new_data:
        return True
    
    # 確保new_data是列表
    if not isinstance(new_data, list):
        new_data = [new_data]
    
    file_lock = get_file_lock(file_path)
    
    for attempt in range(max_retries):
        try:
            with file_lock:
                # 讀取現有資料
                existing_data = read_json_file(file_path, [])
                
                # 如果指定了unique_key，進行去重
                if unique_key:
                    existing_keys = set()
                    for item in existing_data:
                        if isinstance(item, dict) and unique_key in item:
                            existing_keys.add(item[unique_key])
                    
                    # 過濾新資料中的重複項
                    filtered_new_data = []
                    for item in new_data:
                        if isinstance(item, dict) and unique_key in item:
                            if item[unique_key] not in existing_keys:
                                filtered_new_data.append(item)
                                existing_keys.add(item[unique_key])
                        else:
                            # 沒有unique_key的項目直接加入
                            filtered_new_data.append(item)
                    
                    new_data = filtered_new_data
                
                # 合併資料
                combined_data = existing_data + new_data
                
                # 寫入檔案
                if write_json_file(file_path, combined_data, backup=False):
                    if new_data:
                        print(f"✅ 成功append {len(new_data)} 筆新資料到 {os.path.basename(file_path)}")
                    return True
                else:
                    raise Exception("寫入檔案失敗")
                    
        except Exception as e:
            print(f"❌ Append操作失敗 (嘗試 {attempt+1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(1)  # 等待1秒後重試
            else:
                return False
    
    return False

def update_json_data(file_path: str, new_data: List[Dict], update_key: str,
                    append_if_not_exists: bool = True) -> bool:
    """
    更新JSON檔案中的資料，根據指定key進行匹配更新
    
    Args:
        file_path: 檔案路徑
        new_data: 要更新的資料列表
        update_key: 用於匹配更新的key
        append_if_not_exists: 如果資料不存在是否append
        
    Returns:
        操作是否成功
    """
    if not new_data:
        return True
    
    # 確保new_data是列表
    if not isinstance(new_data, list):
        new_data = [new_data]
    
    file_lock = get_file_lock(file_path)
    
    try:
        with file_lock:
            # 讀取現有資料
            existing_data = read_json_file(file_path, [])
            
            # 建立索引
            existing_index = {}
            for i, item in enumerate(existing_data):
                if isinstance(item, dict) and update_key in item:
                    existing_index[item[update_key]] = i
            
            # 處理新資料
            updated_count = 0
            appended_count = 0
            
            for new_item in new_data:
                if not isinstance(new_item, dict) or update_key not in new_item:
                    if append_if_not_exists:
                        existing_data.append(new_item)
                        appended_count += 1
                    continue
                
                key_value = new_item[update_key]
                
                if key_value in existing_index:
                    # 更新現有資料
                    existing_data[existing_index[key_value]] = new_item
                    updated_count += 1
                elif append_if_not_exists:
                    # append新資料
                    existing_data.append(new_item)
                    appended_count += 1
            
            # 寫入檔案
            if write_json_file(file_path, existing_data, backup=False):
                if updated_count > 0:
                    print(f"✅ 更新 {updated_count} 筆資料")
                if appended_count > 0:
                    print(f"✅ 新增 {appended_count} 筆資料")
                return True
            else:
                return False
                
    except Exception as e:
        print(f"❌ 更新操作失敗: {e}")
        return False

def cleanup_json_duplicates(file_path: str, unique_key: str) -> bool:
    """
    清理JSON檔案中的重複資料
    
    Args:
        file_path: 檔案路徑
        unique_key: 用於判斷重複的key
        
    Returns:
        操作是否成功
    """
    try:
        file_lock = get_file_lock(file_path)
        
        with file_lock:
            # 讀取現有資料
            existing_data = read_json_file(file_path, [])
            
            if not existing_data:
                return True
            
            # 去重
            seen_keys = set()
            unique_data = []
            
            for item in existing_data:
                if isinstance(item, dict) and unique_key in item:
                    key_value = item[unique_key]
                    if key_value not in seen_keys:
                        seen_keys.add(key_value)
                        unique_data.append(item)
                else:
                    # 沒有unique_key的項目直接保留
                    unique_data.append(item)
            
            # 如果有重複項目，重新寫入檔案
            if len(unique_data) < len(existing_data):
                removed_count = len(existing_data) - len(unique_data)
                if write_json_file(file_path, unique_data, backup=True):
                    print(f"✅ 清理 {removed_count} 筆重複資料，保留 {len(unique_data)} 筆")
                    return True
                else:
                    return False
            else:
                print(f"✅ 檔案中無重複資料，共 {len(unique_data)} 筆")
                return True
                
    except Exception as e:
        print(f"❌ 清理重複資料失敗: {e}")
        return False

def get_json_stats(file_path: str) -> Dict[str, Any]:
    """
    獲取JSON檔案的統計資訊
    
    Args:
        file_path: 檔案路徑
        
    Returns:
        統計資訊字典
    """
    try:
        if not os.path.exists(file_path):
            return {
                "exists": False,
                "count": 0,
                "size_mb": 0,
                "last_modified": None
            }
        
        data = read_json_file(file_path, [])
        file_stats = os.stat(file_path)
        
        return {
            "exists": True,
            "count": len(data),
            "size_mb": round(file_stats.st_size / 1024 / 1024, 2),
            "last_modified": datetime.fromtimestamp(file_stats.st_mtime).strftime("%Y-%m-%d %H:%M:%S")
        }
        
    except Exception as e:
        print(f"❌ 獲取檔案統計失敗: {e}")
        return {
            "exists": False,
            "count": 0,
            "size_mb": 0,
            "last_modified": None,
            "error": str(e)
        }

# 便利函數：常用的append模式
def append_href_data(platform: str, legislator: str, urls: List[str]) -> bool:
    """
    Append href資料（URL列表）
    
    Args:
        platform: 平台名稱 (youtube, ptt, threads, facebook)
        legislator: 立委名稱
        urls: URL列表
        
    Returns:
        操作是否成功
    """
    if not urls:
        return True
        
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, "href", platform, f"{legislator}.json")
    
    # 將URL字符串轉換為字典格式（可包含時間戳等metadata）
    url_data = []
    for url in urls:
        if isinstance(url, str):
            url_data.append({
                "url": url,
                "added_time": datetime.now().strftime("%Y-%m-%d")
            })
        else:
            url_data.append(url)
    
    return append_json_data(file_path, url_data, unique_key="url")

def append_crawler_data(platform: str, legislator: str, comments: List[Dict]) -> bool:
    """
    Append爬蟲資料（留言數據）
    
    Args:
        platform: 平台名稱 (youtube, ptt, threads, facebook)
        legislator: 立委名稱
        comments: 留言數據列表
        
    Returns:
        操作是否成功
    """
    if not comments:
        return True
        
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, "data", platform, f"{legislator}.json")
    
    # 根據平台決定unique_key
    unique_key_map = {
        "youtube": "video_id",  # YouTube 改用 video_id
        "ptt": "ptt_comment_id", 
        "threads": "threads_post_id",  # Threads 使用 threads_post_id
        "facebook": "facebook_post_id"  # Facebook 使用 facebook_post_id
    }
    
    unique_key = unique_key_map.get(platform)
    return append_json_data(file_path, comments, unique_key=unique_key)

def append_href_data_with_metadata(platform: str, legislator: str, data_entries: List[dict]) -> bool:
    """
    Append href資料（包含完整metadata的字典列表）

    Args:
        platform: 平台名稱 (youtube, ptt, threads, facebook)
        legislator: 立委名稱
        data_entries: 包含完整metadata的字典列表

    Returns:
        bool: 是否成功
    """
    if not data_entries:
        return True

    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, "href", platform, f"{legislator}.json")

    return append_json_data(file_path, data_entries, unique_key="url")

if __name__ == "__main__":
    # 測試函數
    print("🧪 測試JSON工具函數...")
    
    test_file = "test_append.json"
    
    # 測試append
    test_data1 = [{"id": 1, "name": "test1"}, {"id": 2, "name": "test2"}]
    print("測試append:", append_json_data(test_file, test_data1, unique_key="id"))
    
    # 測試重複append
    test_data2 = [{"id": 2, "name": "test2_updated"}, {"id": 3, "name": "test3"}]
    print("測試去重append:", append_json_data(test_file, test_data2, unique_key="id"))
    
    # 檢查結果
    result = read_json_file(test_file)
    print("最終結果:", result)
    print("統計資訊:", get_json_stats(test_file))
    
    # 清理測試檔案
    if os.path.exists(test_file):
        os.remove(test_file)
    
    print("✅ 測試完成")
