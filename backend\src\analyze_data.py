"""
分析時間範圍數據的輔助函數
這個文件包含用於分析時間範圍內數據的函數
"""

import jieba
import re
from collections import Counter
from datetime import datetime
import time

def analyze_time_range_data(crawler_data, start_date=None, end_date=None):
    """
    分析指定時間範圍的數據，生成情感、情緒、詞雲和時間序列
    
    參數:
        crawler_data: 爬蟲數據列表
        start_date: 開始日期 (YYYY-MM-DD 格式)
        end_date: 結束日期 (YYYY-MM-DD 格式)
        
    返回:
        dict: 包含所有前端所需的資料結構
    """
    start_time = time.time()
    
    # 初始化數據結構
    sentiment_counts = {"support_count": 0, "oppose_count": 0}
    emotion_stats = {"positive": {}, "negative": {}}
    word_cloud = []
    time_series_data = {}
    
    # 初始化情緒統計
    allowed_emotions = ['joy', 'anger', 'sadness', 'fear', 'surprise', 'disgust', 'trust', 'anticipation']
    for emotion in allowed_emotions:
        emotion_stats["positive"][emotion] = 0
        emotion_stats["negative"][emotion] = 0
    
    # 詞雲處理
    word_counter = Counter()
    
    # 預定義欄位
    sentiment_field = '情感標籤'
    emotion_field = '情緒'
    date_field = '日期'
    text_fields = ['標題', '留言內容']
      # 檢查前幾條記錄，確定具體的欄位名稱
    sample_data = crawler_data[:min(5, len(crawler_data))]
    for sample in sample_data:
        # 情感標籤欄位檢測
        if '情感標籤' in sample:
            sentiment_field = '情感標籤'
        elif 'sentiment' in sample:
            sentiment_field = 'sentiment'
        elif 'sentiment_label' in sample:
            sentiment_field = 'sentiment_label'
        
        # 情緒欄位檢測
        if '情緒' in sample:
            emotion_field = '情緒'
        elif 'emotion' in sample:
            emotion_field = 'emotion'
        elif 'emotion_label' in sample:
            emotion_field = 'emotion_label'
            
        # 日期欄位檢測
        if '日期' in sample:
            date_field = '日期'
        elif 'date' in sample:
            date_field = 'date'
        elif 'created_at' in sample:
            date_field = 'created_at'
        
        # 檢測可能的文本欄位
        for field in ['留言內容', 'content', 'text', 'comment', '標題', 'title']:
            if field in sample and sample[field]:
                if field not in text_fields:
                    text_fields.append(field)
    
    print(f"📊 使用欄位: 情感={sentiment_field}, 情緒={emotion_field}, 日期={date_field}, 文本={text_fields}")
      # 定義情感標籤集合 - 擴展以匹配更多可能的值
    positive_sentiments = {'POSITIVE', 'positive', '正面', '反對', '反對罷免', 'Positive', '1'}
    negative_sentiments = {'NEGATIVE', 'negative', '負面', '支持', '支持罷免', 'Negative', '0'}
    
    # 載入停用詞
    stopwords = load_stopwords() if 'load_stopwords' in globals() else set()
    
    # 調試數據和情感標籤匹配情況
    debug_data_sentiment_match(crawler_data, sentiment_field, positive_sentiments, negative_sentiments)
    
    # 處理數據
    for record in crawler_data:
        # 處理情感分析
        sentiment = None
        if sentiment_field in record:
            sentiment_value = str(record[sentiment_field]).strip().upper()
            
            if sentiment_value in positive_sentiments:
                sentiment_counts['oppose_count'] += 1
                sentiment = 'positive'  # 反對罷免 = positive
            elif sentiment_value in negative_sentiments:
                sentiment_counts['support_count'] += 1
                sentiment = 'negative'  # 支持罷免 = negative
        
        # 處理情緒
        if sentiment and emotion_field in record:
            emotion = str(record[emotion_field]).strip().lower()
            if emotion in allowed_emotions:
                if sentiment == 'positive':
                    emotion_stats['positive'][emotion] += 1
                else:
                    emotion_stats['negative'][emotion] += 1
        
        # 處理時間序列
        if date_field in record:
            date_str = None
            if isinstance(record[date_field], datetime):
                date_str = record[date_field].strftime('%Y-%m-%d')
            elif isinstance(record[date_field], str):
                date_str = record[date_field]
                
            if date_str:
                if date_str not in time_series_data:
                    time_series_data[date_str] = {"support": 0, "oppose": 0}
                    
                if sentiment == 'positive':
                    time_series_data[date_str]['oppose'] += 1
                elif sentiment == 'negative':
                    time_series_data[date_str]['support'] += 1
        
        # 處理詞雲 - 使用更簡化的處理方式
        text_content = ""
        for field in text_fields:
            if field in record and record[field]:
                text_content += str(record[field]) + " "
                
        if text_content:
            # 使用jieba分詞
            words = jieba.cut(text_content)
            for word in words:
                word = word.strip()
                if (len(word) >= 2 and len(word) <= 8 and  # 詞長度合理
                    not word.isdigit() and  # 不是純數字
                    not all(c in '，。！？；：""''（）【】' for c in word) and  # 不是標點符號
                    word not in stopwords):  # 不是停用詞
                    word_counter[word] += 1
    
    # 生成詞雲
    word_cloud = [
        {"text": word, "weight": count}
        for word, count in word_counter.most_common(50)
        if len(word) >= 2 and count > 1  # 確保只返回有意義的詞
    ]
    
    # 生成時間序列圖表
    time_series_chart = generate_time_series_chart(time_series_data, start_date, end_date)
    
    # 生成雷達圖數據
    radar_chart_labels = allowed_emotions
    radar_data_support = [emotion_stats["negative"].get(emotion, 0) for emotion in radar_chart_labels]
    radar_data_oppose = [emotion_stats["positive"].get(emotion, 0) for emotion in radar_chart_labels]
    
    # 計算處理時間
    end_time = time.time()
    process_time = end_time - start_time
    
    # 返回結果
    result = {
        "sentiment_analysis": sentiment_counts,
        "emotion_analysis": {
            "labels": radar_chart_labels,
            "datasets": [
                {
                    "label": "反對罷免情緒", 
                    "data": radar_data_oppose,
                    "backgroundColor": "rgba(79, 140, 255, 0.2)",
                    "borderColor": "rgba(79, 140, 255, 1)",
                    "pointBackgroundColor": "rgba(79, 140, 255, 1)",
                    "pointBorderColor": "#fff"
                },
                {
                    "label": "支持罷免情緒", 
                    "data": radar_data_support,
                    "backgroundColor": "rgba(248, 113, 113, 0.2)",
                    "borderColor": "rgba(248, 113, 113, 1)",
                    "pointBackgroundColor": "rgba(248, 113, 113, 1)",
                    "pointBorderColor": "#fff"
                }
            ]
        },
        "emotion_analysis_detailed": {
            "positive": emotion_stats["positive"],
            "negative": emotion_stats["negative"]
        },
        "word_cloud": word_cloud,
        "time_series": time_series_chart,
        "total_records": len(crawler_data),
        "processing_time": f"{process_time:.2f}秒"
    }
    
    print(f"✅ 單進程處理完成: 情感統計={sentiment_counts}, 時間序列={len(time_series_data)}個日期, 詞雲={len(word_cloud)}個詞")
    
    return result

def debug_data_sentiment_match(data, sentiment_field, positive_sentiments, negative_sentiments):
    """
    檢查資料中的情感標籤是否與定義的正面/負面情感集合匹配
    
    參數:
        data: 要檢查的數據列表
        sentiment_field: 情感標籤欄位名稱
        positive_sentiments: 正面情感標籤集合
        negative_sentiments: 負面情感標籤集合
    """
    if not sentiment_field:
        print("❌ 未找到情感標籤欄位")
        return
    
    sample_size = min(30, len(data))
    total_count = 0
    positive_count = 0
    negative_count = 0
    unknown_count = 0
    unknown_values = set()
    
    for item in data[:sample_size]:
        if sentiment_field in item:
            total_count += 1
            sentiment = str(item[sentiment_field]).strip().upper()
            
            if sentiment in positive_sentiments:
                positive_count += 1
            elif sentiment in negative_sentiments:
                negative_count += 1
            else:
                unknown_count += 1
                unknown_values.add(sentiment)
    

    if unknown_values:
        print(f"  - 未知情感值: {unknown_values}")
    
    match_rate = (positive_count + negative_count) / total_count if total_count > 0 else 0
    print(f"  - 匹配率: {match_rate:.1%}")

def load_stopwords():
    """載入停用詞文件，使用緩存提升效率"""
    global _stopwords_cache
    if '_stopwords_cache' in globals() and _stopwords_cache is not None:
        return _stopwords_cache

    import os
    stopwords = set()

    try:
        stopwords_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'stopwords.txt')
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):  # 忽略空行和註釋
                    stopwords.add(line)
    except Exception as e:
        print(f"⚠️ 載入停用詞失敗: {e}")
        # 使用基本停用詞作為後備
        stopwords = {'的', '了', '是', '我', '也', '和', '就', '都', '不', '在', '會', '要', '很'}

    _stopwords_cache = stopwords
    return stopwords

def generate_time_series_chart(time_series_data, start_date, end_date):
    """
    生成時間序列圖表數據 - 根據時間範圍動態調整數據點數量
    """
    from datetime import datetime, timedelta

    # 解析日期
    if isinstance(start_date, str):
        start = datetime.strptime(start_date, '%Y-%m-%d')
    else:
        start = start_date if start_date else datetime.now() - timedelta(days=30)

    if isinstance(end_date, str):
        end = datetime.strptime(end_date, '%Y-%m-%d')
    else:
        end = end_date if end_date else datetime.now()

    # 計算時間間隔
    total_days = (end - start).days + 1
    if total_days <= 0:
        return {"labels": [], "datasets": []}

    # 動態決定數據點數量和間隔
    def calculate_optimal_points(days):
        """根據天數動態計算最佳數據點數量"""
        if days <= 7:
            return days, 1  # 7天以內：每天一個點
        elif days <= 30:
            return min(15, days), max(1, days // 15)  # 30天以內：最多15個點
        elif days <= 90:
            return min(20, days // 3), max(1, days // 20)  # 90天以內：最多20個點
        elif days <= 365:
            return min(25, days // 7), max(1, days // 25)  # 一年以內：最多25個點
        else:
            return min(30, days // 14), max(1, days // 30)  # 超過一年：最多30個點

    target_points, interval_days = calculate_optimal_points(total_days)

    # 生成圖表數據
    labels = []
    support_data = []
    oppose_data = []

    current_date = start
    while current_date <= end:
        # 計算這個時間點的數據
        period_support = 0
        period_oppose = 0

        # 累計這個間隔內的數據
        for i in range(interval_days):
            check_date = current_date + timedelta(days=i)
            if check_date > end:
                break
            date_key = check_date.strftime('%Y-%m-%d')
            if date_key in time_series_data:
                period_support += time_series_data[date_key]['support']
                period_oppose += time_series_data[date_key]['oppose']

        labels.append(current_date.strftime('%m/%d'))
        support_data.append(period_support)
        oppose_data.append(period_oppose)

        current_date += timedelta(days=interval_days)

    # 確保至少有一個數據點
    if not labels:
        labels = [start.strftime('%m/%d')]
        support_data = [0]
        oppose_data = [0]

    return {
        "labels": labels,
        "datasets": [
            {
                "label": "支持罷免",
                "data": support_data,
                "borderColor": "#f87171",
                "backgroundColor": "rgba(248, 113, 113, 0.1)",
                "tension": 0.3,
                "fill": True
            },
            {
                "label": "反對罷免",
                "data": oppose_data,
                "borderColor": "#4f8cff",
                "backgroundColor": "rgba(79, 140, 255, 0.1)",
                "tension": 0.3,
                "fill": True
            }
        ]
    }
