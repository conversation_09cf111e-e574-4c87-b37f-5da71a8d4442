#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試優化爬蟲腳本
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def test_optimized_crawler():
    """測試優化爬蟲"""
    logger.info("🧪 測試優化爬蟲管理器...")
    
    try:
        from crawler.optimized_crawler_manager import OptimizedCrawlerManager
        
        manager = OptimizedCrawlerManager()
        
        # 測試優化爬取流程
        result = manager.crawl_politician_optimized(
            politician_name="高虹安",
            cutoff_date_str="2025-01-01",
            platforms=['youtube', 'ptt', 'threads'],
            max_workers_per_platform=4
        )
        
        logger.info("📊 優化爬蟲結果:")
        logger.info(f"   立委: {result.get('politician_name')}")
        logger.info(f"   成功平台: {result['summary']['successful_platforms']}/{result['summary']['total_platforms']}")
        logger.info(f"   成功率: {result['summary']['success_rate']:.2%}")
        
        # 詳細結果
        for platform, platform_result in result.get('content_crawling', {}).items():
            if platform_result.get('success'):
                count = platform_result.get('count', 0)
                logger.info(f"   ✅ {platform.upper()}: {count} 個項目")
            else:
                error = platform_result.get('error', '未知錯誤')
                logger.info(f"   ❌ {platform.upper()}: {error}")
        
        if result['summary']['successful_platforms'] > 0:
            logger.info("✅ 優化爬蟲測試通過")
            return True
        else:
            logger.warning("⚠️ 優化爬蟲所有平台都失敗")
            return False
            
    except Exception as e:
        logger.error(f"❌ 優化爬蟲測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integrated_manager():
    """測試整合管理器"""
    logger.info("🧪 測試整合管理器...")
    
    try:
        from crawler.integrated_crawler_manager import IntegratedCrawlerManager
        
        manager = IntegratedCrawlerManager()
        
        # 測試增量模式
        result = manager.crawl_all_legislators_incremental(
            legislators=["高虹安"],
            platforms=['youtube', 'ptt', 'threads']
        )
        
        logger.info("📊 整合管理器結果:")
        logger.info(f"   模式: {result.get('mode')}")
        logger.info(f"   處理成功: {result.get('legislators_processed')}")
        logger.info(f"   處理失敗: {result.get('legislators_failed')}")
        logger.info(f"   統計更新: {result.get('statistics_updated')}")
        
        if result.get('legislators_processed', 0) > 0:
            logger.info("✅ 整合管理器測試通過")
            return True
        else:
            logger.warning("⚠️ 整合管理器處理失敗")
            return False
            
    except Exception as e:
        logger.error(f"❌ 整合管理器測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    logger.info("🚀 開始測試優化爬蟲系統...")
    
    tests = [
        ("優化爬蟲管理器", test_optimized_crawler),
        ("整合管理器", test_integrated_manager)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"測試: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"測試 {test_name} 時發生異常: {e}")
            results[test_name] = False
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("測試總結")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed == total:
        logger.info("🎉 所有測試都通過！")
        return 0
    else:
        logger.warning(f"⚠️ 有 {total - passed} 個測試失敗")
        return 1

if __name__ == "__main__":
    sys.exit(main())
