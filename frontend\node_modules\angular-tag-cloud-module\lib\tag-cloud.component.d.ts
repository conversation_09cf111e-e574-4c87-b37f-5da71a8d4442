import { CloudData, CloudOptions, ZoomOnHoverOptions } from './tag-cloud.interfaces';
import * as i0 from "@angular/core";
export declare class TagCloudComponent {
    data: import("@angular/core").InputSignal<CloudData[]>;
    width: import("@angular/core").InputSignal<number | undefined>;
    height: import("@angular/core").InputSignal<number | undefined>;
    step: import("@angular/core").InputSignal<number | undefined>;
    overflow: import("@angular/core").InputSignal<boolean | undefined>;
    strict: import("@angular/core").InputSignal<boolean | undefined>;
    zoomOnHover: import("@angular/core").InputSignal<ZoomOnHoverOptions | undefined>;
    realignOnResize: import("@angular/core").InputSignal<boolean | undefined>;
    randomizeAngle: import("@angular/core").InputSignal<boolean | undefined>;
    background: import("@angular/core").InputSignal<string | undefined>;
    font: import("@angular/core").InputSignal<string | undefined>;
    delay: import("@angular/core").InputSignal<number | undefined>;
    config: import("@angular/core").InputSignal<CloudOptions>;
    log: import("@angular/core").InputSignal<false | "warn" | "debug" | undefined>;
    clicked: import("@angular/core").OutputEmitterRef<CloudData>;
    afterInit: import("@angular/core").OutputEmitterRef<void>;
    afterChecked: import("@angular/core").OutputEmitterRef<void>;
    private localConfig;
    cloudDataHtmlElements: HTMLElement[];
    private dataArr;
    private options;
    private timeoutId;
    get calculatedWidth(): number;
    get calculatedHeight(): number;
    private el;
    private r2;
    onResize(event: any): void;
    constructor();
    /**
     * re-draw the word cloud
     * @param changes the change set
     */
    reDraw(): void;
    /**
     * helper to generate a descriptive string for an entry to use when sorting alphabetically
     * @param entry the cloud entry to be used
     */
    private descriptiveEntry;
    /**
     * proceed draw the cloud
     */
    private drawWordCloud;
    /**
     * Helper function to test if an element overlaps others
     * @param rect the DOM rectangle that represents the element's bounds
     */
    private hitTest;
    /**
     * Pairwise overlap detection
     * @param rect the DOM rectangle that represents the element's bounds
     * @param e2 the second element for overlap detection
     */
    private overlapping;
    /**
     * Check if min(weight) > max(weight) otherwise use default
     * @param word the particular word configuration
     */
    private getWeightForWord;
    /**
     * change the HTMLElements color style
     * @param el the HTML element
     * @param color the CSS color value
     */
    private setWordColor;
    /**
     * Add a tooltip to the element
     * @param el the HTML element
     * @param tooltip the tooltip text
     */
    private setTooltip;
    /**
     * change the HTMLElements rotation style
     * @param el the HTML element
     * @param deg the rotation value (degrees)
     */
    private setWordRotation;
    /**
     * wrap the given node into an HTML anchor element
     * @param node the HTML node that should be wrapped
     * @param word the particular word configuration
     */
    private wrapNodeIntoAnchorElement;
    /**
     * wrap the given node into an HTML anchor element
     * @param node the HTML node that should be wrapped
     * @param word the particular word configuration
     */
    private applyZoomStyle;
    /**
     * Place the word at a calculated position
     * @param wordSpan The HTML Span element to be placed
     * @param word The word to be placed
     * @param index The index of the element
     */
    private setPosition;
    /**
     * Methods to draw a word, by moving it in spiral until it finds a suitable empty place.
     * This will be iterated on each word.
     * @param index the index number for the word
     * @param word the particular word configuration
     */
    private drawWord;
    /**
     * Log messages to console
     * @param level the log level
     * @param args extra args to be logged
     */
    private logMessage;
    static ɵfac: i0.ɵɵFactoryDeclaration<TagCloudComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TagCloudComponent, "angular-tag-cloud, ng-tag-cloud, ngtc", never, { "data": { "alias": "data"; "required": false; "isSignal": true; }; "width": { "alias": "width"; "required": false; "isSignal": true; }; "height": { "alias": "height"; "required": false; "isSignal": true; }; "step": { "alias": "step"; "required": false; "isSignal": true; }; "overflow": { "alias": "overflow"; "required": false; "isSignal": true; }; "strict": { "alias": "strict"; "required": false; "isSignal": true; }; "zoomOnHover": { "alias": "zoomOnHover"; "required": false; "isSignal": true; }; "realignOnResize": { "alias": "realignOnResize"; "required": false; "isSignal": true; }; "randomizeAngle": { "alias": "randomizeAngle"; "required": false; "isSignal": true; }; "background": { "alias": "background"; "required": false; "isSignal": true; }; "font": { "alias": "font"; "required": false; "isSignal": true; }; "delay": { "alias": "delay"; "required": false; "isSignal": true; }; "config": { "alias": "config"; "required": false; "isSignal": true; }; "log": { "alias": "log"; "required": false; "isSignal": true; }; }, { "clicked": "clicked"; "afterInit": "afterInit"; "afterChecked": "afterChecked"; }, never, never, true, never>;
}
