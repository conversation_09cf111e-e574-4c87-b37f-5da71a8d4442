/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    BYN: (string | undefined)[];
    EUR: (string | undefined)[];
    GBP: (string | undefined)[];
    JPY: string[];
    PHP: (string | undefined)[];
    USD: string[];
})[];
export default _default;
