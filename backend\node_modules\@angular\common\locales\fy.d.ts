/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AUD: string[];
    CAD: string[];
    FJD: string[];
    JPY: string[];
    SBD: string[];
    THB: string[];
    USD: string[];
    XPF: never[];
} | undefined)[];
export default _default;
