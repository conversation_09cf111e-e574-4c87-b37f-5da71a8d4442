# 立委罷免數據爬蟲系統 - 簡化版

## 概述
這是一個簡化版的立委罷免數據爬蟲系統，只保留核心功能：
- 指定立委進行數據爬取
- 指定爬取天數
- 指定爬取平台
- 自動進行情感分析和數據處理
- 自動更新MongoDB數據庫

## 快速開始

### 1. 環境設置
```bash
# 安裝依賴
pip install -r requirements.txt

# 設置環境變數
export GEMINI_API_KEY="your_gemini_api_key"
```

### 2. 基本使用

#### 爬取最近30天所有立委數據（預設）
```bash
python main_simple.py
```

#### 指定天數
```bash
python main_simple.py --days 400
```

#### 指定平台
```bash
python main_simple.py --platforms youtube,ptt
```

#### 指定立委
```bash
python main_simple.py --legislators 高虹安 牛煦庭
```

#### 組合使用
```bash
python main_simple.py --days 400 --platforms youtube,ptt --legislators 高虹安
```

## 參數說明

| 參數 | 說明 | 預設值 | 範例 |
|------|------|--------|------|
| `--days` | 爬取天數 | 30 | `--days 400` |
| `--platforms` | 爬取平台，用逗號分隔 | youtube,ptt | `--platforms youtube,ptt,threads` |
| `--legislators` | 指定立委姓名 | 所有立委 | `--legislators 高虹安 牛煦庭` |

## 支援的平台
- `youtube` - YouTube影片評論
- `ptt` - PTT討論版文章
- `threads` - Threads貼文（實驗性）

## 支援的立委
系統支援以下27位立委：
- 高虹安、牛煦庭、葉元之、王鴻薇、游顥、徐巧芯、吳宗憲、陳菁徽
- 張智倫、鍾佳濱、林楚茵、吳琪銘、蘇巧慧、何志偉、林昶佐、范雲
- 洪申翰、林宜瑾、莊競程、邱議瑩、林淑芬、賴瑞隆、邱志偉、鄭運鵬
- 湯蕙禎、黃捷、陳椒華

## 處理流程
1. **數據爬取** - 從指定平台收集相關討論
2. **情感分析** - 使用Gemini AI進行情感和情緒分析
3. **數據處理** - 統計分析和文字雲生成
4. **數據庫更新** - 更新MongoDB中的數據
5. **前端優化** - 為前端顯示優化數據格式

## 輸出文件
- `crawler.log` - 執行日誌
- `crawler/processed/` - 處理後的數據文件
- MongoDB數據庫 - 最終統計結果

## 常見問題

### Q: 如何檢查爬蟲是否成功？
A: 查看日誌輸出，成功會顯示 "✅ [立委名] 處理完成"

### Q: 爬蟲速度很慢怎麼辦？
A: 可以先指定單個立委測試：`python main_simple.py --legislators 高虹安 --days 1`

### Q: 如何只爬取特定平台？
A: 使用 `--platforms` 參數：`python main_simple.py --platforms youtube`

### Q: 環境變數設置問題？
A: 確保設置了 `GEMINI_API_KEY` 環境變數

## 注意事項
- 首次運行會創建必要的目錄結構
- 大量數據爬取需要較長時間，建議先小範圍測試
- 確保網路連接穩定
- MongoDB需要正常運行

## 範例命令

```bash
# 測試單個立委1天數據
python main_simple.py --legislators 高虹安 --days 1

# 爬取多個立委最近一週數據
python main_simple.py --legislators 高虹安 牛煦庭 --days 7

# 爬取400天YouTube數據
python main_simple.py --days 400 --platforms youtube

# 爬取所有立委最近30天數據（預設）
python main_simple.py
```
