#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試實際YouTube爬蟲修復效果
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def test_real_youtube_crawling():
    """測試實際YouTube爬蟲"""
    logger.info("🧪 測試實際YouTube爬蟲修復效果...")
    
    try:
        from crawler.yt_crawler import search_youtube_videos
        
        # 測試參數
        politician_name = "牛煦庭"
        cutoff_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        logger.info(f"測試立委: {politician_name}")
        logger.info(f"截止日期: {cutoff_date} (爬取前1天資料)")
        logger.info("開始爬取...")
        
        # 執行URL收集
        url_file, video_data = search_youtube_videos(
            name=politician_name,
            cutoff_date=cutoff_date,
            headless=True
        )
        
        logger.info(f"\n🎯 爬取結果: 收集到 {len(video_data)} 個影片")
        
        if video_data:
            # 分析收集到的影片
            today = datetime.now().strftime('%Y-%m-%d')
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            
            today_count = 0
            yesterday_count = 0
            hour_videos = []
            
            logger.info("\n📊 收集到的影片詳情:")
            for i, video in enumerate(video_data):
                time_str = video.get('time', 'Unknown')
                date_str = video.get('date', 'Unknown')
                post_time = video.get('post_time', 'Missing')
                
                logger.info(f"   {i+1}. 時間: {time_str}")
                logger.info(f"      日期: {date_str}")
                logger.info(f"      post_time: {post_time}")
                logger.info(f"      URL: {video.get('url', 'Unknown')[:60]}...")
                
                # 統計
                if date_str == today:
                    today_count += 1
                elif date_str == yesterday:
                    yesterday_count += 1
                
                # 檢查小時級別的影片
                if '小時前' in time_str:
                    hour_videos.append((time_str, date_str))
                
                logger.info("")
            
            # 統計分析
            logger.info("📈 統計分析:")
            logger.info(f"   今天({today}): {today_count} 個影片")
            logger.info(f"   昨天({yesterday}): {yesterday_count} 個影片")
            logger.info(f"   小時級別影片: {len(hour_videos)} 個")
            
            if hour_videos:
                logger.info("   小時級別影片詳情:")
                for time_str, date_str in hour_videos:
                    logger.info(f"     - {time_str} -> {date_str}")
            
            # 檢查post_time字段
            has_post_time = all('post_time' in video for video in video_data)
            logger.info(f"   post_time字段: {'✅ 完整' if has_post_time else '❌ 缺失'}")
            
            # 驗證修復效果
            if len(video_data) > 2:  # 應該收集到更多影片
                logger.info("\n✅ YouTube修復效果良好！收集到的影片數量明顯增加")
                
                if len(hour_videos) > 0:
                    logger.info("✅ 小時級別的影片也被正確收集")
                
                if has_post_time:
                    logger.info("✅ post_time字段完整保存")
                
                return True
            else:
                logger.warning(f"\n⚠️ 收集到的影片數量仍然較少: {len(video_data)}個")
                logger.warning("可能需要進一步檢查或該立委確實沒有更多近期影片")
                return False
        else:
            logger.error("\n❌ 沒有收集到任何影片")
            return False
            
    except Exception as e:
        logger.error(f"❌ 實際YouTube爬蟲測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_href_file():
    """檢查href文件內容"""
    logger.info("🧪 檢查href文件內容...")
    
    try:
        href_file = os.path.join(current_dir, 'crawler', 'href', 'youtube', '牛煦庭.json')
        
        if os.path.exists(href_file):
            with open(href_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"href文件包含 {len(data)} 個項目")
            
            if data:
                # 檢查最新的幾個項目
                logger.info("最新的3個項目:")
                for i, item in enumerate(data[-3:]):
                    logger.info(f"   {i+1}. URL: {item.get('url', 'Unknown')[:50]}...")
                    logger.info(f"      post_time: {item.get('post_time', 'Missing')}")
                    logger.info(f"      added_time: {item.get('added_time', 'Missing')}")
                    logger.info("")
                
                # 檢查post_time字段覆蓋率
                has_post_time_count = sum(1 for item in data if isinstance(item, dict) and 'post_time' in item)
                coverage = has_post_time_count / len(data) * 100
                
                logger.info(f"post_time字段覆蓋率: {has_post_time_count}/{len(data)} ({coverage:.1f}%)")
                
                return True
            else:
                logger.warning("href文件為空")
                return False
        else:
            logger.warning("href文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"檢查href文件失敗: {e}")
        return False

def main():
    """主測試函數"""
    logger.info("🚀 開始測試實際YouTube爬蟲修復效果...")
    
    tests = [
        ("實際YouTube爬蟲", test_real_youtube_crawling),
        ("href文件檢查", check_href_file)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"測試: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"測試 {test_name} 時發生異常: {e}")
            results[test_name] = False
    
    # 總結
    logger.info(f"\n{'='*60}")
    logger.info("實際YouTube爬蟲修復測試總結")
    logger.info(f"{'='*60}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed >= 1:  # 至少1個測試通過
        logger.info("🎉 YouTube修復基本成功！")
        logger.info("現在應該能收集到更多符合條件的影片了！")
        return 0
    else:
        logger.warning("⚠️ YouTube修復需要進一步檢查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
