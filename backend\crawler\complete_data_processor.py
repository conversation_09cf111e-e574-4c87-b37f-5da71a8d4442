#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整數據處理流程
整合所有步驟：原始數據 -> 用戶格式 -> Gemini分析 -> MongoDB存儲

流程：
1. 合併各平台原始數據到 processed/alldata/
2. 創建用戶格式數據到 processed/user_data/
3. 執行Gemini情感分析，結果存到 processed/final_data/
4. 增量更新到MongoDB的crawler_data和legislators集合

使用方式：
python complete_data_processor.py --legislators 牛煦庭 --days 1
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timedelta

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 設定路徑
PROCESSED_DIR = os.path.join(current_dir, 'processed')
ALLDATA_DIR = os.path.join(PROCESSED_DIR, 'alldata')
USER_DATA_DIR = os.path.join(PROCESSED_DIR, 'user_data')
FINAL_DATA_DIR = os.path.join(PROCESSED_DIR, 'final_data')

# 確保目錄存在
os.makedirs(PROCESSED_DIR, exist_ok=True)
os.makedirs(ALLDATA_DIR, exist_ok=True)
os.makedirs(USER_DATA_DIR, exist_ok=True)
os.makedirs(FINAL_DATA_DIR, exist_ok=True)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def parse_arguments():
    """解析命令列參數"""
    parser = argparse.ArgumentParser(description='完整數據處理流程')
    parser.add_argument('--legislators', nargs='+', 
                       help='指定處理特定立委 (預設: 處理所有立委)')
    parser.add_argument('--days', type=int, default=1,
                       help='處理前N天的資料 (預設: 1)')
    parser.add_argument('--skip-merge', action='store_true',
                       help='跳過數據合併步驟')
    parser.add_argument('--skip-user-format', action='store_true',
                       help='跳過用戶格式化步驟')
    parser.add_argument('--skip-analysis', action='store_true',
                       help='跳過Gemini分析步驟')
    parser.add_argument('--skip-mongodb', action='store_true',
                       help='跳過MongoDB存儲步驟')
    parser.add_argument('--force-reprocess', action='store_true',
                       help='強制重新處理所有資料')
    parser.add_argument('--quiet', action='store_true',
                       help='減少輸出訊息')
    return parser.parse_args()

def step1_merge_platform_data(legislators):
    """步驟1：合併各平台數據到alldata目錄"""
    logger.info("🔄 步驟1：合併各平台數據...")
    
    try:
        from user_data_processor import merge_platform_data
        merge_platform_data()
        logger.info("✅ 步驟1完成：平台數據合併")
        return True
    except Exception as e:
        logger.error(f"❌ 步驟1失敗：{e}")
        return False

def step2_create_user_format(legislators):
    """步驟2：創建用戶格式數據"""
    logger.info("🔄 步驟2：創建用戶格式數據...")
    
    try:
        from user_data_processor import create_user_format_for_gemini
        success = create_user_format_for_gemini(legislators)
        if success:
            logger.info("✅ 步驟2完成：用戶格式數據創建")
            return True
        else:
            logger.error("❌ 步驟2失敗：用戶格式數據創建失敗")
            return False
    except Exception as e:
        logger.error(f"❌ 步驟2失敗：{e}")
        return False

def step3_gemini_analysis(legislators):
    """步驟3：執行Gemini情感分析"""
    logger.info("🔄 步驟3：執行Gemini情感分析...")
    
    try:
        from gemini_emo_user import analyze_legislators_emotions_incremental
        analyze_legislators_emotions_incremental(legislators, batch_size=10, quiet=False)
        logger.info("✅ 步驟3完成：Gemini情感分析")
        return True
    except Exception as e:
        logger.error(f"❌ 步驟3失敗：{e}")
        return False

def step4_mongodb_storage(legislators):
    """步驟4：增量更新到MongoDB"""
    logger.info("🔄 步驟4：增量更新到MongoDB...")
    
    try:
        from data_to_mongo_v2 import DataToMongo
        mongo_handler = DataToMongo()
        
        success_count = 0
        for legislator in legislators:
            try:
                result = mongo_handler.store_crawler_data(legislator, is_incremental=True)
                if result:
                    success_count += 1
                    logger.info(f"✅ {legislator} MongoDB更新成功")
                else:
                    logger.warning(f"⚠️ {legislator} MongoDB更新失敗")
            except Exception as e:
                logger.error(f"❌ {legislator} MongoDB更新異常：{e}")
        
        mongo_handler.close()
        
        if success_count > 0:
            logger.info(f"✅ 步驟4完成：{success_count}/{len(legislators)} 位立委MongoDB更新成功")
            return True
        else:
            logger.error("❌ 步驟4失敗：所有立委MongoDB更新都失敗")
            return False
            
    except Exception as e:
        logger.error(f"❌ 步驟4失敗：{e}")
        return False

def check_data_files(legislators):
    """檢查數據文件是否存在"""
    logger.info("🔍 檢查數據文件...")
    
    missing_files = []
    for legislator in legislators:
        # 檢查原始數據
        data_dir = os.path.join(current_dir, 'data')
        platforms = ['youtube', 'ptt', 'threads', 'facebook']
        
        has_data = False
        for platform in platforms:
            file_path = os.path.join(data_dir, platform, f"{legislator}.json")
            if os.path.exists(file_path):
                has_data = True
                break
        
        if not has_data:
            missing_files.append(f"{legislator} (無任何平台數據)")
    
    if missing_files:
        logger.warning(f"⚠️ 缺少數據文件：{missing_files}")
    else:
        logger.info("✅ 所有立委都有數據文件")
    
    return len(missing_files) == 0

def main():
    """主函數"""
    args = parse_arguments()
    
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    
    logger.info("🚀 開始完整數據處理流程...")
    logger.info(f"處理參數：立委={args.legislators}, 天數={args.days}")
    
    # 確定要處理的立委列表
    if args.legislators:
        legislators = args.legislators
    else:
        # 自動發現所有有數據的立委
        data_dir = os.path.join(current_dir, 'data', 'youtube')
        if os.path.exists(data_dir):
            legislators = [f.replace('.json', '') for f in os.listdir(data_dir) if f.endswith('.json')]
        else:
            logger.error("❌ 找不到數據目錄，請先執行爬蟲")
            return 1
    
    logger.info(f"📋 將處理 {len(legislators)} 位立委：{legislators}")
    
    # 檢查數據文件（寬鬆模式）
    check_data_files(legislators)  # 只檢查，不阻止執行
    
    # 執行處理步驟
    steps = [
        ("合併平台數據", step1_merge_platform_data, args.skip_merge),
        ("創建用戶格式", step2_create_user_format, args.skip_user_format),
        ("Gemini分析", step3_gemini_analysis, args.skip_analysis),
        ("MongoDB存儲", step4_mongodb_storage, args.skip_mongodb)
    ]
    
    success_count = 0
    total_steps = len([s for s in steps if not s[2]])  # 不跳過的步驟數
    
    for step_name, step_func, skip_step in steps:
        if skip_step:
            logger.info(f"⏭️ 跳過步驟：{step_name}")
            continue
        
        logger.info(f"\n{'='*50}")
        logger.info(f"執行步驟：{step_name}")
        logger.info(f"{'='*50}")
        
        try:
            if step_func(legislators):
                success_count += 1
                logger.info(f"✅ {step_name} 完成")
            else:
                logger.error(f"❌ {step_name} 失敗")
                if not args.force_reprocess:
                    logger.error("💥 處理流程中斷，使用 --force-reprocess 強制繼續")
                    break
        except Exception as e:
            logger.error(f"❌ {step_name} 異常：{e}")
            if not args.force_reprocess:
                logger.error("💥 處理流程中斷，使用 --force-reprocess 強制繼續")
                break
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("完整數據處理流程總結")
    logger.info(f"{'='*50}")
    logger.info(f"成功步驟：{success_count}/{total_steps}")
    
    if success_count == total_steps:
        logger.info("🎉 所有步驟完成！數據已準備就緒")
        logger.info(f"📁 處理結果位於：{PROCESSED_DIR}")
        logger.info("   - alldata/: 整合後原始數據")
        logger.info("   - user_data/: 用戶格式數據")
        logger.info("   - final_data/: Gemini分析結果")
        return 0
    else:
        logger.warning(f"⚠️ 部分步驟失敗：{total_steps - success_count} 個步驟未完成")
        return 1

if __name__ == "__main__":
    sys.exit(main())
