#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試完整數據處理流程
驗證新的processed目錄結構和數據格式
"""

import os
import sys
import json
import logging
from datetime import datetime

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def test_directory_structure():
    """測試目錄結構"""
    logger.info("🧪 測試目錄結構...")
    
    required_dirs = [
        'backend/crawler/processed',
        'backend/crawler/processed/alldata',
        'backend/crawler/processed/user_data',
        'backend/crawler/processed/final_data'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = os.path.join(os.path.dirname(current_dir), dir_path.replace('backend/', ''))
        if not os.path.exists(full_path):
            missing_dirs.append(dir_path)
        else:
            logger.info(f"   ✅ {dir_path}")
    
    if missing_dirs:
        logger.error(f"❌ 缺少目錄: {missing_dirs}")
        return False
    else:
        logger.info("✅ 目錄結構完整")
        return True

def test_youtube_data_exists():
    """測試YouTube原始數據是否存在"""
    logger.info("🧪 測試YouTube原始數據...")
    
    youtube_dir = os.path.join(current_dir, 'crawler', 'data', 'youtube')
    if not os.path.exists(youtube_dir):
        logger.error("❌ YouTube數據目錄不存在")
        return False
    
    json_files = [f for f in os.listdir(youtube_dir) if f.endswith('.json')]
    if not json_files:
        logger.error("❌ 沒有YouTube數據文件")
        return False
    
    logger.info(f"✅ 找到 {len(json_files)} 個YouTube數據文件")
    
    # 檢查牛煦庭的數據
    target_file = os.path.join(youtube_dir, '牛煦庭.json')
    if os.path.exists(target_file):
        with open(target_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"   📊 牛煦庭.json: {len(data)} 個項目")
        return True
    else:
        logger.warning("⚠️ 牛煦庭.json 不存在")
        return False

def test_user_data_processor():
    """測試用戶數據處理器"""
    logger.info("🧪 測試用戶數據處理器...")
    
    try:
        from crawler.user_data_processor import create_user_format_for_gemini
        logger.info("   ✅ 用戶數據處理器導入成功")
        
        # 測試創建用戶格式
        result = create_user_format_for_gemini(['牛煦庭'])
        if result:
            logger.info("   ✅ 用戶格式創建成功")
            
            # 檢查輸出文件
            output_file = os.path.join(current_dir, 'crawler', 'processed', 'user_data', '牛煦庭_gemini_format.json')
            if os.path.exists(output_file):
                with open(output_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"   📊 生成用戶數據: {len(data)} 個用戶")
                
                # 檢查數據格式
                if data:
                    sample_user = list(data.keys())[0]
                    sample_data = data[sample_user]
                    
                    required_fields = ['comments', 'latest_date', 'comment_count']
                    missing_fields = [field for field in required_fields if field not in sample_data]
                    
                    if not missing_fields:
                        logger.info("   ✅ 用戶數據格式正確")
                        
                        # 檢查評論格式
                        if sample_data['comments']:
                            sample_comment = sample_data['comments'][0]
                            comment_fields = ['標題', '留言內容', '情感標籤', '情緒', '日期', 'source']
                            missing_comment_fields = [field for field in comment_fields if field not in sample_comment]
                            
                            if not missing_comment_fields:
                                logger.info("   ✅ 評論數據格式正確")
                                return True
                            else:
                                logger.error(f"   ❌ 評論數據缺少字段: {missing_comment_fields}")
                                return False
                        else:
                            logger.warning("   ⚠️ 用戶沒有評論數據")
                            return True
                    else:
                        logger.error(f"   ❌ 用戶數據缺少字段: {missing_fields}")
                        return False
                else:
                    logger.warning("   ⚠️ 沒有用戶數據")
                    return False
            else:
                logger.error("   ❌ 用戶格式文件未生成")
                return False
        else:
            logger.error("   ❌ 用戶格式創建失敗")
            return False
            
    except Exception as e:
        logger.error(f"❌ 用戶數據處理器測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gemini_analyzer():
    """測試Gemini分析器"""
    logger.info("🧪 測試Gemini分析器...")
    
    try:
        from crawler.gemini_emo_user import analyze_legislators_emotions_incremental
        logger.info("   ✅ Gemini分析器導入成功")
        
        # 檢查API配置
        api_file = os.path.join(current_dir, 'api.json')
        if os.path.exists(api_file):
            logger.info("   ✅ API配置文件存在")
            return True
        else:
            logger.warning("   ⚠️ API配置文件不存在，跳過實際分析測試")
            return True
            
    except Exception as e:
        logger.error(f"❌ Gemini分析器測試失敗: {e}")
        return False

def test_mongodb_handler():
    """測試MongoDB處理器"""
    logger.info("🧪 測試MongoDB處理器...")
    
    try:
        from crawler.data_to_mongo_v2 import DataToMongo
        logger.info("   ✅ MongoDB處理器導入成功")
        
        # 創建處理器實例（不實際連接）
        mongo_handler = DataToMongo()
        logger.info("   ✅ MongoDB處理器創建成功")
        
        mongo_handler.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ MongoDB處理器測試失敗: {e}")
        return False

def test_complete_processor():
    """測試完整處理器"""
    logger.info("🧪 測試完整處理器...")
    
    try:
        from crawler.complete_data_processor import parse_arguments
        logger.info("   ✅ 完整處理器導入成功")
        
        # 測試參數解析
        import sys
        original_argv = sys.argv
        sys.argv = ['complete_data_processor.py', '--legislators', '牛煦庭', '--days', '1']
        
        try:
            args = parse_arguments()
            logger.info(f"   ✅ 參數解析成功: {args.legislators}, {args.days}天")
            return True
        finally:
            sys.argv = original_argv
            
    except Exception as e:
        logger.error(f"❌ 完整處理器測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    logger.info("🚀 開始測試完整數據處理流程...")
    
    tests = [
        ("目錄結構", test_directory_structure),
        ("YouTube原始數據", test_youtube_data_exists),
        ("用戶數據處理器", test_user_data_processor),
        ("Gemini分析器", test_gemini_analyzer),
        ("MongoDB處理器", test_mongodb_handler),
        ("完整處理器", test_complete_processor)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"測試: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"測試 {test_name} 時發生異常: {e}")
            results[test_name] = False
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("完整數據處理流程測試總結")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed >= 4:  # 至少4個關鍵測試通過
        logger.info("🎉 完整數據處理流程測試基本成功！")
        logger.info("\n📋 下一步建議:")
        logger.info("1. 運行完整流程: python crawler/complete_data_processor.py --legislators 牛煦庭 --days 1")
        logger.info("2. 檢查processed目錄中的結果文件")
        logger.info("3. 驗證MongoDB中的數據更新")
        return 0
    else:
        logger.warning(f"⚠️ 有 {total - passed} 個測試失敗")
        return 1

if __name__ == "__main__":
    sys.exit(main())
