/* --- 載入動畫樣式 --- */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e3f2fd;
  border-top: 4px solid #2980b9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  margin: 0;
  font-size: 1.1rem;
  color: #2c3e50;
  font-weight: 500;
  font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
}

/* --- 通用卡片樣式 --- */
.card-style {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    overflow: hidden; /* 防止內容溢出圓角 */
}

.card-style:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12); /* 懸停時更明顯的陰影 */
}


/* --- 整體頁面容器 --- */
.map-page-container {
  position: relative;
  width: 100%;
  height: 95vh;
  display: flex;
  overflow: hidden;
  background-color: #f5f5f5;
  font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
}

/* --- 右上角投票日期顯示 --- */
.vote-date-display {
  position: absolute;
  top: 2rem;
  right: 2rem;
  background-color: #c0392b; /* 使用較深的紅色以示重要 */
  color: white;
  padding: 0.8rem 1.2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: bold;
  z-index: 1001; /* 確保在最上層 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.vote-date-display:hover {
  transform: scale(1.05);
}

/* --- 左側篩選區塊容器 (現在只做佈局，不帶卡片樣式) --- */
.filter-panel {
  width: 760px; /* 根據兩個卡片的總寬度調整 */
  /* background: #fff; */ /* 移除背景色 */
  /* border-radius: 1.2rem; */ /* 移除圓角 */
  /* box-shadow: 0 2px 16px rgba(0,0,0,0.08); */ /* 移除陰影 */
  padding: 2rem 1.5rem; /* 內部間距保持不變 */
  margin: 0 1.5rem 2rem 2rem;
  height: fit-content;
  display: flex;
  flex-direction: column;
  /* gap: 1.2rem; */ /* 這裡的 gap 現在是為 filter-layout 服務的 */
}

/* --- 篩選器整體佈局 (Flex 容器，控制兩個卡片左右並排) --- */
.filter-layout {
  display: flex;
  flex-direction: row; /* 關鍵：讓子元素左右並排 */
  gap: 1.5rem; /* 兩個卡片之間的水平間距 */
  align-items: flex-start; /* 確保兩個卡片從頂部對齊 */
}

/* --- 席次列表區塊 (獨立卡片 1) --- */
.area-count-section {
  /* background: #f8f9fa; */ /* 這些樣式會被 .card-style 覆蓋或加強 */
  /* border-radius: 0.8rem; */
  /* box-shadow: 0 1px 6px rgba(0,0,0,0.04); */
  padding: 1.3rem 1rem;
  flex: 1; /* 佔據可用空間的一半 */
  display: flex;
  flex-direction: column;
  min-width: 300px;
  min-height: 200px;
}

.area-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 1rem;
}
.area-header h1 {
    font-size: 1.4rem;
    color: #2c3e50;
    margin: 0;
}
.area-header h4 {
    font-size: 1rem;
    color: #666;
    margin: 0;
    font-weight: 400;
}

.area-scroll-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
}
.area-scroll-container::-webkit-scrollbar { width: 8px; }
.area-scroll-container::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 4px; }
.area-scroll-container::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 4px; }
.area-scroll-container::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }

.area-count-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.area-count-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.6rem 0.3rem;
  border-bottom: 1px solid #ececec;
  font-size: 1.15rem;
  color: #333;
  cursor: pointer;
  transition: background 0.2s;
}
.area-count-item:last-child { border-bottom: none; }
.area-count-item:hover, .area-count-item.active { background: #e3f2fd; border-radius: 4px; }
.area-name { font-weight: 500; }
.area-count {
  background: #eaf6fb;
  color: #2980b9;
  border-radius: 1rem;
  padding: 0.2rem 1.1rem;
  font-size: 1.15rem;
  font-weight: bold;
}

/* --- 罷免狀態篩選器容器 (新加的，僅做佈局，不帶卡片樣式) --- */
.status-filter-container {
  flex: 1; /* 佔據可用空間的另一半 */
  min-width: 300px;
  display: flex;
  flex-direction: column; /* 讓 filter-header 和 filter-content 垂直堆疊 */
  /* 移除這裡的卡片樣式，因為它們會被賦予給 filter-header 和 filter-content */
  /* border: 1px solid #e0e0e0; */
  /* border-radius: 12px; */
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); */
  /* background-color: #ffffff; */
  /* overflow: hidden; */
}

/* --- 篩選器標題 (filter-header 現在擁有自己的卡片樣式) --- */
.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  cursor: pointer;
  background: #fff; /* 這裡的背景色會被 .card-style 覆蓋，但為了明確性保留 */
  /* 邊框、圓角、陰影由 .card-style 提供 */
  /* 確保只有頂部有圓角，底部是直角，以便與 filter-content 連接 */
  border-bottom: none; /* 移除底部邊框，防止雙重線 */
  border-radius: 12px 12px 0 0; /* 頂部圓角 */
}

.filter-header:hover {
  background: #f8f9fa;
  /* box-shadow 效果由 .card-style:hover 處理 */
}

.filter-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
}

/* --- 箭頭圖標 --- */
.arrow-icon {
  margin-right: 10px;
  transition: transform 0.2s ease-in-out;
  color: #666;
  transform: rotate(0deg); /* 初始狀態：假設 path 是向右的 '>' */
  transform-origin: center;
}

.arrow-icon.rotated {
  transform: rotate(90deg); /* 展開狀態：向下的 'v' */
}

.filter-summary {
  font-size: 0.9em;
  color: #555555;
  background-color: #e9ecef;
  padding: 4px 10px;
  border-radius: 16px;
  font-weight: 500;
  min-width: 80px;
  text-align: center;
}

/* --- 篩選內容區塊 (filter-content 現在擁有自己的卡片樣式) --- */
.filter-content {
  padding: 15px 20px;
  /* background-color: #ffffff; */ /* 會被 .card-style 覆蓋 */
  /* 確保只有底部有圓角 */
  border-top: none; /* 移除頂部邊框 */
  border-radius: 0 0 12px 12px; /* 底部圓角 */
  margin-top: -1px; /* 向上微調，覆蓋 header 的底邊框，使其無縫連接 */
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* --- 狀態選項列表 (確保垂直排列) --- */
.status-options {
  display: flex;
  flex-direction: column; /* 確保選項垂直堆疊 */
  gap: 12px;
  margin-bottom: 20px;
}

.status-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 0;
  transition: background-color 0.15s ease;
}
.status-option:hover { background-color: #f9f9f9; }
.status-option input[type="checkbox"] {
  margin-right: 10px;
  transform: scale(1.1);
  cursor: pointer;
}
.status-label-content {
  display: flex;
  align-items: center;
  font-size: 1em;
  color: #444444;
}
.status-icon {
  margin-right: 8px;
  flex-shrink: 0;
}
.status-count {
  font-size: 0.85em;
  color: #777777;
  margin-left: 5px;
  font-weight: 400;
}

/* --- 操作按鈕 --- */
.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid #eeeeee;
}

.btn-clear, .btn-apply {
  padding: 8px 18px;
  border-radius: 6px;
  font-size: 0.95em;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s ease, color 0.2s ease;
}
.btn-clear { background-color: #f0f0f0; color: #666666; }
.btn-clear:hover { background-color: #e0e0e0; color: #333333; }
.btn-apply { background-color: #007bff; color: white; font-weight: 500; }
.btn-apply:hover { background-color: #0056b3; }


/* --- 地圖相關樣式 (保持不變) --- */
.map-container {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: visible;
  padding: 10px;
}

.taiwan-svg {
  width: 85%;
  height: 100%;
  max-height: 90vh;
  cursor: pointer;
  object-fit: contain;
}

.taiwan-svg path {
  stroke: #fff;
  stroke-width: 1;
  transition: fill 0.3s, opacity 0.3s, transform 0.3s;
  position: relative;
  z-index: 1;
}

.taiwan-svg path.selected {
  stroke: #fff;
  stroke-width: 2;
  filter: drop-shadow(0 0 5px rgba(0,0,0,0.3));
  opacity: 1;
  z-index: 10;
}

/* 地圖上的被罷免立委數量顯示 */
.county-recall-count {
  font-size: 28px; /* 加大字體 */
  font-weight: 900; /* 使用最粗的字重以增加可讀性 */
  fill: #2c3e50; /* 深藍灰色文字，對比度高 */
  stroke: #ffffff; /* 添加白色外框 */
  stroke-width: 1.5px; /* 外框寬度 */
  paint-order: stroke; /* 確保外框在文字下方，使文字輪廓更清晰 */
  pointer-events: none;
  font-family: "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
  user-select: none;
  animation: fadeIn 0.5s ease-in-out; /* 添加淡入動畫 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.map-tip {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  pointer-events: none;
  opacity: 0.8;
  animation: fadeInUp 1s ease, pulse-opacity 2s infinite;
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translate(-50%, 20px); }
  to { opacity: 0.8; transform: translate(-50%, 0); }
}

@keyframes pulse-opacity {
  0% { opacity: 0.8; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}

/* --- 右側信息面板 --- */
.county-info-panel {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 400px;
  background: #fff;
  border-radius: 1.2rem;
  box-shadow: 0 2px 16px rgba(0,0,0,0.1);
  padding: 2rem;
  animation: fadeInRight 0.7s;
  z-index: 1002; /* 提高 z-index，確保它在投票日期之上 */
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}
.county-info-panel h2 {
  font-size: 2rem;
  margin-bottom: 0;
  color: #333;
  text-align: center;
  padding-bottom: 0;
  flex-grow: 1;
}
.county-info-panel h4 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: #555;
}

.back-btn {
  background: #2ecc71;
  color: #fff;
  border: none;
  border-radius: 2rem;
  padding: 0.6rem 1.8rem;
  font-size: 1.2rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
  flex-shrink: 0;
}
.back-btn:hover {
  background: #27ae60;
}

.legislators-scroll-container {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}
.legislators-scroll-container::-webkit-scrollbar { width: 8px; }
.legislators-scroll-container::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 4px; }
.legislators-scroll-container::-webkit-scrollbar-thumb { background: #c1c1c1; border-radius: 4px; }
.legislators-scroll-container::-webkit-scrollbar-thumb:hover { background: #a8a8a8; }

.legislators-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.legislator-card {
  display: flex;
  background: #fafafa;
  border-radius: 0.8rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}
.legislator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  background-color: #f0f0f0;
}
.legislator-card.no-click { cursor: default; }
.legislator-card.no-click:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  background-color: #fafafa;
}

.legislator-image { flex: 0 0 100px; height: 120px; overflow: hidden; }
.legislator-image img { width: 100%; height: 100%; object-fit: cover; }
.default-avatar {
  width: 100%;
  height: 100%;
  background-color: #3498db;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2.5rem;
  font-weight: bold;
}

.legislator-info {
  flex: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}
.legislator-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
  color: #2980b9;
  cursor: pointer;
}
.legislator-info h3:hover { color: #3498db; text-decoration: underline; }
.legislator-info .constituency { margin: 0 0 0.5rem 0; font-size: 0.9rem; color: #555; }
.legislator-info .party {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.3rem 0.6rem;
  border-radius: 1rem;
  display: inline-block;
  color: #fff;
  background-color: #7f8c8d;
}

/* 政黨顏色 */
.party-kmt { background-color: #0078d4 !important; color: white !important; }
.party-dpp { background-color: #00a65a !important; color: white !important; }
.party-tpp { background-color: #f5f5f5 !important; color: #333 !important; border: 1px solid #ddd; }

/* 無資料顯示 */
.no-data {
  text-align: center;
  padding: 2rem;
  color: #7f8c8d;
  font-style: italic;
}

@keyframes fadeInRight {
  from { opacity: 0; transform: translateX(48px); }
  to { opacity: 1; transform: none; }
}

/* 響應式設計 */
@media (max-width: 900px) {
  .map-page-container { flex-direction: column; }
  .filter-panel {
    width: 100%;
    margin: 0;
    border-radius: 0 0 1.2rem 1.2rem;
    box-shadow: none;
    padding: 1rem 0.5rem;
    order: 1;
    .filter-layout {
        flex-direction: column;
        gap: 1rem;
    }
  }
  .map-container { width: 100%; height: 60vh; }
  .county-info-panel {
    position: fixed;
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    max-height: 40vh;
    border-radius: 1.2rem 1.2rem 0 0;
    padding: 1.5rem;
  }
  .legislator-image { flex: 0 0 80px; height: 100px; }
}

/* 罷免狀態樣式 */
.recall-tip {
  margin-top: 0.5rem;
  color: #e74c3c;
  font-weight: bold;
  font-size: 1rem;
  background: #fff0f0;
  border-radius: 0.5rem;
  padding: 0.2rem 0.7rem;
  display: inline-block;
}

.recall-status {
  margin-top: 0.5rem;
  font-size: 0.875rem;

  .status-petition-ongoing { color: #f39c12; font-weight: 500; }
  .status-petition-failed { color: #95a5a6; font-weight: 500; }
  .status-recall-ongoing { color: #e74c3c; font-weight: 500; }
  .status-recall-failed { color: #7f8c8d; font-weight: 500; }
  .status-recall-success { color: #27ae60; font-weight: 500; }
  .status-survey { color: #3498db; font-weight: 500; }
}

/* 罷免日期資訊樣式 */
.recall-date-info {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #666;
  background: #fff3cd;
  border-radius: 0.5rem;
  padding: 0.4rem 0.7rem;
  border-left: 3px solid #e74c3c;
  
  strong {
    color: #2c3e50;
    font-weight: 600;
  }
}

.no-recall-info {
  justify-content: center;
  align-items: center;
  text-align: center;
}
.no-recall-title { color: #7f8c8d !important; font-size: 1.1rem !important; }
.no-recall-title:hover { color: #7f8c8d !important; text-decoration: none !important; }

/* 確保狀態篩選器按鈕樣式 */
.status-filter-btn {
  font-size: 0.875rem;
  padding: 0.5rem 0.75rem;
  white-space: nowrap;
  margin: 0.25rem;
}
.status-filter-btn .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* 新增的 back-btn-small 樣式 */
.back-btn-small {
  background: #2ecc71;
  color: white;
  border: none;
  border-radius: 1rem;
  padding: 0.3rem 0.8rem;
  font-size: 0.8rem;
  cursor: pointer;
}
.back-btn-small:hover { background: #27ae60; }

.usage-modal-backdrop {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.usage-modal {
  background: #fff;
  padding: 2rem 2.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.2);
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}
.usage-modal h2 {
  margin-top: 0;
}
.usage-modal button {
  margin-top: 1.5rem;
  padding: 0.5rem 1.5rem;
  background: #3498db;
  color: #fff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.usage-help-btn {
  position: fixed;
  top: 12px;
  right: 12px;
  z-index: 1100;
  background: #3498db;
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.5rem;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
  cursor: pointer;
  transition: background 0.2s;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.usage-help-btn:hover {
  background: #217dbb;
}

/* 罷免統計面板樣式 */
.recall-stats-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 20px;

  .stats-header {
    text-align: center;
    margin-bottom: 20px;

    h3 {
      color: #2c3e50;
      font-size: 1.4rem;
      font-weight: 600;
      margin: 0 0 5px 0;
    }

    .stats-subtitle {
      color: #7f8c8d;
      font-size: 0.9rem;
      margin: 0;
    }
  }

  .stats-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 25px;

    .stat-card {
      display: flex;
      align-items: center;
      padding: 15px;
      border-radius: 10px;
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .stat-icon {
        font-size: 2rem;
        margin-right: 15px;
      }

      .stat-content {
        flex: 1;

        .stat-number {
          font-size: 1.8rem;
          font-weight: 700;
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 0.9rem;
          opacity: 0.8;
          font-weight: 500;
        }
      }

      &.support-card {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
      }

      &.oppose-card {
        background: linear-gradient(135deg, #27ae60, #229954);
        color: white;
      }

      &.neutral-card {
        background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        color: white;
      }
    }
  }

  .region-stats {
    border-top: 2px solid #ecf0f1;
    padding-top: 20px;

    h4 {
      color: #2c3e50;
      font-size: 1.1rem;
      margin: 0 0 15px 0;
      font-weight: 600;
    }

    .region-stat-bars {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .stat-bar {
        display: flex;
        align-items: center;
        gap: 10px;

        .stat-bar-label {
          min-width: 40px;
          font-size: 0.9rem;
          font-weight: 500;
          color: #34495e;
        }

        .stat-bar-container {
          flex: 1;
          height: 20px;
          background: #ecf0f1;
          border-radius: 10px;
          overflow: hidden;
          position: relative;

          .stat-bar-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.8s ease;

            &.support-bar {
              background: linear-gradient(90deg, #e74c3c, #c0392b);
            }

            &.oppose-bar {
              background: linear-gradient(90deg, #27ae60, #229954);
            }

            &.neutral-bar {
              background: linear-gradient(90deg, #95a5a6, #7f8c8d);
            }
          }
        }

        .stat-bar-value {
          min-width: 35px;
          text-align: right;
          font-size: 0.9rem;
          font-weight: 600;
          color: #2c3e50;
        }
      }
    }
  }
}

// 調整filter-layout以容納新的統計面板
.filter-layout {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .area-count-section {
    flex: 1;
  }
}

// 響應式設計
@media (min-width: 1200px) {
  .filter-layout {
    flex-direction: row;

    .area-count-section {
      flex: 1;
    }

    .recall-stats-section {
      flex: 0 0 350px;
      margin-top: 0;
      margin-left: 20px;
    }
  }
}