{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["開始讀取數據: gemini/data/0323/0323_myself.json\n", "讀取了 448 條數據\n", "開始分割數據...\n", "已保存 90 條數據到 validation_data.json\n", "已保存 100 條數據到 20250402_100train_data.json\n", "已保存 200 條數據到 20250402_200train_data.json\n", "已保存 300 條數據到 20250402_300train_data.json\n", "數據分割完成！\n"]}], "source": ["import json\n", "import random\n", "import time\n", "from sklearn.model_selection import train_test_split\n", "\n", "# 1. 讀取原始數據\n", "def load_json_data(file_path):\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    return data\n", "\n", "# 2. 保存JSON數據到文件\n", "def save_json_data(data, file_path):\n", "    with open(file_path, 'w', encoding='utf-8') as f:\n", "        json.dump(data, f, ensure_ascii=False, indent=4)\n", "    print(f\"已保存 {len(data)} 條數據到 {file_path}\")\n", "\n", "# 3. 分割數據集\n", "def split_data(data, validation_size=0.2, random_seed=42):\n", "    # 首先分離出驗證集\n", "    train_data, validation_data = train_test_split(\n", "        data, test_size=validation_size, random_state=random_seed\n", "    )\n", "    \n", "    # 計算需要的訓練集大小\n", "    total_train = len(train_data)\n", "    \n", "    # 確保我們能夠提取所需大小的數據\n", "    sizes = [100,200,300]\n", "    valid_sizes = [size for size in sizes if size <= total_train]\n", "    \n", "    if not valid_sizes:\n", "        print(f\"警告：無法創建任何指定大小的訓練集。總訓練數據量僅為 {total_train}。\")\n", "        return {}, validation_data\n", "    \n", "    # 為了保持一致性，打亂數據但使用固定的隨機種子\n", "    random.seed(random_seed)\n", "    shuffled_train_data = train_data.copy()\n", "    random.shuffle(shuffled_train_data)\n", "    \n", "    # 創建不同大小的訓練集\n", "    train_sets = {}\n", "    for size in valid_sizes:\n", "        train_sets[size] = shuffled_train_data[:size]\n", "    \n", "    return train_sets, validation_data\n", "\n", "# 4. 主函數：處理數據並分割\n", "def main():\n", "    # 設置輸入和輸出文件路徑\n", "    input_file = \"gemini/data/0323/0323_myself.json\"\n", "    \n", "    try:\n", "        # 讀取數據\n", "        print(f\"開始讀取數據: {input_file}\")\n", "        data = load_json_data(input_file)\n", "        print(f\"讀取了 {len(data)} 條數據\")\n", "        \n", "        # 分割數據\n", "        print(\"開始分割數據...\")\n", "        train_sets, validation_data = split_data(data)\n", "        \n", "        # 保存驗證集\n", "        save_json_data(validation_data, \"validation_data.json\")\n", "        \n", "        # 保存不同大小的訓練集\n", "        for size, train_data in train_sets.items():\n", "            date = time.strftime(\"%Y%m%d\", time.localtime())\n", "            save_json_data(train_data, f\"{date}_{size}train_data.json\")\n", "        \n", "        print(\"數據分割完成！\")\n", "        \n", "    except Exception as e:\n", "        print(f\"處理數據時發生錯誤: {str(e)}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["使用設備: cuda\n"]}], "source": ["import json\n", "import torch\n", "import numpy as np\n", "from sklearn.metrics import accuracy_score, classification_report\n", "from transformers import (\n", "    AutoTokenizer, \n", "    AutoModelForSequenceClassification,\n", "    Trainer, \n", "    TrainingArguments,\n", "    pipeline,\n", "    DataCollatorWithPadding,\n", "    EarlyStoppingCallback\n", ")\n", "from datasets import Dataset, DatasetDict\n", "\n", "# 檢查GPU是否可用\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "print(f\"使用設備: {device}\")\n", "\n", "# 1. 準備數據\n", "def load_data_from_json(file_path):\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    return data\n", "\n", "# 假設我們有三個預先分好的訓練數據集和一個驗證集\n", "train_files = {\n", "    \"100\": \"20250402_100train_data.json\",\n", "    \"200\": \"20250402_200train_data.json\",\n", "    \"300\": \"20250402_300train_data.json\"\n", "}\n", "val_file = \"validation_data.json\"\n", "\n", "# 2. 定義情感分析的標籤映射\n", "sentiment_id2label = {0: \"NEGATIVE\", 1: \"POSITIVE\"}\n", "sentiment_label2id = {\"NEGATIVE\": 0, \"POSITIVE\": 1}\n", "sentiment_labels = [\"NEGATIVE\", \"POSITIVE\"]\n", "# 3. 情緒分類的標籤列表 (根據你的數據調整)\n", "emotion_labels = [\"joy\", \"trust\", \"fear\", \"surprise\", \"sadness\", \"disgust\", \"anger\", \"anticipation\"]\n", "emotion_mapping = {\n", "    \"joy\": 0,\n", "    \"sadness\": 1,\n", "    \"anger\": 2,\n", "    \"fear\": 3,\n", "    \"trust\": 4,\n", "    \"surprise\": 5,\n", "    \"disgust\": 6,\n", "    \"anticipation\": 7\n", "}\n", "label_mapping = {\n", "    \"POSITIVE\": 1,\n", "    \"NEGATIVE\": 0\n", "}\n", "# 4. 數據處理函數\n", "def preprocess_data_for_sentiment(data):\n", "    data['label'] = [sentiment_labels.index(label) for label in data['label']]\n", "    return data\n", "\n", "\n", "def preprocess_data_for_emotion(data):\n", "    data['emotion'] = [emotion_labels.index(emotion_label) for emotion_label in data['emotion']]\n", "    return data\n", "\n", "\n", "\n", "def prepare_datasets(train_file, val_file, task=\"sentiment\"):\n", "    # 載入數據\n", "    train_data = load_data_from_json(train_file)\n", "    val_data = load_data_from_json(val_file)\n", "    \n", "    # 創建Dataset對象\n", "    train_dataset = Dataset.from_dict({\n", "        \"context\": [item[\"context\"] for item in train_data],\n", "        \"reply\": [item[\"reply\"] for item in train_data],\n", "        \"label\": [item[\"label\"] for item in train_data],\n", "        \"emotion\": [item[\"emotion\"] for item in train_data]\n", "    })\n", "\n", "    val_dataset = Dataset.from_dict({\n", "        \"context\": [item[\"context\"] for item in val_data],\n", "        \"reply\": [item[\"reply\"] for item in val_data],\n", "        \"label\": [item[\"label\"] for item in val_data],\n", "        \"emotion\": [item[\"emotion\"]for item in val_data]\n", "    })\n", "    \n", "    # 根據任務預處理數據\n", "    if task == \"sentiment\":\n", "        print(\"🔧 開始處理情感分析數據\")\n", "        train_dataset = train_dataset.map(preprocess_data_for_sentiment, batched=True)\n", "        val_dataset = val_dataset.map(preprocess_data_for_sentiment, batched=True)\n", "    else:  # emotion\n", "        print(\"🔧 開始處理情緒分析數據\")\n", "        train_dataset = train_dataset.map(preprocess_data_for_emotion, batched=True)\n", "        val_dataset = val_dataset.map(preprocess_data_for_emotion, batched=True)\n", "    \n", "    # 組合成DatasetDict\n", "    dataset_dict = DatasetDict({\n", "        \"train\": train_dataset,\n", "        \"validation\": val_dataset\n", "    })\n", "    print(f\"✅ 數據集準備完成: {len(train_dataset)} 訓練樣本, {len(val_dataset)} 驗證樣本\")\n", "    return dataset_dict\n", "\n", "\n", "# 6. 情感分析模型微調函數\n", "def fine_tune_sentiment_model(dataset_dict, model_name=\"uer/roberta-base-finetuned-jd-binary-chinese\", output_dir=\"sentiment_model\"):\n", "    # 載入tokenizer和模型\n", "    tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "    model = AutoModelForSequenceClassification.from_pretrained(\n", "        model_name,\n", "        num_labels=2,\n", "        id2label=sentiment_id2label,\n", "        label2id=sentiment_label2id\n", "    )\n", "    model.to(device)\n", "    # 定義tokenize函數\n", "    def tokenize_function(examples):\n", "        return tokenizer(\n", "            examples[\"reply\"],\n", "            padding=\"max_length\",\n", "            truncation=True,\n", "            max_length=128\n", "        )\n", "    \n", "    # 對數據集應用tokenizer\n", "    tokenized_datasets = dataset_dict.map(tokenize_function, batched=True)\n", "    \n", "    # 設置訓練參數\n", "    training_args = TrainingArguments(\n", "        output_dir=output_dir,\n", "        learning_rate=2e-5,\n", "        per_device_train_batch_size=16,\n", "        per_device_eval_batch_size=16,\n", "        num_train_epochs=3,\n", "        weight_decay=0.001,\n", "        evaluation_strategy=\"epoch\",\n", "        save_strategy=\"epoch\",\n", "        load_best_model_at_end=True,\n", "        push_to_hub=False,\n", "        report_to=\"none\",  # 避免報告到Wandb等\n", "        save_total_limit=1,  # 只保留最後一個模型\n", "    )\n", "    \n", "    # 定義Trainer\n", "    trainer = Trainer(\n", "        model=model,\n", "        args=training_args,\n", "        train_dataset=tokenized_datasets[\"train\"],\n", "        eval_dataset=tokenized_datasets[\"validation\"],\n", "        tokenizer=tokenizer,\n", "        callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]#如果 2 個 Epoch 內評估指標沒有改善，則提前停止訓練。\n", "    )\n", "    \n", "    # 開始訓練\n", "    trainer.train()\n", "    \n", "    # 評估模型\n", "    eval_results = trainer.evaluate()\n", "    print(f\"評估結果: {eval_results}\")\n", "    \n", "    # 保存模型\n", "    trainer.save_model(output_dir)\n", "    tokenizer.save_pretrained(output_dir)\n", "    \n", "    return model, tokenizer, eval_results\n", "\n", "# 7. 情緒分類模型微調函數\n", "def fine_tune_emotion_model(dataset_dict, model_name=\"facebook/bart-large-mnli\", output_dir=\"emotion_model\"):\n", "    tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "    model = AutoModelForSequenceClassification.from_pretrained(\n", "        model_name,\n", "        num_labels=len(emotion_labels),\n", "        id2label={i: label for i, label in enumerate(emotion_labels)},\n", "        label2id={label: i for i, label in enumerate(emotion_labels)},\n", "        ignore_mismatched_sizes=True\n", "    )\n", "    model.to(device)\n", "\n", "    def tokenize_function(examples):\n", "        tokenized_inputs = tokenizer(\n", "            examples[\"reply\"],\n", "            padding=\"max_length\",\n", "            truncation=True,\n", "            max_length=128\n", "        )\n", "        tokenized_inputs[\"label\"] = examples[\"emotion\"]  \n", "        return tokenized_inputs\n", "    \n", "    # Tokenize dataset\n", "    tokenized_datasets = dataset_dict.map(tokenize_function, batched=True)\n", "\n", "    # Data collator\n", "    data_collator = DataCollatorWithPadding(tokenizer=tokenizer)\n", "\n", "    training_args = TrainingArguments(\n", "        output_dir=output_dir,\n", "        learning_rate=3e-5,\n", "        per_device_train_batch_size=16,\n", "        per_device_eval_batch_size=16,\n", "        num_train_epochs=3,\n", "        weight_decay=0.01,\n", "        evaluation_strategy=\"epoch\",\n", "        save_strategy=\"epoch\",\n", "        load_best_model_at_end=True,\n", "        push_to_hub=False,\n", "        report_to=\"none\",\n", "        save_total_limit=1,  # 只保留最後一個模型\n", "    )\n", "\n", "    trainer = Trainer(\n", "        model=model,\n", "        args=training_args,\n", "        train_dataset=tokenized_datasets[\"train\"],\n", "        eval_dataset=tokenized_datasets[\"validation\"],\n", "        tokenizer=tokenizer,\n", "        data_collator=data_collator,  # ✅ 確保數據填充一致\n", "        callbacks=[EarlyStoppingCallback(early_stopping_patience=2)]#如果 2 個 Epoch 內評估指標沒有改善，則提前停止訓練。\n", "    )\n", "\n", "    trainer.train()\n", "    \n", "    eval_results = trainer.evaluate()\n", "    print(f\"評估結果: {eval_results}\")\n", "    \n", "    trainer.save_model(output_dir)\n", "    tokenizer.save_pretrained(output_dir)\n", "    \n", "    return model, tokenizer, eval_results\n", "\n", "# 定義測試模型性能的函數\n", "def test_sentiment_model_performance(model, tokenizer, test_data,size):\n", "    # 設定最大長度與訓練時相同\n", "    max_length = 128  # 與訓練時相同\n", "    # 創建情感分析模型pipeline\n", "    sentiment_classifier = pipeline(\n", "        \"sentiment-analysis\",\n", "        model=model,\n", "        tokenizer=tokenizer,\n", "        device=device,\n", "        max_length=max_length,\n", "        truncation=True\n", "    )\n", "    true_labels = []\n", "    sentiment_predictions = []\n", "    prediction_results = []\n", "\n", "    # 對每一條測試數據進行處理\n", "    for item in test_data:\n", "        # 進行情感分析\n", "        sentiment_result = sentiment_classifier(item[\"reply\"], truncation=True)[0]\n", "        sentiment_label = sentiment_result[\"label\"].upper()  # 轉成 \"POSITIVE\" / \"NEGATIVE\"\n", "        sentiment_predictions.append(sentiment_label)\n", "        \n", "        # 獲取真實標籤\n", "        true_label = item[\"label\"].upper()\n", "        true_labels.append(true_label)\n", "        \n", "        # 保存該條數據的預測結果\n", "        prediction_info = {\n", "            \"context\": item[\"context\"],\n", "            \"reply\": item[\"reply\"],\n", "            \"true_sentiment\": true_label,\n", "            \"predicted_sentiment\": sentiment_label,\n", "            \"sentiment_score\": sentiment_result[\"score\"],\n", "            \"true_emotion\": item[\"emotion\"],\n", "            \"predicted_emotion\": \"\",  # 稍後由情緒模型填充\n", "            \"emotion_score\": 0.0      # 稍後由情緒模型填充\n", "        }\n", "        prediction_results.append(prediction_info)\n", "        # 顯示每個測試數據的分類結果\n", "        print(f\"回應: {item['reply']}\")\n", "        print(f\"真實標籤: {item['label']}\")\n", "        print(f\"預測標籤: {sentiment_label}\")\n", "        print(f\"情感預測分數: {sentiment_result['score']:.4f}\")\n", "        print(\"-\" * 40)\n", "\n", "   \n", "    # 計算情感分析準確率和生成分類報告\n", "    sentiment_accuracy = accuracy_score(sentiment_predictions, true_labels)\n", "    sentiment_report = classification_report(\n", "        true_labels, \n", "        sentiment_predictions, \n", "        target_names=[\"NEGATIVE\", \"POSITIVE\"]\n", "    )\n", "\n", "    print(f\"Sentiment 分類準確率: {sentiment_accuracy:.4f}\")\n", "    print(f\"Sentiment 分類報告:\\n{sentiment_report}\")\n", "\n", "    return sentiment_accuracy, sentiment_report,prediction_results\n", "\n", "# 定義測試模型性能的函數\n", "def test_emotion_model_performance(model, tokenizer, test_data, prediction_results):\n", "    # 設定最大長度與訓練時相同\n", "    max_length = 128  # 與訓練時相同\n", "    # 創建情感分析 pipeline\n", "    emotion_classifier = pipeline(\n", "        \"text-classification\",\n", "        model=model,\n", "        tokenizer=tokenizer,\n", "        device=device,\n", "        max_length=max_length,\n", "        truncation=True\n", "    )\n", "\n", "    true_emotion_labels = []\n", "    emotion_predictions = []\n", "\n", "    # 逐條測試數據\n", "    for i,item in enumerate(test_data):\n", "        # 進行情緒分類\n", "        emotion_result = emotion_classifier(item[\"reply\"], truncation=True)[0]\n", "        emotion_label = emotion_result[\"label\"].lower()  # 確保標籤統一為小寫\n", "        emotion_predictions.append(emotion_label)\n", "        true_emotion_labels.append(item[\"emotion\"].lower())  # 確保標籤統一為小寫\n", "\n", "        # 更新預測結果中的情緒信息\n", "        prediction_results[i][\"predicted_emotion\"] = emotion_label\n", "        prediction_results[i][\"emotion_score\"] = emotion_result[\"score\"]\n", "        # 顯示每個測試數據的分類結果\n", "        print(f\"回應: {item['reply']}\")\n", "        print(f\"真實情緒: {item['emotion']}\")\n", "        print(f\"預測情緒: {emotion_label}\")\n", "        print(f\"情緒預測分數: {emotion_result['score']:.4f}\")\n", "        print(\"-\" * 40)\n", "\n", "    # 取得所有不同的標籤類別（避免 `target_names` 不匹配）\n", "\n", "    # 計算準確率與分類報告\n", "    emotion_accuracy = accuracy_score(true_emotion_labels, emotion_predictions)\n", "    emotion_report = classification_report(true_emotion_labels, emotion_predictions, \n", "                                         target_names=emotion_labels)\n", "    print(f\"Emotion 分類準確率: {emotion_accuracy:.4f}\")\n", "    print(f\"Emotion 分類報告:\\n{emotion_report}\")\n", "\n", "    return emotion_accuracy, emotion_report\n", "\n", "\n", "\n", "# 9. 主程序：訓練和評估各種大小的訓練集\n", "def main():\n", "    # 儲存結果\n", "    results = {}\n", "    \n", "    # 對每個訓練集大小進行實驗\n", "    for size, train_file in train_files.items():\n", "        print(f\"\\n=========== 開始處理訓練集大小: {size} ===========\")\n", "        \n", "        # 情感分析模型\n", "        print(f\"訓練情感分析模型 (訓練集大小: {size})...\")\n", "        sentiment_datasets = prepare_datasets(train_file, val_file, task=\"sentiment\")\n", "        sentiment_model, sentiment_tokenizer, sentiment_eval = fine_tune_sentiment_model(\n", "            sentiment_datasets, \n", "            output_dir=f\"sentiment_model_{size}\"\n", "        )\n", "        \n", "        # 情緒分類模型\n", "        print(f\"訓練情緒分類模型 (訓練集大小: {size})...\")\n", "        emotion_datasets = prepare_datasets(train_file, val_file, task=\"emotion\")\n", "        emotion_model, emotion_tokenizer, emotion_eval = fine_tune_emotion_model(\n", "            emotion_datasets, \n", "            output_dir=f\"emotion_model_{size}\"\n", "        )\n", "        \n", "        # 載入驗證數據進行最終評估\n", "        val_data = load_data_from_json(val_file)\n", "        \n", "        # 評估模型\n", "        print(f\"評估情感分析模型 (訓練集大小: {size})...\")\n", "        sentiment_acc, sentiment_report, prediction_results = test_sentiment_model_performance(\n", "            sentiment_model, sentiment_tokenizer, val_data,size\n", "        )\n", "        \n", "        print(f\"評估情緒分類模型 (訓練集大小: {size})...\")\n", "        emotion_acc, emotion_report = test_emotion_model_performance(\n", "            emotion_model, emotion_tokenizer, val_data, prediction_results\n", "        )\n", "        # 將完整的預測結果保存到JSON文件\n", "        prediction_file = f\"{size}_model_predict.json\"\n", "        with open(prediction_file, 'w', encoding='utf-8') as f:\n", "            json.dump(prediction_results, f, ensure_ascii=False, indent=4)\n", "        print(f\"預測結果已保存到 {prediction_file}\")\n", "        # 儲存結果\n", "        results[size] = {\n", "            'sentiment': {\n", "                'accuracy': sentiment_acc,\n", "                'eval_results': sentiment_eval,\n", "                'report': sentiment_report\n", "            },\n", "            'emotion': {\n", "                'accuracy': emotion_acc,\n", "                'eval_results': emotion_eval,\n", "                'report': emotion_report\n", "            }\n", "        }\n", "    \n", "    # 輸出結果摘要\n", "    print(\"\\n=========== 實驗結果摘要 ===========\")\n", "    for size in train_files.keys():\n", "        print(f\"\\n訓練集大小: {size}\")\n", "        print(f\"情感分析準確率: {results[size]['sentiment']['accuracy']:.4f}\")\n", "        print(f\"情緒分類準確率: {results[size]['emotion']['accuracy']:.4f}\")\n", "    \n", "    # 儲存結果到JSON文件\n", "    with open('training_results.json', 'w', encoding='utf-8') as f:\n", "        json.dump(results, f, ensure_ascii=False, indent=4)\n", "    \n", "    print(\"\\n實驗結果已保存到training_results.json\")\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=========== 開始處理訓練集大小: 100 ===========\n", "訓練情感分析模型 (訓練集大小: 100)...\n", "🔧 開始處理情感分析數據\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "684efc4fabe84a779b03bc2ef5a08629", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/100 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "de76ca964e904040b513ff56bd70c574", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ 數據集準備完成: 100 訓練樣本, 90 驗證樣本\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "245f192a1ec945fbab612312a02732aa", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/100 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "abdf270e0b9649d5893e87aee8de85d2", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26188\\1096956646.py:145: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.\n", "  trainer = Trainer(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6677e62962c34a57acdf4ad0ad596a55", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/21 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2e831c42bd754a799fd793bb8a44be57", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.7626418471336365, 'eval_runtime': 0.2526, 'eval_samples_per_second': 356.358, 'eval_steps_per_second': 23.757, 'epoch': 1.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3f826d21800e41cd817b00123bb40ff2", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.7206063866615295, 'eval_runtime': 0.2522, 'eval_samples_per_second': 356.895, 'eval_steps_per_second': 23.793, 'epoch': 2.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "25fc8e1faa814daab6ff65e70395edb8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.7047551870346069, 'eval_runtime': 0.445, 'eval_samples_per_second': 202.245, 'eval_steps_per_second': 13.483, 'epoch': 3.0}\n", "{'train_runtime': 59.97, 'train_samples_per_second': 5.003, 'train_steps_per_second': 0.35, 'train_loss': 0.6759866532825288, 'epoch': 3.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "457aa9d6a2f5489896fe57731012f052", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["評估結果: {'eval_loss': 0.7047551870346069, 'eval_runtime': 0.2255, 'eval_samples_per_second': 399.192, 'eval_steps_per_second': 26.613, 'epoch': 3.0}\n", "訓練情緒分類模型 (訓練集大小: 100)...\n", "🔧 開始處理情緒分析數據\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f25675bad944482ea682d4e2566ee529", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/100 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "02b936cec1e8492c9df52a37f0f12e42", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ 數據集準備完成: 100 訓練樣本, 90 驗證樣本\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Some weights of BartForSequenceClassification were not initialized from the model checkpoint at facebook/bart-large-mnli and are newly initialized because the shapes did not match:\n", "- classification_head.out_proj.bias: found shape torch.<PERSON><PERSON>([3]) in the checkpoint and torch.<PERSON><PERSON>([8]) in the model instantiated\n", "- classification_head.out_proj.weight: found shape torch.<PERSON><PERSON>([3, 1024]) in the checkpoint and torch.<PERSON><PERSON>([8, 1024]) in the model instantiated\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "df25d0b7fe154fc098ba4d3e988a8678", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/100 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e2780e1aaec4413ea0c6ef3ad0b643aa", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\transformers\\training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26188\\1096956646.py:210: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.\n", "  trainer = Trainer(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "310171ac0da248a28ab943198bbf8553", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/21 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "947fea5ed898472ebb63dac0cd3917f3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 1.9136934280395508, 'eval_runtime': 1.9818, 'eval_samples_per_second': 45.414, 'eval_steps_per_second': 3.028, 'epoch': 1.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b5dba29c05524f06a7ea42ed61e35afd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 1.8978140354156494, 'eval_runtime': 1.925, 'eval_samples_per_second': 46.752, 'eval_steps_per_second': 3.117, 'epoch': 2.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2af2be99fa7b43f6a42d8e766c2b1250", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 1.8931642770767212, 'eval_runtime': 1.9927, 'eval_samples_per_second': 45.165, 'eval_steps_per_second': 3.011, 'epoch': 3.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["There were missing keys in the checkpoint model loaded: ['model.encoder.embed_tokens.weight', 'model.decoder.embed_tokens.weight'].\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'train_runtime': 232.5455, 'train_samples_per_second': 1.29, 'train_steps_per_second': 0.09, 'train_loss': 1.8275584266299294, 'epoch': 3.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a681d2732a5f46da843b743584ffea52", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["評估結果: {'eval_loss': 1.8931642770767212, 'eval_runtime': 1.7492, 'eval_samples_per_second': 51.452, 'eval_steps_per_second': 3.43, 'epoch': 3.0}\n", "評估情感分析模型 (訓練集大小: 100)...\n", "回應: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5226\n", "----------------------------------------\n", "回應: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5302\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5336\n", "----------------------------------------\n", "回應: #鏡新聞 中指可以比出來啊～\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5976\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6634\n", "----------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["You seem to be using the pipelines sequentially on GPU. In order to maximize efficiency please use a dataset\n"]}, {"name": "stdout", "output_type": "stream", "text": ["回應: 國民黨吃台灣賣台灣。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5884\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.6567\n", "----------------------------------------\n", "回應: 請支持國共合作\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5613\n", "----------------------------------------\n", "回應: 民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5721\n", "----------------------------------------\n", "回應: #鏡新聞 醒醒\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5169\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5077\n", "----------------------------------------\n", "回應: 霸到底救國家\n", "霸到底救台灣\n", "霸到底救民主\n", "霸到底救人民\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5949\n", "----------------------------------------\n", "回應: 國民黨在恐嚇選民，真的好可怕\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5583\n", "----------------------------------------\n", "回應: 行政院可以不接受。让国民党倒阁，解散国会。\n", "国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5395\n", "----------------------------------------\n", "回應: 千萬別被他騙了!!!霸定了舉手部隊。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9239\n", "----------------------------------------\n", "回應: 廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5443\n", "----------------------------------------\n", "回應: 中共又再胡說八道，鬼扯，。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6838\n", "----------------------------------------\n", "回應: 原汁沒開玩笑 是在裝瘋賣傻\n", "人一藍 腦就殘\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8794\n", "----------------------------------------\n", "回應: 死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9561\n", "----------------------------------------\n", "回應: 累就滾回家\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5323\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\n", "但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5595\n", "----------------------------------------\n", "回應: 遷户口準備中\n", "徐阿花你等著\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5572\n", "----------------------------------------\n", "回應: 罷免葉元之就是罷免陳玉珍\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5098\n", "----------------------------------------\n", "回應: 那張衰臉真是原汁\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8269\n", "----------------------------------------\n", "回應: 無恥的人，還敢公然鬼扯，罷定了!\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8521\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5323\n", "----------------------------------------\n", "回應: 領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8993\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5100\n", "----------------------------------------\n", "回應: 葉猿之可惡至極，非罷不可 。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6286\n", "----------------------------------------\n", "回應: 除惡務盡，一戰改變台灣政治生態，台派加油！\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5681\n", "----------------------------------------\n", "回應: 這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5226\n", "----------------------------------------\n", "回應: 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\n", "馬來西亞華人都是穴今\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5380\n", "----------------------------------------\n", "回應: 國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.6043\n", "----------------------------------------\n", "回應: 人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5403\n", "----------------------------------------\n", "回應: 【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5277\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 还有民众党\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5519\n", "----------------------------------------\n", "回應: 元之你是在做復健嗎?\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5243\n", "----------------------------------------\n", "回應: 朝聖! 柿子要挑軟的吃\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5932\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.9851\n", "----------------------------------------\n", "回應: 建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7022\n", "----------------------------------------\n", "回應: #王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5409\n", "----------------------------------------\n", "回應: 葉元之從頭到尾鬼扯\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5366\n", "----------------------------------------\n", "回應: 傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5437\n", "----------------------------------------\n", "回應: #鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5425\n", "----------------------------------------\n", "回應: 真不相信李蟾蜍會是一個中立的X X\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8543\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6371\n", "----------------------------------------\n", "回應: 葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.7516\n", "----------------------------------------\n", "回應: 葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5470\n", "----------------------------------------\n", "回應: 國民黨出葉元之這種貨色\n", "不倒才怪\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6200\n", "----------------------------------------\n", "回應: 笑噴～真的要出來把沒用的破嘴51席全罷掉\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9194\n", "----------------------------------------\n", "回應: 國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； \n", "1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5077\n", "----------------------------------------\n", "回應: 中共在逼了他們為何要這麼急\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5094\n", "----------------------------------------\n", "回應: 看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5459\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 青年軍 : \"什麼~要坐牢 ?!  黨主席, 救救我 ! \"\n", "朱立倫 : \"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\"\n", "青年軍 : \"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \"\n", "朱立倫 : \"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \"\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5426\n", "----------------------------------------\n", "回應: 葉原之是立委? 我以為他是政論節目通告咖\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5358\n", "----------------------------------------\n", "回應: #鏡新聞 支持罷免徐三八！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5519\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9439\n", "----------------------------------------\n", "回應: 為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5880\n", "----------------------------------------\n", "回應: 自己的國家自己救 !\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5175\n", "----------------------------------------\n", "回應: #鏡新聞 葉李的說話水準素質堪憂\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5033\n", "----------------------------------------\n", "回應: 這屆藍白真的有史以來最爛的\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9337\n", "----------------------------------------\n", "回應: 賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5643\n", "----------------------------------------\n", "回應: #鏡新聞 請問秀中指算不算帶著仇恨?\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5365\n", "----------------------------------------\n", "回應: 元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5382\n", "----------------------------------------\n", "回應: 感覺想專政\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7259\n", "----------------------------------------\n", "回應: 鳳姐也要「刪Q」？\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6531\n", "----------------------------------------\n", "回應: 這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5307\n", "----------------------------------------\n", "回應: 黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews ‪@hance63‬ 支持罷免爛藍白立委\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5368\n", "----------------------------------------\n", "回應: 所以该节目到底是蓝是绿？\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5115\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5764\n", "----------------------------------------\n", "回應: 大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5554\n", "----------------------------------------\n", "回應: 輸不起就不要出來選嗎\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9361\n", "----------------------------------------\n", "回應: 亂七八糟，真的大搞綠色恐怖，肅清異己\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5879\n", "----------------------------------------\n", "回應: 凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9493\n", "----------------------------------------\n", "回應: 葉之再凹就聽不下去了.你令人討厭耶.\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9279\n", "----------------------------------------\n", "回應: 加油！罷免藍營立委\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5939\n", "----------------------------------------\n", "回應: 主持人要加油加油\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.7166\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5722\n", "----------------------------------------\n", "回應: 破產都要把牠罷下來！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8809\n", "----------------------------------------\n", "回應: 美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5300\n", "----------------------------------------\n", "回應: 元之委員加油\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.6122\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5383\n", "----------------------------------------\n", "回應: 往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7169\n", "----------------------------------------\n", "回應: 審預算沒看內容還能表決，這立委6歲小孩都能當\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5255\n", "----------------------------------------\n", "回應: 如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8040\n", "----------------------------------------\n", "回應: 哇，清大有這種老師教憲法！悲哀啊！\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5155\n", "----------------------------------------\n", "回應: 不止罷免江啟程。\n", "凡是國民黨議員。立委。縣市長。\n", "都該被罷免。\n", "趕出台灣。\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5346\n", "----------------------------------------\n", "回應: 罷候\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5582\n", "----------------------------------------\n", "回應: 抗議的不知道自己在抗什麼\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7286\n", "----------------------------------------\n", "回應: 我們直接罷掉不適任的總統！更快！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5802\n", "----------------------------------------\n", "Sentiment 分類準確率: 0.6000\n", "Sentiment 分類報告:\n", "              precision    recall  f1-score   support\n", "\n", "    NEGATIVE       0.82      0.52      0.63        60\n", "    POSITIVE       0.44      0.77      0.56        30\n", "\n", "    accuracy                           0.60        90\n", "   macro avg       0.63      0.64      0.60        90\n", "weighted avg       0.69      0.60      0.61        90\n", "\n", "評估情緒分類模型 (訓練集大小: 100)...\n", "回應: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4404\n", "----------------------------------------\n", "回應: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4203\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.2632\n", "----------------------------------------\n", "回應: #鏡新聞 中指可以比出來啊～\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4430\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.4780\n", "----------------------------------------\n", "回應: 國民黨吃台灣賣台灣。\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.5753\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4235\n", "----------------------------------------\n", "回應: 請支持國共合作\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3571\n", "----------------------------------------\n", "回應: 民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.4748\n", "----------------------------------------\n", "回應: #鏡新聞 醒醒\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4194\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4077\n", "----------------------------------------\n", "回應: 霸到底救國家\n", "霸到底救台灣\n", "霸到底救民主\n", "霸到底救人民\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.4400\n", "----------------------------------------\n", "回應: 國民黨在恐嚇選民，真的好可怕\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3880\n", "----------------------------------------\n", "回應: 行政院可以不接受。让国民党倒阁，解散国会。\n", "国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.5120\n", "----------------------------------------\n", "回應: 千萬別被他騙了!!!霸定了舉手部隊。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5224\n", "----------------------------------------\n", "回應: 廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.2997\n", "----------------------------------------\n", "回應: 中共又再胡說八道，鬼扯，。\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.5201\n", "----------------------------------------\n", "回應: 原汁沒開玩笑 是在裝瘋賣傻\n", "人一藍 腦就殘\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2839\n", "----------------------------------------\n", "回應: 死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.5234\n", "----------------------------------------\n", "回應: 累就滾回家\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.2878\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\n", "但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.2652\n", "----------------------------------------\n", "回應: 遷户口準備中\n", "徐阿花你等著\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4049\n", "----------------------------------------\n", "回應: 罷免葉元之就是罷免陳玉珍\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3795\n", "----------------------------------------\n", "回應: 那張衰臉真是原汁\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3876\n", "----------------------------------------\n", "回應: 無恥的人，還敢公然鬼扯，罷定了!\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4795\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5075\n", "----------------------------------------\n", "回應: 領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3347\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.4676\n", "----------------------------------------\n", "回應: 葉猿之可惡至極，非罷不可 。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5270\n", "----------------------------------------\n", "回應: 除惡務盡，一戰改變台灣政治生態，台派加油！\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.4896\n", "----------------------------------------\n", "回應: 這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4827\n", "----------------------------------------\n", "回應: 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\n", "馬來西亞華人都是穴今\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3798\n", "----------------------------------------\n", "回應: 國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.4223\n", "----------------------------------------\n", "回應: 人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.5465\n", "----------------------------------------\n", "回應: 【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.2200\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 还有民众党\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.4365\n", "----------------------------------------\n", "回應: 元之你是在做復健嗎?\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.4516\n", "----------------------------------------\n", "回應: 朝聖! 柿子要挑軟的吃\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.4086\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4798\n", "----------------------------------------\n", "回應: 建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3570\n", "----------------------------------------\n", "回應: #王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3197\n", "----------------------------------------\n", "回應: 葉元之從頭到尾鬼扯\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3236\n", "----------------------------------------\n", "回應: 傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.5057\n", "----------------------------------------\n", "回應: #鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。\n", "真實情緒: trust\n", "預測情緒: anger\n", "情緒預測分數: 0.4964\n", "----------------------------------------\n", "回應: 真不相信李蟾蜍會是一個中立的X X\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4475\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5045\n", "----------------------------------------\n", "回應: 葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.5518\n", "----------------------------------------\n", "回應: 葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.5154\n", "----------------------------------------\n", "回應: 國民黨出葉元之這種貨色\n", "不倒才怪\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3467\n", "----------------------------------------\n", "回應: 笑噴～真的要出來把沒用的破嘴51席全罷掉\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.4456\n", "----------------------------------------\n", "回應: 國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； \n", "1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.4272\n", "----------------------------------------\n", "回應: 中共在逼了他們為何要這麼急\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3678\n", "----------------------------------------\n", "回應: 看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5216\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 青年軍 : \"什麼~要坐牢 ?!  黨主席, 救救我 ! \"\n", "朱立倫 : \"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\"\n", "青年軍 : \"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \"\n", "朱立倫 : \"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \"\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2516\n", "----------------------------------------\n", "回應: 葉原之是立委? 我以為他是政論節目通告咖\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3781\n", "----------------------------------------\n", "回應: #鏡新聞 支持罷免徐三八！\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.5037\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5462\n", "----------------------------------------\n", "回應: 為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4941\n", "----------------------------------------\n", "回應: 自己的國家自己救 !\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.5003\n", "----------------------------------------\n", "回應: #鏡新聞 葉李的說話水準素質堪憂\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3720\n", "----------------------------------------\n", "回應: 這屆藍白真的有史以來最爛的\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4058\n", "----------------------------------------\n", "回應: 賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2520\n", "----------------------------------------\n", "回應: #鏡新聞 請問秀中指算不算帶著仇恨?\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4605\n", "----------------------------------------\n", "回應: 元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4623\n", "----------------------------------------\n", "回應: 感覺想專政\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3803\n", "----------------------------------------\n", "回應: 鳳姐也要「刪Q」？\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.4574\n", "----------------------------------------\n", "回應: 這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3966\n", "----------------------------------------\n", "回應: 黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews ‪@hance63‬ 支持罷免爛藍白立委\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3155\n", "----------------------------------------\n", "回應: 所以该节目到底是蓝是绿？\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4489\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.4648\n", "----------------------------------------\n", "回應: 大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。\n", "真實情緒: trust\n", "預測情緒: anger\n", "情緒預測分數: 0.5348\n", "----------------------------------------\n", "回應: 輸不起就不要出來選嗎\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4024\n", "----------------------------------------\n", "回應: 亂七八糟，真的大搞綠色恐怖，肅清異己\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3737\n", "----------------------------------------\n", "回應: 凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4772\n", "----------------------------------------\n", "回應: 葉之再凹就聽不下去了.你令人討厭耶.\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.3973\n", "----------------------------------------\n", "回應: 加油！罷免藍營立委\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.2404\n", "----------------------------------------\n", "回應: 主持人要加油加油\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3471\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3926\n", "----------------------------------------\n", "回應: 破產都要把牠罷下來！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4981\n", "----------------------------------------\n", "回應: 美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5104\n", "----------------------------------------\n", "回應: 元之委員加油\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.2851\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！\n", "真實情緒: trust\n", "預測情緒: anger\n", "情緒預測分數: 0.2730\n", "----------------------------------------\n", "回應: 往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.4705\n", "----------------------------------------\n", "回應: 審預算沒看內容還能表決，這立委6歲小孩都能當\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3060\n", "----------------------------------------\n", "回應: 如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3864\n", "----------------------------------------\n", "回應: 哇，清大有這種老師教憲法！悲哀啊！\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.5265\n", "----------------------------------------\n", "回應: 不止罷免江啟程。\n", "凡是國民黨議員。立委。縣市長。\n", "都該被罷免。\n", "趕出台灣。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5411\n", "----------------------------------------\n", "回應: 罷候\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4799\n", "----------------------------------------\n", "回應: 抗議的不知道自己在抗什麼\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3245\n", "----------------------------------------\n", "回應: 我們直接罷掉不適任的總統！更快！\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.4783\n", "----------------------------------------\n", "Emotion 分類準確率: 0.3556\n", "Emotion 分類報告:\n", "              precision    recall  f1-score   support\n", "\n", "         joy       0.36      1.00      0.52        32\n", "       trust       0.00      0.00      0.00         6\n", "        fear       0.00      0.00      0.00        12\n", "    surprise       0.00      0.00      0.00        10\n", "     sadness       0.00      0.00      0.00        10\n", "     disgust       0.00      0.00      0.00         4\n", "       anger       0.00      0.00      0.00        13\n", "anticipation       0.00      0.00      0.00         3\n", "\n", "    accuracy                           0.36        90\n", "   macro avg       0.04      0.12      0.07        90\n", "weighted avg       0.13      0.36      0.19        90\n", "\n", "預測結果已保存到 100_model_predict.json\n", "\n", "=========== 開始處理訓練集大小: 200 ===========\n", "訓練情感分析模型 (訓練集大小: 200)...\n", "🔧 開始處理情感分析數據\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "258960590ba24af5927711ce34b44468", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/200 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "77d4961ec3524c91a89e07c31b02b395", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ 數據集準備完成: 200 訓練樣本, 90 驗證樣本\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2090c93616d641a9b06680d2924f3d3c", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/200 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d43c2757ac404aa1a84ca2dc30ba4dcb", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\transformers\\training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26188\\1096956646.py:145: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.\n", "  trainer = Trainer(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dec0002e41af4c819f8f981a1fad0329", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b8f5d219d5c3414e9884a3be610a9a25", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.6510643362998962, 'eval_runtime': 0.2465, 'eval_samples_per_second': 365.128, 'eval_steps_per_second': 24.342, 'epoch': 1.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d0a50be57b8440e1bdb107c1c5b136ea", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.6331576704978943, 'eval_runtime': 0.2475, 'eval_samples_per_second': 363.686, 'eval_steps_per_second': 24.246, 'epoch': 2.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c9a7abe672654668ad94f678a1907302", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.6279239058494568, 'eval_runtime': 0.3824, 'eval_samples_per_second': 235.356, 'eval_steps_per_second': 15.69, 'epoch': 3.0}\n", "{'train_runtime': 57.4949, 'train_samples_per_second': 10.436, 'train_steps_per_second': 0.678, 'train_loss': 0.6509085435133714, 'epoch': 3.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fd962dfa0eae4911ab0a91d909a0fc37", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["評估結果: {'eval_loss': 0.6279239058494568, 'eval_runtime': 0.2224, 'eval_samples_per_second': 404.678, 'eval_steps_per_second': 26.979, 'epoch': 3.0}\n", "訓練情緒分類模型 (訓練集大小: 200)...\n", "🔧 開始處理情緒分析數據\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "63e3b88ea0e0447f81561c79c0ecb737", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/200 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b24daf246038477f9ee7c6aca7c4e7e4", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ 數據集準備完成: 200 訓練樣本, 90 驗證樣本\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Some weights of BartForSequenceClassification were not initialized from the model checkpoint at facebook/bart-large-mnli and are newly initialized because the shapes did not match:\n", "- classification_head.out_proj.bias: found shape torch.<PERSON><PERSON>([3]) in the checkpoint and torch.<PERSON><PERSON>([8]) in the model instantiated\n", "- classification_head.out_proj.weight: found shape torch.<PERSON><PERSON>([3, 1024]) in the checkpoint and torch.<PERSON><PERSON>([8, 1024]) in the model instantiated\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "da2deae455814e099d03084b5df4ed2f", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/200 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bcd82304647b46039da5706b5581d5b2", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\transformers\\training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26188\\1096956646.py:210: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.\n", "  trainer = Trainer(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cf0ce160f90c47e9a5c56f155d2e623d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/39 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e5cf2f094f0a423d92f67eba80efda4c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 1.9252089262008667, 'eval_runtime': 3.2652, 'eval_samples_per_second': 27.563, 'eval_steps_per_second': 1.838, 'epoch': 1.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "13d1fb0647a04d78bd9b334e68f0cddc", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 1.9319595098495483, 'eval_runtime': 3.0327, 'eval_samples_per_second': 29.677, 'eval_steps_per_second': 1.978, 'epoch': 2.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "427af0043d364be2980f0a5845e5358b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 1.9122799634933472, 'eval_runtime': 2.9881, 'eval_samples_per_second': 30.12, 'eval_steps_per_second': 2.008, 'epoch': 3.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["There were missing keys in the checkpoint model loaded: ['model.encoder.embed_tokens.weight', 'model.decoder.embed_tokens.weight'].\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'train_runtime': 291.1256, 'train_samples_per_second': 2.061, 'train_steps_per_second': 0.134, 'train_loss': 1.795134519919371, 'epoch': 3.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9dccc7601f594d569ea9ed90d9449b9d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["評估結果: {'eval_loss': 1.9122799634933472, 'eval_runtime': 3.0693, 'eval_samples_per_second': 29.322, 'eval_steps_per_second': 1.955, 'epoch': 3.0}\n", "評估情感分析模型 (訓練集大小: 200)...\n", "回應: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5519\n", "----------------------------------------\n", "回應: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5176\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5427\n", "----------------------------------------\n", "回應: #鏡新聞 中指可以比出來啊～\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5698\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7435\n", "----------------------------------------\n", "回應: 國民黨吃台灣賣台灣。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7267\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5243\n", "----------------------------------------\n", "回應: 請支持國共合作\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6742\n", "----------------------------------------\n", "回應: 民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5610\n", "----------------------------------------\n", "回應: #鏡新聞 醒醒\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6443\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7948\n", "----------------------------------------\n", "回應: 霸到底救國家\n", "霸到底救台灣\n", "霸到底救民主\n", "霸到底救人民\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5156\n", "----------------------------------------\n", "回應: 國民黨在恐嚇選民，真的好可怕\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5429\n", "----------------------------------------\n", "回應: 行政院可以不接受。让国民党倒阁，解散国会。\n", "国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5579\n", "----------------------------------------\n", "回應: 千萬別被他騙了!!!霸定了舉手部隊。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9643\n", "----------------------------------------\n", "回應: 廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5092\n", "----------------------------------------\n", "回應: 中共又再胡說八道，鬼扯，。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7378\n", "----------------------------------------\n", "回應: 原汁沒開玩笑 是在裝瘋賣傻\n", "人一藍 腦就殘\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8729\n", "----------------------------------------\n", "回應: 死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9626\n", "----------------------------------------\n", "回應: 累就滾回家\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5241\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\n", "但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5261\n", "----------------------------------------\n", "回應: 遷户口準備中\n", "徐阿花你等著\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5148\n", "----------------------------------------\n", "回應: 罷免葉元之就是罷免陳玉珍\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6081\n", "----------------------------------------\n", "回應: 那張衰臉真是原汁\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8499\n", "----------------------------------------\n", "回應: 無恥的人，還敢公然鬼扯，罷定了!\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8828\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5196\n", "----------------------------------------\n", "回應: 領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9096\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7454\n", "----------------------------------------\n", "回應: 葉猿之可惡至極，非罷不可 。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9057\n", "----------------------------------------\n", "回應: 除惡務盡，一戰改變台灣政治生態，台派加油！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5168\n", "----------------------------------------\n", "回應: 這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5347\n", "----------------------------------------\n", "回應: 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\n", "馬來西亞華人都是穴今\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5646\n", "----------------------------------------\n", "回應: 國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5547\n", "----------------------------------------\n", "回應: 人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5101\n", "----------------------------------------\n", "回應: 【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5592\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 还有民众党\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5513\n", "----------------------------------------\n", "回應: 元之你是在做復健嗎?\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5179\n", "----------------------------------------\n", "回應: 朝聖! 柿子要挑軟的吃\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.6087\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.7252\n", "----------------------------------------\n", "回應: 建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8328\n", "----------------------------------------\n", "回應: #王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5194\n", "----------------------------------------\n", "回應: 葉元之從頭到尾鬼扯\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6157\n", "----------------------------------------\n", "回應: 傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5121\n", "----------------------------------------\n", "回應: #鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5556\n", "----------------------------------------\n", "回應: 真不相信李蟾蜍會是一個中立的X X\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8928\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7807\n", "----------------------------------------\n", "回應: 葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5053\n", "----------------------------------------\n", "回應: 葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5259\n", "----------------------------------------\n", "回應: 國民黨出葉元之這種貨色\n", "不倒才怪\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7122\n", "----------------------------------------\n", "回應: 笑噴～真的要出來把沒用的破嘴51席全罷掉\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9296\n", "----------------------------------------\n", "回應: 國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； \n", "1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6104\n", "----------------------------------------\n", "回應: 中共在逼了他們為何要這麼急\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5854\n", "----------------------------------------\n", "回應: 看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5084\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 青年軍 : \"什麼~要坐牢 ?!  黨主席, 救救我 ! \"\n", "朱立倫 : \"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\"\n", "青年軍 : \"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \"\n", "朱立倫 : \"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \"\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5007\n", "----------------------------------------\n", "回應: 葉原之是立委? 我以為他是政論節目通告咖\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6615\n", "----------------------------------------\n", "回應: #鏡新聞 支持罷免徐三八！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5475\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9577\n", "----------------------------------------\n", "回應: 為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5358\n", "----------------------------------------\n", "回應: 自己的國家自己救 !\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6401\n", "----------------------------------------\n", "回應: #鏡新聞 葉李的說話水準素質堪憂\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6511\n", "----------------------------------------\n", "回應: 這屆藍白真的有史以來最爛的\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9376\n", "----------------------------------------\n", "回應: 賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5165\n", "----------------------------------------\n", "回應: #鏡新聞 請問秀中指算不算帶著仇恨?\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5877\n", "----------------------------------------\n", "回應: 元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5168\n", "----------------------------------------\n", "回應: 感覺想專政\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7692\n", "----------------------------------------\n", "回應: 鳳姐也要「刪Q」？\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7390\n", "----------------------------------------\n", "回應: 這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6215\n", "----------------------------------------\n", "回應: 黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews ‪@hance63‬ 支持罷免爛藍白立委\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5087\n", "----------------------------------------\n", "回應: 所以该节目到底是蓝是绿？\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5465\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5191\n", "----------------------------------------\n", "回應: 大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6360\n", "----------------------------------------\n", "回應: 輸不起就不要出來選嗎\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9347\n", "----------------------------------------\n", "回應: 亂七八糟，真的大搞綠色恐怖，肅清異己\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7074\n", "----------------------------------------\n", "回應: 凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9515\n", "----------------------------------------\n", "回應: 葉之再凹就聽不下去了.你令人討厭耶.\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9349\n", "----------------------------------------\n", "回應: 加油！罷免藍營立委\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5489\n", "----------------------------------------\n", "回應: 主持人要加油加油\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.6270\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5317\n", "----------------------------------------\n", "回應: 破產都要把牠罷下來！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9124\n", "----------------------------------------\n", "回應: 美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5352\n", "----------------------------------------\n", "回應: 元之委員加油\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5210\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7966\n", "----------------------------------------\n", "回應: 往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7929\n", "----------------------------------------\n", "回應: 審預算沒看內容還能表決，這立委6歲小孩都能當\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5813\n", "----------------------------------------\n", "回應: 如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8913\n", "----------------------------------------\n", "回應: 哇，清大有這種老師教憲法！悲哀啊！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5785\n", "----------------------------------------\n", "回應: 不止罷免江啟程。\n", "凡是國民黨議員。立委。縣市長。\n", "都該被罷免。\n", "趕出台灣。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5289\n", "----------------------------------------\n", "回應: 罷候\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6266\n", "----------------------------------------\n", "回應: 抗議的不知道自己在抗什麼\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7809\n", "----------------------------------------\n", "回應: 我們直接罷掉不適任的總統！更快！\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5190\n", "----------------------------------------\n", "Sentiment 分類準確率: 0.7111\n", "Sentiment 分類報告:\n", "              precision    recall  f1-score   support\n", "\n", "    NEGATIVE       0.72      0.92      0.81        60\n", "    POSITIVE       0.64      0.30      0.41        30\n", "\n", "    accuracy                           0.71        90\n", "   macro avg       0.68      0.61      0.61        90\n", "weighted avg       0.70      0.71      0.68        90\n", "\n", "評估情緒分類模型 (訓練集大小: 200)...\n", "回應: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3783\n", "----------------------------------------\n", "回應: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2730\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.2310\n", "----------------------------------------\n", "回應: #鏡新聞 中指可以比出來啊～\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3975\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.6213\n", "----------------------------------------\n", "回應: 國民黨吃台灣賣台灣。\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.3542\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.2797\n", "----------------------------------------\n", "回應: 請支持國共合作\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.2853\n", "----------------------------------------\n", "回應: 民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.2884\n", "----------------------------------------\n", "回應: #鏡新聞 醒醒\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4421\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.6311\n", "----------------------------------------\n", "回應: 霸到底救國家\n", "霸到底救台灣\n", "霸到底救民主\n", "霸到底救人民\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.2724\n", "----------------------------------------\n", "回應: 國民黨在恐嚇選民，真的好可怕\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3358\n", "----------------------------------------\n", "回應: 行政院可以不接受。让国民党倒阁，解散国会。\n", "国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.2393\n", "----------------------------------------\n", "回應: 千萬別被他騙了!!!霸定了舉手部隊。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2999\n", "----------------------------------------\n", "回應: 廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.2277\n", "----------------------------------------\n", "回應: 中共又再胡說八道，鬼扯，。\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.4086\n", "----------------------------------------\n", "回應: 原汁沒開玩笑 是在裝瘋賣傻\n", "人一藍 腦就殘\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3495\n", "----------------------------------------\n", "回應: 死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.3711\n", "----------------------------------------\n", "回應: 累就滾回家\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.2776\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\n", "但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.2272\n", "----------------------------------------\n", "回應: 遷户口準備中\n", "徐阿花你等著\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3000\n", "----------------------------------------\n", "回應: 罷免葉元之就是罷免陳玉珍\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.2773\n", "----------------------------------------\n", "回應: 那張衰臉真是原汁\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3141\n", "----------------------------------------\n", "回應: 無恥的人，還敢公然鬼扯，罷定了!\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4004\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.6009\n", "----------------------------------------\n", "回應: 領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.2874\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.5965\n", "----------------------------------------\n", "回應: 葉猿之可惡至極，非罷不可 。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2648\n", "----------------------------------------\n", "回應: 除惡務盡，一戰改變台灣政治生態，台派加油！\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.2971\n", "----------------------------------------\n", "回應: 這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4029\n", "----------------------------------------\n", "回應: 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\n", "馬來西亞華人都是穴今\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3370\n", "----------------------------------------\n", "回應: 國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.2851\n", "----------------------------------------\n", "回應: 人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.4289\n", "----------------------------------------\n", "回應: 【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.2373\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 还有民众党\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.6257\n", "----------------------------------------\n", "回應: 元之你是在做復健嗎?\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3266\n", "----------------------------------------\n", "回應: 朝聖! 柿子要挑軟的吃\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3471\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3094\n", "----------------------------------------\n", "回應: 建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2483\n", "----------------------------------------\n", "回應: #王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3734\n", "----------------------------------------\n", "回應: 葉元之從頭到尾鬼扯\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.2805\n", "----------------------------------------\n", "回應: 傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.3405\n", "----------------------------------------\n", "回應: #鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。\n", "真實情緒: trust\n", "預測情緒: anger\n", "情緒預測分數: 0.3848\n", "----------------------------------------\n", "回應: 真不相信李蟾蜍會是一個中立的X X\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4218\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2835\n", "----------------------------------------\n", "回應: 葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.2387\n", "----------------------------------------\n", "回應: 葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.2525\n", "----------------------------------------\n", "回應: 國民黨出葉元之這種貨色\n", "不倒才怪\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3494\n", "----------------------------------------\n", "回應: 笑噴～真的要出來把沒用的破嘴51席全罷掉\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.2820\n", "----------------------------------------\n", "回應: 國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； \n", "1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.2660\n", "----------------------------------------\n", "回應: 中共在逼了他們為何要這麼急\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3991\n", "----------------------------------------\n", "回應: 看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3170\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 青年軍 : \"什麼~要坐牢 ?!  黨主席, 救救我 ! \"\n", "朱立倫 : \"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\"\n", "青年軍 : \"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \"\n", "朱立倫 : \"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \"\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5234\n", "----------------------------------------\n", "回應: 葉原之是立委? 我以為他是政論節目通告咖\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2673\n", "----------------------------------------\n", "回應: #鏡新聞 支持罷免徐三八！\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.4155\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.6107\n", "----------------------------------------\n", "回應: 為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2891\n", "----------------------------------------\n", "回應: 自己的國家自己救 !\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.2756\n", "----------------------------------------\n", "回應: #鏡新聞 葉李的說話水準素質堪憂\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3905\n", "----------------------------------------\n", "回應: 這屆藍白真的有史以來最爛的\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3406\n", "----------------------------------------\n", "回應: 賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2025\n", "----------------------------------------\n", "回應: #鏡新聞 請問秀中指算不算帶著仇恨?\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3985\n", "----------------------------------------\n", "回應: 元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3221\n", "----------------------------------------\n", "回應: 感覺想專政\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3642\n", "----------------------------------------\n", "回應: 鳳姐也要「刪Q」？\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3223\n", "----------------------------------------\n", "回應: 這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3639\n", "----------------------------------------\n", "回應: 黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews ‪@hance63‬ 支持罷免爛藍白立委\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2374\n", "----------------------------------------\n", "回應: 所以该节目到底是蓝是绿？\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3461\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.3057\n", "----------------------------------------\n", "回應: 大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。\n", "真實情緒: trust\n", "預測情緒: anger\n", "情緒預測分數: 0.4089\n", "----------------------------------------\n", "回應: 輸不起就不要出來選嗎\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.2600\n", "----------------------------------------\n", "回應: 亂七八糟，真的大搞綠色恐怖，肅清異己\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3835\n", "----------------------------------------\n", "回應: 凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3057\n", "----------------------------------------\n", "回應: 葉之再凹就聽不下去了.你令人討厭耶.\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.2798\n", "----------------------------------------\n", "回應: 加油！罷免藍營立委\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.3406\n", "----------------------------------------\n", "回應: 主持人要加油加油\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3678\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.2887\n", "----------------------------------------\n", "回應: 破產都要把牠罷下來！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2851\n", "----------------------------------------\n", "回應: 美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3066\n", "----------------------------------------\n", "回應: 元之委員加油\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.3146\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！\n", "真實情緒: trust\n", "預測情緒: anger\n", "情緒預測分數: 0.2394\n", "----------------------------------------\n", "回應: 往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.2845\n", "----------------------------------------\n", "回應: 審預算沒看內容還能表決，這立委6歲小孩都能當\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.2946\n", "----------------------------------------\n", "回應: 如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3716\n", "----------------------------------------\n", "回應: 哇，清大有這種老師教憲法！悲哀啊！\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3256\n", "----------------------------------------\n", "回應: 不止罷免江啟程。\n", "凡是國民黨議員。立委。縣市長。\n", "都該被罷免。\n", "趕出台灣。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3719\n", "----------------------------------------\n", "回應: 罷候\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3911\n", "----------------------------------------\n", "回應: 抗議的不知道自己在抗什麼\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3149\n", "----------------------------------------\n", "回應: 我們直接罷掉不適任的總統！更快！\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.3311\n", "----------------------------------------\n", "Emotion 分類準確率: 0.3556\n", "Emotion 分類報告:\n", "              precision    recall  f1-score   support\n", "\n", "         joy       0.36      1.00      0.52        32\n", "       trust       0.00      0.00      0.00         6\n", "        fear       0.00      0.00      0.00        12\n", "    surprise       0.00      0.00      0.00        10\n", "     sadness       0.00      0.00      0.00        10\n", "     disgust       0.00      0.00      0.00         4\n", "       anger       0.00      0.00      0.00        13\n", "anticipation       0.00      0.00      0.00         3\n", "\n", "    accuracy                           0.36        90\n", "   macro avg       0.04      0.12      0.07        90\n", "weighted avg       0.13      0.36      0.19        90\n", "\n", "預測結果已保存到 200_model_predict.json\n", "\n", "=========== 開始處理訓練集大小: 300 ===========\n", "訓練情感分析模型 (訓練集大小: 300)...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🔧 開始處理情感分析數據\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3c01534846a34d51ba56fc89bed3d934", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/300 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c0649aa925464d78adbd1314e7426fbf", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ 數據集準備完成: 300 訓練樣本, 90 驗證樣本\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f5c3bfcc931c4d8cb4b9d4b839bff5bc", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/300 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bfc54459ee4c45919d09bb4c881731f0", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\transformers\\training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26188\\1096956646.py:145: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.\n", "  trainer = Trainer(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a2a3e9ab4b0e44d1a7a39d94467258dd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/57 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "85211c1353e641e79a595240762f93ff", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.6536445021629333, 'eval_runtime': 0.4751, 'eval_samples_per_second': 189.449, 'eval_steps_per_second': 12.63, 'epoch': 1.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "512b773e104b4a88b6e06dfa065ca98a", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.6232889294624329, 'eval_runtime': 0.2501, 'eval_samples_per_second': 359.796, 'eval_steps_per_second': 23.986, 'epoch': 2.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5fc36cdbca4b423198bff9d735a18f1f", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.6205417513847351, 'eval_runtime': 0.3549, 'eval_samples_per_second': 253.588, 'eval_steps_per_second': 16.906, 'epoch': 3.0}\n", "{'train_runtime': 56.6588, 'train_samples_per_second': 15.885, 'train_steps_per_second': 1.006, 'train_loss': 0.6590776945415296, 'epoch': 3.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "64decfa44752469ba98460a293b384a3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["評估結果: {'eval_loss': 0.6205417513847351, 'eval_runtime': 0.2257, 'eval_samples_per_second': 398.832, 'eval_steps_per_second': 26.589, 'epoch': 3.0}\n", "訓練情緒分類模型 (訓練集大小: 300)...\n", "🔧 開始處理情緒分析數據\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "41b31cd33a144b7c897b2740934d5d41", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/300 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "726e091fb3ce414b89f28b34dc71c373", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ 數據集準備完成: 300 訓練樣本, 90 驗證樣本\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Some weights of BartForSequenceClassification were not initialized from the model checkpoint at facebook/bart-large-mnli and are newly initialized because the shapes did not match:\n", "- classification_head.out_proj.bias: found shape torch.<PERSON><PERSON>([3]) in the checkpoint and torch.<PERSON><PERSON>([8]) in the model instantiated\n", "- classification_head.out_proj.weight: found shape torch.<PERSON><PERSON>([3, 1024]) in the checkpoint and torch.<PERSON><PERSON>([8, 1024]) in the model instantiated\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3d5331cfef9a44f7b0cc6be727bbe101", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/300 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7c84cc367fcd4b819466e7fd474fc962", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/90 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\transformers\\training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_26188\\1096956646.py:210: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.\n", "  trainer = Trainer(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "260ea75ddfd741eca2c141e0aaa9ef7c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/57 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4903e2af2fc4499a9bc84dc7bcc0ea10", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 1.9516054391860962, 'eval_runtime': 1.0054, 'eval_samples_per_second': 89.518, 'eval_steps_per_second': 5.968, 'epoch': 1.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3d667eb4c9484334b18ab074c5fb0a30", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 1.9378314018249512, 'eval_runtime': 1.0097, 'eval_samples_per_second': 89.137, 'eval_steps_per_second': 5.942, 'epoch': 2.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "59b8a50ad4f34e9394f3bcc3be74fb31", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 1.9075236320495605, 'eval_runtime': 1.0287, 'eval_samples_per_second': 87.49, 'eval_steps_per_second': 5.833, 'epoch': 3.0}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["There were missing keys in the checkpoint model loaded: ['model.encoder.embed_tokens.weight', 'model.decoder.embed_tokens.weight'].\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'train_runtime': 326.2293, 'train_samples_per_second': 2.759, 'train_steps_per_second': 0.175, 'train_loss': 1.8408610026041667, 'epoch': 3.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4c4588b95a444eb7b77856e3c5788c95", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["評估結果: {'eval_loss': 1.9075236320495605, 'eval_runtime': 0.85, 'eval_samples_per_second': 105.878, 'eval_steps_per_second': 7.059, 'epoch': 3.0}\n", "評估情感分析模型 (訓練集大小: 300)...\n", "回應: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5934\n", "----------------------------------------\n", "回應: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5624\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5854\n", "----------------------------------------\n", "回應: #鏡新聞 中指可以比出來啊～\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6361\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7428\n", "----------------------------------------\n", "回應: 國民黨吃台灣賣台灣。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6167\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5803\n", "----------------------------------------\n", "回應: 請支持國共合作\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7613\n", "----------------------------------------\n", "回應: 民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5076\n", "----------------------------------------\n", "回應: #鏡新聞 醒醒\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7310\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8227\n", "----------------------------------------\n", "回應: 霸到底救國家\n", "霸到底救台灣\n", "霸到底救民主\n", "霸到底救人民\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5323\n", "----------------------------------------\n", "回應: 國民黨在恐嚇選民，真的好可怕\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5545\n", "----------------------------------------\n", "回應: 行政院可以不接受。让国民党倒阁，解散国会。\n", "国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5623\n", "----------------------------------------\n", "回應: 千萬別被他騙了!!!霸定了舉手部隊。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9600\n", "----------------------------------------\n", "回應: 廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5494\n", "----------------------------------------\n", "回應: 中共又再胡說八道，鬼扯，。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7612\n", "----------------------------------------\n", "回應: 原汁沒開玩笑 是在裝瘋賣傻\n", "人一藍 腦就殘\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9148\n", "----------------------------------------\n", "回應: 死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9548\n", "----------------------------------------\n", "回應: 累就滾回家\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6105\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\n", "但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5134\n", "----------------------------------------\n", "回應: 遷户口準備中\n", "徐阿花你等著\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5592\n", "----------------------------------------\n", "回應: 罷免葉元之就是罷免陳玉珍\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7114\n", "----------------------------------------\n", "回應: 那張衰臉真是原汁\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8966\n", "----------------------------------------\n", "回應: 無恥的人，還敢公然鬼扯，罷定了!\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8717\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5623\n", "----------------------------------------\n", "回應: 領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9041\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8097\n", "----------------------------------------\n", "回應: 葉猿之可惡至極，非罷不可 。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9383\n", "----------------------------------------\n", "回應: 除惡務盡，一戰改變台灣政治生態，台派加油！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5505\n", "----------------------------------------\n", "回應: 這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5704\n", "----------------------------------------\n", "回應: 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\n", "馬來西亞華人都是穴今\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6113\n", "----------------------------------------\n", "回應: 國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5428\n", "----------------------------------------\n", "回應: 人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5586\n", "----------------------------------------\n", "回應: 【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6547\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 还有民众党\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6129\n", "----------------------------------------\n", "回應: 元之你是在做復健嗎?\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5566\n", "----------------------------------------\n", "回應: 朝聖! 柿子要挑軟的吃\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5034\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!\n", "真實標籤: NEGATIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.6605\n", "----------------------------------------\n", "回應: 建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8465\n", "----------------------------------------\n", "回應: #王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5460\n", "----------------------------------------\n", "回應: 葉元之從頭到尾鬼扯\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7154\n", "----------------------------------------\n", "回應: 傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5538\n", "----------------------------------------\n", "回應: #鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5826\n", "----------------------------------------\n", "回應: 真不相信李蟾蜍會是一個中立的X X\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8950\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8217\n", "----------------------------------------\n", "回應: 葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5334\n", "----------------------------------------\n", "回應: 葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6089\n", "----------------------------------------\n", "回應: 國民黨出葉元之這種貨色\n", "不倒才怪\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7312\n", "----------------------------------------\n", "回應: 笑噴～真的要出來把沒用的破嘴51席全罷掉\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9431\n", "----------------------------------------\n", "回應: 國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； \n", "1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5413\n", "----------------------------------------\n", "回應: 中共在逼了他們為何要這麼急\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6527\n", "----------------------------------------\n", "回應: 看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5463\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 青年軍 : \"什麼~要坐牢 ?!  黨主席, 救救我 ! \"\n", "朱立倫 : \"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\"\n", "青年軍 : \"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \"\n", "朱立倫 : \"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \"\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5220\n", "----------------------------------------\n", "回應: 葉原之是立委? 我以為他是政論節目通告咖\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6611\n", "----------------------------------------\n", "回應: #鏡新聞 支持罷免徐三八！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6017\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9587\n", "----------------------------------------\n", "回應: 為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5418\n", "----------------------------------------\n", "回應: 自己的國家自己救 !\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6791\n", "----------------------------------------\n", "回應: #鏡新聞 葉李的說話水準素質堪憂\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7915\n", "----------------------------------------\n", "回應: 這屆藍白真的有史以來最爛的\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9360\n", "----------------------------------------\n", "回應: 賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5091\n", "----------------------------------------\n", "回應: #鏡新聞 請問秀中指算不算帶著仇恨?\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6454\n", "----------------------------------------\n", "回應: 元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5707\n", "----------------------------------------\n", "回應: 感覺想專政\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8089\n", "----------------------------------------\n", "回應: 鳳姐也要「刪Q」？\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7923\n", "----------------------------------------\n", "回應: 這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6328\n", "----------------------------------------\n", "回應: 黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews ‪@hance63‬ 支持罷免爛藍白立委\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5472\n", "----------------------------------------\n", "回應: 所以该节目到底是蓝是绿？\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6028\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5767\n", "----------------------------------------\n", "回應: 大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6152\n", "----------------------------------------\n", "回應: 輸不起就不要出來選嗎\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9407\n", "----------------------------------------\n", "回應: 亂七八糟，真的大搞綠色恐怖，肅清異己\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7037\n", "----------------------------------------\n", "回應: 凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9551\n", "----------------------------------------\n", "回應: 葉之再凹就聽不下去了.你令人討厭耶.\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9444\n", "----------------------------------------\n", "回應: 加油！罷免藍營立委\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5192\n", "----------------------------------------\n", "回應: 主持人要加油加油\n", "真實標籤: POSITIVE\n", "預測標籤: POSITIVE\n", "情感預測分數: 0.5690\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5713\n", "----------------------------------------\n", "回應: 破產都要把牠罷下來！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9216\n", "----------------------------------------\n", "回應: 美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5641\n", "----------------------------------------\n", "回應: 元之委員加油\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5556\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8602\n", "----------------------------------------\n", "回應: 往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8892\n", "----------------------------------------\n", "回應: 審預算沒看內容還能表決，這立委6歲小孩都能當\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6714\n", "----------------------------------------\n", "回應: 如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.9051\n", "----------------------------------------\n", "回應: 哇，清大有這種老師教憲法！悲哀啊！\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.6700\n", "----------------------------------------\n", "回應: 不止罷免江啟程。\n", "凡是國民黨議員。立委。縣市長。\n", "都該被罷免。\n", "趕出台灣。\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5523\n", "----------------------------------------\n", "回應: 罷候\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.7699\n", "----------------------------------------\n", "回應: 抗議的不知道自己在抗什麼\n", "真實標籤: NEGATIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.8309\n", "----------------------------------------\n", "回應: 我們直接罷掉不適任的總統！更快！\n", "真實標籤: POSITIVE\n", "預測標籤: NEGATIVE\n", "情感預測分數: 0.5797\n", "----------------------------------------\n", "Sentiment 分類準確率: 0.6667\n", "Sentiment 分類報告:\n", "              precision    recall  f1-score   support\n", "\n", "    NEGATIVE       0.67      0.97      0.79        60\n", "    POSITIVE       0.50      0.07      0.12        30\n", "\n", "    accuracy                           0.67        90\n", "   macro avg       0.59      0.52      0.46        90\n", "weighted avg       0.62      0.67      0.57        90\n", "\n", "評估情緒分類模型 (訓練集大小: 300)...\n", "回應: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3790\n", "----------------------------------------\n", "回應: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4448\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.4020\n", "----------------------------------------\n", "回應: #鏡新聞 中指可以比出來啊～\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4215\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.5779\n", "----------------------------------------\n", "回應: 國民黨吃台灣賣台灣。\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.3553\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.2746\n", "----------------------------------------\n", "回應: 請支持國共合作\n", "真實情緒: disgust\n", "預測情緒: fear\n", "情緒預測分數: 0.2408\n", "----------------------------------------\n", "回應: 民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3172\n", "----------------------------------------\n", "回應: #鏡新聞 醒醒\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4177\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.6256\n", "----------------------------------------\n", "回應: 霸到底救國家\n", "霸到底救台灣\n", "霸到底救民主\n", "霸到底救人民\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.2730\n", "----------------------------------------\n", "回應: 國民黨在恐嚇選民，真的好可怕\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.3303\n", "----------------------------------------\n", "回應: 行政院可以不接受。让国民党倒阁，解散国会。\n", "国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.3014\n", "----------------------------------------\n", "回應: 千萬別被他騙了!!!霸定了舉手部隊。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3238\n", "----------------------------------------\n", "回應: 廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3313\n", "----------------------------------------\n", "回應: 中共又再胡說八道，鬼扯，。\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.4309\n", "----------------------------------------\n", "回應: 原汁沒開玩笑 是在裝瘋賣傻\n", "人一藍 腦就殘\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4221\n", "----------------------------------------\n", "回應: 死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.3614\n", "----------------------------------------\n", "回應: 累就滾回家\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3278\n", "----------------------------------------\n", "回應: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\n", "但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.3747\n", "----------------------------------------\n", "回應: 遷户口準備中\n", "徐阿花你等著\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3220\n", "----------------------------------------\n", "回應: 罷免葉元之就是罷免陳玉珍\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3110\n", "----------------------------------------\n", "回應: 那張衰臉真是原汁\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.2940\n", "----------------------------------------\n", "回應: 無恥的人，還敢公然鬼扯，罷定了!\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4127\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.6174\n", "----------------------------------------\n", "回應: 領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.2800\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.6195\n", "----------------------------------------\n", "回應: 葉猿之可惡至極，非罷不可 。\n", "真實情緒: anger\n", "預測情緒: fear\n", "情緒預測分數: 0.2635\n", "----------------------------------------\n", "回應: 除惡務盡，一戰改變台灣政治生態，台派加油！\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.2934\n", "----------------------------------------\n", "回應: 這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3736\n", "----------------------------------------\n", "回應: 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\n", "馬來西亞華人都是穴今\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3657\n", "----------------------------------------\n", "回應: 國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3343\n", "----------------------------------------\n", "回應: 人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.4276\n", "----------------------------------------\n", "回應: 【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.4076\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 还有民众党\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.6265\n", "----------------------------------------\n", "回應: 元之你是在做復健嗎?\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.4453\n", "----------------------------------------\n", "回應: 朝聖! 柿子要挑軟的吃\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3825\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2861\n", "----------------------------------------\n", "回應: 建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3566\n", "----------------------------------------\n", "回應: #王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3872\n", "----------------------------------------\n", "回應: 葉元之從頭到尾鬼扯\n", "真實情緒: surprise\n", "預測情緒: fear\n", "情緒預測分數: 0.2530\n", "----------------------------------------\n", "回應: 傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.3429\n", "----------------------------------------\n", "回應: #鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。\n", "真實情緒: trust\n", "預測情緒: anger\n", "情緒預測分數: 0.4143\n", "----------------------------------------\n", "回應: 真不相信李蟾蜍會是一個中立的X X\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3967\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！\n", "真實情緒: anger\n", "預測情緒: fear\n", "情緒預測分數: 0.2776\n", "----------------------------------------\n", "回應: 葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。\n", "真實情緒: disgust\n", "預測情緒: fear\n", "情緒預測分數: 0.2538\n", "----------------------------------------\n", "回應: 葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!\n", "真實情緒: surprise\n", "預測情緒: fear\n", "情緒預測分數: 0.2254\n", "----------------------------------------\n", "回應: 國民黨出葉元之這種貨色\n", "不倒才怪\n", "真實情緒: disgust\n", "預測情緒: anger\n", "情緒預測分數: 0.3442\n", "----------------------------------------\n", "回應: 笑噴～真的要出來把沒用的破嘴51席全罷掉\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.3062\n", "----------------------------------------\n", "回應: 國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； \n", "1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。\n", "真實情緒: sadness\n", "預測情緒: anger\n", "情緒預測分數: 0.3267\n", "----------------------------------------\n", "回應: 中共在逼了他們為何要這麼急\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4140\n", "----------------------------------------\n", "回應: 看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3873\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 青年軍 : \"什麼~要坐牢 ?!  黨主席, 救救我 ! \"\n", "朱立倫 : \"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\"\n", "青年軍 : \"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \"\n", "朱立倫 : \"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \"\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.5979\n", "----------------------------------------\n", "回應: 葉原之是立委? 我以為他是政論節目通告咖\n", "真實情緒: anger\n", "預測情緒: fear\n", "情緒預測分數: 0.2463\n", "----------------------------------------\n", "回應: #鏡新聞 支持罷免徐三八！\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.4294\n", "----------------------------------------\n", "回應: 三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.6166\n", "----------------------------------------\n", "回應: 為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3048\n", "----------------------------------------\n", "回應: 自己的國家自己救 !\n", "真實情緒: anticipation\n", "預測情緒: joy\n", "情緒預測分數: 0.2405\n", "----------------------------------------\n", "回應: #鏡新聞 葉李的說話水準素質堪憂\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4129\n", "----------------------------------------\n", "回應: 這屆藍白真的有史以來最爛的\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3790\n", "----------------------------------------\n", "回應: 賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！\n", "真實情緒: anger\n", "預測情緒: fear\n", "情緒預測分數: 0.2191\n", "----------------------------------------\n", "回應: #鏡新聞 請問秀中指算不算帶著仇恨?\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4258\n", "----------------------------------------\n", "回應: 元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4135\n", "----------------------------------------\n", "回應: 感覺想專政\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4026\n", "----------------------------------------\n", "回應: 鳳姐也要「刪Q」？\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.2883\n", "----------------------------------------\n", "回應: 這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3744\n", "----------------------------------------\n", "回應: 黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews ‪@hance63‬ 支持罷免爛藍白立委\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2720\n", "----------------------------------------\n", "回應: 所以该节目到底是蓝是绿？\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3666\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？\n", "真實情緒: joy\n", "預測情緒: fear\n", "情緒預測分數: 0.2910\n", "----------------------------------------\n", "回應: 大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。\n", "真實情緒: trust\n", "預測情緒: anger\n", "情緒預測分數: 0.3804\n", "----------------------------------------\n", "回應: 輸不起就不要出來選嗎\n", "真實情緒: fear\n", "預測情緒: fear\n", "情緒預測分數: 0.2416\n", "----------------------------------------\n", "回應: 亂七八糟，真的大搞綠色恐怖，肅清異己\n", "真實情緒: fear\n", "預測情緒: anger\n", "情緒預測分數: 0.4155\n", "----------------------------------------\n", "回應: 凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3904\n", "----------------------------------------\n", "回應: 葉之再凹就聽不下去了.你令人討厭耶.\n", "真實情緒: joy\n", "預測情緒: joy\n", "情緒預測分數: 0.2526\n", "----------------------------------------\n", "回應: 加油！罷免藍營立委\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.3729\n", "----------------------------------------\n", "回應: 主持人要加油加油\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3629\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠\n", "真實情緒: fear\n", "預測情緒: fear\n", "情緒預測分數: 0.2785\n", "----------------------------------------\n", "回應: 破產都要把牠罷下來！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3197\n", "----------------------------------------\n", "回應: 美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.2999\n", "----------------------------------------\n", "回應: 元之委員加油\n", "真實情緒: joy\n", "預測情緒: anger\n", "情緒預測分數: 0.4100\n", "----------------------------------------\n", "回應: 黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！\n", "真實情緒: trust\n", "預測情緒: fear\n", "情緒預測分數: 0.2707\n", "----------------------------------------\n", "回應: 往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.4609\n", "----------------------------------------\n", "回應: 審預算沒看內容還能表決，這立委6歲小孩都能當\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.4337\n", "----------------------------------------\n", "回應: 如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.4128\n", "----------------------------------------\n", "回應: 哇，清大有這種老師教憲法！悲哀啊！\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.3643\n", "----------------------------------------\n", "回應: 不止罷免江啟程。\n", "凡是國民黨議員。立委。縣市長。\n", "都該被罷免。\n", "趕出台灣。\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.4913\n", "----------------------------------------\n", "回應: 罷候\n", "真實情緒: anger\n", "預測情緒: anger\n", "情緒預測分數: 0.3257\n", "----------------------------------------\n", "回應: 抗議的不知道自己在抗什麼\n", "真實情緒: surprise\n", "預測情緒: anger\n", "情緒預測分數: 0.2781\n", "----------------------------------------\n", "回應: 我們直接罷掉不適任的總統！更快！\n", "真實情緒: anticipation\n", "預測情緒: anger\n", "情緒預測分數: 0.3575\n", "----------------------------------------\n", "Emotion 分類準確率: 0.3444\n", "Emotion 分類報告:\n", "              precision    recall  f1-score   support\n", "\n", "         joy       0.37      0.88      0.52        32\n", "       trust       0.00      0.00      0.00         6\n", "        fear       0.00      0.00      0.00        12\n", "    surprise       0.17      0.20      0.18        10\n", "     sadness       0.50      0.10      0.17        10\n", "     disgust       0.00      0.00      0.00         4\n", "       anger       0.00      0.00      0.00        13\n", "anticipation       0.00      0.00      0.00         3\n", "\n", "    accuracy                           0.34        90\n", "   macro avg       0.13      0.15      0.11        90\n", "weighted avg       0.21      0.34      0.22        90\n", "\n", "預測結果已保存到 300_model_predict.json\n", "\n", "=========== 實驗結果摘要 ===========\n", "\n", "訓練集大小: 100\n", "情感分析準確率: 0.6000\n", "情緒分類準確率: 0.3556\n", "\n", "訓練集大小: 200\n", "情感分析準確率: 0.7111\n", "情緒分類準確率: 0.3556\n", "\n", "訓練集大小: 300\n", "情感分析準確率: 0.6667\n", "情緒分類準確率: 0.3444\n", "\n", "實驗結果已保存到training_results.json\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "d:\\Anaconda\\envs\\transformers\\Lib\\site-packages\\sklearn\\metrics\\_classification.py:1565: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}], "source": ["if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["You passed along `num_labels=3` with an incompatible id to label map: {'0': 'joy', '1': 'trust', '2': 'fear', '3': 'surprise', '4': 'sadness', '5': 'disgust', '6': 'anger', '7': 'anticipation'}. The number of labels wil be overwritten to 8.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📊 測試情感分析模型...\n", "📊 測試情緒分類模型...\n", "\n", "=== 📈 測試結果總結 ===\n", "情感分析準確率: 0.0000\n", "情感分類報告:\n", "無真實標籤，無法計算報告\n", "情緒分類準確率: 0.0000\n", "情緒分類報告:\n", "無真實標籤，無法計算報告\n", "\n", "✅ 預測與報告已儲存完成。\n"]}], "source": ["import json\n", "from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline\n", "from sklearn.metrics import accuracy_score, classification_report\n", "import torch\n", "\n", "# 使用 GPU（如果有）\n", "device = 0 if torch.cuda.is_available() else -1\n", "\n", "# 1. 載入模型與 tokenizer\n", "sentiment_model_path = \"sentiment_model_200\"\n", "emotion_model_path = \"emotion_model_300\"\n", "\n", "sentiment_tokenizer = AutoTokenizer.from_pretrained(sentiment_model_path)\n", "sentiment_model = AutoModelForSequenceClassification.from_pretrained(sentiment_model_path)\n", "\n", "emotion_tokenizer = AutoTokenizer.from_pretrained(emotion_model_path)\n", "emotion_model = AutoModelForSequenceClassification.from_pretrained(emotion_model_path)\n", "\n", "# 2. 載入驗證資料\n", "def load_data_from_json(filepath):\n", "    with open(filepath, \"r\", encoding=\"utf-8\") as f:\n", "        return json.load(f)\n", "\n", "val_data = load_data_from_json(\"bert_data/vali_data.json\")\n", "\n", "# 3. 測試情感模型\n", "def test_sentiment_model(model, tokenizer, data):\n", "    classifier = pipeline(\"sentiment-analysis\", model=model, tokenizer=tokenizer, device=device)\n", "    preds, labels, results = [], [], []\n", "\n", "    for item in data:\n", "        text = item[\"reply\"]\n", "        result = classifier(text)[0]\n", "        pred_label = result[\"label\"].lower()\n", "        true_label = item.get(\"label\", \"\").lower()  # 若無標籤可跳過比較\n", "        preds.append(pred_label)\n", "        labels.append(true_label)\n", "\n", "        results.append({\n", "            \"context\": item[\"context\"],\n", "            \"reply\": item[\"reply\"],\n", "            \"predicted_sentiment\": pred_label,\n", "            \"sentiment_score\": result[\"score\"],\n", "            \"predicted_emotion\": \"\",\n", "            \"emotion_score\": 0.0\n", "        })\n", "\n", "    acc = accuracy_score(labels, preds) if all(labels) else 0\n", "    report = classification_report(labels, preds, zero_division=0) if all(labels) else \"無真實標籤，無法計算報告\"\n", "    return acc, report, results\n", "\n", "\n", "# 4. 測試情緒模型\n", "def test_emotion_model(model, tokenizer, data, results):\n", "    classifier = pipeline(\"text-classification\", model=model, tokenizer=tokenizer, device=device)\n", "    preds, labels = [], []\n", "\n", "    for i, item in enumerate(data):\n", "        text = item[\"context\"] + \" \" + item[\"reply\"]\n", "        result = classifier(text)[0]\n", "        pred_label = result[\"label\"].lower()\n", "        true_label = item.get(\"emotion\", \"\").lower()\n", "        preds.append(pred_label)\n", "        labels.append(true_label)\n", "\n", "        results[i][\"predicted_emotion\"] = pred_label\n", "        results[i][\"emotion_score\"] = result[\"score\"]\n", "\n", "    acc = accuracy_score(labels, preds) if all(labels) else 0\n", "    report = classification_report(labels, preds, zero_division=0) if all(labels) else \"無真實標籤，無法計算報告\"\n", "    return acc, report, results\n", "\n", "\n", "# 5. 執行測試流程\n", "if __name__ == \"__main__\":\n", "    print(\"📊 測試情感分析模型...\")\n", "    sentiment_acc, sentiment_report, prediction_results = test_sentiment_model(\n", "        sentiment_model, sentiment_tokenizer, val_data\n", "    )\n", "\n", "    print(\"📊 測試情緒分類模型...\")\n", "    emotion_acc, emotion_report, prediction_results = test_emotion_model(\n", "        emotion_model, emotion_tokenizer, val_data, prediction_results\n", "    )\n", "\n", "    # 6. 輸出結果\n", "    print(\"\\n=== 📈 測試結果總結 ===\")\n", "    print(f\"情感分析準確率: {sentiment_acc:.4f}\")\n", "    print(f\"情感分類報告:\\n{sentiment_report}\")\n", "    print(f\"情緒分類準確率: {emotion_acc:.4f}\")\n", "    print(f\"情緒分類報告:\\n{emotion_report}\")\n", "\n", "    # 7. 儲存結果\n", "    with open(\"./bert_data/bert_only_reply.json\", \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(prediction_results, f, ensure_ascii=False, indent=4)\n", "\n", "    results_summary = {\n", "        \"sentiment\": {\"accuracy\": sentiment_acc, \"report\": sentiment_report},\n", "        \"emotion\": {\"accuracy\": emotion_acc, \"report\": emotion_report}\n", "    }\n", "\n", "   # with open(\"./berbert_only_reply.json\", \"w\", encoding=\"utf-8\") as f:\n", "   #     json.dump(results_summary, f, ensure_ascii=False, indent=4)\n", "\n", "    print(\"\\n✅ 預測與報告已儲存完成。\")\n"]}], "metadata": {"kernelspec": {"display_name": "transformers", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}