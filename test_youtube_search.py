import sys
import os
import time
from datetime import datetime

# 加入項目根目錄到系統路徑，以便導入模組
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'backend'))
sys.path.append(os.path.join(current_dir, 'backend', 'crawler'))

# 導入爬蟲模組
try:
    from backend.crawler.yt_crawler import search_youtube_videos
except ImportError:
    print("嘗試使用替代導入路徑...")
    from yt_crawler import search_youtube_videos

def test_youtube_search():
    """測試 YouTube 搜尋功能"""
    print("測試 YouTube 搜尋功能...")
    
    # 設定輸出目錄
    output_dir = os.path.join(current_dir, 'backend', 'crawler', 'href', 'youtube')
    os.makedirs(output_dir, exist_ok=True)
    
    # 執行搜尋
    politician_name = "葉元之"  # 可以替換為其他政治人物名字
    cutoff_date_str = (datetime.now().replace(day=1)).strftime('%Y-%m-%d')  # 本月第一天
    
    print(f"搜尋 YouTube 影片: {politician_name}, 截止日期: {cutoff_date_str}")
    start_time = time.time()
    
    url_file, video_data = search_youtube_videos(
        name=politician_name,
        output_dir=output_dir,
        headless=True,
        cutoff_date=cutoff_date_str
    )
    
    end_time = time.time()
    duration = end_time - start_time
    
    if url_file and video_data:
        print(f"✅ 測試成功: 找到 {len(video_data)} 個影片")
        print(f"⏱️ 耗時: {duration:.2f} 秒")
        
        # 檢查影片資料
        print("\n影片範例 (前3個):")
        for i, video in enumerate(video_data[:3], 1):
            print(f"影片 {i}:")
            print(f"  標題: {video.get('title', 'N/A')}")
            print(f"  URL: {video.get('url', 'N/A')}")
            print(f"  上傳時間: {video.get('upload_time', 'N/A')}")
    else:
        print(f"❌ 測試失敗: 無法找到影片")
    
    return video_data

if __name__ == "__main__":
    test_youtube_search()
