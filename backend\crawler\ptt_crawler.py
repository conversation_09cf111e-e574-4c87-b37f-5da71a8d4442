import os
import json
import time
import random
import traceback
import re
import threading
import queue
from datetime import datetime, timedelta
from threading import Lock
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from json_utils import append_json_data, read_json_file, append_href_data, append_crawler_data

def setup_driver(headless=True, retries=3, thread_id=None):
    """初始化PTT專用的Chrome瀏覽器驅動"""
    options = webdriver.ChromeOptions()
    
    # 基本設定
    if headless:
        options.add_argument("--headless=new")
    
    # 修正PTT爬蟲的穩定性設定
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-images")  # 加速載入
    options.add_argument("--disable-javascript")  # PTT不需要JS
    options.add_argument("--disable-plugins")
    options.add_argument("--disable-background-timer-throttling")
    options.add_argument("--disable-backgrounding-occluded-windows")
    options.add_argument("--disable-renderer-backgrounding")
    
    # 記憶體和效能優化
    options.add_argument("--memory-pressure-off")
    options.add_argument("--max_old_space_size=4096")
    
    # 避免崩潰的關鍵設定
    options.add_argument("--disable-features=VizDisplayCompositor")
    options.add_argument("--disable-features=TranslateUI")
    options.add_argument("--disable-ipc-flooding-protection")
    
    # 視窗設定
    if not headless:
        options.add_argument("--start-maximized")
        options.add_argument("--window-size=1920,1080")
    
    # 使用穩定的User-Agent
    options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.set_page_load_timeout(30)
        
        if not headless:
            driver.maximize_window()
            driver.set_window_size(1920, 1080)
            
        return driver
    except Exception as e:
        print(f"❌ PTT瀏覽器初始化失敗: {e}")
        raise

def handle_age_verification(driver):
    try:
        print("等待 45 秒後點選年齡確認按鈕...")
        time.sleep(15)  # 先等待 45 秒
        
        # 先嘗試檢查頁面是否有年齡確認按鈕
        try:
            # 使用更寬容的定位方式檢查是否有年齡確認按鈕
            content = driver.page_source
            if "我同意，我已年滿十八歲" in content or "18 years old" in content:
                print("頁面確實含有年齡確認元素")
            else:
                print("頁面似乎沒有年齡確認元素，可能已通過或不需要年齡確認")
                return True
        except Exception as check_e:
            print(f"檢查頁面年齡確認內容時發生錯誤: {check_e}")
        
        # 嘗試使用 XPath 定位按鈕
        try:
            xpath_button = driver.find_element(By.XPATH, "/html/body/div[2]/form/div[1]/button")
            print("使用文字內容 XPath 找到年齡確認按鈕")
            xpath_button.click()
            print("已點選年齡確認按鈕 (文字內容 XPath)")
            return True
        except Exception as e1:
            print(f"使用文字內容 XPath 定位年齡確認按鈕失敗: {e1}")
        
        # 嘗試使用一般 XPath 定位按鈕
        try:
            xpath_button = driver.find_element(By.XPATH, "/html/body/div[2]/form/div[1]/button")
            print("使用 XPath 找到年齡確認按鈕")
            xpath_button.click()
            print("已點選年齡確認按鈕 (XPath)")
            return True
        except Exception as e2:
            print(f"使用 XPath 定位年齡確認按鈕失敗: {e2}")
        
        # 嘗試使用 CSS 選擇器作為備用方法
        try:
            print("嘗試使用 CSS 選擇器定位年齡確認按鈕...")
            button = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "body > div.bbs-screen.bbs-content.center.clear > form > div:nth-child(2) > button"))
            )
            print("使用 CSS 選擇器找到年齡確認按鈕")
            button.click()
            print("已點選年齡確認按鈕 (CSS)")
            return True
        except Exception as e3:
            print(f"使用 CSS 選擇器定位年齡確認按鈕失敗: {e3}")
            print("所有方法皆無法找到年齡確認按鈕")
            return False
            
    except (TimeoutException, Exception) as e:
        print(f"處理年齡確認時發生錯誤: {e}")
        return False

def search_article(driver, input_name):
    try:
        driver.get("https://www.ptt.cc/bbs/Gossiping/index.html")
        print("開啟 PTT 頁面，等待 10 秒...")
        time.sleep(10)  # 增加等待時間為 60 秒
        handle_age_verification(driver)
        time.sleep(1)
        search_input = WebDriverWait(driver, 20).until(  # 增加等待時間至 20 秒
            EC.presence_of_element_located((By.CSS_SELECTOR, "input.query"))
        )
        search_input.clear()
        search_input.send_keys(input_name)
        search_input.send_keys(Keys.RETURN)
        print("搜尋後等待 60 秒...")
        time.sleep(10)  # 增加等待時間為 60 秒
        return True
    except Exception as e:
        print(f"搜尋文章失敗: {e}")
        return False

def btn_next(driver):
    try:
        prev_button = driver.find_element(By.LINK_TEXT, "‹ 上頁")
        href_value = prev_button.get_attribute('href')
        if not href_value:
            return False
        time.sleep(2)
        prev_button.click()
        return True
    except (NoSuchElementException, Exception) as e:
        print(f"點擊下一頁失敗: {e}")
        return False

def extract_links(driver, search_term):
    try:
        elements = driver.find_elements(By.XPATH, '//a[contains(@href, "/bbs/Gossiping/M.")]')
        href_list = [element.get_attribute('href') for element in elements]
        today = datetime.now().strftime("%Y%m%d")
        
        # 使用當前crawler目錄下的href路徑，統一命名為立委.json
        current_dir = os.path.dirname(os.path.abspath(__file__))
        href_dir = os.path.join(current_dir, "href", "ptt")
        json_filename = os.path.join(href_dir, f"{search_term}.json")
        
        os.makedirs(href_dir, exist_ok=True)
        
        # 使用新的JSON工具函數讀取和寫入
        existing_urls = read_json_file(json_filename, [])
        
        # 如果existing_urls是字典格式，轉換為URL字符串列表
        if existing_urls and isinstance(existing_urls[0], dict):
            existing_url_strings = [item.get("url", item) for item in existing_urls if isinstance(item, dict)]
        else:
            existing_url_strings = existing_urls
        
        new_urls = [url for url in href_list if url not in existing_url_strings]
        
        # 使用append方式寫入新URL，並添加post_time字段
        if new_urls:
            # 將URL轉換為包含post_time的格式
            new_url_entries = []
            for url in new_urls:
                # 從URL中提取時間戳並轉換為日期
                url_match = re.search(r'M\.(\d+)\.A\.([A-Za-z0-9]+)\.html', url)
                if url_match:
                    timestamp = int(url_match.group(1))
                    post_date = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                else:
                    post_date = datetime.now().strftime('%Y-%m-%d')

                new_url_entries.append({
                    'url': url,
                    'post_time': post_date,  # 新增：用於快速篩選的標準化時間
                    'added_time': datetime.now().strftime('%Y-%m-%d')
                })

            # 直接寫入新格式的數據
            try:
                with open(json_filename, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except:
                existing_data = []

            # 確保現有數據也有post_time字段
            for item in existing_data:
                if isinstance(item, dict) and 'post_time' not in item:
                    url = item.get('url', '')
                    url_match = re.search(r'M\.(\d+)\.A\.([A-Za-z0-9]+)\.html', url)
                    if url_match:
                        timestamp = int(url_match.group(1))
                        item['post_time'] = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d')
                    else:
                        item['post_time'] = datetime.now().strftime('%Y-%m-%d')

            # 添加新數據
            existing_data.extend(new_url_entries)

            # 寫回文件
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)

            print(f"🔄 PTT: 當前頁面新增 {len(new_urls)} 個連結（已添加post_time字段）")
        
        # 重新讀取總數統計
        all_data = read_json_file(json_filename, [])
        total_count = len(all_data)
        return len(href_list), json_filename
    except Exception as e:
        print(f"提取連結時發生錯誤: {e}")
        return 0, None

def fetch_ptt_links(driver, search_term):
    """收集PTT搜尋結果的URL連結"""
    total_links = 0
    page_count = 0
    json_filename = None

    # 首先執行搜尋
    print(f"🔍 開始搜尋PTT關鍵字: {search_term}")
    try:
        # 導航到PTT並執行搜尋
        driver.get("https://www.ptt.cc/bbs/Gossiping/index.html")
        print("📄 開啟PTT頁面，等待載入...")
        time.sleep(10)

        # 處理年齡確認
        if not handle_age_verification(driver):
            print("❌ 年齡確認失敗，無法繼續搜尋")
            return 0

        # 等待搜尋框出現並執行搜尋
        search_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "input.query"))
        )
        search_input.clear()
        search_input.send_keys(search_term)
        search_input.send_keys(Keys.RETURN)

        print(f"✅ 搜尋完成，開始收集連結...")
        time.sleep(3)

    except Exception as e:
        print(f"❌ PTT搜尋初始化失敗: {e}")
        return 0

    # 超時控制
    start_time = time.time()
    max_total_time = 300  # 5分鐘超時
    max_pages = 50  # 最大翻頁數

    while True:
        # 檢查超時
        if time.time() - start_time > max_total_time:
            print(f"⏰ PTT搜尋超時（{max_total_time}秒），結束搜尋")
            break
        
        # 檢查最大頁數
        if page_count >= max_pages:
            print(f"📄 PTT達到最大頁數限制（{max_pages}頁），結束搜尋")
            break
            
        links_found, current_filename = extract_links(driver, search_term)
        if current_filename:
            json_filename = current_filename
        total_links += links_found
        page_count += 1
        
        print(f"📄 PTT: 第{page_count}頁，本頁{links_found}個連結，總計{total_links}個")
        
        if not btn_next(driver):
            print("📄 PTT: 無更多頁面，搜尋完成")
            break
        time.sleep(random.uniform(1, 2))
    return total_links, json_filename

def read_urls_from_json(json_filename):
    """
    讀取 JSON 檔案中的 URL 列表，支援新舊格式轉換
    
    參數:
        json_filename: JSON 檔案路徑
        
    返回:
        去重後的 URL 列表
    """
    # 1. 使用新的JSON工具函數讀取主檔案
    urls = []
    data = read_json_file(json_filename, [])
    
    # 處理不同的資料格式
    for item in data:
        if isinstance(item, dict):
            if "url" in item:
                urls.append(item["url"])
            else:
                # 可能是舊格式的字典，嘗試提取URL
                for key, value in item.items():
                    if isinstance(value, str) and "ptt.cc" in value:
                        urls.append(value)
        elif isinstance(item, str):
            urls.append(item)
    
    print(f"📚 從主檔案讀取 {len(urls)} 個 URL")
    
    # 2. 檢查是否有舊格式的檔案（含日期）
    dirname = os.path.dirname(json_filename)
    basename = os.path.basename(json_filename)
    
    # 新格式檔名是立委.json，舊格式是 ptt_name_href.json 或 ptt_name_YYYYMMDD_href.json
    if basename.endswith('.json'):
        import glob
        
        # 提取出立委名稱（新格式）
        name = basename.replace('.json', '')
        
        # 搜尋所有包含此立委的舊檔案格式
        old_patterns = [
            f"{dirname}/ptt_{name}_href.json",
            f"{dirname}/ptt_{name}_*_href.json"
        ]
        
        # 讀取所有舊檔案的內容
        for pattern in old_patterns:
            old_files = glob.glob(pattern)
            for old_file in old_files:
                if old_file != json_filename:  # 避免重複讀取
                    try:
                        old_data = read_json_file(old_file, [])
                        old_urls = []
                        for item in old_data:
                            if isinstance(item, str):
                                old_urls.append(item)
                            elif isinstance(item, dict) and "url" in item:
                                old_urls.append(item["url"])
                        
                        if old_urls:
                            print(f"🔄 已從舊檔案 {os.path.basename(old_file)} 讀取 {len(old_urls)} 個 URL")
                            urls.extend(old_urls)
                    except Exception as e:
                        print(f"⚠️ 讀取舊檔案 {old_file} 時發生錯誤: {e}")
    
    # 去重並返回
    unique_urls = list(set(urls))
    print(f"📊 PTT: 總共讀取到 {len(unique_urls)} 個唯一 URL")
    return unique_urls

def scrape_ptt_comments_with_title(driver, url):
    driver.get(url)
    print(f"開啟文章頁面 {url}，等待 60 秒...")
    time.sleep(1.5)  # 增加等待時間為 60 秒
    
    # 處理年齡確認並檢查是否成功
    age_verified = handle_age_verification(driver)
    if not age_verified:
        print(f"警告: 年齡確認處理失敗，嘗試繼續爬取 {url}")
        # 再等待 30 秒，給網頁更多載入時間
        time.sleep(1.5)
        print("額外等待 30 秒後繼續")
        # 即使年齡確認失敗，也嘗試繼續爬取
    else:
        print(f"年齡確認處理成功，繼續爬取 {url}")
        # 成功後再等待 10 秒確保頁面完全載入
        time.sleep(1)
    
    data_list = []
    
    # 從 URL 中提取 PTT 文章的唯一識別碼
    ptt_post_id = None
    url_match = re.search(r'M\.(\d+)\.A\.([A-Za-z0-9]+)\.html', url)
    if url_match:
        ptt_post_id = f"{url_match.group(1)}.{url_match.group(2)}"
    
    try:
        # 1. 首先嘗試從文章資訊中獲取年份和發文日期
        meta_elements = driver.find_elements(By.CSS_SELECTOR, ".article-meta-value")
        title = None
        post_year = None
        post_date = None
        author = None
        
        for i, meta_element in enumerate(meta_elements):
            text = meta_element.text
            # 找作者 (通常在第一個 meta-value)
            if i == 0:
                author = text
                
            # 找標題 (通常在第二個 meta-value)
            if i == 1 or any(tag in text for tag in ['[問卦]', '新聞', '討論', '爆卦']):
                title = text
            
            # 找發文時間 (通常在第四個 meta-value)
            if i == 3:  # 第四個元素通常是時間
                # 嘗試從完整日期中提取年份和月份
                full_date_match = re.search(r'(\w{3})\s+(\d+)\s+(\d+):(\d+):(\d+)\s+(\d{4})', text)
                if full_date_match:
                    post_year = int(full_date_match.group(6))
                    # 將月份名稱轉換為數字 (例如：Jun -> 6)
                    month_name = full_date_match.group(1)
                    month_map = {'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6, 
                                'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12}
                    post_month = month_map.get(month_name, 0)
                    post_day = int(full_date_match.group(2))
                    post_hour = int(full_date_match.group(3))
                    post_minute = int(full_date_match.group(4))
                    post_second = int(full_date_match.group(5))
                    
                    try:
                        post_date = datetime(post_year, post_month, post_day, post_hour, post_minute, post_second)
                    except ValueError:
                        # 日期無效，使用當前日期
                        post_date = datetime.now()
                else:
                    # 如果沒有完整日期，嘗試只提取年份
                    year_match = re.search(r'\b\d{4}\b', text)
                    if year_match:
                        post_year = int(year_match.group())
        
        # 取得當前日期用於驗證
        current_date = datetime.now()
        current_year = current_date.year
        
        # 如果找不到年份，使用當前年份
        if not post_year:
            post_year = current_year
            
        # 如果找不到貼文日期，設為當年1月1日
        if not post_date:
            post_date = datetime(post_year, 1, 1)
        
        # 3. 處理推文 - 移除發信站過濾邏輯
        push_elements = driver.find_elements(By.CSS_SELECTOR, ".push")
        for push_element in push_elements:
            user_elements = push_element.find_elements(By.CSS_SELECTOR, ".f3.hl.push-userid")
            context_elements = push_element.find_elements(By.CSS_SELECTOR, ".f3.push-content")
            time_elements = push_element.find_elements(By.CSS_SELECTOR, ".push-ipdatetime")
            tag_elements = push_element.find_elements(By.CSS_SELECTOR, ".hl.push-tag")
            
            for tag_element, user_element, context_element, time_element in zip(
                tag_elements, user_elements, context_elements, time_elements
            ):
                tag_text = tag_element.text
                user_text = user_element.text
                content_text = context_element.text
                time_text = time_element.text
                
                # 4. 解析日期 - 格式通常是 "IP 月/日 時:分"
                date_match = re.search(r'(\d{1,2})/(\d{1,2})', time_text)
                time_match = re.search(r'(\d{1,2}):(\d{1,2})', time_text)
                
                comment_date = None
                comment_timestamp = None
                if date_match:
                    month, day = int(date_match.group(1)), int(date_match.group(2))
                    
                    # 直接過濾掉月份大於貼文時間的留言
                    if post_date and month > post_date.month:
                        print(f"跳過月份大於貼文時間的留言: {month}/{day}, 用戶: {user_text}, 貼文月份: {post_date.month}")
                        continue
                    
                    # 檢查日期是否合法
                    try:
                        # 先假設使用文章發布年份
                        hour, minute = 0, 0
                        if time_match:
                            hour = int(time_match.group(1))
                            minute = int(time_match.group(2))
                            
                        proposed_date = datetime(post_year, month, day, hour, minute)
                        
                        # 處理年份邊界問題
                        # 如果月份是12月且當前月份是1月，可能是去年的推文
                        if month == 12 and current_date.month == 1:
                            proposed_date = datetime(post_year-1, month, day, hour, minute)
                        # 如果月份是1月且當前月份是12月，可能是明年的推文
                        elif month == 1 and current_date.month == 12:
                            proposed_date = datetime(post_year+1, month, day, hour, minute)
                        
                        # 檢查日期是否在未來
                        if proposed_date > current_date:
                            # 日期在未來，直接跳過
                            print(f"跳過未來日期留言: {month}/{day}, 用戶: {user_text}")
                            continue
                        
                        comment_date = proposed_date
                        comment_timestamp = comment_date.timestamp()
                        date = f"{proposed_date.year}/{month:02d}/{day:02d} {hour:02d}:{minute:02d}"
                    except ValueError:
                        # 無效日期（如2月30日），跳過
                        print(f"跳過無效日期留言: {month}/{day}, 用戶: {user_text}")
                        continue
                else:
                    # 無法解析日期，使用空字串
                    date = ""
                
                # 5. 提取IP地址
                ip_address = time_text.split()[0] if time_text.split() else ""
                
                # 6. 生成留言的唯一 ID
                comment_id = None
                if ptt_post_id and user_text and comment_date:
                    # 以 "文章ID-用戶-留言時間" 為留言唯一識別碼
                    comment_id = f"{ptt_post_id}-{user_text}-{comment_date.strftime('%Y%m%d%H%M')}"
                
                # 7. 添加到數據列表
                if tag_text == "→ " and data_list:
                    data_list[-1].setdefault("回覆", []).append({
                        "用戶": user_text,
                        "留言": content_text,
                        "日期": date,
                        "IP地址": ip_address,
                        "ptt_comment_id": comment_id,
                        "timestamp": comment_timestamp
                    })
                else:
                    data_list.append({
                        "標籤": tag_text,
                        "用戶": user_text,
                        "標題": title,
                        "留言": content_text,
                        "日期": date,
                        "IP地址": ip_address,
                        "ptt_post_id": ptt_post_id,
                        "ptt_comment_id": comment_id,
                        "timestamp": comment_timestamp,
                        "post_date": post_date.strftime("%Y-%m-%d %H:%M:%S") if post_date else None,
                        "author": author,
                        "url": url,
                        "回覆": []
                    })
        return data_list
    except Exception as e:
        print(f"抓取留言時發生錯誤: {e}")
        traceback.print_exc()
        return []
    
# 全域變數和鎖定以保證線程安全
url_queue = queue.Queue()
processed_urls = set()
url_lock = Lock()
data_lock = Lock()
combined_data = []

def worker_function(worker_id):
    """
    工作執行緒函數，負責從佈局中獲取 URL 並處理
    
    參數:
        worker_id: 工作執行緒 ID
    """
    thread_driver = None
    max_retries = 3
    retry_count = 0
    
    # 嘗試建立瀏覽器驅動，最多重試3次
    while thread_driver is None and retry_count < max_retries:
        try:
            thread_driver = setup_driver(headless=True, thread_id=worker_id)
            print(f"工作執行緒 {worker_id} 成功啟動瀏覽器 (headless模式)")
        except Exception as e:
            retry_count += 1
            print(f"工作執行緒 {worker_id} 啟動瀏覽器失敗 (嘗試 {retry_count}/{max_retries}): {e}")
            time.sleep(3 )  # 隨著重試次數增加等待時間
    
    # 如果無法建立瀏覽器驅動，退出執行緒
    if thread_driver is None:
        print(f"工作執行緒 {worker_id} 無法啟動瀏覽器，放棄執行")
        return
    
    count = 0
    urls_processed = 0
    start_time = time.time()
    
    try:
        while True:
            try:
                # 嘗試從佈局中獲取 URL，超時時間5秒
                url = url_queue.get(timeout=5)  # 設置5秒超時，避免永久阻塞
            except queue.Empty:
                # 佈局為空，執行緒退出
                print(f"工作執行緒 {worker_id} 佈局為空，退出")
                break
                
            if url is None:
                url_queue.task_done()
                print(f"工作執行緒 {worker_id} 收到退出信號")
                break
                
            with url_lock:
                # 檢查 URL 是否已經處理過
                if url in processed_urls:
                    url_queue.task_done()
                    continue
                processed_urls.add(url)
                
            # 處理 URL
            try:
                print(f"工作執行緒 {worker_id} 正在處理 URL: {url}")
                
                # 每處理10個URL或每30分鐘重啟瀏覽器一次，防止記憶體洩漏
                urls_processed += 1
                current_time = time.time()
                if urls_processed % 10 == 0 or (current_time - start_time) > 1800:  # 30分鐘 = 1800秒
                    try:
                        print(f"工作執行緒 {worker_id} 已處理 {urls_processed} 個URL，重啟瀏覽器...")
                        if thread_driver:
                            thread_driver.quit()
                        thread_driver = setup_driver(headless=True, thread_id=worker_id)
                        start_time = time.time()  # 重置計時器
                    except Exception as restart_e:
                        print(f"工作執行緒 {worker_id} 重啟瀏覽器失敗: {restart_e}")
                
                # 如果重啟後瀏覽器為空，重試建立
                if thread_driver is None:
                    print(f"工作執行緒 {worker_id} 瀏覽器為空，嘗試重新建立...")
                    try:
                        thread_driver = setup_driver(headless=True, thread_id=worker_id)
                    except Exception as init_e:
                        print(f"工作執行緒 {worker_id} 重新建立瀏覽器失敗: {init_e}")
                        url_queue.task_done()
                        continue
                
                # 清除瀏覽器狀態，避免之前頁面的影響
                try:
                    thread_driver.execute_script("window.localStorage.clear();")
                    thread_driver.execute_script("window.sessionStorage.clear();")
                    thread_driver.delete_all_cookies()
                except Exception as clear_e:
                    print(f"工作執行緒 {worker_id} 清除瀏覽器狀態失敗: {clear_e}")
                
                # 使用重試機制處理URL
                data = None
                url_retries = 3
                for attempt in range(url_retries):
                    try:
                        data = scrape_ptt_comments_with_title(thread_driver, url)
                        if data:
                            break
                    except Exception as scrape_e:
                        print(f"工作執行緒 {worker_id} 處理URL第 {attempt+1} 次失敗: {scrape_e}")
                        if attempt < url_retries - 1:
                            time.sleep(1.5)  # 增加重試間隔時間為 60 秒
                            try:
                                # 嘗試重新載入頁面
                                thread_driver.get("about:blank")
                                print("加載空白頁，等待 60 秒...")
                                time.sleep(1.5)  # 增加等待時間為 60 秒
                            except:
                                pass
                                pass
                
                if data:
                    with data_lock:
                        combined_data.extend(data)
                    count += 1
                    print(f"工作執行緒 {worker_id} 已處理 {count} 個 URL")
                
                # 添加隨機延遲，避免請求過於頻繁
                delay_time = random.uniform(1, 5)  # 增加延遲時間為 30-60 秒
                print(f"工作執行緒 {worker_id} 處理完一個 URL，等待 {delay_time:.1f} 秒...")
                time.sleep(delay_time)  # 增加延遲時間，避免PTT反爬蟲機制
            except Exception as e:
                print(f"工作執行緒 {worker_id} 處理 URL 時發生錯誤: {e}")
                traceback.print_exc()
            finally:
                # 無論處理成功與否，都標記此任務為完成
                url_queue.task_done()
    except Exception as e:
        print(f"工作執行緒 {worker_id} 發生意外錯誤: {e}")
        traceback.print_exc()
    finally:
        # 確保關閉瀏覽器
        if thread_driver:
            print(f"工作執行緒 {worker_id} 關閉瀏覽器")
            try:
                thread_driver.quit()
            except Exception as close_e:
                print(f"工作執行緒 {worker_id} 關閉瀏覽器時出錯: {close_e}")
                # 如果正常關閉失敗，嘗試強制結束進程
                try:
                    import psutil
                    import os
                    current_process = psutil.Process(os.getpid())
                    children = current_process.children(recursive=True)
                    for child in children:
                        if "chromedriver" in child.name().lower() or "chrome" in child.name().lower():
                            child.terminate()
                except:
                    pass

def scrape_and_save_ptt_data(urls, search_term, output_dir=None, num_threads=4, existing_data=None):
    """
    多執行緒抓取 PTT 文章及留言，並儲存為 JSON 檔案
    
    參數:
        urls: 要抓取的 URL 列表
        search_term: 搜尋關鍵字
        output_dir: 輸出目錄
        num_threads: 執行緒數量
        existing_data: 現有的資料，用於合併
    
    返回:
        輸出文件的路徑
    """
    global url_queue, processed_urls, combined_data
    url_queue = queue.Queue()
    processed_urls = set()
    combined_data = []
    
    # 設定預設輸出目錄
    if output_dir is None:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(current_dir, "data", "ptt")
    
    os.makedirs(output_dir, exist_ok=True)
    output_filename = f"{search_term}.json"  # 統一命名為立委.json
    output_path = os.path.join(output_dir, output_filename)
    
    # 如果要處理的URL超過1000個，減少並行執行緒數量以避免資源耗盡
    adjusted_num_threads = num_threads
    if len(urls) > 1000:
        adjusted_num_threads = min(num_threads, 8)  # 對於大量URL，限制最多3個執行緒
        print(f"由於URL數量較多 ({len(urls)} 個)，調整執行緒數量為 {adjusted_num_threads}")
    elif len(urls) > 500:
        adjusted_num_threads = min(num_threads, 4)  # 中等數量URL，限制最多4個執行緒
        print(f"由於URL數量較多 ({len(urls)} 個)，調整執行緒數量為 {adjusted_num_threads}")
    elif len(urls) < 20:
        adjusted_num_threads = 1  # 少量URL使用單執行緒處理，避免多執行緒的開銷
        print(f"由於URL數量很少 ({len(urls)} 個)，使用單執行緒模式")
    
    # 添加URL到佇列
    for url in urls:
        url_queue.put(url)
        
    print(f"開始使用 {adjusted_num_threads} 個執行緒爬取 {len(urls)} 個URL...")
    
    # 使用分批啟動執行緒的方式，避免同時啟動過多瀏覽器造成資源競爭
    threads = []
    for i in range(adjusted_num_threads):
        thread = threading.Thread(target=worker_function, args=(i+1,))
        thread.daemon = True
        threads.append(thread)
        
        # 啟動執行緒並等待一段時間，避免同時啟動多個瀏覽器實例
        thread.start()
        print(f"已啟動執行緒 {i+1}/{adjusted_num_threads}")
        time.sleep(1)  # 等待60秒再啟動下一個執行緒
    
    # 監控爬蟲進度
    start_time = time.time()
    last_save_time = start_time
    total_urls = len(urls)
    save_interval = max(10, total_urls // 10)  # 每完成10%的URL或至少10個URL就保存一次
    
    try:
        while not url_queue.empty():
            remaining = url_queue.qsize()
            processed = total_urls - remaining
            progress = (processed / total_urls) * 100
            elapsed = time.time() - start_time
            speed = processed / elapsed if elapsed > 0 else 0
            
            print(f"進度: {processed}/{total_urls} ({progress:.1f}%), "
                  f"速度: {speed:.2f} URLs/秒, 已用時間: {elapsed:.1f}秒")
            
            # 每隔10分鐘或處理足夠多的URL後保存一次中間結果
            current_time = time.time()
            if (processed > 0 and processed % save_interval == 0) or (current_time - last_save_time > 600):
                temp_output_path = f"{output_path}.temp"
                try:
                    # 先保存到臨時文件，成功後再替換，避免寫入失敗導致數據丟失
                    with open(temp_output_path, "w", encoding="utf-8") as f:
                        json.dump(combined_data, f, ensure_ascii=False, indent=4)
                    
                    # 如果有現有數據，合併
                    if existing_data and isinstance(existing_data, list):
                        merged_data = existing_data.copy()
                        merged_data.extend(combined_data)
                        with open(output_path, "w", encoding="utf-8") as f:
                            json.dump(merged_data, f, ensure_ascii=False, indent=4)
                    else:
                        # 直接移動臨時文件
                        import shutil
                        shutil.move(temp_output_path, output_path)
                    
                    print(f"已保存中間結果，目前處理了 {len(combined_data)} 條記錄")
                    last_save_time = current_time
                except Exception as e:
                    print(f"保存中間結果時出錯: {e}")
                    traceback.print_exc()
            
            # 等待一段時間再檢查進度
            time.sleep(1)
        
        # 等待所有執行緒完成
        for t in threads:
            t.join()
    except KeyboardInterrupt:
        print("\n收到中斷信號，正在安全退出...")
    finally:
        # 使用新的JSON工具函數保存最終結果
        if combined_data:
            # 使用append模式保存新抓取的留言
            append_success = append_crawler_data("ptt", search_term, combined_data)
            if append_success:
                print(f"✅ PTT爬蟲完成，新增 {len(combined_data)} 條記錄")
            else:
                print(f"⚠️ 部分資料保存失敗")
        else:
            print(f"⚠️ 沒有新的留言資料")
        
    return output_path

def filter_comments_by_legislator(comments, legislator_name):
    """
    過濾與目標立委相關的留言

    Args:
        comments: 留言列表
        legislator_name: 立委名稱

    Returns:
        過濾後的留言列表
    """
    if not comments:
        return []

    filtered_comments = []

    for comment in comments:
        # 檢查主文章標題是否包含立委名稱
        title = comment.get('標題', '')
        content = comment.get('留言', '')

        # 檢查主文章內容或標題是否包含立委名稱
        if legislator_name in title or legislator_name in content:
            filtered_comments.append(comment)
            continue

        # 檢查回覆中是否有提到立委名稱
        replies = comment.get('回覆', [])
        has_related_reply = False

        for reply in replies:
            reply_content = reply.get('留言', '')
            if legislator_name in reply_content:
                has_related_reply = True
                break

        if has_related_reply:
            filtered_comments.append(comment)

    return filtered_comments

def crawl_ptt(name, num_threads=5, last_crawled_time=None, headless=True):
    """
    爬取 PTT 特定關鍵字的文章和留言，支援增量式更新
    
    參數:
        name: 要搜尋的關鍵字
        num_threads: 使用的執行緒數量
        last_crawled_time: 上次爬取的時間點 (datetime 物件)，如果提供，只爬取此時間後的文章
        headless: 是否使用無頭模式 (預設為True)
    
    返回:
        (url_json_file, data_json_file): URL 檔案路徑和資料檔案路徑
    """
    href_dir = "href/ptt"
    output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "ptt")
    url_json_file = f"{href_dir}/{name}.json"  # 統一命名為立委.json
    data_json_file = f"{output_dir}/{name}.json"  # 統一命名為立委.json
    
    # 讀取已存在的資料，用於檢查重複和增量更新
    existing_data = []
    processed_post_ids = set()
    
    if os.path.exists(data_json_file):
        try:
            with open(data_json_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                # 提取已處理的貼文 ID
                for item in existing_data:
                    if isinstance(item, dict) and "ptt_post_id" in item and item["ptt_post_id"]:
                        processed_post_ids.add(item["ptt_post_id"])
            print(f"已從 {data_json_file} 中讀取 {len(existing_data)} 條記錄，{len(processed_post_ids)} 個唯一貼文 ID")
        except Exception as e:
            print(f"讀取現有資料時發生錯誤: {e}")
    
    # 轉換 last_crawled_time 為時間戳記
    last_timestamp = None
    if last_crawled_time:
        if isinstance(last_crawled_time, str):
            try:
                # 嘗試多種常見的日期格式
                for date_format in ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y', '%Y%m%d']:
                    try:
                        parsed_date = datetime.strptime(last_crawled_time, date_format)
                        last_timestamp = parsed_date.timestamp()
                        print(f"成功解析日期: {last_crawled_time} -> {parsed_date.strftime('%Y-%m-%d')}")
                        break
                    except ValueError:
                        continue
                
                # 如果以上格式都不符合，嘗試 ISO 格式
                if last_timestamp is None:
                    parsed_date = datetime.fromisoformat(last_crawled_time)
                    last_timestamp = parsed_date.timestamp()
                    print(f"使用 ISO 格式解析日期: {parsed_date.strftime('%Y-%m-%d')}")
            except Exception as e:
                print(f"無法解析日期格式: {last_crawled_time}，錯誤: {e}")
                print("將不使用日期篩選")
        elif isinstance(last_crawled_time, datetime):
            last_timestamp = last_crawled_time.timestamp()
            print(f"使用提供的 datetime 物件作為日期篩選: {last_crawled_time.strftime('%Y-%m-%d')}")
    
    if last_timestamp:
        print(f"將篩選時間戳記 {last_timestamp} ({datetime.fromtimestamp(last_timestamp).strftime('%Y-%m-%d')}) 之後的文章")
    else:
        print("未指定日期篩選，將爬取所有文章")
    
    # 計算一個月前的時間戳記，用於判斷是否需要重新檢查
    one_month_ago = (datetime.now() - timedelta(days=30)).timestamp()
    
    # 建立主瀏覽器實例
    print(f"為 '{name}' 爬蟲建立主瀏覽器 (headless={headless})")
    main_driver = setup_driver(headless=headless)
    try:
        if not search_article(main_driver, name):
            return url_json_file, data_json_file
            
        _, json_filename = fetch_ptt_links(main_driver, name)
        if json_filename:
            url_json_file = json_filename
            
        all_urls = read_urls_from_json(url_json_file)
        if not all_urls:
            return url_json_file, data_json_file
        
        # 過濾需要處理的 URL
        filtered_urls = []
        print(f"檢查 {len(all_urls)} 個文章URL...")
        
        # 第一步：先讀取所有URL，稍後再根據日期過濾
        urls_to_check = []
        for url in all_urls:
            url_match = re.search(r'M\.(\d+)\.A\.([A-Za-z0-9]+)\.html', url)
            if url_match:
                post_id = f"{url_match.group(1)}.{url_match.group(2)}"
                # 如果沒有處理過此貼文，或是貼文在一個月內且需要重新檢查
                if post_id not in processed_post_ids:
                    urls_to_check.append((url, post_id))
        
        print(f"找到 {len(urls_to_check)} 個未處理過的URL，準備檢查日期...")
        
        # 如果指定了日期篩選，需要額外爬取文章頁面獲取發布時間
        if last_timestamp and urls_to_check:
            print(f"需要檢查 {len(urls_to_check)} 個URL的發布時間，建立日期檢查瀏覽器")
            
            # 使用分批處理來減少瀏覽器實例的生命週期
            batch_size = 10  # 減少每批處理的URL數量，從20減為10
            total_batches = (len(urls_to_check) + batch_size - 1) // batch_size  # 向上取整
            
            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, len(urls_to_check))
                current_batch = urls_to_check[start_idx:end_idx]
                
                print(f"處理批次 {batch_idx+1}/{total_batches}，共 {len(current_batch)} 個URL")
                
                date_check_driver = None
                retry_count = 0
                max_retries = 3
                
                # 為每個批次建立新的WebDriver實例
                while date_check_driver is None and retry_count < max_retries:
                    try:
                        date_check_driver = setup_driver(headless=True, thread_id="date_check")
                        print(f"成功建立批次 {batch_idx+1} 的日期檢查瀏覽器 (headless模式)")
                    except Exception as e:
                        retry_count += 1
                        print(f"建立日期檢查瀏覽器失敗 (嘗試 {retry_count}/{max_retries}): {e}")
                        time.sleep(3 )
                
                if date_check_driver is None:
                    print(f"無法為批次 {batch_idx+1} 建立瀏覽器，跳過此批URL")
                    # 如果無法建立瀏覽器，預設將此批URL全部加入處理列表
                    for url, _ in current_batch:
                        filtered_urls.append(url)
                    continue
                
                try:
                    # 設置頁面載入超時
                    date_check_driver.set_page_load_timeout(20)
                    
                    # 處理此批次的URL
                    for url_index, (url, post_id) in enumerate(current_batch):
                        try:
                            print(f"檢查URL {url_index+1}/{len(current_batch)}: {url}")
                            
                            # 每檢查10個URL，清除一次瀏覽器狀態
                            if url_index > 0 and url_index % 10 == 0:
                                try:
                                    date_check_driver.execute_script("window.localStorage.clear();")
                                    date_check_driver.execute_script("window.sessionStorage.clear();")
                                    date_check_driver.delete_all_cookies()
                                except Exception as clear_e:
                                    print(f"清除瀏覽器狀態失敗: {clear_e}")
                            
                            # 使用重試機制加載頁面
                            load_success = False
                            for load_attempt in range(3):
                                try:
                                    date_check_driver.get(url)
                                    print(f"檢查日期頁面載入 {url}，等待 60 秒...")
                                    time.sleep(1)  # 增加等待時間為 60 秒
                                    
                                    # 處理年齡確認
                                    age_verified = handle_age_verification(date_check_driver)
                                    if not age_verified:
                                        print(f"警告: 日期檢查時年齡確認處理失敗，嘗試繼續檢查 {url}")
                                    else:
                                        print(f"日期檢查時年齡確認處理成功，繼續檢查 {url}")
                                    
                                    # 等待文章元素出現
                                    WebDriverWait(date_check_driver, 60).until(  # 增加等待時間至 60 秒
                                        EC.presence_of_element_located((By.CLASS_NAME, "article-metaline"))
                                    )
                                    load_success = True
                                    break
                                except Exception as load_e:
                                    if load_attempt < 2:
                                        print(f"載入頁面失敗，重試第 {load_attempt+2} 次: {load_e}")
                                        time.sleep(1)  # 增加等待時間為 60 秒
                                        try:
                                            date_check_driver.get("about:blank")  # 加載空白頁，重置狀態
                                            print("加載空白頁，等待 60 秒...")
                                            time.sleep(1)  # 增加等待時間為 60 秒
                                        except:
                                            pass
                                    else:
                                        print(f"無法載入頁面 {url}: {load_e}")
                            
                            if not load_success:
                                # 載入失敗，預設處理此URL
                                print(f"無法載入 URL {url}，將預設處理")
                                filtered_urls.append(url)
                                continue
                            
                            # 獲取文章發布時間
                            date_elements = date_check_driver.find_elements(By.CSS_SELECTOR, ".article-metaline")
                            post_time = None
                            
                            for element in date_elements:
                                tag = element.find_element(By.CSS_SELECTOR, ".article-meta-tag")
                                if tag.text == "時間":
                                    value = element.find_element(By.CSS_SELECTOR, ".article-meta-value")
                                    post_time_str = value.text
                                    try:
                                        # 解析 PTT 時間格式 (如: Sat Jul 1 00:00:00 2023)
                                        post_time = datetime.strptime(post_time_str, "%a %b %d %H:%M:%S %Y")
                                        break
                                    except ValueError:
                                        try:
                                            # 嘗試更寬鬆的格式解析
                                            post_time = parse_ptt_time(post_time_str)
                                        except:
                                            post_time = None
                            
                            if post_time:
                                post_timestamp = post_time.timestamp()
                                # 判斷文章是否在篩選日期之後
                                if post_timestamp >= last_timestamp:
                                    print(f"URL {url} 發布於 {post_time.strftime('%Y-%m-%d')}，符合篩選條件")
                                    filtered_urls.append(url)
                                else:
                                    print(f"URL {url} 發布於 {post_time.strftime('%Y-%m-%d')}，早於篩選日期，跳過")
                            else:
                                # 如果無法獲取時間，默認處理此URL
                                print(f"無法獲取 URL {url} 的發布時間，將預設處理")
                                filtered_urls.append(url)
                            
                            # 添加適當延遲，避免請求過於頻繁
                            delay_time = random.uniform(1, 5)  # 增加延遲時間為 30-60 秒
                            print(f"URL 處理完成，等待 {delay_time:.1f} 秒...")
                            time.sleep(delay_time)  # 增加延遲時間，避免觸發反爬蟲
                            
                        except Exception as e:
                            print(f"檢查 URL {url} 時出錯: {e}")
                            # 如果出錯，也預設處理此URL
                            filtered_urls.append(url)
                
                finally:
                    # 完成此批次後，關閉瀏覽器
                    try:
                        if date_check_driver:
                            date_check_driver.quit()
                            print(f"批次 {batch_idx+1} 處理完成，已關閉瀏覽器")
                    except Exception as e:
                        print(f"關閉批次 {batch_idx+1} 瀏覽器時出錯: {e}")
                
                # 批次之間添加一些延遲，讓系統有時間釋放資源
                if batch_idx < total_batches - 1:
                    delay_time = random.uniform(1, 5)  # 增加批次間隔時間為 60-120 秒
                    print(f"等待 {delay_time:.1f} 秒後處理下一批...")
                    time.sleep(delay_time)
            
            # 確保關閉主瀏覽器
            if main_driver:
                try:
                    main_driver.quit()
                    print("主瀏覽器已關閉")
                except Exception as e:
                    print(f"關閉主瀏覽器時出錯: {e}")
        else:
            # 如果沒有指定日期篩選，則處理所有未處理過的URL
            filtered_urls = [url for url, _ in urls_to_check]
        
        if filtered_urls:
            # 處理新的 URL 並與現有資料合併
            output_file = scrape_and_save_ptt_data(filtered_urls, name, output_dir, num_threads, existing_data if len(processed_post_ids) > 0 else None)
            return url_json_file, output_file
        else:
            print(f"沒有新的 URL 需要處理，保留現有資料 {data_json_file}")
            return url_json_file, data_json_file
    except Exception as e:
        print(f"發生錯誤: {e}")
        traceback.print_exc()
        return url_json_file, data_json_file
    finally:
        main_driver.quit()

def crawl_ptt_with_pool(name, webdriver_pool, last_crawled_time=None, max_threads=3):
    """
    使用 WebDriver 池爬取 PTT 資料，支援多線程優化
    
    參數:
        name: 要搜尋的關鍵字
        webdriver_pool: WebDriver 池實例
        last_crawled_time: 上次爬取的時間點
        max_threads: 最大線程數
    """
    import threading
    from concurrent.futures import ThreadPoolExecutor, as_completed
    
    print(f"🚀 使用 WebDriver 池爬取 PTT: {name}")
    
    # 設定輸出目錄
    current_dir = os.path.dirname(os.path.abspath(__file__))
    href_dir = os.path.join(current_dir, "href", "ptt")
    output_dir = os.path.join(current_dir, "data", "ptt")
    os.makedirs(href_dir, exist_ok=True)
    os.makedirs(output_dir, exist_ok=True)
    
    url_json_file = f"{href_dir}/{name}.json"  # 統一命名為立委.json
    data_json_file = f"{output_dir}/{name}.json"  # 統一命名為立委.json
    
    # 第一步：搜尋文章 URL
    try:
        with webdriver_pool.get_driver() as driver:
            print(f"🔍 搜尋 {name} 的 PTT 文章...")
            
            # 搜尋文章
            if not search_article(driver, name):
                print("❌ 搜尋文章失敗")
                return None, None
                
            # 獲取文章連結
            _, json_filename = fetch_ptt_links(driver, name)
            if json_filename:
                url_json_file = json_filename
            
            # 讀取所有 URL
            all_urls = read_urls_from_json(url_json_file)
            if not all_urls:
                print("⚠️  沒有找到任何文章URL")
                return url_json_file, None
            
            print(f"✅ 找到 {len(all_urls)} 個文章URL")
            
    except Exception as e:
        print(f"❌ 搜尋文章時出錯: {e}")
        return None, None
    
    # 讀取已存在的資料
    existing_data = []
    processed_post_ids = set()
    
    if os.path.exists(data_json_file):
        try:
            with open(data_json_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                for item in existing_data:
                    if isinstance(item, dict) and "ptt_post_id" in item and item["ptt_post_id"]:
                        processed_post_ids.add(item["ptt_post_id"])
            print(f"📚 已讀取 {len(existing_data)} 條現有記錄")
        except Exception as e:
            print(f"⚠️  讀取現有資料時出錯: {e}")
    
    # 過濾需要處理的 URL
    urls_to_process = []
    for url in all_urls:
        url_match = re.search(r'M\.(\d+)\.A\.([A-Za-z0-9]+)\.html', url)
        if url_match:
            post_id = f"{url_match.group(1)}.{url_match.group(2)}"
            if post_id not in processed_post_ids:
                urls_to_process.append(url)
    
    # 日期篩選
    if last_crawled_time and urls_to_process:
        print(f"🗓️  執行日期篩選...")
        filtered_urls = []
        
        # 處理 last_crawled_time 參數
        cutoff_timestamp = None
        if isinstance(last_crawled_time, str):
            try:
                cutoff_date = datetime.strptime(last_crawled_time, '%Y-%m-%d')
                cutoff_timestamp = cutoff_date.timestamp()
            except:
                cutoff_timestamp = None
        elif hasattr(last_crawled_time, 'timestamp'):
            cutoff_timestamp = last_crawled_time.timestamp()
        
        if cutoff_timestamp:
            print(f"只處理 {datetime.fromtimestamp(cutoff_timestamp).strftime('%Y-%m-%d')} 之後的文章")
            
            # 使用池中的driver檢查文章日期
            def check_article_date(url):
                try:
                    with webdriver_pool.get_driver() as check_driver:
                        check_driver.get(url)
                        time.sleep(1)
                        
                        # 處理年齡確認
                        handle_age_verification(check_driver)
                        
                        # 獲取文章時間
                        date_elements = check_driver.find_elements(By.CSS_SELECTOR, ".article-metaline")
                        for element in date_elements:
                            tag = element.find_element(By.CSS_SELECTOR, ".article-meta-tag")
                            if tag.text == "時間":
                                value = element.find_element(By.CSS_SELECTOR, ".article-meta-value")
                                post_time_str = value.text
                                try:
                                    post_time = datetime.strptime(post_time_str, "%a %b %d %H:%M:%S %Y")
                                    if post_time.timestamp() >= cutoff_timestamp:
                                        return url
                                    else:
                                        return None
                                except:
                                    return url  # 解析失敗時預設包含
                                break
                        return url  # 找不到時間時預設包含
                except:
                    return url  # 檢查失敗時預設包含
            
            # 並行檢查文章日期（批次處理以避免資源衝突）
            batch_size = min(20, max_threads * 2)  # 動態批次大小
            total_urls = len(urls_to_process)
            print(f"將檢查 {total_urls} 個URL的發布時間，批次大小: {batch_size}")
            
            with ThreadPoolExecutor(max_workers=min(3, max_threads)) as date_executor:
                for i in range(0, total_urls, batch_size):
                    batch_urls = urls_to_process[i:i+batch_size]
                    print(f"檢查批次 {i//batch_size + 1}/{(total_urls + batch_size - 1)//batch_size}: {len(batch_urls)} 個URL")
                    
                    future_to_url = {
                        date_executor.submit(check_article_date, url): url 
                        for url in batch_urls
                    }
                    
                    for future in as_completed(future_to_url):
                        result = future.result()
                        if result:
                            filtered_urls.append(result)
            
            urls_to_process = filtered_urls
            print(f"📅 日期篩選後剩餘 {len(urls_to_process)} 個URL")
    
    if not urls_to_process:
        print("⚠️  沒有需要處理的文章")
        return url_json_file, data_json_file
    
    # 第二步：並行爬取文章內容
    all_comments = []
    successful_articles = 0

    def crawl_single_article(url, target_name=name):
        """爬取單篇文章的留言，並過濾與目標立委相關的內容"""
        try:
            with webdriver_pool.get_driver() as article_driver:
                print(f"📰 爬取文章: {url}")
                comments = scrape_ptt_comments_with_title(article_driver, url)
                if comments:
                    # 過濾與目標立委相關的內容
                    filtered_comments = filter_comments_by_legislator(comments, target_name)
                    if filtered_comments:
                        print(f"✅ 文章爬取完成，收集到 {len(filtered_comments)} 條與 {target_name} 相關的留言")
                        return filtered_comments
                    else:
                        print(f"⚠️  文章中沒有與 {target_name} 相關的留言: {url}")
                        return []
                else:
                    print(f"⚠️  文章無留言: {url}")
                    return []
        except Exception as e:
            print(f"❌ 爬取文章時出錯: {e}")
            return []
    
    # 使用線程池並行處理
    print(f"🔄 使用 {max_threads} 個線程並行爬取 {len(urls_to_process)} 篇文章...")
    
    with ThreadPoolExecutor(max_workers=max_threads) as executor:
        future_to_url = {
            executor.submit(crawl_single_article, url): url
            for url in urls_to_process[:100]  # 限制處理數量
        }
        
        for future in as_completed(future_to_url):
            url = future_to_url[future]
            try:
                comments = future.result()
                if comments:
                    all_comments.extend(comments)
                    successful_articles += 1
            except Exception as e:
                print(f"❌ 線程執行失敗: {e}")
    
    # 第三步：合併並保存結果
    if all_comments or existing_data:
        # 合併新舊資料
        final_data = existing_data.copy() if existing_data else []
        final_data.extend(all_comments)
        
        # 保存結果
        with open(data_json_file, 'w', encoding='utf-8') as f:
            json.dump(final_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ PTT 爬取完成")
        print(f"   處理文章數: {successful_articles}/{len(urls_to_process)}")
        print(f"   新增留言數: {len(all_comments)}")
        print(f"   總記錄數: {len(final_data)}")
        print(f"   輸出檔案: {data_json_file}")
        
        return {
            'success': True,
            'url_file': url_json_file,
            'data_file': data_json_file,
            'count': len(all_comments),
            'articles_processed': successful_articles,
            'total_articles': len(urls_to_process)
        }
    else:
        print("⚠️  沒有收集到任何留言")
        return {
            'success': False,
            'error': '沒有收集到任何留言',
            'url_file': url_json_file,
            'data_file': None
        }

def parse_ptt_time(time_str):
    """
    解析 PTT 的各種時間格式
    
    Args:
        time_str: PTT 時間字符串
        
    Returns:
        解析後的 datetime 對象
    """
    # 嘗試各種可能的格式
    formats = [
        "%a %b %d %H:%M:%S %Y",      # 標準 PTT 格式，如 Mon Jul 1 12:34:56 2023
        "%Y/%m/%d %H:%M:%S",         # 如 2023/07/01 12:34:56
        "%Y-%m-%d %H:%M:%S",         # 如 2023-07-01 12:34:56
        "%Y年%m月%d日 %H:%M:%S",      # 如 2023年07月01日 12:34:56
        "%m/%d/%Y %H:%M:%S",         # 如 07/01/2023 12:34:56
        "%d/%m/%Y %H:%M:%S",         # 如 01/07/2023 12:34:56
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(time_str, fmt)
        except ValueError:
            continue
    
    # 如果上述格式都不符合，嘗試只提取日期部分
    date_patterns = [
        r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})',  # 2023-07-01 或 2023/07/01
        r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})',  # 07-01-2023 或 07/01/2023
        r'(\d{4})年(\d{1,2})月(\d{1,2})日',     # 2023年07月01日
    ]
    
    for pattern in date_patterns:
        match = re.search(pattern, time_str)
        if match:
            groups = match.groups()
            if len(groups[0]) == 4:  # 年份在前
                year, month, day = int(groups[0]), int(groups[1]), int(groups[2])
            else:  # 年份在後
                month, day, year = int(groups[0]), int(groups[1]), int(groups[2])
            
            try:
                return datetime(year, month, day)
            except ValueError:
                continue
    
    # 最後嘗試從字符串中提取所有數字，假設格式為 年月日
    numbers = re.findall(r'\d+', time_str)
    if len(numbers) >= 3:
        try:
            if len(numbers[0]) == 4:  # 假設第一個是年份
                year, month, day = int(numbers[0]), int(numbers[1]), int(numbers[2])
            elif len(numbers[2]) == 4:  # 假設最後一個是年份
                month, day, year = int(numbers[0]), int(numbers[1]), int(numbers[2])
            else:
                raise ValueError("無法識別年份")
                
            return datetime(year, month, day)
        except (ValueError, IndexError):
            pass
    
    # 如果所有嘗試都失敗，拋出異常
    raise ValueError(f"無法解析時間字符串: {time_str}")

if __name__ == "__main__":
    # 使用headless模式執行爬蟲
    crawl_ptt('葉元之', num_threads=5, headless=True)