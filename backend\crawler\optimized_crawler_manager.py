#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
優化的爬蟲管理器
實現你要求的流程：
1. 快速收集所有平台URL（並行）
2. 逐平台多線程爬取內容（YouTube 4線程 -> PTT 4線程 -> Threads 4線程）
3. 合併資料到 process/alldata
4. 統計用戶資料到 user_data
5. Gemini情感分析到 final_data
6. 存儲到 MongoDB
7. 更新統計
"""

import os
import sys
import json
import time
import logging
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 導入爬蟲模組
from webdriver_pool import get_global_pool
from yt_crawler import search_youtube_videos, crawl_youtube_comments_with_pool
from ptt_crawler import crawl_ptt_with_pool
from thread_crawler import crawl_threads_with_pool

class OptimizedCrawlerManager:
    """優化的爬蟲管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.platforms = ['youtube', 'ptt', 'threads']
        
    def crawl_politician_optimized(self, politician_name: str, cutoff_date_str: str, 
                                 platforms: List[str] = None, max_workers_per_platform: int = 4) -> Dict:
        """
        優化的立委爬取流程
        
        Args:
            politician_name: 立委姓名
            cutoff_date_str: 截止日期
            platforms: 平台列表
            max_workers_per_platform: 每個平台的最大線程數
        
        Returns:
            Dict: 爬取結果
        """
        platforms = platforms or self.platforms
        
        self.logger.info(f"🚀 開始優化爬取流程: {politician_name}")
        self.logger.info(f"📅 截止日期: {cutoff_date_str}")
        self.logger.info(f"🌐 平台: {platforms}")
        self.logger.info(f"🧵 每平台線程數: {max_workers_per_platform}")
        
        result = {
            'politician_name': politician_name,
            'cutoff_date': cutoff_date_str,
            'platforms': platforms,
            'url_collection': {},
            'content_crawling': {},
            'summary': {
                'successful_platforms': 0,
                'total_platforms': len(platforms),
                'success_rate': 0
            }
        }
        
        # 階段1：快速並行收集所有平台URL
        self.logger.info("🔗 階段1：快速並行收集所有平台URL...")
        url_results = self._collect_urls_fast_parallel(politician_name, cutoff_date_str, platforms)
        result['url_collection'] = url_results
        
        # 階段2：逐平台多線程爬取內容
        self.logger.info(f"📊 階段2：逐平台多線程爬取內容（每平台 {max_workers_per_platform} 線程）...")
        content_results = self._crawl_content_sequential_optimized(
            politician_name, cutoff_date_str, platforms, max_workers_per_platform, url_results
        )
        result['content_crawling'] = content_results
        
        # 計算成功率
        successful_platforms = sum(1 for r in content_results.values() if r.get('success'))
        result['summary']['successful_platforms'] = successful_platforms
        result['summary']['success_rate'] = successful_platforms / len(platforms) if platforms else 0
        
        self.logger.info(f"✅ 爬取完成: {successful_platforms}/{len(platforms)} 平台成功")
        
        return result
    
    def _collect_urls_fast_parallel(self, politician_name: str, cutoff_date_str: str, platforms: List[str]) -> Dict:
        """快速並行收集URL"""
        url_results = {}
        
        # 創建小型WebDriver池用於URL收集
        url_pool = get_global_pool(max_instances=3, headless=True)
        
        with ThreadPoolExecutor(max_workers=3) as executor:
            future_to_platform = {}

            # 提交所有URL收集任務
            for platform in platforms:
                self.logger.info(f"📤 提交 {platform.upper()} URL收集任務...")
                future = executor.submit(self._collect_platform_urls_fast, platform, politician_name, cutoff_date_str, url_pool)
                future_to_platform[future] = platform

            # 等待所有URL收集任務完成
            self.logger.info("⏳ 等待所有平台URL收集完成...")
            for future in as_completed(future_to_platform):
                platform = future_to_platform[future]
                try:
                    result = future.result()
                    url_results[platform] = result
                    count = result.get('count', 0)
                    self.logger.info(f"✅ {platform.upper()} URL收集完成: {count} 個項目")
                except Exception as e:
                    self.logger.error(f"❌ {platform.upper()} URL收集失敗: {e}")
                    url_results[platform] = {'success': False, 'error': str(e)}

        # 確保所有URL收集任務都已完成
        self.logger.info("🎯 所有平台URL收集階段完成，準備進入內容爬取階段...")
        
        return url_results
    
    def _collect_platform_urls_fast(self, platform: str, politician_name: str, cutoff_date_str: str, webdriver_pool) -> Dict:
        """快速收集單個平台的URL"""
        try:
            if platform == 'youtube':
                self.logger.info(f"🎬 快速收集 YouTube URL: {politician_name}")
                output_dir = os.path.join(current_dir, 'href', 'youtube')
                url_file, video_data = search_youtube_videos(
                    name=politician_name,
                    output_dir=output_dir,
                    headless=True,
                    cutoff_date=cutoff_date_str
                )
                if video_data and len(video_data) > 0:
                    return {'success': True, 'url_file': url_file, 'count': len(video_data)}
                else:
                    return {'success': False, 'error': 'YouTube URL收集無結果'}
                    
            elif platform == 'ptt':
                self.logger.info(f"📝 快速收集 PTT URL: {politician_name}")
                # PTT快速收集：只檢查現有文件或做最小搜索
                href_dir = os.path.join(current_dir, 'href', 'ptt')
                os.makedirs(href_dir, exist_ok=True)
                href_file = os.path.join(href_dir, f'{politician_name}.json')
                
                if os.path.exists(href_file):
                    try:
                        with open(href_file, 'r', encoding='utf-8') as f:
                            href_data = json.load(f)
                        return {'success': True, 'url_file': href_file, 'count': len(href_data)}
                    except:
                        pass
                
                # 如果沒有現有文件，標記為需要在內容爬取階段處理
                return {'success': True, 'message': 'PTT 將在內容爬取階段搜尋', 'count': 0}
                
            elif platform == 'threads':
                self.logger.info(f"🧵 快速收集 Threads URL: {politician_name}")
                # Threads快速收集：嘗試快速搜索
                href_dir = os.path.join(current_dir, 'href', 'threads')
                os.makedirs(href_dir, exist_ok=True)
                href_file = os.path.join(href_dir, f'{politician_name}.json')

                # 先檢查現有文件
                if os.path.exists(href_file):
                    try:
                        with open(href_file, 'r', encoding='utf-8') as f:
                            href_data = json.load(f)
                        return {'success': True, 'url_file': href_file, 'count': len(href_data)}
                    except:
                        pass

                # 嘗試快速搜索Threads URL
                try:
                    with webdriver_pool.get_driver() as driver:
                        from thread_crawler import search_in_threads, scrape_post_urls_with_time

                        # 快速搜索（只搜索不登入，或使用現有cookies）
                        self.logger.info(f"🔍 嘗試快速搜索 Threads: {politician_name}")

                        # 訪問Threads搜索頁面
                        driver.get("https://www.threads.net/search")
                        time.sleep(2)

                        # 嘗試搜索
                        search_in_threads(driver, politician_name)
                        time.sleep(3)

                        # 快速收集URL（只收集前幾個，並傳遞日期篩選）
                        # 將字符串轉換為datetime對象
                        cutoff_date = datetime.strptime(cutoff_date_str, '%Y-%m-%d') if cutoff_date_str else None
                        post_data = scrape_post_urls_with_time(driver, max_scroll_attempts=2, cutoff_date=cutoff_date)

                        if post_data:
                            # 保存URL
                            with open(href_file, 'w', encoding='utf-8') as f:
                                json.dump(post_data, f, ensure_ascii=False, indent=2)
                            return {'success': True, 'url_file': href_file, 'count': len(post_data)}
                        else:
                            return {'success': True, 'message': 'Threads 快速搜索無結果，將在內容爬取階段重試', 'count': 0}

                except Exception as e:
                    self.logger.warning(f"Threads 快速搜索失敗: {e}")
                    return {'success': True, 'message': 'Threads 將在內容爬取階段搜尋', 'count': 0}
                
            else:
                return {'success': False, 'error': f'不支援的平台: {platform}'}
                
        except Exception as e:
            return {'success': False, 'error': f'{platform} URL收集失敗: {str(e)}'}
    
    def _crawl_content_sequential_optimized(self, politician_name: str, cutoff_date_str: str, 
                                          platforms: List[str], max_workers: int, url_results: Dict) -> Dict:
        """逐平台多線程爬取內容"""
        content_results = {}
        
        for i, platform in enumerate(platforms, 1):
            self.logger.info(f"📍 [{i}/{len(platforms)}] 開始爬取平台: {platform.upper()} (使用 {max_workers} 個線程)")
            
            # 為每個平台創建專用的WebDriver池
            platform_pool = get_global_pool(max_instances=max_workers, headless=True)
            
            try:
                start_time = time.time()
                
                if platform == 'youtube':
                    result = crawl_youtube_comments_with_pool(
                        name=politician_name,
                        webdriver_pool=platform_pool,
                        last_crawled_time=cutoff_date_str,
                        max_threads=max_workers
                    )
                    
                elif platform == 'ptt':
                    result = crawl_ptt_with_pool(
                        name=politician_name,
                        webdriver_pool=platform_pool,
                        last_crawled_time=cutoff_date_str,
                        max_threads=max_workers
                    )
                    
                elif platform == 'threads':
                    output_dir = os.path.join(current_dir, 'data', 'threads')
                    result = crawl_threads_with_pool(
                        name=politician_name,
                        output_dir=output_dir,
                        webdriver_pool=platform_pool,
                        last_crawled_time=cutoff_date_str,
                        max_threads=max_workers
                    )
                    
                else:
                    result = {'success': False, 'error': f'不支援的平台: {platform}'}
                
                elapsed_time = time.time() - start_time
                
                if result and result.get('success'):
                    count = result.get('count', 0)
                    self.logger.info(f"✅ {platform.upper()} 內容爬取完成: {count} 個項目 (耗時 {elapsed_time:.1f}s)")
                else:
                    error_msg = result.get('error') if result else '未知錯誤'
                    self.logger.warning(f"⚠️ {platform.upper()} 內容爬取失敗: {error_msg}")
                
                content_results[platform] = result
                
                # 平台間等待，讓資源釋放
                if i < len(platforms):
                    self.logger.info(f"⏳ 等待 3 秒，讓 {platform.upper()} 資源釋放...")
                    time.sleep(3)
                    
            except Exception as e:
                self.logger.error(f"❌ {platform.upper()} 內容爬取異常: {e}")
                content_results[platform] = {'success': False, 'error': str(e)}
        
        return content_results

if __name__ == "__main__":
    # 測試程式
    logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
    
    manager = OptimizedCrawlerManager()
    
    # 測試優化爬取
    result = manager.crawl_politician_optimized(
        politician_name="高虹安",
        cutoff_date_str="2025-01-01",
        platforms=['youtube', 'ptt', 'threads'],
        max_workers_per_platform=4
    )
    
    print(json.dumps(result, ensure_ascii=False, indent=2))
