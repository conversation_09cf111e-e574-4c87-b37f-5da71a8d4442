#!/usr/bin/env python3
"""
用戶留言資料處理工具

功能：
1. 合併各大平台的原始資料（YouTube、PTT、Threads、Facebook）到 alldata
2. 從 alldata 讀取已合併的原始資料
3. 統整每個用戶的留言內容，處理時間格式
4. 生成用戶留言統整結果，儲存至 processed/user_data 目錄

用法：
python user_data_processor.py [--merge-first] [--legislators 立委名稱...]
"""

import argparse
import os
import sys
import logging
import json
import re
import glob
from collections import defaultdict
from datetime import datetime

# 增加命令列參數支援
def parse_arguments():
    """解析命令列參數"""
    parser = argparse.ArgumentParser(description='用戶留言資料處理工具')
    parser.add_argument('--legislators', nargs='+', 
                       help='指定處理特定立委 (預設: 處理所有立委)')
    parser.add_argument('--force-reprocess', action='store_true',
                       help='強制重新處理所有資料')
    parser.add_argument('--merge-first', action='store_true',
                       help='先執行各平台資料合併再處理用戶資料')
    parser.add_argument('--quiet', action='store_true',
                       help='減少輸出訊息')
    return parser.parse_args()

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('user_data_processor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('user_data_processor')

# 設定路徑 - 修改為使用 processed 資料夾
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(CURRENT_DIR, 'data')  # 各平台原始資料
PROCESSED_DIR = os.path.join(CURRENT_DIR, 'processed')  # 處理後資料根目錄
ALLDATA_DIR = os.path.join(PROCESSED_DIR, 'alldata')  # 整合後原始資料
USER_DATA_DIR = os.path.join(PROCESSED_DIR, 'user_data')  # 按用戶整理的資料
FINAL_DATA_DIR = os.path.join(PROCESSED_DIR, 'final_data')  # Gemini分析後的最終資料

# 確保目錄存在
os.makedirs(PROCESSED_DIR, exist_ok=True)
os.makedirs(ALLDATA_DIR, exist_ok=True)
os.makedirs(USER_DATA_DIR, exist_ok=True)
os.makedirs(FINAL_DATA_DIR, exist_ok=True)

def merge_platform_data():
    """
    合併各大平台的資料到 all_data 目錄
    """
    logger.info("🔄 開始合併各平台資料...")
    os.makedirs(ALLDATA_DIR, exist_ok=True)
    platforms = {
        'youtube': os.path.join(DATA_DIR, 'youtube'),
        'ptt': os.path.join(DATA_DIR, 'ptt'),
        'threads': os.path.join(DATA_DIR, 'threads'),
        'facebook': os.path.join(DATA_DIR, 'facebook')
    }
    legislators_data = defaultdict(list)
    for platform_name, platform_dir in platforms.items():
        if not os.path.exists(platform_dir):
            logger.warning(f"⚠️  平台目錄不存在: {platform_dir}")
            continue
        logger.info(f"📋 處理 {platform_name.upper()} 平台資料...")
        json_files = glob.glob(os.path.join(platform_dir, '*.json'))
        for json_file in json_files:
            filename = os.path.basename(json_file)
            logger.info(f"   📄 處理檔案: {filename}")
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                processed_data = process_platform_data(data, platform_name, filename)
                legislator_name = extract_legislator_name(filename, platform_name)
                if legislator_name and processed_data:
                    legislators_data[legislator_name].extend(processed_data)
                    logger.info(f"   ✅ {legislator_name}: 新增 {len(processed_data)} 筆資料")
            except json.JSONDecodeError as e:
                logger.error(f"   ❌ JSON 格式錯誤: {filename} - {e}")
            except Exception as e:
                logger.error(f"   ❌ 處理檔案錯誤: {filename} - {e}")
    total_legislators = 0
    total_comments = 0
    for legislator_name, comments in legislators_data.items():
        output_file = os.path.join(ALLDATA_DIR, f"{legislator_name}.json")
        try:
            existing_data = []
            if os.path.exists(output_file):
                with open(output_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                logger.info(f"   📝 合併現有資料: {legislator_name} ({len(existing_data)} 筆)")
            all_comments = existing_data + comments
            unique_comments = remove_duplicates(all_comments)
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(unique_comments, f, ensure_ascii=False, indent=2)
            total_legislators += 1
            total_comments += len(unique_comments)
            logger.info(f"   💾 儲存: {legislator_name}.json ({len(unique_comments)} 筆資料)")
        except Exception as e:
            logger.error(f"   ❌ 儲存失敗: {legislator_name} - {e}")
    logger.info(f"✅ 資料合併完成！")
    logger.info(f"   📊 處理立委數: {total_legislators}")
    logger.info(f"   💬 總留言數: {total_comments}")
    logger.info(f"   📁 輸出目錄: {ALLDATA_DIR}")
    return total_legislators > 0

def process_platform_data(data, platform, filename):
    """
    根據平台類型處理資料格式，統一化資料結構
    
    Args:
        data: 原始資料
        platform: 平台名稱 ('youtube', 'ptt', 'threads', 'facebook')
        filename: 檔案名稱
    
    Returns:
        list: 統一格式的留言資料列表
    """
    processed_comments = []
    
    try:
        if platform == 'youtube':
            processed_comments = process_youtube_data(data, filename)
        elif platform == 'ptt':
            processed_comments = process_ptt_data(data, filename)
        elif platform == 'threads':
            processed_comments = process_threads_data(data, filename)
        elif platform == 'facebook':
            processed_comments = process_facebook_data(data, filename)
        else:
            logger.warning(f"   ⚠️  未知平台: {platform}")
            
    except Exception as e:
        logger.error(f"   ❌ {platform} 資料處理錯誤: {e}")
    
    return processed_comments

def process_youtube_data(data, filename):
    """處理YouTube資料格式"""
    comments = []
    
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict):
                # 新格式：影片資料結構
                if '留言資料' in item:
                    video_title = item.get('影片標題', '')
                    video_url = item.get('video_url', '')
                    
                    for comment_thread in item['留言資料']:
                        # 主留言
                        if '主留言' in comment_thread:
                            main_comment = comment_thread['主留言']
                            comment = {
                                "用戶": main_comment.get('用戶名', ''),
                                "標題": video_title,
                                "留言內容": main_comment.get('留言內容', ''),
                                "日期": main_comment.get('留言時間', ''),
                                "source": "youtube",
                                "url": video_url,
                                "按讚數": main_comment.get('按讚數', '0')
                            }
                            if comment["留言內容"]:
                                comments.append(comment)
                        
                        # 回覆留言
                        if '回覆' in comment_thread:
                            for reply in comment_thread['回覆']:
                                comment = {
                                    "用戶": reply.get('回覆留言者', ''),
                                    "標題": video_title,
                                    "留言內容": reply.get('回覆留言內容', ''),
                                    "日期": reply.get('回覆留言時間', ''),
                                    "source": "youtube",
                                    "url": video_url,
                                    "按讚數": reply.get('回覆留言按讚數', '0')
                                }
                                if comment["留言內容"]:
                                    comments.append(comment)
                
                # 舊格式：直接是評論
                else:
                    comment = {
                        "用戶": item.get('用戶', item.get('user', '')),
                        "標題": item.get('影片標題', item.get('video_title', '')),
                        "留言內容": item.get('留言內容', item.get('text', item.get('content', ''))),
                        "日期": item.get('日期', item.get('date', item.get('crawl_time', ''))),
                        "source": "youtube",
                        "url": item.get('video_url', ''),
                        "按讚數": item.get('按讚數', item.get('likes', '0'))
                    }
                    if comment["留言內容"]:  # 只保留有內容的留言
                        comments.append(comment)
    
    elif isinstance(data, dict):
        # 舊格式：可能是影片資料結構
        for video_title, video_data in data.items():
            if isinstance(video_data, dict) and '留言資料' in video_data:
                for comment_thread in video_data['留言資料']:
                    # 主留言
                    if '主留言' in comment_thread:
                        main_comment = comment_thread['主留言']
                        comment = {
                            "用戶": main_comment.get('用戶名', ''),
                            "標題": video_title,
                            "留言內容": main_comment.get('留言內容', ''),
                            "日期": main_comment.get('留言時間', ''),
                            "source": "youtube",
                            "按讚數": main_comment.get('按讚數', '0')
                        }
                        if comment["留言內容"]:
                            comments.append(comment)
                    
                    # 回覆留言
                    if '回覆' in comment_thread:
                        for reply in comment_thread['回覆']:
                            comment = {
                                "用戶": reply.get('回覆留言者', ''),
                                "標題": video_title,
                                "留言內容": reply.get('回覆留言內容', ''),
                                "日期": reply.get('回覆留言時間', ''),
                                "source": "youtube",
                                "按讚數": reply.get('回覆留言按讚數', '0')
                            }
                            if comment["留言內容"]:
                                comments.append(comment)
    
    return comments

def process_ptt_data(data, filename):
    """處理PTT資料格式"""
    comments = []
    
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict):
                # 主留言
                comment = {
                    "用戶": item.get('用戶', ''),
                    "標題": item.get('標題', ''),
                    "留言內容": item.get('留言', ''),  # PTT 使用 '留言' 欄位
                    "日期": item.get('日期', ''),
                    "source": "ptt",
                    "url": item.get('url', ''),
                    "推噓": item.get('標籤', ''),  # PTT 使用 '標籤' 表示推噓
                    "IP地址": item.get('IP地址', '')
                }
                if comment["留言內容"]:
                    comments.append(comment)
                
                # 處理回覆留言
                if '回覆' in item and isinstance(item['回覆'], list):
                    for reply in item['回覆']:
                        reply_comment = {
                            "用戶": reply.get('用戶', ''),
                            "標題": item.get('標題', ''),  # 使用主留言的標題
                            "留言內容": reply.get('留言', ''),
                            "日期": reply.get('日期', ''),
                            "source": "ptt",
                            "url": item.get('url', ''),
                            "推噓": "回覆",
                            "IP地址": reply.get('IP地址', '')
                        }
                        if reply_comment["留言內容"]:
                            comments.append(reply_comment)
    
    return comments

def process_threads_data(data, filename):
    """處理Threads資料格式"""
    comments = []
    
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict):
                # 處理主貼文
                main_post = item.get('主貼文', {})
                if main_post and main_post.get('內容'):
                    comment = {
                        "用戶": main_post.get('用戶', ''),
                        "標題": "",  # Threads沒有標題概念
                        "留言內容": main_post.get('內容', ''),
                        "日期": main_post.get('時間', ''),
                        "source": "threads",
                        "threads_post_id": item.get('threads_post_id', ''),
                        "threads_post_url": item.get('threads_post_url', ''),
                        "推噓": "推",  # 主貼文標記為推
                        "按讚數": 0,
                        "IP地址": "",
                        "是否為主留言": True
                    }
                    comments.append(comment)
                
                # 處理回覆
                replies = item.get('回覆', [])
                for reply in replies:
                    if reply.get('留言'):
                        comment = {
                            "用戶": reply.get('用戶', ''),
                            "標題": "",
                            "留言內容": reply.get('留言', ''),
                            "日期": reply.get('時間', ''),
                            "source": "threads",
                            "threads_comment_id": reply.get('threads_comment_id', ''),
                            "threads_post_url": item.get('threads_post_url', ''),
                            "推噓": "推",  # 預設為推
                            "按讚數": reply.get('按讚數', 0),
                            "IP地址": "",
                            "是否為主留言": False
                        }
                        comments.append(comment)
    
    return comments

def process_facebook_data(data, filename):
    """處理Facebook資料格式"""
    comments = []
    
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict):
                # 處理主貼文
                post_content = item.get('貼文內容', '')
                if post_content:
                    comment = {
                        "用戶": "",  # Facebook主貼文通常不儲存用戶名
                        "標題": "",
                        "留言內容": post_content,
                        "日期": "",
                        "source": "facebook",
                        "facebook_post_id": "",  # 需要從爬蟲補充
                        "推噓": "推",  # 主貼文標記為推
                        "按讚數": 0,
                        "IP地址": "",
                        "是否為主留言": True
                    }
                    comments.append(comment)
                
                # 處理留言
                fb_comments = item.get('留言', [])
                for fb_comment in fb_comments:
                    if fb_comment.get('留言內容'):
                        comment = {
                            "用戶": fb_comment.get('用戶名', ''),
                            "標題": "",
                            "留言內容": fb_comment.get('留言內容', ''),
                            "日期": fb_comment.get('留言時間', ''),
                            "source": "facebook",
                            "facebook_comment_id": "",  # 需要從爬蟲補充
                            "推噓": "推",  # 預設為推
                            "按讚數": fb_comment.get('按讚數', 0),
                            "IP地址": "",
                            "是否為主留言": False
                        }
                        comments.append(comment)
    
    return comments

def extract_legislator_name(filename, platform):
    """
    從檔案名稱提取立委名稱
    
    常見格式：
    - youtube_comments_葉元之_20250101_120000.json
    - ptt_葉元之_20250101.json
    - threads_葉元之.json
    - 葉元之_facebook.json
    """
    # 移除副檔名
    name = os.path.splitext(filename)[0]
    
    # 移除時間戳記 (YYYYMMDD_HHMMSS 或 YYYYMMDD)
    name = re.sub(r'_\d{8}(_\d{6})?$', '', name)
    
    # 移除平台前綴
    for prefix in ['youtube_comments_', 'youtube_', 'ptt_', 'threads_', 'facebook_']:
        if name.startswith(prefix):
            name = name[len(prefix):]
            break
    
    # 移除平台後綴
    for suffix in ['_youtube', '_ptt', '_threads', '_facebook']:
        if name.endswith(suffix):
            name = name[:-len(suffix)]
            break
    
    # 清理多餘的底線
    name = name.strip('_')
    
    return name if name else None

def remove_duplicates(comments):
    """
    移除重複的留言資料
    根據用戶、內容、日期來判斷是否重複
    """
    seen = set()
    unique_comments = []
    
    for comment in comments:
        # 建立唯一識別鍵
        key = (
            comment.get('用戶', ''),
            comment.get('留言內容', ''),
            comment.get('日期', ''),
            comment.get('source', '')
        )
        
        if key not in seen:
            seen.add(key)
            unique_comments.append(comment)
    
    return unique_comments

# 確保輸出目錄存在 - 已在上方創建，此行移除

# 日期解析相關的正則表達式
DATE_PATTERNS = [
    (re.compile(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})'), '%Y-%m-%d'),  # 標準日期格式: 2023-05-15, 2023/05/15
    (re.compile(r'(\d{1,2})[/-](\d{1,2})[/-](\d{4})'), '%d-%m-%Y'),  # 日/月/年格式: 15-05-2023, 15/05/2023
]

def parse_date(date_str):
    """解析不同格式的日期字串為統一的日期物件"""
    if not date_str:
        return None
    
    # 嘗試直接解析標準格式
    try:
        return datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        pass
    
    # 嘗試解析其他可能的格式
    for pattern, fmt in DATE_PATTERNS:
        match = pattern.search(date_str)
        if match:
            try:
                if fmt == '%Y-%m-%d':
                    year, month, day = match.groups()
                else:  # '%d-%m-%Y'
                    day, month, year = match.groups()
                
                year = int(year)
                month = int(month)
                day = int(day)
                
                # 簡單的日期驗證
                if 1 <= month <= 12 and 1 <= day <= 31:
                    return datetime(year, month, day)
            except (ValueError, TypeError):
                pass
    
    # 無法解析，返回None
    return None

def consolidate_comments_from_files_by_user_parsed():
    """
    從本地資料夾中讀取每個立委的 JSON 檔案，
    然後針對每個立委，將其所有用戶的留言標題、內容、情感標籤和情緒統整為
    一個包含多個留言字典的列表。
    """
    # 最終輸出數據結構：
    # { "立委姓名": { "用戶ID": { "comments": [ { "標題": "...", "留言內容": "...", "情感標籤": "...", "情緒": "..." }, ... ] } } }
    consolidated_output_for_all = {}

    if not os.path.exists(ALLDATA_DIR):
        logger.error(f"錯誤：找不到資料夾 {ALLDATA_DIR}。請檢查路徑。")
        return {}

    # 遍歷資料夾中的所有 JSON 檔案
    for filename in os.listdir(ALLDATA_DIR):
        if filename.endswith('.json'):
            file_path = os.path.join(ALLDATA_DIR, filename)
            legislator_name = os.path.splitext(filename)[0]

            logger.info(f"正在處理立委檔案：{filename} (立委: {legislator_name})...")

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    comments_list = json.load(f)
            except json.JSONDecodeError:
                logger.warning(f"警告：檔案 {filename} 格式錯誤，跳過。")
                continue
            except Exception as e:
                logger.error(f"讀取檔案 {filename} 時發生錯誤: {e}，跳過。")
                continue

            # 儲存當前立委下各用戶的留言列表
            user_comments_for_legislator = defaultdict(lambda: {"comments": []})

            # 處理每條留言
            for comment in comments_list:
                # 確保有用戶字段
                user = comment.get('用戶')
                if not user:
                    user = comment.get('user')
                    if not user:
                        continue

                # 構建留言字典
                comment_dict = {
                    "標題": comment.get('標題') or comment.get('title') or "",
                    "留言內容": comment.get('留言內容') or comment.get('content') or "",
                    "情感標籤": comment.get('情感標籤') or "",
                    "情緒": comment.get('情緒') or "",
                    "日期": comment.get('日期') or comment.get('date') or "",
                    "source": comment.get('source') or ""
                }

                # 合併上文留言到標題
                上文留言 = comment.get('上文留言', '')
                if 上文留言 and 上文留言.strip():
                    if comment_dict["標題"]:
                        comment_dict["標題"] = f"{comment_dict['標題']} | 上文: {上文留言}"

                # 添加到用戶的留言列表
                user_comments_for_legislator[user]["comments"].append(comment_dict)

            # 將處理好的用戶留言添加到總輸出中
            consolidated_output_for_all[legislator_name] = user_comments_for_legislator
            logger.info(f"成功處理 {legislator_name} 的資料，共 {len(user_comments_for_legislator)} 個用戶")

    return consolidated_output_for_all

def save_consolidated_comments_by_user_parsed(consolidated_data):
    """將整合後的用戶留言資料保存到檔案中"""
    # 統計所有用戶數量
    all_users_count = 0
    all_users_info = {}
    
    # 遍歷所有立委的資料
    for legislator_name, user_data in consolidated_data.items():
        # 輸出檔案路徑
        output_path = os.path.join(USER_DATA_DIR, f"{legislator_name}_user.json")
        
        # 寫入檔案
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(user_data, f, ensure_ascii=False, indent=2)
        
        user_count = len(user_data)
        all_users_count += user_count
        all_users_info[legislator_name] = user_count
        
        logger.info(f"已保存 {legislator_name} 的用戶資料，共 {user_count} 個用戶")
    
    # 保存用戶總覽資訊
    summary_path = os.path.join(USER_DATA_DIR, "all_users_summary.json")
    with open(summary_path, 'w', encoding='utf-8') as f:
        summary = {
            "total_users": all_users_count,
            "legislators_info": all_users_info,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    logger.info(f"已保存用戶總覽資訊，共 {all_users_count} 個用戶")

def process_all_legislators():
    """處理所有立委的用戶資料"""
    # 1. 統整用戶留言資料
    logger.info("開始統整用戶留言資料...")
    consolidated_data = consolidate_comments_from_files_by_user_parsed()
    if not consolidated_data:
        logger.error("統整用戶留言資料失敗")
        return False
    
    # 2. 保存統整後的用戶留言資料
    logger.info("開始保存統整後的用戶留言資料...")
    save_consolidated_comments_by_user_parsed(consolidated_data)
    
    logger.info("用戶留言資料處理完成")
    return True

def extract_comment_details(user_comments):
    """
    從用戶留言中提取詳細資訊（時間、平台等）
    
    Args:
        user_comments: 用戶的所有留言列表
    
    Returns:
        dict: 包含最後留言時間、來源平台等資訊
    """
    latest_time = None
    source_platforms = set()
    total_comments = len(user_comments)
    
    for comment in user_comments:
        # 提取日期
        comment_time = parse_date(comment.get('日期', ''))
        if comment_time and (not latest_time or comment_time > latest_time):
            latest_time = comment_time
        
        # 提取平台
        platform = comment.get('source', '')
        if platform:
            source_platforms.add(platform)
    
    return {
        'latest_comment_time': latest_time.strftime('%Y-%m-%d') if latest_time else '',
        'source_platforms': list(source_platforms),
        'primary_platform': list(source_platforms)[0] if source_platforms else '未知',
        'comment_count': total_comments
    }

def prepare_for_emotion_analysis(user_data_summary):
    """
    準備用戶資料給情感分析使用
    生成符合 gemini_emo_user.py 預期格式的資料
    
    Args:
        user_data_summary: 用戶統計資料列表
    
    Returns:
        dict: 格式化後的用戶資料
    """
    formatted_data = {}
    
    for user_info in user_data_summary:
        user_name = user_info['user_name']
        comments = user_info['all_comments']
        
        # 格式化留言內容
        formatted_comments = []
        for comment in comments:
            formatted_comment = {
                '標題': comment.get('標題', ''),
                '留言內容': comment.get('留言內容', ''),
                '日期': comment.get('日期', ''),
                '來源': comment.get('source', '')
            }
            formatted_comments.append(formatted_comment)
        
        formatted_data[user_name] = {
            'comments': formatted_comments
        }
    
    return formatted_data

def process_legislators_data(specific_legislators=None, force_reprocess=False, quiet=False):
    """
    統一的立委資料處理接口
    
    Args:
        specific_legislators: 要處理的特定立委列表，None表示處理所有立委
        force_reprocess: 是否強制重新處理
        quiet: 是否使用安靜模式
    
    Returns:
        bool: 處理是否成功
    """
    if quiet:
        logger.setLevel(logging.WARNING)
    
    if specific_legislators:
        logger.info(f"開始處理特定立委的用戶資料：{specific_legislators}")
        
        # 先執行合併步驟（針對特定立委）
        merge_success = merge_specific_legislators_data(specific_legislators)
        if not merge_success:
            logger.warning("合併資料失敗，嘗試處理現有的合併資料...")
        
        # 然後處理用戶統計（針對特定立委）
        process_success = process_specific_legislators_users(specific_legislators)
        if not process_success:
            logger.error("用戶統計失敗")
            return False
        
        # 只要用戶統計成功就算成功
        return True
            
    else:
        logger.info("開始處理所有立委的用戶資料")
        
        # 先執行合併步驟
        success = merge_platform_data()
        if not success:
            logger.error("合併資料失敗")
            return False
        
        # 然後處理用戶統計
        success = process_all_legislators()
        if not success:
            logger.error("用戶統計失敗")
            return False
    
    logger.info("用戶資料處理完成")
    return True

def merge_specific_legislators_data(legislators):
    """合併特定立委的平台資料"""
    logger.info(f"🔄 開始合併特定立委資料: {legislators}")
    
    # 確保輸出目錄存在
    os.makedirs(ALLDATA_DIR, exist_ok=True)
    
    # 平台資料目錄映射
    platforms = {
        'youtube': os.path.join(DATA_DIR, 'youtube'),
        'ptt': os.path.join(DATA_DIR, 'ptt'),
        'threads': os.path.join(DATA_DIR, 'threads'),
        'facebook': os.path.join(DATA_DIR, 'facebook')
    }
    
    success_count = 0
    
    for legislator in legislators:
        logger.info(f"   📋 處理立委: {legislator}")
        
        # 儲存該立委的合併資料
        legislator_data = []
        
        # 遍歷各平台
        for platform_name, platform_dir in platforms.items():
            if not os.path.exists(platform_dir):
                continue
                
            # 查找該立委在該平台的檔案（新的統一命名格式）
            # 優先查找新格式: 立委名.json
            json_file = os.path.join(platform_dir, f'{legislator}.json')
            json_files = []
            if os.path.exists(json_file):
                json_files = [json_file]
            else:
                # 兼容舊格式
                pattern = os.path.join(platform_dir, f'*{legislator}*.json')
                json_files = glob.glob(pattern)
            
            for json_file in json_files:
                filename = os.path.basename(json_file)
                logger.info(f"     📄 處理檔案: {filename}")
                
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 根據平台類型處理資料格式
                    processed_data = process_platform_data(data, platform_name, filename)
                    
                    if processed_data:
                        legislator_data.extend(processed_data)
                        logger.info(f"     ✅ 新增 {len(processed_data)} 筆資料")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"     ❌ JSON 格式錯誤: {filename} - {e}")
                except Exception as e:
                    logger.error(f"     ❌ 處理檔案錯誤: {filename} - {e}")
        
        # 儲存該立委的合併資料
        if legislator_data:
            output_file = os.path.join(ALLDATA_DIR, f"{legislator}.json")
            
            try:
                # 如果檔案已存在，合併資料
                existing_data = []
                if os.path.exists(output_file):
                    with open(output_file, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                    logger.info(f"   📝 合併現有資料: {legislator} ({len(existing_data)} 筆)")
                
                # 合併並去重
                all_comments = existing_data + legislator_data
                unique_comments = remove_duplicates(all_comments)
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(unique_comments, f, ensure_ascii=False, indent=2)
                
                success_count += 1
                logger.info(f"   💾 儲存: {legislator}.json ({len(unique_comments)} 筆資料)")
                
            except Exception as e:
                logger.error(f"   ❌ 儲存失敗: {legislator} - {e}")
        else:
            logger.warning(f"   ⚠️  {legislator} 沒有找到任何資料檔案（可能需要先執行爬蟲）")
    
    logger.info(f"✅ 特定立委資料合併完成！處理立委數: {success_count}")
    return success_count > 0

# 為simple_crawler_manager提供的別名
def merge_legislators_data(legislators):
    """合併立委數據的別名函數"""
    return merge_specific_legislators_data(legislators)

def process_specific_legislators_users(legislators):
    """處理特定立委的用戶統計"""
    logger.info(f"📊 開始處理特定立委的用戶統計: {legislators}")
    # 目錄已在上方創建
    success_count = 0
    for legislator in legislators:
        logger.info(f"   📋 處理立委: {legislator}")
        alldata_file = os.path.join(ALLDATA_DIR, f"{legislator}.json")
        if not os.path.exists(alldata_file):
            logger.warning(f"   ⚠️  找不到 {legislator} 的合併資料檔案: {alldata_file}")
            logger.info(f"   💡 建議先執行爬蟲或檢查資料是否存在")
            continue
        try:
            with open(alldata_file, 'r', encoding='utf-8') as f:
                comments = json.load(f)
            user_stats = defaultdict(list)
            for comment in comments:
                user_name = (comment.get('用戶', '') or comment.get('user', '')).strip()
                if user_name:
                    user_stats[user_name].append(comment)
            user_summary = []
            for user_name, user_comments in user_stats.items():
                details = extract_comment_details(user_comments)
                user_info = {
                    'user_name': user_name,
                    'comment_count': details['comment_count'],
                    'latest_comment_time': details['latest_comment_time'],
                    'source_platforms': details['source_platforms'],
                    'primary_platform': details['primary_platform'],
                    'all_comments': user_comments
                }
                user_summary.append(user_info)
            output_file = os.path.join(USER_DATA_DIR, f"{legislator}_users.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(user_summary, f, ensure_ascii=False, indent=2)
            emotion_analysis_data = prepare_for_emotion_analysis(user_summary)
            # 修復文件名：使用_gemini_format.json而不是_emotion_ready.json
            emotion_file = os.path.join(USER_DATA_DIR, f"{legislator}_gemini_format.json")
            with open(emotion_file, 'w', encoding='utf-8') as f:
                json.dump(emotion_analysis_data, f, ensure_ascii=False, indent=2)
            success_count += 1
            logger.info(f"   💾 用戶統計完成: {legislator} ({len(user_summary)} 位用戶)")
        except Exception as e:
            logger.error(f"   ❌ 處理用戶統計失敗: {legislator} - {e}")
    logger.info(f"✅ 特定立委用戶統計完成！處理立委數: {success_count}")
    if success_count == 0:
        logger.info("💡 沒有處理任何立委資料，可能需要先執行爬蟲收集資料")
    return True

def main():
    """主程式"""
    args = parse_arguments()
    
    if args.quiet:
        logger.setLevel(logging.WARNING)
    
    if not args.quiet:
        print("\n===== 用戶留言資料處理工具 =====\n")
        print("功能：")
        print("1. 合併各大平台資料到 alldata")
        print("2. 統整用戶留言並儲存結果到 processed/user_data 目錄")
        print("\n處理中，請稍候...")
    
    # 步驟1：如果指定了 --merge-first，先執行資料合併
    if args.merge_first:
        if not args.quiet:
            print("\n🔄 步驟1: 合併各平台資料...")
        try:
            merge_platform_data()
            if not args.quiet:
                print("✅ 資料合併完成!")
        except Exception as e:
            logger.error(f"資料合併失敗: {e}")
            if not args.quiet:
                print("❌ 資料合併失敗，請查看日誌")
            return
    
    # 步驟2：執行用戶留言資料處理
    if not args.quiet:
        print("\n📊 步驟2: 處理用戶留言資料...")
    
    success = False
    if args.legislators:
        # 處理指定立委
        for legislator in args.legislators:
            if not args.quiet:
                print(f"處理立委: {legislator}")
            # 處理特定立委的邏輯
            success = process_all_legislators()
    else:
        # 處理所有立委
        success = process_all_legislators()
    
    if success:
        if not args.quiet:
            print("\n✅ 處理完成！資料已儲存到 processed/user_data 目錄")
    else:
        if not args.quiet:
            print("\n❌ 處理過程中發生錯誤，請查看日誌檔案")

def process_legislators_data_legacy():
    """
    提供給舊版 main.py 調用的函數（已棄用）
    合併各平台資料並處理用戶留言資料
    """
    logger.info("🔄 開始處理立委資料...")
    
    try:
        # 先合併各平台資料
        merge_platform_data()
        
        # 再處理用戶留言資料
        success = process_all_legislators()
        
        if success:
            logger.info("✅ 立委資料處理完成")
            return True
        else:
            logger.error("❌ 用戶資料處理失敗")
            return False
            
    except Exception as e:
        logger.error(f"❌ 處理立委資料時發生錯誤: {e}")
        return False

def create_user_format_for_gemini(legislators=None):
    """
    創建符合Gemini分析要求的用戶格式數據
    格式: "user_id": {"comments": [...], "latest_date": "..."}

    Args:
        legislators: 要處理的立委列表，None表示處理所有立委

    Returns:
        bool: 處理是否成功
    """
    logger.info("🔄 開始創建Gemini分析格式的用戶數據...")

    try:
        if legislators is None:
            # 處理所有立委
            alldata_files = glob.glob(os.path.join(ALLDATA_DIR, '*.json'))
            legislators = [os.path.splitext(os.path.basename(f))[0] for f in alldata_files]

        success_count = 0
        for legislator in legislators:
            logger.info(f"📋 處理立委: {legislator}")

            # 讀取整合後的原始數據
            alldata_file = os.path.join(ALLDATA_DIR, f"{legislator}.json")
            if not os.path.exists(alldata_file):
                logger.warning(f"⚠️ 找不到 {legislator} 的整合數據文件")
                continue

            try:
                with open(alldata_file, 'r', encoding='utf-8') as f:
                    raw_data = json.load(f)

                # 按用戶ID整理數據
                user_data = defaultdict(list)

                for item in raw_data:
                    # 提取用戶ID和留言信息
                    user_id = None
                    comment_data = None

                    # 根據不同平台提取數據
                    if item.get('source') == 'youtube':
                        # YouTube評論 - 使用'用戶'字段作為user_id
                        if '用戶' in item:
                            user_id = item['用戶']
                            comment_data = {
                                "標題": item.get('標題', ''),
                                "留言內容": item.get('留言內容', ''),
                                "情感標籤": "",  # 待Gemini分析
                                "情緒": "",     # 待Gemini分析
                                "日期": item.get('日期', ''),
                                "source": "youtube"
                            }
                    elif item.get('source') == 'ptt':
                        # PTT留言 - 使用'用戶'字段作為user_id
                        if '用戶' in item:
                            user_id = item['用戶']
                            comment_data = {
                                "標題": item.get('標題', ''),
                                "留言內容": item.get('留言內容', ''),
                                "情感標籤": "",  # 待Gemini分析
                                "情緒": "",     # 待Gemini分析
                                "日期": item.get('日期', ''),
                                "source": "ptt"
                            }
                    elif item.get('source') == 'threads':
                        # Threads貼文 - 使用'用戶'字段作為user_id
                        if '用戶' in item:
                            user_id = item['用戶']
                            comment_data = {
                                "標題": item.get('標題', ''),
                                "留言內容": item.get('內容', ''),
                                "情感標籤": "",  # 待Gemini分析
                                "情緒": "",     # 待Gemini分析
                                "日期": item.get('日期', ''),
                                "source": "threads"
                            }

                    # 添加到用戶數據中
                    if user_id and comment_data:
                        user_data[user_id].append(comment_data)

                # 轉換為最終格式
                final_user_data = {}
                for user_id, comments in user_data.items():
                    # 按日期排序，取最新日期
                    sorted_comments = sorted(comments, key=lambda x: x.get('日期', ''), reverse=True)
                    latest_date = sorted_comments[0].get('日期', '') if sorted_comments else ''

                    final_user_data[user_id] = {
                        "comments": sorted_comments,
                        "latest_date": latest_date,
                        "comment_count": len(comments)
                    }

                # 保存到user_data目錄
                output_file = os.path.join(USER_DATA_DIR, f"{legislator}_gemini_format.json")
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(final_user_data, f, ensure_ascii=False, indent=2)

                logger.info(f"✅ {legislator} 用戶格式數據已保存: {len(final_user_data)} 個用戶")
                success_count += 1

            except Exception as e:
                logger.error(f"❌ 處理 {legislator} 時發生錯誤: {e}")
                continue

        logger.info(f"🎉 Gemini格式用戶數據創建完成！成功處理 {success_count} 位立委")
        return success_count > 0

    except Exception as e:
        logger.error(f"❌ 創建Gemini格式數據時發生錯誤: {e}")
        return False

if __name__ == "__main__":
    main()
