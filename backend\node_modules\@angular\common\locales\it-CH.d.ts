/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    BRL: (string | undefined)[];
    BYN: (string | undefined)[];
    EGP: (string | undefined)[];
    HKD: (string | undefined)[];
    INR: (string | undefined)[];
    JPY: (string | undefined)[];
    KRW: (string | undefined)[];
    MXN: (string | undefined)[];
    NOK: (string | undefined)[];
    THB: string[];
    TWD: (string | undefined)[];
    USD: (string | undefined)[];
    VND: (string | undefined)[];
} | undefined)[];
export default _default;
