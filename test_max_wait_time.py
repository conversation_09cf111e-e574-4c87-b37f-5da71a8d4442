"""
測試 YouTube 爬蟲的等待時間設定
"""

import os
import sys
import time

# 添加必要的路徑
current_path = os.path.dirname(os.path.abspath(__file__))
backend_path = os.path.join(current_path, 'backend')
crawler_path = os.path.join(backend_path, 'crawler')

sys.path.append(current_path)
sys.path.append(backend_path) 
sys.path.append(crawler_path)

# 模擬 max_wait_time 的問題
def simulate_max_wait_time():
    print("測試 max_wait_time 變數...")
    
    # 顯示初始值
    max_wait_time = 60
    print(f"初始 max_wait_time = {max_wait_time}")
    
    # 模擬代碼執行過程
    time.sleep(1)
    print(f"使用變數輸出: {max_wait_time}秒內沒有新影片")
    
    # 模擬固定值輸出
    time.sleep(1)
    print(f"使用固定值輸出: 60秒內沒有新影片")
    
    print("測試完成")

if __name__ == "__main__":
    simulate_max_wait_time()
