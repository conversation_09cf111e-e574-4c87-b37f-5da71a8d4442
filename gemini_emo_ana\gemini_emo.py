#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import re
import time
import random
from concurrent.futures import ThreadPoolExecutor
from google import genai
from google.genai import types
from dotenv import load_dotenv

load_dotenv()

# ===== 工具與設定 =====
def load_api_keys(path='api_list.json'):
    with open(path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data.get('api_keys', [])

emotion_keywords = {
    "anger": ["氣", "火大", "無恥", "去死", "惱怒", "抓狂", "氣死人"],
    "disgust": ["噁", "垃圾", "爛", "快吐", "髒", "惡心", "臭"],
    "fear": ["怕", "恐", "好可怕", "擔憂", "不安", "害怕"],
    "surprise": ["竟然", "傻眼", "沒想到", "驚呆", "嚇到", "不可思議"],
    "joy": ["太好了", "爽翻", "開心", "歡呼", "開心到不行"],
    "trust": ["支持", "相信", "挺你", "信任", "為你加油"],
    "anticipation": ["期待", "快點", "拭目以待", "等著瞧", "有感覺"],
    "sadness": ["可憐", "難過", "失望", "痛心", "心碎", "悲傷"]
}

# Build the LLM prompt
def build_prompt(context, reply, person_name, positive_party, negative_party):
    return f"""
你是一位專業的語意理解與輿情分析員，任務是根據政治留言的語意、情緒與立場，判斷其對於「立法委員{person_name}罷免案的情緒與立場判斷」，並回傳格式正確的 JSON 結果。
支持罷免{person_name}是負面(NEGATIVE)
反對罷免{person_name}是正面(POSITIVE)

【分析任務】
請根據下列 Context（新聞事件或討論標題）與 Reply（網友留言），判斷：
1. 該留言的立場（Label）：
   - 是否支持罷免{person_name}（{positive_party}立委）NEGATIVE？
   - 或是反對罷免（即支持{person_name}或批評{negative_party}）POSITIVE？

2. 該留言的情緒（Emotion）：從以下八種中選擇最接近的情緒。

【立場分類規則】
- NEGATIVE：留言表達支持罷免{person_name}，或批評{positive_party}、{person_name}本人，支持{negative_party}或其立場。
- POSITIVE：留言反對罷免{person_name}，或批評{negative_party}、支持{positive_party}、或{person_name}本人。
- 若留言內容模糊或無法判斷，請以語氣傾向與上下文意圖推論最可能立場。
- 反對或批評{negative_party}為POSITIVE
- 國民黨是藍營，民進黨是綠營，依據立委所屬政黨判斷是否支持罷免
- 如果政黨為國民黨那批評民進黨、賴清德總統、相關民進黨議員為POSITIVE，反之亦然
- 如果政黨為民進黨那批評國民黨、陳水扁、馬英九、相關國民黨議員為POSITIVE，反之亦然

【強化判讀重點】
- 若出現嘲諷、反話、雙關語、鄉民語氣，需判斷是否為反語，並根據語境還原真實立場。
- 留言若批評對象是"{negative_party}"、"總統"、"綠營"傾向 POSITIVE，批評"{positive_party}"、"{person_name}"、"藍營"傾向 NEGATIVE。
- 若內容太模糊，可根據語氣合理推測立場，但不強行猜測。
- 強調{positive_party}與共產黨合作的為負面
- 如果有表達對政治人物的反感，則根據反感的陣營，判別是否支持罷免
- 忽略新聞網站來源文字

【情緒分類規則】
請從以下八種英文小寫情緒中選擇最符合者：
{emotion_keywords}

請務必以以下 JSON Schema 格式回傳：
{{
  "Label": "POSITIVE" | "NEGATIVE",
  "Emotion": "joy" | "trust" | "anticipation" | "sadness" | "surprise" | "disgust" | "fear" | "anger"
}}

JSON Response:
Context: {context}
Reply: {reply}
"""

def analyze_comment(client, context, reply, date, user, person_name, positive_party, negative_party, jsonfile, txtfile, max_retries=6):
    prompt = build_prompt(context, reply, person_name, positive_party, negative_party)
    safety = [
        types.SafetySetting(category=c, threshold="BLOCK_NONE")
        for c in [
            "HARM_CATEGORY_HATE_SPEECH",
            "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            "HARM_CATEGORY_DANGEROUS_CONTENT",
            "HARM_CATEGORY_HARASSMENT",
            "HARM_CATEGORY_CIVIC_INTEGRITY",
        ]
    ]
    for attempt in range(max_retries):
        try:
            res = client.models.generate_content(
                model="gemini-2.0-flash",
                contents=prompt,
                config=types.GenerateContentConfig(safety_settings=safety)
            )
            text = res.text.strip()
            if not text:
                raise ValueError("Empty response")
            label = re.search(r'Label\s*:\s*(\w+)', text, re.IGNORECASE)
            emotion = re.search(r'Emotion\s*:\s*(\w+)', text, re.IGNORECASE)
            result = {
                "標題": context,
                "留言內容": reply,
                "情感標籤": label.group(1) if label else "Unknown",
                "情緒": emotion.group(1) if emotion else "Unknown",
                "日期": date,
                "用戶": user
            }
            if os.path.exists(jsonfile):
                with open(jsonfile, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = []
            data.append(result)
            with open(jsonfile, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            with open(txtfile, 'a', encoding='utf-8') as f:
                f.write(f"=== {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n")
                f.write(f"Context: {context}\nReply: {reply}\nResponse: {text}\n\n")

            return result
        except Exception as e:
            print(f"Retry {attempt+1}: {e}")
            time.sleep(random.uniform(2,4))
    return None

def run_batch(batch_index, batch_data, api_key, person_name, positive_party, negative_party):
    safe_name = person_name.replace(" ", "_")
    temp_dir = f'temp/{safe_name}'
    os.makedirs(temp_dir, exist_ok=True)

    output_path = f'{temp_dir}/batch_{batch_index}.json'
    log_path = f'{temp_dir}/log_{batch_index}.txt'

    client = genai.Client(api_key=api_key)
    print(f"[{person_name}][Batch {batch_index}] 開始處理，共 {len(batch_data)} 筆")

    for i, item in enumerate(batch_data):
        context = item.get("標題", "")
        reply = item.get("留言", "")
        date = item.get("日期", "")
        user = item.get("用戶", "")
        print(f"[{person_name}][Batch {batch_index}] 處理第 {i+1}/{len(batch_data)} 則")
        analyze_comment(client, context, reply, date, user, person_name, positive_party, negative_party, output_path, log_path)
        time.sleep(random.uniform(1.5, 3.0))

def split_batches(data, size):
    return [data[i:i + size] for i in range(0, len(data), size)]

def merge_temp_results(person_name):
    safe_name = person_name.replace(" ", "_")
    temp_dir = f'temp/{safe_name}'
    output_file = f'data/{safe_name}.json'
    log_output = f'data/{safe_name}_log.txt'
    os.makedirs("data", exist_ok=True)

    all_results = []
    with open(log_output, 'w', encoding='utf-8') as log_out:
        for fname in sorted(os.listdir(temp_dir)):
            path = os.path.join(temp_dir, fname)
            if fname.endswith(".json"):
                with open(path, 'r', encoding='utf-8') as f:
                    batch_data = json.load(f)
                    all_results.extend(batch_data)
            elif fname.endswith(".txt"):
                with open(path, 'r', encoding='utf-8') as log_in:
                    log_out.write(f"\n=== {fname} ===\n")
                    log_out.write(log_in.read())

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)

    print(f"✅ 合併完成：{output_file}（共 {len(all_results)} 筆）")

def main():
    batch_size = 1000
    api_key = os.environ.get('GEMINI_API_KEY')
    NEWSAPI_KEY = os.environ.get('NEWSAPI_KEY')
    if api_key:
        genai.configure(api_key=api_key)
    else:
        print("❌ 錯誤：未設定 Gemini API Key。請在程式碼中設定或使用環境變數。")
        exit()

    tasks = [
        {
            "input_path": "data/set1_comments.json",
            "person": "葉元之",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        }

    ]

    all_futures = []
    persons = []

    with ThreadPoolExecutor(max_workers=len(api_keys)) as executor:
        task_id = 0
        for task in tasks:
            input_path = task["input_path"]
            person = task["person"]
            positive_party = task["positive_party"]
            negative_party = task["negative_party"]

            data = json.load(open(input_path, 'r', encoding='utf-8'))
            batches = split_batches(data, batch_size)
            print(f"\n📦 {person}: {len(data)} comments → {len(batches)} batches")

            for i, batch in enumerate(batches):
                key = api_keys[(task_id * 1000 + i) % len(api_keys)]
                batch_index = f"{i}"
                futures = executor.submit(
                    run_batch,
                    batch_index,
                    batch,
                    key,
                    person,
                    positive_party,
                    negative_party
                )
                all_futures.append(futures)
            persons.append(person)
            task_id += 1

        for future in all_futures:
            future.result()

    print("\n📂 所有批次已完成，開始合併暫存資料...")
    for p in persons:
        merge_temp_results(p)

    print("\n✅ 所有任務完成，結果存於 data/")

if __name__ == '__main__':
    main()