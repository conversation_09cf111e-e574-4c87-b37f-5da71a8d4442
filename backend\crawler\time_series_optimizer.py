#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
時間序列優化器
實現動態密度的時間點分布，越接近現在時間間隔越短
"""

import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Any
from collections import defaultdict
import json

class TimeSeriesOptimizer:
    """時間序列優化器"""
    
    def __init__(self):
        self.now = datetime.now()
    
    def generate_dynamic_time_points(self, start_date: datetime, end_date: datetime = None) -> List[datetime]:
        """
        生成動態密度的時間點
        
        規則：
        - 最近1個月：每週1個點 (4個點)
        - 1-3個月前：每2週1個點 (4個點)  
        - 3-6個月前：每月1個點 (3個點)
        - 6個月以上：每2個月1個點
        
        Args:
            start_date: 開始日期
            end_date: 結束日期，默認為現在
            
        Returns:
            時間點列表
        """
        if end_date is None:
            end_date = self.now
        
        time_points = []
        current = end_date
        
        # 最近1個月：每週1個點
        for i in range(4):
            point = current - timedelta(weeks=i)
            if point >= start_date:
                time_points.append(point)
        
        # 1-3個月前：每2週1個點
        current = end_date - timedelta(days=30)  # 1個月前開始
        for i in range(4):
            point = current - timedelta(weeks=i*2)
            if point >= start_date:
                time_points.append(point)
        
        # 3-6個月前：每月1個點
        current = end_date - timedelta(days=90)  # 3個月前開始
        for i in range(3):
            point = current - timedelta(days=i*30)
            if point >= start_date:
                time_points.append(point)
        
        # 6個月以上：每2個月1個點
        current = end_date - timedelta(days=180)  # 6個月前開始
        while current >= start_date:
            time_points.append(current)
            current -= timedelta(days=60)  # 2個月
        
        # 確保包含開始日期
        if start_date not in time_points:
            time_points.append(start_date)
        
        # 排序並去重
        time_points = sorted(list(set(time_points)))
        
        return time_points
    
    def aggregate_data_by_time_points(self, data: List[Dict], time_points: List[datetime]) -> Dict[str, List]:
        """
        根據時間點聚合數據
        
        Args:
            data: 原始數據列表，每個元素包含時間信息
            time_points: 時間點列表
            
        Returns:
            聚合後的時間序列數據
        """
        # 初始化結果
        result = {
            'labels': [],
            'positive_data': [],
            'negative_data': [],
            'neutral_data': []
        }
        
        # 為每個時間點創建區間
        intervals = []
        for i in range(len(time_points)):
            if i == 0:
                # 第一個點：從開始到第一個時間點
                start = time_points[0] - timedelta(days=1)
                end = time_points[0]
            else:
                # 其他點：從上一個點到當前點
                start = time_points[i-1]
                end = time_points[i]
            
            intervals.append((start, end, time_points[i]))
        
        # 聚合數據
        for start, end, label_time in intervals:
            positive_count = 0
            negative_count = 0
            neutral_count = 0
            
            for item in data:
                item_time = self._parse_item_time(item)
                if item_time and start <= item_time <= end:
                    sentiment = item.get('情感標籤', 'NEUTRAL')
                    if sentiment == 'POSITIVE':
                        positive_count += 1
                    elif sentiment == 'NEGATIVE':
                        negative_count += 1
                    else:
                        neutral_count += 1
            
            # 生成標籤
            label = self._generate_time_label(label_time)
            
            result['labels'].append(label)
            result['positive_data'].append(positive_count)
            result['negative_data'].append(negative_count)
            result['neutral_data'].append(neutral_count)
        
        return result
    
    def optimize_monthly_stats(self, monthly_stats: Dict[str, Dict]) -> Dict[str, Any]:
        """
        優化月份統計數據，生成適合前端的時間序列格式
        
        Args:
            monthly_stats: 月份統計數據 {'2024-01': {...}, '2024-02': {...}}
            
        Returns:
            優化後的時間序列數據
        """
        if not monthly_stats:
            return {
                'labels': [],
                'datasets': [
                    {'label': '支持罷免', 'data': [], 'borderColor': '#dc2626'},
                    {'label': '反對罷免', 'data': [], 'borderColor': '#059669'},
                    {'label': '中性', 'data': [], 'borderColor': '#6b7280'}
                ]
            }
        
        # 解析月份並排序
        month_data = []
        for month_str, stats in monthly_stats.items():
            try:
                month_date = datetime.strptime(month_str, '%Y-%m')
                month_data.append((month_date, stats))
            except ValueError:
                continue
        
        month_data.sort(key=lambda x: x[0])
        
        # 生成動態時間點
        if month_data:
            start_date = month_data[0][0]
            end_date = month_data[-1][0]
            
            # 創建月份到統計的映射
            month_stats_map = {month.strftime('%Y-%m'): stats for month, stats in month_data}
            
            # 生成動態時間點（月份級別）
            time_points = self._generate_monthly_time_points(start_date, end_date)
            
            # 聚合數據
            labels = []
            positive_data = []
            negative_data = []
            neutral_data = []
            
            for time_point in time_points:
                month_key = time_point.strftime('%Y-%m')
                stats = month_stats_map.get(month_key, {})
                
                labels.append(self._generate_month_label(time_point))
                positive_data.append(stats.get('正面', 0))
                negative_data.append(stats.get('負面', 0))
                neutral_data.append(stats.get('中性', 0))
            
            return {
                'labels': labels,
                'datasets': [
                    {
                        'label': '支持罷免',
                        'data': negative_data,  # 負面 = 支持罷免
                        'borderColor': '#dc2626',
                        'backgroundColor': 'rgba(220, 38, 38, 0.1)',
                        'tension': 0.4
                    },
                    {
                        'label': '反對罷免',
                        'data': positive_data,  # 正面 = 反對罷免
                        'borderColor': '#059669',
                        'backgroundColor': 'rgba(5, 150, 105, 0.1)',
                        'tension': 0.4
                    },
                    {
                        'label': '中性',
                        'data': neutral_data,
                        'borderColor': '#6b7280',
                        'backgroundColor': 'rgba(107, 114, 128, 0.1)',
                        'tension': 0.4
                    }
                ]
            }
        
        return {'labels': [], 'datasets': []}
    
    def _generate_monthly_time_points(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """生成月份級別的動態時間點"""
        time_points = []
        current = end_date
        
        # 最近4個月：每月1個點
        for i in range(4):
            point = current - timedelta(days=i*30)
            if point >= start_date:
                time_points.append(point.replace(day=1))  # 月初
        
        # 4-8個月前：每2個月1個點
        current = end_date - timedelta(days=120)  # 4個月前
        for i in range(2):
            point = current - timedelta(days=i*60)
            if point >= start_date:
                time_points.append(point.replace(day=1))
        
        # 8個月以上：每3個月1個點
        current = end_date - timedelta(days=240)  # 8個月前
        while current >= start_date:
            time_points.append(current.replace(day=1))
            current -= timedelta(days=90)  # 3個月
        
        # 確保包含開始月份
        start_month = start_date.replace(day=1)
        if start_month not in time_points:
            time_points.append(start_month)
        
        # 排序並去重
        time_points = sorted(list(set(time_points)))
        
        return time_points
    
    def _parse_item_time(self, item: Dict) -> datetime:
        """解析數據項的時間"""
        time_str = item.get('時間', '') or item.get('time', '')
        if not time_str:
            return None
        
        try:
            # 嘗試多種格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(time_str, fmt)
                except ValueError:
                    continue
            
            return None
        except Exception:
            return None
    
    def _generate_time_label(self, time_point: datetime) -> str:
        """生成時間標籤"""
        now = datetime.now()
        diff = now - time_point
        
        if diff.days <= 7:
            return time_point.strftime('%m/%d')
        elif diff.days <= 30:
            return time_point.strftime('%m/%d')
        elif diff.days <= 90:
            return time_point.strftime('%Y/%m')
        else:
            return time_point.strftime('%Y/%m')
    
    def _generate_month_label(self, time_point: datetime) -> str:
        """生成月份標籤"""
        now = datetime.now()
        diff = now - time_point
        
        if diff.days <= 90:  # 3個月內顯示月份
            return time_point.strftime('%Y/%m')
        else:  # 3個月以上顯示年份
            return time_point.strftime('%Y')

def optimize_legislator_time_series(legislator_name: str, monthly_stats: Dict) -> Dict[str, Any]:
    """
    優化單個立委的時間序列數據
    
    Args:
        legislator_name: 立委姓名
        monthly_stats: 月份統計數據
        
    Returns:
        優化後的時間序列數據
    """
    optimizer = TimeSeriesOptimizer()
    return optimizer.optimize_monthly_stats(monthly_stats)

def main():
    """測試函數"""
    # 創建測試數據
    test_monthly_stats = {
        '2024-01': {'正面': 10, '負面': 15, '中性': 5},
        '2024-02': {'正面': 12, '負面': 18, '中性': 7},
        '2024-03': {'正面': 8, '負面': 22, '中性': 6},
        '2024-04': {'正面': 15, '負面': 12, '中性': 8},
        '2024-05': {'正面': 20, '負面': 10, '中性': 5},
        '2024-06': {'正面': 18, '負面': 14, '中性': 9},
        '2024-07': {'正面': 25, '負面': 8, '中性': 7}
    }
    
    optimizer = TimeSeriesOptimizer()
    result = optimizer.optimize_monthly_stats(test_monthly_stats)
    
    print("時間序列優化結果:")
    print(f"標籤: {result['labels']}")
    print(f"數據集數量: {len(result['datasets'])}")
    
    for dataset in result['datasets']:
        print(f"{dataset['label']}: {dataset['data']}")

if __name__ == "__main__":
    main()
