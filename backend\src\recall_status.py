"""
罷免案狀態判斷模組

此模組提供根據《公職人員選舉罷免法》判斷立委罷免案狀態的函數。
"""
import logging
import re
from datetime import datetime
from config import LEGISLATOR_ELECTORAL_COUNTS

# 設置日誌
logger = logging.getLogger(__name__)

# 罷免案狀態定義
RECALL_STATUSES = {
    "STAGE1_FAILED": {"status": "一階失敗", "description": "提案階段失敗，提議書未達選舉人總數1%"},
    "STAGE2_ONGOING": {"status": "二階進行中", "description": "第二階段連署收集或審核中"},
    "NEED_SUPPLEMENT": {"status": "需補件", "description": "連署書查核後需補件"},
    "STAGE2_FAILED": {"status": "二階失敗", "description": "連署階段失敗，連署書未達選舉人總數10%"},
    "STAGE2_SUCCESS": {"status": "二階成功", "description": "連署階段成功，連署書達選舉人總數10%以上"},
    "STAGE3_ONGOING": {"status": "三階進行中", "description": "罷免投票階段進行中"},
    "RECALL_SUCCESS": {"status": "罷免成功", "description": "罷免投票通過"},
    "RECALL_FAILED": {"status": "罷免失敗", "description": "罷免投票未通過"},
    "UNKNOWN": {"status": "狀態未知", "description": "無法判斷罷免案狀態"}
}

def extract_number(s):
    """從字串中提取數字，處理可能包含逗號的數字字串"""
    if not s or not isinstance(s, str):
        return 0
    
    # 移除所有非數字字元，但保留小數點
    result = re.sub(r'[^\d.]', '', s)
    return int(float(result)) if result else 0

def convert_recall_data_to_dict(recall_data):
    """
    將從 MongoDB 取得的罷免資料轉換為適合 determine_recall_status 函數使用的格式
    
    Args:
        recall_data (dict): MongoDB 中的罷免資料
        
    Returns:
        dict: 轉換後的字典，包含各階段數據
    """
    if not recall_data:
        return {}
    
    # 獲取所有數值數據
    result = {}
    
    # 第一階段提議書數量
    if "目前收件" in recall_data:
        stage1_count = extract_number(recall_data.get("目前收件", "0"))
        result["stage1_verified_count"] = stage1_count
    
    # 判斷第一階段是否已過期
    if "總天數" in recall_data and "已進行天數" in recall_data:
        total_days = extract_number(recall_data.get("總天數", "0"))
        days_passed = extract_number(recall_data.get("已進行天數", "0"))
        result["stage1_deadline_passed"] = days_passed >= total_days if total_days > 0 else False
    
    # 第二階段連署書數量
    if "累積連署人數" in recall_data:
        stage2_count = extract_number(recall_data.get("累積連署人數", "0"))
        result["stage2_verified_count"] = stage2_count
        
        # 如果進度大於等於 10% 且狀態不是網路聲量調查，則判定為第二階段
        if "進度" in recall_data:
            progress = extract_number(recall_data.get("進度", "0").replace("%", ""))
            if progress >= 10 and recall_data.get("狀態") != "網路聲量調查":
                result["current_stage"] = 2
        
    # 第三階段投票數據
    if "三階投票有效同意數" in recall_data:
        result["stage3_agree_votes"] = extract_number(recall_data.get("三階投票有效同意數", "0"))
        result["current_stage"] = 3
        result["stage3_voting_completed"] = True
    
    if "三階投票有效不同意數" in recall_data:
        result["stage3_disagree_votes"] = extract_number(recall_data.get("三階投票有效不同意數", "0"))
        
    # 依據狀態判斷階段
    status = recall_data.get("狀態", "")
    if "網路聲量調查" in status:
        result["current_stage"] = 1
    elif "罷免投票" in status and "三階投票有效同意數" not in recall_data:
        result["current_stage"] = 3
        result["stage3_voting_completed"] = False
    
    return result

def determine_recall_status(legislator_name, recall_data):
    """
    根據《公職人員選舉罷免法》判斷立委罷免案的狀態
    
    Args:
        legislator_name (str): 立法委員姓名
        recall_data (dict): 罷免案相關數據，包含以下可能的鍵值：
            - stage1_proposal_count: 第一階段提議書數量
            - stage1_verified_count: 第一階段查核通過數量
            - stage1_deadline_passed: 第一階段提交截止日是否已過
            - stage2_petition_count: 第二階段連署書數量
            - stage2_verified_count: 第二階段查核通過數量
            - stage2_supplements_needed: 需補件數量
            - stage2_deadline_passed: 第二階段提交截止日是否已過
            - stage2_verification_announced: 第二階段查核結果是否已公告
            - stage3_voting_date: 第三階段投票日期
            - stage3_voting_completed: 第三階段投票是否已完成
            - stage3_agree_votes: 同意罷免票數
            - stage3_disagree_votes: 不同意罷免票數
            - current_stage: 目前所處階段 (1, 2, 3)
            - latest_announcement: 最新公告內容或URL
            
    Returns:
        dict: 罷免案狀態，包含status和description鍵值
    """
    # 檢查立委是否在名單中
    if legislator_name not in LEGISLATOR_ELECTORAL_COUNTS:
        logger.warning(f"立委 {legislator_name} 不在選舉人數據庫中")
        return RECALL_STATUSES["UNKNOWN"]
    
    # 取得該選區選舉人總數
    electoral_count = LEGISLATOR_ELECTORAL_COUNTS[legislator_name]["electoral_count"]
    
    # 計算各階段門檻
    stage1_threshold = int(electoral_count * 0.01)  # 1%
    stage2_threshold = int(electoral_count * 0.10)  # 10%
    stage3_threshold = int(electoral_count * 0.25)  # 25%
    
    # 確保recall_data是字典
    if not isinstance(recall_data, dict):
        logger.warning(f"罷免資料格式錯誤: {type(recall_data)}")
        return RECALL_STATUSES["UNKNOWN"]
    
    # 取得目前階段 (默認為未知)
    current_stage = recall_data.get("current_stage", 0)
    
    # 第一階段判斷
    if current_stage == 1 or (current_stage == 0 and recall_data.get("stage1_deadline_passed", False)):
        stage1_verified = recall_data.get("stage1_verified_count", 0)
        if recall_data.get("stage1_deadline_passed", False) and stage1_verified < stage1_threshold:
            return RECALL_STATUSES["STAGE1_FAILED"]
    
    # 第二階段判斷
    if current_stage == 2:
        stage2_verified = recall_data.get("stage2_verified_count", 0)
        supplements_needed = recall_data.get("stage2_supplements_needed", 0)
        
        # 需補件狀態
        if (stage2_verified < stage2_threshold and 
            stage2_verified + supplements_needed >= stage2_threshold and
            not recall_data.get("stage2_deadline_passed", False)):
            return RECALL_STATUSES["NEED_SUPPLEMENT"]
        
        # 連署階段失敗
        if (recall_data.get("stage2_deadline_passed", False) or 
            recall_data.get("stage2_verification_announced", False)) and stage2_verified < stage2_threshold:
            return RECALL_STATUSES["STAGE2_FAILED"]
        
        # 連署階段成功
        if stage2_verified >= stage2_threshold:
            return RECALL_STATUSES["STAGE2_SUCCESS"]
        
        # 其他情況視為進行中
        return RECALL_STATUSES["STAGE2_ONGOING"]
    
    # 第三階段判斷
    if current_stage == 3:
        # 投票尚未完成
        if not recall_data.get("stage3_voting_completed", False):
            return RECALL_STATUSES["STAGE3_ONGOING"]
        
        # 投票已完成，判斷結果
        agree_votes = recall_data.get("stage3_agree_votes", 0)
        disagree_votes = recall_data.get("stage3_disagree_votes", 0)
        
        # 罷免成功條件：同意票 > 不同意票 且 同意票 >= 選舉人總數的25%
        if agree_votes > disagree_votes and agree_votes >= stage3_threshold:
            return RECALL_STATUSES["RECALL_SUCCESS"]
        else:
            return RECALL_STATUSES["RECALL_FAILED"]
    
    # 根據各項數據推斷當前狀態
    # 如果有第二階段數據但沒有明確指出階段
    if "stage2_verified_count" in recall_data:
        stage2_verified = recall_data.get("stage2_verified_count", 0)
        if stage2_verified >= stage2_threshold:
            # 推斷為第二階段成功
            return RECALL_STATUSES["STAGE2_SUCCESS"]
        elif recall_data.get("stage2_deadline_passed", False):
            # 推斷為第二階段失敗
            return RECALL_STATUSES["STAGE2_FAILED"]
        else:
            # 推斷為第二階段進行中
            return RECALL_STATUSES["STAGE2_ONGOING"]
    
    # 如果有投票日期或投票數據
    if "stage3_voting_date" in recall_data or "stage3_agree_votes" in recall_data:
        if not recall_data.get("stage3_voting_completed", False):
            return RECALL_STATUSES["STAGE3_ONGOING"]
        
        agree_votes = recall_data.get("stage3_agree_votes", 0)
        disagree_votes = recall_data.get("stage3_disagree_votes", 0)
        
        if agree_votes > disagree_votes and agree_votes >= stage3_threshold:
            return RECALL_STATUSES["RECALL_SUCCESS"]
        else:
            return RECALL_STATUSES["RECALL_FAILED"]
    
    # 無法判斷的情況
    return RECALL_STATUSES["UNKNOWN"]

def update_recall_status_in_mongodb(db, legislator_name):
    """
    更新單一立委的罷免案狀態
    
    Args:
        db: MongoDB 資料庫連接
        legislator_name (str): 立法委員姓名
        
    Returns:
        bool: 更新是否成功
    """
    try:
        # 從資料庫讀取立委資料
        legislators_col = db['legislators']
        legislator = legislators_col.find_one({"name": legislator_name})
        
        if not legislator or 'recall_data' not in legislator:
            logger.warning(f"立委 {legislator_name} 無罷免資料")
            return False
        
        # 轉換罷免資料格式
        recall_data = legislator['recall_data']
        converted_data = convert_recall_data_to_dict(recall_data)
        
        # 判斷罷免案狀態
        status_result = determine_recall_status(legislator_name, converted_data)
        
        # 更新資料庫
        update_result = legislators_col.update_one(
            {"_id": legislator["_id"]},
            {"$set": {
                "recall_data.罷免狀態": status_result["status"],
                "recall_data.罷免狀態描述": status_result["description"],
                "recall_data.最後更新時間": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }}
        )
        
        if update_result.modified_count > 0:
            logger.info(f"已更新 {legislator_name} 的罷免狀態為: {status_result['status']}")
            return True
        else:
            logger.info(f"{legislator_name} 的罷免狀態無需更新")
            return True
            
    except Exception as e:
        logger.error(f"更新 {legislator_name} 的罷免狀態時發生錯誤: {e}")
        return False

def update_all_recall_statuses(db):
    """
    更新所有立委的罷免案狀態
    
    Args:
        db: MongoDB 資料庫連接
        
    Returns:
        tuple: (成功更新數, 失敗數)
    """
    try:
        # 取得所有有罷免資料的立委
        legislators_col = db['legislators']
        legislators = list(legislators_col.find({"recall_data": {"$exists": True}}))
        
        success_count = 0
        error_count = 0
        
        for legislator in legislators:
            name = legislator.get("name")
            if not name:
                continue
                
            if update_recall_status_in_mongodb(db, name):
                success_count += 1
            else:
                error_count += 1
                
        logger.info(f"批次更新完成：成功更新 {success_count} 筆，失敗 {error_count} 筆")
        return (success_count, error_count)
        
    except Exception as e:
        logger.error(f"批次更新罷免狀態時發生錯誤: {e}")
        return (0, 0)
