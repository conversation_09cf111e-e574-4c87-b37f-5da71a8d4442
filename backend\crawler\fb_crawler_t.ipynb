from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from PIL import Image
import time
import os

from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import NoSuchElementException
import csv
import os
import time
import sys
import re
from selenium.webdriver.common.keys import Keys
import json
import os
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time



driver = webdriver.Chrome()

# 打開你的目標頁面
driver.get("https://www.facebook.com/yeh.seafood/?locale=zh_TW")
driver.maximize_window()
time.sleep(5)  # 等待動態元素載入

def double_click_on_comment1(driver, retry=3):
    """
    嘗試點擊「最相關」元素，失敗時最多重試3次
    """
    for attempt in range(1, retry + 1):
        try:
            element = driver.find_element(By.XPATH, "//*[contains(text(), '最相關')]")
            action = ActionChains(driver)
            action.click(element).perform()
            return True
        except Exception as e:
            print(f"第 {attempt} 次點擊『最相關』元素失敗: {e}")
            if attempt == retry:
                print("已達最大重試次數，放棄點擊。")
                return False
            time.sleep(1)  # 等待一秒後重試

def double_click_on_comment2(driver, retry=3):
    """
    嘗試點擊「所有留言」元素，失敗時最多重試3次
    """
    for attempt in range(1, retry + 1):
        try:
            element = driver.find_element(By.XPATH, "//*[contains(text(), '所有留言')]")
            action = ActionChains(driver)
            action.click(element).perform()
            return True
        except Exception as e:
            print(f"第 {attempt} 次點擊『所有留言』元素失敗: {e}")
            if attempt == retry:
                print("已達最大重試次數，放棄點擊。")
                return False
            time.sleep(1)  # 等待一秒後重試
def process_combined_text(driver):
    """
    根據給定的 CSS 選擇器從頁面中抓取文本，處理並返回最終的文本
    """
    # 使用 CSS 選擇器抓取元素
    elements = driver.find_elements(By.CSS_SELECTOR, '.x193iq5w.xeuugli.x13faqbe.x1vvkbs.x1xmvt09.x1lliihq.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.xudqn12.x3x7a5m.x6prxxf.xvq8zen.xo1l8bm.xzsf02u.x1yc453h')
    # 用來存儲分割後的文字
    all_text_split = []

    # 遍歷每個抓取到的元素並處理文字內容
    for element in elements:
        # 抓取每個元素的文本內容
        text = element.text

        # 使用正規表達式將文本按換行符 \n 分割
        split_text = re.split(r'\n+', text.strip())

        # 把分割後的結果加入 all_text_split 列表
        all_text_split.extend(split_text)

    # 檢查最後一個空字串的位置並刪除之前的資料
    if '' in all_text_split:
        last_empty_index = len(all_text_split) - 1 - all_text_split[::-1].index('')
        # 刪除最後一個空字串前的所有資料
        all_text_split = all_text_split[last_empty_index + 1:]

    # 將處理後的文本集合成一個字符串
    combined_text = ''.join(all_text_split)

    # 檢查是否存在 "…… 查看更多"，並進行替換
    if '…… 查看更多' in combined_text:
        combined_text = combined_text.replace('…… 查看更多', '')

    # 返回處理後的文本
    return combined_text
def scroll_to_bottom_kk(driver, scroll_times=1000, max_no_change_times=5500):
    body = driver.find_element(By.TAG_NAME, 'body')
    
    no_change_count = 0  # 初始化页面高度没有变化的计数器
    
    for _ in range(scroll_times):
        previous_height = driver.execute_script("return document.body.scrollHeight")
        
        # 按下「下」鍵來滾動頁面
        
        action = ActionChains(driver)
        action.send_keys(Keys.ARROW_DOWN).perform()
        
        new_height = driver.execute_script("return document.body.scrollHeight")
        #tab_to_target(driver, max_tabs=7, target_text="查看")
        if new_height == previous_height:
            no_change_count += 1
            time.sleep(0.2)
            if no_change_count >= max_no_change_times:
                # 如果页面高度连续多次没有变化，说明已经到底部，结束滚动
                print("检测到页面底部，停止滚动。")
                break
        else:
            no_change_count = 0  # 如果高度发生变化，重置计数器
    
    print("滚动结束。")
    
def click_reply_elements(driver):
    """
    遍歷指定容器內的所有元素，並點擊包含「則回覆」的元素。
    
    參數:
        driver: Selenium WebDriver 物件，用於與瀏覽器交互。
        
    返回:
        bool: 如果找到並點擊了至少一個包含「則回覆」的元素，則返回 True；否則返回 False。
    """
    try:
        # 初始化找到並點擊的標記
        found_and_clicked = False

        # 找到特定範圍的容器元素
        container = driver.find_element(By.CSS_SELECTOR, ".html-div.x11i5rnm.xat24cr.x1mh8g0r.xexx8yu.x4uap5.x18d9i69.xkhd6sd.x1gslohp")

        # 使用 JavaScript 遍歷容器內的所有元素，篩選出包含「則回覆」的元素
        while True:  # 使用迴圈持續查找，直到不再找到新元素
            elements = driver.execute_script("""
                let container = arguments[0];
                let matches = [];
                container.querySelectorAll('*').forEach(function(el) {
                    if (el.innerText && el.innerText.includes("則回覆")) {
                        matches.push(el);
                    }
                });
                return matches;
            """, container)

            # 遍歷找到的所有匹配元素
            if not elements:
                break  # 沒有找到新的「則回覆」元素，退出迴圈

            for element in elements:
                try:
                    driver.execute_script("arguments[0].scrollIntoView({ behavior: 'smooth', block: 'center' });", element)
                    time.sleep(0.5)  # 滾動後的等待
                    driver.execute_script("arguments[0].click();", element)  # 點擊元素
                    #print(f"成功點擊包含『則回覆』的元素：{element.text}")
                    found_and_clicked = True  # 設置標記為找到並點擊過元素
                    time.sleep(1)  # 等待頁面更新
                    
                except Exception as e:
                    print(f"操作失敗，跳過元素: {e}")
        found_and_clicked = 0
        return found_and_clicked

    except Exception as e:
        found_and_clicked = False
        pass
def click_all_reply_buttons(driver, max_rounds=10):
    """
    遍歷所有可見的「則回覆」按鈕並點擊展開。
    
    參數:
        driver: Selenium WebDriver 物件
        max_rounds: 最多執行的迴圈次數，避免無限迴圈

    回傳:
        int: 成功點擊的按鈕數量
    """
    clicked_total = 0
    round_count = 0

    while round_count < max_rounds:
        round_count += 1
        print(f"🔁 第 {round_count} 回合查找『則回覆』...")

        # 找出所有顯示中的「則回覆」按鈕
        reply_buttons = [
            el for el in driver.find_elements(
                By.XPATH, "//span[normalize-space(text()) and contains(text(), '則回覆')]"
            ) if el.is_displayed()
        ]

        if not reply_buttons:
            print("✅ 沒有更多『則回覆』可點擊，結束。")
            break

        for btn in reply_buttons:
            try:
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", btn)
                time.sleep(0.3)
                driver.execute_script("arguments[0].click();", btn)
                print(f"✅ 已點擊一個『則回覆』")
                clicked_total += 1
                time.sleep(1.0)  # 等待留言載入
            except Exception as e:
                print(f"❌ 點擊失敗，跳過：{e}")
                continue

    print(f"📌 共點擊 {clicked_total} 個『則回覆』按鈕")
    return clicked_total

    
def get_last_comment_count(driver):
    # 使用 XPath 查找包含 '則留言' 的元素
    comments_elements = driver.find_elements(By.XPATH, "//*[contains(text(),'則留言')]")
    
    # 初始化一个变量来存储最后一个匹配的留言数
    last_comment_count = None
    
    # 遍历找到的元素，提取最后一个数字并保存
    for element in comments_elements:
        text = element.text
        # 使用正则表达式查找 '則留言' 前面的数字，支持千位分隔符
        match = re.search(r'([\d,]+)\s*則留言', text)
        if match:
            # 去除數字中的逗號，轉換為整數，並儲存為最後一個找到的數字
            last_comment_count = int(match.group(1).replace(',', ''))
    
    # 返回最后一个留言数
    return last_comment_count

        
def click_display_block_right_0px_Chang(driver):
    try:
        # 使用 XPath 查找包含“新到舊”文本的按鈕
        element = driver.find_element(By.XPATH, "//*[contains(text(), '新到舊')]")
        
        # 使用 ActionChains 執行雙擊操作
        action = ActionChains(driver)
        action.double_click(element).perform()

        print("成功雙擊包含『新到舊』的按鈕。")

    except Exception as e:
        print(f"雙擊失敗: {e}")



def extract_comments(driver, post_info, filename='12.json'):
    """
    從網頁中提取評論，將評論、時間、按讚數和使用者名稱分別添加到各自的列表中，並儲存為 JSON 文件。
    
    參數:
        driver: Selenium WebDriver 物件，用於與瀏覽器交互。
        filename: 儲存 JSON 文件的名稱，預設為 '12.json'。

    返回:
        dict: 包含 `comments_list`, `times_list`, `likes_list`, `User_list` 的字典。
    """
    print(f"=== 開始提取評論 ===")
    print(f"貼文資訊: {post_info}")
    print(f"檔案名稱: {filename}")
    
    comments_list = []
    times_list = []
    likes_list = []
    User_list = []

    try:
        # 使用 XPath 定位包含指定類名的評論元素
        print("正在尋找評論元素...")
        elements = driver.find_elements(
    By.XPATH,
    "//div[contains(@class, 'x1n2onr6') and contains(@class, 'x1g0dm76') and contains(@class, 'x1iorvi4')]"
)
        print(f"找到 {len(elements)} 個評論元素")
        
        # 遍歷元素並清理文本後添加到各列表中
        for i, element in enumerate(elements):
            print(f"\n--- 處理第 {i+1} 個元素 ---")
            text = element.text
            print(f"原始文本: {text[:200]}...")  # 只顯示前200字元
            
            likes = None
            user = None

            # 清除特定的無用文本
            text = re.sub(r'\s*·\s*', '', text)
            for phrase in ['追蹤', '讚', '回覆', '已編輯', '頭號粉絲', '翻譯年糕']:
                text = text.replace(phrase, '')
            print(f"清理後文本: {text[:200]}...")

            # 根據行分割並過濾空行
            text_lines = [line for line in text.splitlines() if line]
            print(f"分割後行數: {len(text_lines)}")
            print(f"各行內容: {text_lines}")
            
            if len(text_lines) < 3:
                print("行數不足3，清空處理")
                text_lines = []  # 若行數不足3，清空

            # 確定包含時間的行索引
            indices_with_days = [index for index, item in enumerate(text_lines) if re.search(r'(\d+\s*[天週分小時])', item)]
            print(f"包含時間的行索引: {indices_with_days}")
        
            # 處理留言內容
            if len(text_lines) == 3:
                comment_text = text_lines[1]
                print(f"3行模式，留言內容: {comment_text}")
            else:
                if indices_with_days:
                    comment_text = " ".join(text_lines[1:indices_with_days[-1]])  # 合併為一條留言
                    print(f"多行模式，留言內容: {comment_text}")
                else:
                    comment_text = " ".join(text_lines)
                    print(f"無時間模式，留言內容: {comment_text}")
        
            if comment_text:
                comments_list.append(comment_text)  # 非空才加入
                print(f"已添加留言: {comment_text[:100]}...")
            else:
                print("留言內容為空，跳過")
                
            # 處理時間
            if indices_with_days:
                first_time = text_lines[indices_with_days[-1]]
                print(f"找到時間: {first_time}")
                calculated_date = calculate_date(first_time)
                formatted_date = calculated_date.strftime('%Y-%m-%d')
                times_list.append(formatted_date)
                print(f"格式化時間: {formatted_date}")
            else:
                print("未找到時間資訊")
                
            # 處理使用者名稱
            if text_lines:
                user = text_lines[0]
                print(f"使用者名稱: {user}")
            if user:
                User_list.append(user)
                print(f"已添加使用者: {user}")
            else:
                print("未找到使用者名稱")

            # 處理按讚數
            last_time_value = text_lines[-1] if text_lines else ""
            print(f"最後一行值: {last_time_value}")
            if last_time_value:
                if re.search(r'[天分週小時]', last_time_value):
                    likes = 0  # 包含時間單位則按讚數設為 0
                    print("包含時間單位，按讚數設為 0")
                elif last_time_value.isdigit():
                    likes = int(last_time_value)
                    print(f"數字格式，按讚數: {likes}")
                else:
                    likes = None
                    print("無法解析按讚數")
            if likes is not None:
                likes_list.append(likes)
                print(f"已添加按讚數: {likes}")
            else:
                print("按讚數為 None，跳過")

        print(f"\n=== 提取完成 ===")
        print(f"留言數量: {len(comments_list)}")
        print(f"時間數量: {len(times_list)}")
        print(f"按讚數數量: {len(likes_list)}")
        print(f"使用者數量: {len(User_list)}")
        
        # 顯示前幾個結果作為範例
        for i in range(min(3, len(comments_list))):
            print(f"範例 {i+1}:")
            print(f"  使用者: {User_list[i] if i < len(User_list) else 'N/A'}")
            print(f"  留言: {comments_list[i] if i < len(comments_list) else 'N/A'}")
            print(f"  時間: {times_list[i] if i < len(times_list) else 'N/A'}")
            print(f"  按讚: {likes_list[i] if i < len(likes_list) else 'N/A'}")

        # 使用 organize_comments 將結果儲存為 JSON 文件
        print("\n開始組織評論結構...")
        organized_data = organize_comments(comments_list, times_list, likes_list, User_list, post_info, save_to_file=True, filename=filename)
        return organized_data
        
    except Exception as e:
        print(f"抓取內容失敗: {e}")
        import traceback
        print(f"詳細錯誤: {traceback.format_exc()}")

def organize_comments(comments_list, times_list, likes_list, User_list, post_info, save_to_file=False, filename="comments_structure.json"):
    """
    將留言組織為單層平鋪結構，並儲存為簡化 JSON 格式。

    格式範例：
    {
        "標題": "Re: [貼文標題]",
        "留言內容": "留言文字",
        "情感標籤": "",
        "情緒": "",
        "日期": "2025-07-08",
        "用戶": "使用者名稱",
        "上文留言": "被回覆的使用者名稱"（如果有）
    }
    """
    print(f"=== 開始組織評論 ===")
    print(f"輸入資料統計:")
    print(f"  留言數量: {len(comments_list)}")
    print(f"  時間數量: {len(times_list)}")
    print(f"  按讚數數量: {len(likes_list)}")
    print(f"  使用者數量: {len(User_list)}")

    flat_comment_list = []

    for i, comment in enumerate(comments_list):
        user = User_list[i] if i < len(User_list) else ""
        time_str = times_list[i] if i < len(times_list) else ""
        reply_to = ""

        # 嘗試找出「上文留言」：若留言內容中包含其他用戶
        for other_user in User_list:
            if other_user in comment and other_user != user:
                reply_to = other_user
                break

        flat_comment = {
            "標題": f"{post_info}",
            "留言內容": comment,
            "情感標籤": "",
            "情緒": "",
            "日期": time_str,
            "用戶": user,
            "上文留言": f"{post_info}"
        }
        flat_comment_list.append(flat_comment)

    if save_to_file:
        if os.path.exists(filename):
            with open(filename, "r", encoding="utf-8") as f:
                existing_data = json.load(f)
            existing_data.extend(flat_comment_list)
        else:
            existing_data = flat_comment_list

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)

        print(f"✅ JSON 已儲存為 {filename}")

    return flat_comment_list

#scrape_and_save_to_csv(driver, 'output32.csv')
def calculate_date(time_string):
    # 获取当前时间
    now = datetime.now()
   

    # 使用正则表达式匹配时间类型和数值
    week_match = re.search(r'(\d+)\s*週', time_string)
    day_match = re.search(r'(\d+)\s*天', time_string)
    hour_match = re.search(r'(\d+)\s*時', time_string)
    minute_match = re.search(r'(\d+)\s*分', time_string)

    # 初始化用于存储最终时间的变量
    calculated_date = now

    # 根据匹配到的时间类型调整时间
    if week_match:
        weeks = int(week_match.group(1))
        calculated_date = now - timedelta(weeks=weeks)
    elif day_match:
        days = int(day_match.group(1))
        calculated_date = now - timedelta(days=days)
    elif hour_match:
        hours = int(hour_match.group(1))
        calculated_date = now - timedelta(hours=hours)
    elif minute_match:
        minutes = int(minute_match.group(1))
        calculated_date = now - timedelta(minutes=minutes)

    
    # 返回最终计算后的时间
    return calculated_date


#data = extract_comments(driver,"77", filename='comments_data13.json')

from selenium.webdriver.common.by import By
import time

def test_extract_by_class(driver):
    """
    測試用：抓取所有 class="x78zum5 xdt5ytf" 的元素，列印其文字。
    """
    try:
        print("🔍 開始抓取 class='x78zum5 xdt5ytf' 的元素...")
        elements =  driver.find_elements(
    By.XPATH,
    "//div[contains(@class, 'x1n2onr6') and contains(@class, 'x1g0dm76') and contains(@class, 'x1iorvi4')]"
)
        print(f"✅ 共找到 {len(elements)} 個元素")

        for i, el in enumerate(elements):
            text = el.text.strip()
            print(f"\n--- 元素 {i+1} ---")
            print(text if text else "(無文字)")
            print("----------------------")

    except Exception as e:
        print(f"⚠️ 抓取時發生錯誤：{e}")
#test_extract_by_class(driver)


commt_ct =  get_last_comment_count(driver)
print(commt_ct)

def run_post_crawler(driver,filename):
    """
    執行抓取貼文留言的流程。
    """
    double_click_on_comment1(driver)
    double_click_on_comment2(driver)
    time.sleep(1)
    click_display_block_right_0px_Chang(driver)
    comb_text = process_combined_text(driver)
    print(comb_text)
    commt_ct = get_last_comment_count(driver)
    fnct = commt_ct * 15
    scroll_to_bottom_kk(driver, fnct, fnct / 10)
    #for i in range(500):
    #    print(f"第 {i+1} 次執行 click_reply_elements")
        #click_all_reply_buttons(driver)
   # data = extract_comments(driver, comb_text, filename='葉元之.json')  # 4月七日
    data = extract_comments(driver, comb_text, filename)  # 4月七日
    return data


def click_and_scrape_each_modal(driver):
    """
    依序點擊每個「則留言」，在跳出的 modal 裡擷取留言資料，再關閉 modal。
    """
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.common.action_chains import ActionChains
    import time

    # 抓取所有「則留言」的元素
    comment_buttons = driver.find_elements(By.XPATH, "//span[contains(text(), '則留言')]")
    print(f"共找到 {len(comment_buttons)} 個留言按鈕")

    for index in range(100):  # 最多處理 100 筆（或依需求改）
        try:
            # 每次都重新抓元素列表，並選擇第 index 個（還沒被點擊的）
            comment_buttons = driver.find_elements(By.XPATH, "//span[contains(text(), '則留言')]")

            if index >= len(comment_buttons):
                print("✅ 沒有更多留言按鈕可以點擊了")
                break

            button = comment_buttons[index]
            print(f"\n🔘 點擊第 {index+1} 個留言")

            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
            time.sleep(0.5)
            button.click()
            time.sleep(2.5)

            # 在這裡抓留言資料（你自己的函式）
            # extract_comments(driver, ...)
            run_post_crawler(driver,"鄭正鈐.json")
            # 關閉 modal
            ActionChains(driver).send_keys(Keys.ESCAPE).perform()
            print("❌ 已關閉 modal")
            time.sleep(1)
            driver.execute_script("window.scrollBy(0, 1000);")
            time.sleep(1)  # 等待滾動動畫與 DOM 更新

        except Exception as e:
            print(f"⚠️ 點擊第 {index+1} 個留言時發生錯誤：{e}")
            continue

click_and_scrape_each_modal(driver)

driver.execute_script("window.scrollBy(0, 1000);")
time.sleep(1)  # 等待滾動動畫與 DOM 更新

def click_and_scrape_each_modal(driver, max_comments=100):
    """
    不斷向下滾動並點擊「則留言」按鈕，每個都點開、抓留言、再關閉 modal。
    當累積留言數達到 max_comments 時停止。
    """
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.common.action_chains import ActionChains
    import time

    clicked_set = set()
    scroll_attempt = 0
    max_scroll_try = 20
    total_clicked = 0
    total_comments = 0

    while scroll_attempt < max_scroll_try:
        # 找出所有「則留言」的按鈕
        comment_buttons = driver.find_elements(By.XPATH, "//span[contains(text(), '則留言')]")
        print(f"\n🔍 畫面上找到 {len(comment_buttons)} 個留言按鈕")

        new_found = False

        for btn in comment_buttons:
            try:
                btn_text = btn.text.strip()
                btn_html = btn.get_attribute("outerHTML")

                if btn_html in clicked_set:
                    continue  # 跳過已點擊過的

                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", btn)
                time.sleep(0.8)
                btn.click()
                print(f"\n🔘 點擊留言按鈕：{btn_text}")
                clicked_set.add(btn_html)
                new_found = True

           

                time.sleep(3)
                run_post_crawler(driver,"魯明哲.json")
                # 關閉 modal
                ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                time.sleep(1)

                # 滾動一段距離
                driver.execute_script("window.scrollBy(0, 1000);")
                time.sleep(1)

                if total_comments >= max_comments:
                    print(f"\n🎯 已抓滿 {max_comments} 則留言，停止執行")
                    return

            except Exception as e:
                print(f"⚠️ 點擊留言時發生錯誤：{e}")
                continue

        if not new_found:
            scroll_attempt += 1
            print(f"⬇️ 沒有新按鈕，第 {scroll_attempt} 次向下滾動...")
            driver.execute_script("window.scrollBy(0, 1200);")
            time.sleep(2)
        else:
            scroll_attempt = 0  # 重置滾動次數

    print(f"\n🏁 總共成功抓取留言 {total_comments} 則，點擊 {total_clicked} 個 modal。")
click_and_scrape_each_modal(driver)

from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
import time

def click_and_scrape_each_modal(driver, max_comments=100, json_name="default.json"):
    """
    爬取當前頁面所有「則留言」modal，直到 max_comments，儲存為指定 json。
    """
    clicked_set = set()
    scroll_attempt = 0
    max_scroll_try = 20
    total_comments = 0

    while scroll_attempt < max_scroll_try:
        comment_buttons = driver.find_elements(By.XPATH, "//span[contains(text(), '則留言')]")
        print(f"\n🔍 畫面上找到 {len(comment_buttons)} 個留言按鈕")

        new_found = False

        for btn in comment_buttons:
            try:
                btn_text = btn.text.strip()
                btn_html = btn.get_attribute("outerHTML")

                if btn_html in clicked_set:
                    continue

                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", btn)
                time.sleep(0.8)
                btn.click()
                print(f"\n🔘 點擊留言按鈕：{btn_text}")
                clicked_set.add(btn_html)
                new_found = True

                time.sleep(3)
                run_post_crawler(driver, f"{json_name}.json")

                ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                time.sleep(1)

                driver.execute_script("window.scrollBy(0, 1000);")
                time.sleep(1)

                total_comments += 1
                if total_comments >= max_comments:
                    print(f"\n🎯 已抓滿 {max_comments} 則留言，停止執行")
                    return

            except Exception as e:
                print(f"⚠️ 點擊留言時發生錯誤：{e}")
                continue

        if not new_found:
            scroll_attempt += 1
            print(f"⬇️ 沒有新按鈕，第 {scroll_attempt} 次向下滾動...")
            driver.execute_script("window.scrollBy(0, 1200);")
            time.sleep(2)
        else:
            scroll_attempt = 0

    print(f"\n🏁 抓取完成，共 {total_comments} 則留言。")


def click_and_scrape_all_urls(driver, url_dict, max_comments_per_url=100):
    """
    依據 {檔名: 貼文網址} 格式進行爬取，每篇留言存入對應 JSON，間隔 3 分鐘。
    """
    for idx, (json_name, url) in enumerate(url_dict.items()):
        print(f"\n🔗 開始處理第 {idx+1} 篇貼文：{json_name} -> {url}")
        try:
            driver.get(url)
            time.sleep(5)  # 等待頁面載入
            click_and_scrape_each_modal(driver, max_comments=max_comments_per_url, json_name=json_name)
        except Exception as e:
            print(f"❌ 處理網址時發生錯誤：{e}")
        print(f"😴 休息 3 分鐘再進入下一篇...\n")
        time.sleep(180)

    print("✅ 所有網址處理完畢！")


# ✅ 建立網址對應 JSON 檔名的 dict
url_dict = {
   # "林德福": "https://www.facebook.com/lintefu/",
   #"林思銘": "https://www.facebook.com/linsm.hc/",
    #"林沛祥": "https://www.facebook.com/keelungup/",
   # "洪孟楷": "https://www.facebook.com/MongKaiHUNG/",
  #  "涂權吉": "https://www.facebook.com/agi0511/",
  #  "牛煦庭": "https://www.facebook.com/18NIUstart/",  
    #"王鴻薇": "https://www.facebook.com/hungwei.org/",  
   # "羅明才": "https://www.facebook.com/mtlo.tw/",  
 #3ˇˇˇˇ   "羅智強": "https://www.facebook.com/debatelo/",  
    #"萬美玲": "https://www.facebook.com/wanmeiling.tw/",  
    "賴士葆": "https://www.facebook.com/abao.tw/",  
    "邱若華": "https://www.facebook.com/Tai.Chill2022/",
    "徐巧芯": "https://www.facebook.com/HsuChiaoHsin/",
    "徐欣瑩": "https://www.facebook.com/shesinging.tw/",
    "李彥秀": "https://www.facebook.com/ashow.tpe/",
}

# ✅ 呼叫
click_and_scrape_all_urls(driver, url_dict, max_comments_per_url=100)
