{"cells": [{"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from PIL import Image\n", "import ddddocr\n", "import time\n", "import pytesseract\n", "import os\n", "\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from selenium.common.exceptions import NoSuchElementException\n", "import csv\n", "import os\n", "import time\n", "import sys\n", "import re\n", "from selenium.webdriver.common.keys import Keys\n", "import json\n", "import os\n", "from datetime import datetime, timedelta\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["driver = webdriver.Chrome()\n", "\n", "# 打開你的目標頁面\n", "driver.get(\"https://www.facebook.com/yeh.seafood/?locale=zh_TW\")\n", "driver.maximize_window()\n", "time.sleep(5)  # 等待動態元素載入"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["def double_click_on_comment1(driver):\n", "    try:\n", "        # 使用 XPath 定位包含 '則留言' 文本的元素\n", "        element = driver.find_element(By.XPATH, \"//*[contains(text(), '最相關')]\")\n", "        # 使用 ActionChains 进行双击操作\n", "        action = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "        action.click(element).perform()\n", "        return True\n", "    except Exception as e:\n", "        print(f\"双击 '則留言' 元素失败: {e}\")\n", "        return False\n", "def double_click_on_comment2(driver):\n", "    try:\n", "        # 使用 XPath 定位包含 '則留言' 文本的元素\n", "        element = driver.find_element(By.XPATH, \"//*[contains(text(), '所有留言')]\")\n", "        # 使用 ActionChains 进行双击操作\n", "        action = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "        action.click(element).perform()\n", "        return True\n", "    except Exception as e:\n", "        print(f\"双击 '則留言' 元素失败: {e}\")\n", "        return False\n", "def process_combined_text(driver):\n", "    \"\"\"\n", "    根據給定的 CSS 選擇器從頁面中抓取文本，處理並返回最終的文本\n", "    \"\"\"\n", "    # 使用 CSS 選擇器抓取元素\n", "    elements = driver.find_elements(By.CSS_SELECTOR, '.x193iq5w.xeuugli.x13faqbe.x1vvkbs.x1xmvt09.x1lliihq.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.xudqn12.x3x7a5m.x6prxxf.xvq8zen.xo1l8bm.xzsf02u.x1yc453h')\n", "    # 用來存儲分割後的文字\n", "    all_text_split = []\n", "\n", "    # 遍歷每個抓取到的元素並處理文字內容\n", "    for element in elements:\n", "        # 抓取每個元素的文本內容\n", "        text = element.text\n", "\n", "        # 使用正規表達式將文本按換行符 \\n 分割\n", "        split_text = re.split(r'\\n+', text.strip())\n", "\n", "        # 把分割後的結果加入 all_text_split 列表\n", "        all_text_split.extend(split_text)\n", "\n", "    # 檢查最後一個空字串的位置並刪除之前的資料\n", "    if '' in all_text_split:\n", "        last_empty_index = len(all_text_split) - 1 - all_text_split[::-1].index('')\n", "        # 刪除最後一個空字串前的所有資料\n", "        all_text_split = all_text_split[last_empty_index + 1:]\n", "\n", "    # 將處理後的文本集合成一個字符串\n", "    combined_text = ''.join(all_text_split)\n", "\n", "    # 檢查是否存在 \"…… 查看更多\"，並進行替換\n", "    if '…… 查看更多' in combined_text:\n", "        combined_text = combined_text.replace('…… 查看更多', '')\n", "\n", "    # 返回處理後的文本\n", "    return combined_text\n", "def scroll_to_bottom_kk(driver, scroll_times=1000, max_no_change_times=5500):\n", "    body = driver.find_element(By.TAG_NAME, 'body')\n", "    \n", "    no_change_count = 0  # 初始化页面高度没有变化的计数器\n", "    \n", "    for _ in range(scroll_times):\n", "        previous_height = driver.execute_script(\"return document.body.scrollHeight\")\n", "        \n", "        # 按下「下」鍵來滾動頁面\n", "        \n", "        action = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "        action.send_keys(Keys.ARROW_DOWN).perform()\n", "        \n", "        new_height = driver.execute_script(\"return document.body.scrollHeight\")\n", "        #tab_to_target(driver, max_tabs=7, target_text=\"查看\")\n", "        if new_height == previous_height:\n", "            no_change_count += 1\n", "            time.sleep(0.2)\n", "            if no_change_count >= max_no_change_times:\n", "                # 如果页面高度连续多次没有变化，说明已经到底部，结束滚动\n", "                print(\"检测到页面底部，停止滚动。\")\n", "                break\n", "        else:\n", "            no_change_count = 0  # 如果高度发生变化，重置计数器\n", "    \n", "    print(\"滚动结束。\")\n", "    \n", "def click_reply_elements(driver):\n", "    \"\"\"\n", "    遍歷指定容器內的所有元素，並點擊包含「則回覆」的元素。\n", "    \n", "    參數:\n", "        driver: Selenium WebDriver 物件，用於與瀏覽器交互。\n", "        \n", "    返回:\n", "        bool: 如果找到並點擊了至少一個包含「則回覆」的元素，則返回 True；否則返回 False。\n", "    \"\"\"\n", "    try:\n", "        # 初始化找到並點擊的標記\n", "        found_and_clicked = False\n", "\n", "        # 找到特定範圍的容器元素\n", "        container = driver.find_element(By.CSS_SELECTOR, \".html-div.x11i5rnm.xat24cr.x1mh8g0r.xexx8yu.x4uap5.x18d9i69.xkhd6sd.x1gslohp\")\n", "\n", "        # 使用 JavaScript 遍歷容器內的所有元素，篩選出包含「則回覆」的元素\n", "        while True:  # 使用迴圈持續查找，直到不再找到新元素\n", "            elements = driver.execute_script(\"\"\"\n", "                let container = arguments[0];\n", "                let matches = [];\n", "                container.querySelectorAll('*').forEach(function(el) {\n", "                    if (el.innerText && el.innerText.includes(\"則回覆\")) {\n", "                        matches.push(el);\n", "                    }\n", "                });\n", "                return matches;\n", "            \"\"\", container)\n", "\n", "            # 遍歷找到的所有匹配元素\n", "            if not elements:\n", "                break  # 沒有找到新的「則回覆」元素，退出迴圈\n", "\n", "            for element in elements:\n", "                try:\n", "                    driver.execute_script(\"arguments[0].scrollIntoView({ behavior: 'smooth', block: 'center' });\", element)\n", "                    time.sleep(0.5)  # 滾動後的等待\n", "                    driver.execute_script(\"arguments[0].click();\", element)  # 點擊元素\n", "                    #print(f\"成功點擊包含『則回覆』的元素：{element.text}\")\n", "                    found_and_clicked = True  # 設置標記為找到並點擊過元素\n", "                    time.sleep(1)  # 等待頁面更新\n", "                    \n", "                except Exception as e:\n", "                    print(f\"操作失敗，跳過元素: {e}\")\n", "        found_and_clicked = 0\n", "        return found_and_clicked\n", "\n", "    except Exception as e:\n", "        found_and_clicked = False\n", "        pass\n", "def click_all_reply_buttons(driver, max_rounds=10):\n", "    \"\"\"\n", "    遍歷所有可見的「則回覆」按鈕並點擊展開。\n", "    \n", "    參數:\n", "        driver: <PERSON><PERSON><PERSON> WebDriver 物件\n", "        max_rounds: 最多執行的迴圈次數，避免無限迴圈\n", "\n", "    回傳:\n", "        int: 成功點擊的按鈕數量\n", "    \"\"\"\n", "    clicked_total = 0\n", "    round_count = 0\n", "\n", "    while round_count < max_rounds:\n", "        round_count += 1\n", "        print(f\"🔁 第 {round_count} 回合查找『則回覆』...\")\n", "\n", "        # 找出所有顯示中的「則回覆」按鈕\n", "        reply_buttons = [\n", "            el for el in driver.find_elements(\n", "                By.XPATH, \"//span[normalize-space(text()) and contains(text(), '則回覆')]\"\n", "            ) if el.is_displayed()\n", "        ]\n", "\n", "        if not reply_buttons:\n", "            print(\"✅ 沒有更多『則回覆』可點擊，結束。\")\n", "            break\n", "\n", "        for btn in reply_buttons:\n", "            try:\n", "                driver.execute_script(\"arguments[0].scrollIntoView({block: 'center'});\", btn)\n", "                time.sleep(0.3)\n", "                driver.execute_script(\"arguments[0].click();\", btn)\n", "                print(f\"✅ 已點擊一個『則回覆』\")\n", "                clicked_total += 1\n", "                time.sleep(1.0)  # 等待留言載入\n", "            except Exception as e:\n", "                print(f\"❌ 點擊失敗，跳過：{e}\")\n", "                continue\n", "\n", "    print(f\"📌 共點擊 {clicked_total} 個『則回覆』按鈕\")\n", "    return clicked_total\n", "\n", "    \n", "def get_last_comment_count(driver):\n", "    # 使用 XPath 查找包含 '則留言' 的元素\n", "    comments_elements = driver.find_elements(By.XPATH, \"//*[contains(text(),'則留言')]\")\n", "    \n", "    # 初始化一个变量来存储最后一个匹配的留言数\n", "    last_comment_count = None\n", "    \n", "    # 遍历找到的元素，提取最后一个数字并保存\n", "    for element in comments_elements:\n", "        text = element.text\n", "        # 使用正则表达式查找 '則留言' 前面的数字，支持千位分隔符\n", "        match = re.search(r'([\\d,]+)\\s*則留言', text)\n", "        if match:\n", "            # 去除數字中的逗號，轉換為整數，並儲存為最後一個找到的數字\n", "            last_comment_count = int(match.group(1).replace(',', ''))\n", "    \n", "    # 返回最后一个留言数\n", "    return last_comment_count\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def extract_comments(driver, post_info, filename='12.json'):\n", "    \"\"\"\n", "    從網頁中提取評論，將評論、時間、按讚數和使用者名稱分別添加到各自的列表中，並儲存為 JSON 文件。\n", "    \n", "    參數:\n", "        driver: Selenium WebDriver 物件，用於與瀏覽器交互。\n", "        filename: 儲存 JSON 文件的名稱，預設為 '12.json'。\n", "\n", "    返回:\n", "        dict: 包含 `comments_list`, `times_list`, `likes_list`, `User_list` 的字典。\n", "    \"\"\"\n", "    print(f\"=== 開始提取評論 ===\")\n", "    print(f\"貼文資訊: {post_info}\")\n", "    print(f\"檔案名稱: {filename}\")\n", "    \n", "    comments_list = []\n", "    times_list = []\n", "    likes_list = []\n", "    User_list = []\n", "\n", "    try:\n", "        # 使用 XPath 定位包含指定類名的評論元素\n", "        print(\"正在尋找評論元素...\")\n", "        elements =  driver.find_elements(\n", "    By.XPATH,\n", "    \"//div[contains(@class, 'x1n2onr6') and contains(@class, 'x1g0dm76') and contains(@class, 'x1iorvi4')]\"\n", ")\n", "        print(f\"找到 {len(elements)} 個評論元素\")\n", "        \n", "        # 遍歷元素並清理文本後添加到各列表中\n", "        for i, element in enumerate(elements):\n", "            print(f\"\\n--- 處理第 {i+1} 個元素 ---\")\n", "            text = element.text\n", "            print(f\"原始文本: {text[:200]}...\")  # 只顯示前200字元\n", "            \n", "            likes = None\n", "            user = None\n", "\n", "            # 清除特定的無用文本\n", "            text = re.sub(r'\\s*·\\s*', '', text)\n", "            for phrase in ['追蹤', '讚', '回覆', '已編輯', '頭號粉絲', '翻譯年糕']:\n", "                text = text.replace(phrase, '')\n", "            print(f\"清理後文本: {text[:200]}...\")\n", "\n", "            # 根據行分割並過濾空行\n", "            text_lines = [line for line in text.splitlines() if line]\n", "            print(f\"分割後行數: {len(text_lines)}\")\n", "            print(f\"各行內容: {text_lines}\")\n", "            \n", "            if len(text_lines) < 3:\n", "                print(\"行數不足3，清空處理\")\n", "                text_lines = []  # 若行數不足3，清空\n", "\n", "            # 確定包含時間的行索引\n", "            indices_with_days = [index for index, item in enumerate(text_lines) if re.search(r'(\\d+\\s*[天週分小時])', item)]\n", "            print(f\"包含時間的行索引: {indices_with_days}\")\n", "        \n", "            # 處理留言內容\n", "            if len(text_lines) == 3:\n", "                comment_text = text_lines[1]\n", "                print(f\"3行模式，留言內容: {comment_text}\")\n", "            else:\n", "                if indices_with_days:\n", "                    comment_text = \" \".join(text_lines[1:indices_with_days[-1]])  # 合併為一條留言\n", "                    print(f\"多行模式，留言內容: {comment_text}\")\n", "                else:\n", "                    comment_text = \" \".join(text_lines)\n", "                    print(f\"無時間模式，留言內容: {comment_text}\")\n", "        \n", "            if comment_text:\n", "                comments_list.append(comment_text)  # 非空才加入\n", "                print(f\"已添加留言: {comment_text[:100]}...\")\n", "            else:\n", "                print(\"留言內容為空，跳過\")\n", "                \n", "            # 處理時間\n", "            if indices_with_days:\n", "                first_time = text_lines[indices_with_days[-1]]\n", "                print(f\"找到時間: {first_time}\")\n", "                calculated_date = calculate_date(first_time)\n", "                formatted_date = calculated_date.strftime('%Y-%m-%d')\n", "                times_list.append(formatted_date)\n", "                print(f\"格式化時間: {formatted_date}\")\n", "            else:\n", "                print(\"未找到時間資訊\")\n", "                \n", "            # 處理使用者名稱\n", "            if text_lines:\n", "                user = text_lines[0]\n", "                print(f\"使用者名稱: {user}\")\n", "            if user:\n", "                User_list.append(user)\n", "                print(f\"已添加使用者: {user}\")\n", "            else:\n", "                print(\"未找到使用者名稱\")\n", "\n", "            # 處理按讚數\n", "            last_time_value = text_lines[-1] if text_lines else \"\"\n", "            print(f\"最後一行值: {last_time_value}\")\n", "            if last_time_value:\n", "                if re.search(r'[天分週小時]', last_time_value):\n", "                    likes = 0  # 包含時間單位則按讚數設為 0\n", "                    print(\"包含時間單位，按讚數設為 0\")\n", "                elif last_time_value.isdigit():\n", "                    likes = int(last_time_value)\n", "                    print(f\"數字格式，按讚數: {likes}\")\n", "                else:\n", "                    likes = None\n", "                    print(\"無法解析按讚數\")\n", "            if likes is not None:\n", "                likes_list.append(likes)\n", "                print(f\"已添加按讚數: {likes}\")\n", "            else:\n", "                print(\"按讚數為 None，跳過\")\n", "\n", "        print(f\"\\n=== 提取完成 ===\")\n", "        print(f\"留言數量: {len(comments_list)}\")\n", "        print(f\"時間數量: {len(times_list)}\")\n", "        print(f\"按讚數數量: {len(likes_list)}\")\n", "        print(f\"使用者數量: {len(User_list)}\")\n", "        \n", "        # 顯示前幾個結果作為範例\n", "        for i in range(min(3, len(comments_list))):\n", "            print(f\"範例 {i+1}:\")\n", "            print(f\"  使用者: {User_list[i] if i < len(User_list) else 'N/A'}\")\n", "            print(f\"  留言: {comments_list[i] if i < len(comments_list) else 'N/A'}\")\n", "            print(f\"  時間: {times_list[i] if i < len(times_list) else 'N/A'}\")\n", "            print(f\"  按讚: {likes_list[i] if i < len(likes_list) else 'N/A'}\")\n", "\n", "        # 使用 organize_comments 將結果儲存為 JSON 文件\n", "        print(\"\\n開始組織評論結構...\")\n", "        organized_data = organize_comments(comments_list, times_list, likes_list, User_list, post_info, save_to_file=True, filename=filename)\n", "        return organized_data\n", "        \n", "    except Exception as e:\n", "        print(f\"抓取內容失敗: {e}\")\n", "        import traceback\n", "        print(f\"詳細錯誤: {traceback.format_exc()}\")\n", "\n", "def organize_comments(comments_list, times_list, likes_list, User_list, post_info, save_to_file=False, filename=\"comments_structure.json\"):\n", "    \"\"\"\n", "    將留言組織為單層平鋪結構，並儲存為簡化 JSON 格式。\n", "\n", "    格式範例：\n", "    {\n", "        \"標題\": \"Re: [貼文標題]\",\n", "        \"留言內容\": \"留言文字\",\n", "        \"情感標籤\": \"\",\n", "        \"情緒\": \"\",\n", "        \"日期\": \"2025-07-08\",\n", "        \"用戶\": \"使用者名稱\",\n", "        \"上文留言\": \"被回覆的使用者名稱\"（如果有）\n", "    }\n", "    \"\"\"\n", "    print(f\"=== 開始組織評論 ===\")\n", "    print(f\"輸入資料統計:\")\n", "    print(f\"  留言數量: {len(comments_list)}\")\n", "    print(f\"  時間數量: {len(times_list)}\")\n", "    print(f\"  按讚數數量: {len(likes_list)}\")\n", "    print(f\"  使用者數量: {len(User_list)}\")\n", "\n", "    flat_comment_list = []\n", "\n", "    for i, comment in enumerate(comments_list):\n", "        user = User_list[i] if i < len(User_list) else \"\"\n", "        time_str = times_list[i] if i < len(times_list) else \"\"\n", "        reply_to = \"\"\n", "\n", "        # 嘗試找出「上文留言」：若留言內容中包含其他用戶\n", "        for other_user in User_list:\n", "            if other_user in comment and other_user != user:\n", "                reply_to = other_user\n", "                break\n", "\n", "        flat_comment = {\n", "            \"標題\": f\"{post_info}\",\n", "            \"留言內容\": comment,\n", "            \"情感標籤\": \"\",\n", "            \"情緒\": \"\",\n", "            \"日期\": time_str,\n", "            \"用戶\": user,\n", "            \"上文留言\": f\"{post_info}\"\n", "        }\n", "        flat_comment_list.append(flat_comment)\n", "\n", "    if save_to_file:\n", "        if os.path.exists(filename):\n", "            with open(filename, \"r\", encoding=\"utf-8\") as f:\n", "                existing_data = json.load(f)\n", "            existing_data.extend(flat_comment_list)\n", "        else:\n", "            existing_data = flat_comment_list\n", "\n", "        with open(filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(existing_data, f, ensure_ascii=False, indent=2)\n", "\n", "        print(f\"✅ JSON 已儲存為 {filename}\")\n", "\n", "    return flat_comment_list\n", "\n", "#scrape_and_save_to_csv(driver, 'output32.csv')\n", "def calculate_date(time_string):\n", "    # 获取当前时间\n", "    now = datetime.now()\n", "   \n", "\n", "    # 使用正则表达式匹配时间类型和数值\n", "    week_match = re.search(r'(\\d+)\\s*週', time_string)\n", "    day_match = re.search(r'(\\d+)\\s*天', time_string)\n", "    hour_match = re.search(r'(\\d+)\\s*時', time_string)\n", "    minute_match = re.search(r'(\\d+)\\s*分', time_string)\n", "\n", "    # 初始化用于存储最终时间的变量\n", "    calculated_date = now\n", "\n", "    # 根据匹配到的时间类型调整时间\n", "    if week_match:\n", "        weeks = int(week_match.group(1))\n", "        calculated_date = now - timed<PERSON><PERSON>(weeks=weeks)\n", "    elif day_match:\n", "        days = int(day_match.group(1))\n", "        calculated_date = now - timed<PERSON>ta(days=days)\n", "    elif hour_match:\n", "        hours = int(hour_match.group(1))\n", "        calculated_date = now - timedelta(hours=hours)\n", "    elif minute_match:\n", "        minutes = int(minute_match.group(1))\n", "        calculated_date = now - <PERSON><PERSON><PERSON>(minutes=minutes)\n", "\n", "    \n", "    # 返回最终计算后的时间\n", "    return calculated_date\n", "\n", "\n", "#data = extract_comments(driver,\"77\", filename='comments_data1.json')"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["104\n"]}], "source": ["commt_ct =  get_last_comment_count(driver)\n", "print(commt_ct)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["粉絲專頁 · 政治人物02 2258 0355【反制大罷免 民進黨幹話撲克牌免費索取】大家早安一早站路口 反惡意罷免週末繼續到市場宣傳反惡罷不同意這種想要一黨專政的罷免元之邀請您來欣賞表演一起共度愉快的週末夜晚大家早安一早站路口 反惡意罷免台灣需要不同的聲音台灣需要制衡的力量我們不同意一黨專政的罷免我們不同意獨裁專制的罷免7/26一起發出人民的力量出來蓋「不同意罷免」告訴政府好好做事 不要一天到晚搞鬥爭\n", "检测到页面底部，停止滚动。\n", "滚动结束。\n", "第 1 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 已點擊一個『則回覆』\n", "✅ 已點擊一個『則回覆』\n", "✅ 已點擊一個『則回覆』\n", "🔁 第 2 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 3 個『則回覆』按鈕\n", "第 2 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 3 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 4 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 5 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 6 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 7 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 8 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 9 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 10 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 11 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 12 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 13 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 14 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 15 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 16 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 17 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 18 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 19 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 20 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 21 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 22 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 23 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 24 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 25 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 26 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 27 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 28 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 29 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 30 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 31 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 32 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 33 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 34 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 35 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 36 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 37 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 38 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 39 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 40 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 41 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 42 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 43 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 44 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 45 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 46 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 47 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 48 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 49 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 50 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 51 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 52 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 53 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 54 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 55 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 56 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 57 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 58 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 59 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 60 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 61 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 62 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 63 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 64 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 65 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 66 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 67 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 68 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 69 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 70 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 71 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 72 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 73 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 74 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 75 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 76 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 77 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 78 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 79 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 80 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 81 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 82 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 83 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 84 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 85 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 86 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 87 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 88 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 89 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 90 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 91 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 92 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 93 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 94 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 95 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 96 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 97 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 98 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 99 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 100 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 101 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 102 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 103 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 104 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 105 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 106 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 107 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 108 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 109 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 110 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 111 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 112 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 113 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 114 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 115 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 116 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 117 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 118 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 119 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 120 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 121 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 122 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 123 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 124 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 125 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 126 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 127 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 128 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 129 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 130 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 131 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 132 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 133 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 134 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 135 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 136 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 137 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 138 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 139 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 140 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 141 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 142 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 143 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 144 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 145 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 146 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 147 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 148 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 149 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 150 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 151 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 152 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 153 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 154 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 155 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 156 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 157 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 158 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 159 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 160 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 161 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 162 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 163 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 164 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 165 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 166 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 167 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 168 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 169 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 170 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 171 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 172 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 173 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 174 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 175 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 176 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 177 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 178 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 179 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 180 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 181 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 182 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 183 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 184 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 185 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 186 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 187 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 188 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 189 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 190 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 191 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 192 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 193 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 194 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 195 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 196 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 197 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 198 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 199 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 200 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 201 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 202 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 203 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 204 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 205 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 206 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 207 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 208 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 209 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 210 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 211 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 212 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 213 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 214 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 215 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 216 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 217 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 218 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 219 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 220 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 221 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 222 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 223 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 224 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 225 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 226 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 227 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 228 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 229 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 230 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 231 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 232 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 233 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 234 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 235 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 236 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 237 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 238 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 239 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 240 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 241 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 242 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 243 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 244 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 245 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 246 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 247 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 248 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 249 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 250 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 251 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 252 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 253 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 254 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 255 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 256 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 257 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 258 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 259 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 260 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 261 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 262 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 263 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 264 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 265 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 266 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 267 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 268 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 269 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 270 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 271 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 272 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 273 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 274 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 275 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 276 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 277 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 278 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 279 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 280 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 281 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 282 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 283 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 284 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 285 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 286 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 287 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 288 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 289 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 290 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 291 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 292 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 293 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 294 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 295 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 296 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 297 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 298 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 299 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 300 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 301 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 302 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 303 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 304 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 305 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 306 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 307 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 308 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 309 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 310 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 311 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 312 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 313 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 314 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 315 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 316 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 317 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 318 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 319 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 320 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 321 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 322 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 323 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 324 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 325 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 326 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 327 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 328 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 329 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 330 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 331 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 332 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 333 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 334 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 335 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 336 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 337 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 338 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 339 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 340 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 341 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 342 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 343 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 344 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 345 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 346 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 347 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 348 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 349 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 350 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 351 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 352 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 353 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 354 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 355 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 356 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 357 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 358 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 359 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 360 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 361 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 362 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 363 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 364 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 365 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 366 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 367 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 368 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 369 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 370 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 371 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 372 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 373 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 374 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 375 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 376 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 377 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 378 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 379 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 380 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 381 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 382 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 383 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 384 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 385 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 386 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 387 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 388 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 389 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 390 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 391 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 392 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 393 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 394 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 395 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 396 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 397 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 398 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 399 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 400 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 401 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 402 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 403 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 404 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 405 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 406 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 407 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 408 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 409 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 410 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 411 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 412 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 413 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 414 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 415 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 416 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 417 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 418 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 419 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 420 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 421 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 422 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 423 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 424 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 425 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 426 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 427 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 428 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 429 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 430 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 431 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 432 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 433 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 434 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 435 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 436 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 437 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 438 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 439 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 440 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 441 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 442 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 443 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 444 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 445 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 446 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 447 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 448 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 449 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 450 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 451 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 452 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 453 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 454 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 455 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 456 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 457 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 458 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 459 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 460 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 461 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 462 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 463 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 464 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 465 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 466 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 467 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 468 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 469 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 470 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 471 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 472 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 473 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 474 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 475 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 476 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 477 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 478 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 479 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 480 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 481 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 482 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 483 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 484 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 485 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 486 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 487 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 488 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 489 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 490 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 491 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 492 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 493 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 494 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 495 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 496 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 497 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 498 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 499 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "第 500 次執行 click_reply_elements\n", "🔁 第 1 回合查找『則回覆』...\n", "✅ 沒有更多『則回覆』可點擊，結束。\n", "📌 共點擊 0 個『則回覆』按鈕\n", "=== 開始提取評論 ===\n", "貼文資訊: 粉絲專頁 · 政治人物02 2258 0355【反制大罷免 民進黨幹話撲克牌免費索取】大家早安一早站路口 反惡意罷免週末繼續到市場宣傳反惡罷不同意這種想要一黨專政的罷免元之邀請您來欣賞表演一起共度愉快的週末夜晚大家早安一早站路口 反惡意罷免台灣需要不同的聲音台灣需要制衡的力量我們不同意一黨專政的罷免我們不同意獨裁專制的罷免7/26一起發出人民的力量出來蓋「不同意罷免」告訴政府好好做事 不要一天到晚搞鬥爭\n", "檔案名稱: 333.json\n", "正在尋找評論元素...\n", "找到 42 個評論元素\n", "\n", "--- 處理第 1 個元素 ---\n", "原始文本: 張繼慈\n", "葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "5小時\n", "讚\n", "回覆\n", "40...\n", "清理後文本: 張繼慈\n", "葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "5小時\n", "\n", "\n", "40...\n", "分割後行數: 4\n", "各行內容: ['張繼慈', '葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！', '5小時', '40']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "已添加留言: 葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 張繼慈\n", "已添加使用者: 張繼慈\n", "最後一行值: 40\n", "數字格式，按讚數: 40\n", "已添加按讚數: 40\n", "\n", "--- 處理第 2 個元素 ---\n", "原始文本: 林賢明\n", "委員加油加油\n", "3天\n", "讚\n", "回覆\n", "13...\n", "清理後文本: 林賢明\n", "委員加油加油\n", "3天\n", "\n", "\n", "13...\n", "分割後行數: 4\n", "各行內容: ['林賢明', '委員加油加油', '3天', '13']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 委員加油加油\n", "已添加留言: 委員加油加油...\n", "找到時間: 3天\n", "格式化時間: 2025-07-05\n", "使用者名稱: 林賢明\n", "已添加使用者: 林賢明\n", "最後一行值: 13\n", "數字格式，按讚數: 13\n", "已添加按讚數: 13\n", "\n", "--- 處理第 3 個元素 ---\n", "原始文本: 孫振文\n", "帥葉元之委員\n", "真的需要國民黨立委職務\n", "1分鐘\n", "讚\n", "回覆\n", "已編輯...\n", "清理後文本: 孫振文\n", "帥葉元之委員\n", "真的需要國民黨立委職務\n", "1分鐘\n", "\n", "\n", "...\n", "分割後行數: 4\n", "各行內容: ['孫振文', '帥葉元之委員', '真的需要國民黨立委職務', '1分鐘']\n", "包含時間的行索引: [3]\n", "多行模式，留言內容: 帥葉元之委員 真的需要國民黨立委職務\n", "已添加留言: 帥葉元之委員 真的需要國民黨立委職務...\n", "找到時間: 1分鐘\n", "格式化時間: 2025-07-08\n", "使用者名稱: 孫振文\n", "已添加使用者: 孫振文\n", "最後一行值: 1分鐘\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 4 個元素 ---\n", "原始文本: 楊秀瑄\n", "加油委員辛苦\n", "21分鐘\n", "讚\n", "回覆...\n", "清理後文本: 楊秀瑄\n", "加油委員辛苦\n", "21分鐘\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['楊秀瑄', '加油委員辛苦', '21分鐘']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油委員辛苦\n", "已添加留言: 加油委員辛苦...\n", "找到時間: 21分鐘\n", "格式化時間: 2025-07-08\n", "使用者名稱: 楊秀瑄\n", "已添加使用者: 楊秀瑄\n", "最後一行值: 21分鐘\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 5 個元素 ---\n", "原始文本: 李家樺\n", "委員\n", "請助理把今年\n", "各部會的總預算\n", "跟去年的總預算\n", "發在選傳上\n", "保證你一定安全\n", "最好連蔡政府最後\n", "一任的總預算一比\n", "那骯髒黨到一半\n", "真的要快\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: 李家樺\n", "委員\n", "請助理把今年\n", "各部會的總預算\n", "跟去年的總預算\n", "發在選傳上\n", "保證你一定安全\n", "最好連蔡政府最後\n", "一任的總預算一比\n", "那骯髒黨到一半\n", "真的要快\n", "1小時\n", "\n", "...\n", "分割後行數: 12\n", "各行內容: ['李家樺', '委員', '請助理把今年', '各部會的總預算', '跟去年的總預算', '發在選傳上', '保證你一定安全', '最好連蔡政府最後', '一任的總預算一比', '那骯髒黨到一半', '真的要快', '1小時']\n", "包含時間的行索引: [11]\n", "多行模式，留言內容: 委員 請助理把今年 各部會的總預算 跟去年的總預算 發在選傳上 保證你一定安全 最好連蔡政府最後 一任的總預算一比 那骯髒黨到一半 真的要快\n", "已添加留言: 委員 請助理把今年 各部會的總預算 跟去年的總預算 發在選傳上 保證你一定安全 最好連蔡政府最後 一任的總預算一比 那骯髒黨到一半 真的要快...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 李家樺\n", "已添加使用者: 李家樺\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 6 個元素 ---\n", "原始文本: 劉綺霞\n", "加油！\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: 劉綺霞\n", "加油！\n", "1小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['劉綺霞', '加油！', '1小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油！\n", "已添加留言: 加油！...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 劉綺霞\n", "已添加使用者: 劉綺霞\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 7 個元素 ---\n", "原始文本: 游珍珍\n", "支持您， 台灣加油\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: 游珍珍\n", "支持您， 台灣加油\n", "1小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['游珍珍', '支持您， 台灣加油', '1小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 支持您， 台灣加油\n", "已添加留言: 支持您， 台灣加油...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 游珍珍\n", "已添加使用者: 游珍珍\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 8 個元素 ---\n", "原始文本: 葉美秀\n", "委員辛苦囉加油哦\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: 葉美秀\n", "委員辛苦囉加油哦\n", "1小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['葉美秀', '委員辛苦囉加油哦', '1小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 委員辛苦囉加油哦\n", "已添加留言: 委員辛苦囉加油哦...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 葉美秀\n", "已添加使用者: 葉美秀\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 9 個元素 ---\n", "原始文本: <PERSON>\n", "有嗎？？？\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "有嗎？？？\n", "1小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '有嗎？？？', '1小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 有嗎？？？\n", "已添加留言: 有嗎？？？...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 10 個元素 ---\n", "原始文本: 張皓祐\n", "委員加油\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 張皓祐\n", "委員加油\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['張皓祐', '委員加油', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 委員加油\n", "已添加留言: 委員加油...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 張皓祐\n", "已添加使用者: 張皓祐\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 11 個元素 ---\n", "原始文本: <PERSON><PERSON><PERSON><PERSON><PERSON>g\n", "加油，罷免\n", "葉元之．板橋\n", "！\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON><PERSON><PERSON><PERSON><PERSON>g\n", "加油，罷免\n", "葉元之．板橋\n", "！\n", "6小時\n", "\n", "...\n", "分割後行數: 5\n", "各行內容: ['<PERSON><PERSON><PERSON><PERSON><PERSON> Weng', '加油，罷免', '葉元之．板橋', '！', '6小時']\n", "包含時間的行索引: [4]\n", "多行模式，留言內容: 加油，罷免 葉元之．板橋 ！\n", "已添加留言: 加油，罷免 葉元之．板橋 ！...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "已添加使用者: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 12 個元素 ---\n", "原始文本: 羅自在\n", "好人有好報·葉言之一定不會被罷免了·大家團結一致\n", "8小時\n", "讚\n", "回覆...\n", "清理後文本: 羅自在\n", "好人有好報葉言之一定不會被罷免了大家團結一致\n", "8小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['羅自在', '好人有好報葉言之一定不會被罷免了大家團結一致', '8小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 好人有好報葉言之一定不會被罷免了大家團結一致\n", "已添加留言: 好人有好報葉言之一定不會被罷免了大家團結一致...\n", "找到時間: 8小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 羅自在\n", "已添加使用者: 羅自在\n", "最後一行值: 8小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 13 個元素 ---\n", "原始文本: 黃鶯鳳\n", "反惡霸+3 刮大風下大雨全家也一定去投票\n", "9小時\n", "讚\n", "回覆\n", "4...\n", "清理後文本: 黃鶯鳳\n", "反惡霸+3 刮大風下大雨全家也一定去投票\n", "9小時\n", "\n", "\n", "4...\n", "分割後行數: 4\n", "各行內容: ['黃鶯鳳', '反惡霸+3 刮大風下大雨全家也一定去投票', '9小時', '4']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 反惡霸+3 刮大風下大雨全家也一定去投票\n", "已添加留言: 反惡霸+3 刮大風下大雨全家也一定去投票...\n", "找到時間: 9小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 黃鶯鳳\n", "已添加使用者: 黃鶯鳳\n", "最後一行值: 4\n", "數字格式，按讚數: 4\n", "已添加按讚數: 4\n", "\n", "--- 處理第 14 個元素 ---\n", "原始文本: 安信懷\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 安信懷\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "2小時\n", "\n", "...\n", "分割後行數: 14\n", "各行內容: ['安信懷', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '2小時']\n", "包含時間的行索引: [13]\n", "多行模式，留言內容: 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！\n", "已添加留言: 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 安信懷\n", "已添加使用者: 安信懷\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 15 個元素 ---\n", "原始文本: 安信懷\n", "現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因：\n", "1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。\n", "民進黨認為只要鼓動他們40%的選民中25%投下同意罷免票，而藍白和中間選民可能因為厭惡罷免或對此罷免通過要件不清楚而未出來投反對票，民進黨即可穩操勝券，經由罷免將立院結構翻轉，重回過去他們ㄧ黨獨大可以為所欲為的狀態。\n", "...\n", "清理後文本: 安信懷\n", "現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因：\n", "1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。\n", "民進黨認為只要鼓動他們40%的選民中25%投下同意罷免票，而藍白和中間選民可能因為厭惡罷免或對此罷免通過要件不清楚而未出來投反對票，民進黨即可穩操勝券，經由罷免將立院結構翻轉，重回過去他們ㄧ黨獨大可以為所欲為的狀態。\n", "...\n", "分割後行數: 6\n", "各行內容: ['安信懷', '現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因：', '1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。', '民進黨認為只要鼓動他們40%的選民中25%投下同意罷免票，而藍白和中間選民可能因為厭惡罷免或對此罷免通過要件不清楚而未出來投反對票，民進黨即可穩操勝券，經由罷免將立院結構翻轉，重回過去他們ㄧ黨獨大可以為所欲為的狀態。', '所以，如果你反對大罷免，不是坐在家裡不投票，而是要出來投下‘’不同意罷免票‘’。', '2小時']\n", "包含時間的行索引: [5]\n", "多行模式，留言內容: 現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因： 1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。 民進黨認為只要鼓動他們40%的選民中25%投下同意罷免票，而藍白和中間選民可能因為厭惡罷免或對此罷免通過要件不清楚而未出來投反對票，民進黨即可穩操勝券，經由罷免將立院結構翻轉，重回過去他們ㄧ黨獨大可以為所欲為的狀態。 所以，如果你反對大罷免，不是坐在家裡不投票，而是要出來投下‘’不同意罷免票‘’。\n", "已添加留言: 現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因： 1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。 民進黨認為只要鼓動他們40...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 安信懷\n", "已添加使用者: 安信懷\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 16 個元素 ---\n", "原始文本: 王樹旺\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 王樹旺\n", "2小時\n", "\n", "...\n", "分割後行數: 2\n", "各行內容: ['王樹旺', '2小時']\n", "行數不足3，清空處理\n", "包含時間的行索引: []\n", "無時間模式，留言內容: \n", "留言內容為空，跳過\n", "未找到時間資訊\n", "未找到使用者名稱\n", "最後一行值: \n", "按讚數為 None，跳過\n", "\n", "--- 處理第 17 個元素 ---\n", "原始文本: <PERSON><PERSON>\n", "委員加油\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON><PERSON> <PERSON>\n", "委員加油\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['Ye<PERSON> <PERSON>', '委員加油', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 委員加油\n", "已添加留言: 委員加油...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON><PERSON> <PERSON>\n", "已添加使用者: <PERSON><PERSON> <PERSON>\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 18 個元素 ---\n", "原始文本: 呂世俊\n", "加油\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 呂世俊\n", "加油\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['呂世俊', '加油', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 呂世俊\n", "已添加使用者: 呂世俊\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 19 個元素 ---\n", "原始文本: 陳全斌\n", "加油，您一定可以的\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 陳全斌\n", "加油，您一定可以的\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['陳全斌', '加油，您一定可以的', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油，您一定可以的\n", "已添加留言: 加油，您一定可以的...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 陳全斌\n", "已添加使用者: 陳全斌\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 20 個元素 ---\n", "原始文本: 鄧竹芳\n", "加加油\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 鄧竹芳\n", "加加油\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['鄧竹芳', '加加油', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加加油\n", "已添加留言: 加加油...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 鄧竹芳\n", "已添加使用者: 鄧竹芳\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 21 個元素 ---\n", "原始文本: <PERSON>\n", "元之委員，加油！\n", "726出門投不同意\n", "守護民主、守護正義\n", "3小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "元之委員，加油！\n", "726出門投不同意\n", "守護民主、守護正義\n", "3小時\n", "\n", "...\n", "分割後行數: 5\n", "各行內容: ['<PERSON>', '元之委員，加油！', '726出門投不同意', '守護民主、守護正義', '3小時']\n", "包含時間的行索引: [4]\n", "多行模式，留言內容: 元之委員，加油！ 726出門投不同意 守護民主、守護正義\n", "已添加留言: 元之委員，加油！ 726出門投不同意 守護民主、守護正義...\n", "找到時間: 3小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 3小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 22 個元素 ---\n", "原始文本: 頭號粉絲\n", "林永訓\n", "加油，加油\n", "3小時\n", "讚\n", "回覆...\n", "清理後文本: \n", "林永訓\n", "加油，加油\n", "3小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['林永訓', '加油，加油', '3小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油，加油\n", "已添加留言: 加油，加油...\n", "找到時間: 3小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 林永訓\n", "已添加使用者: 林永訓\n", "最後一行值: 3小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 23 個元素 ---\n", "原始文本: <PERSON>\n", "加油\n", "3小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "加油\n", "3小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '加油', '3小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 3小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 3小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 24 個元素 ---\n", "原始文本: 秦少悅\n", "站十幾個小時路口就過了\n", "3小時\n", "讚\n", "回覆...\n", "清理後文本: 秦少悅\n", "站十幾個小時路口就過了\n", "3小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['秦少悅', '站十幾個小時路口就過了', '3小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 站十幾個小時路口就過了\n", "已添加留言: 站十幾個小時路口就過了...\n", "找到時間: 3小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 秦少悅\n", "已添加使用者: 秦少悅\n", "最後一行值: 3小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 25 個元素 ---\n", "原始文本: 李家勳\n", "大罷免大成功\n", "3小時\n", "讚\n", "回覆...\n", "清理後文本: 李家勳\n", "大罷免大成功\n", "3小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['李家勳', '大罷免大成功', '3小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 大罷免大成功\n", "已添加留言: 大罷免大成功...\n", "找到時間: 3小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 李家勳\n", "已添加使用者: 李家勳\n", "最後一行值: 3小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 26 個元素 ---\n", "原始文本: 楊惠欣\n", "加油加油加油，辛苦了\n", "4小時\n", "讚\n", "回覆...\n", "清理後文本: 楊惠欣\n", "加油加油加油，辛苦了\n", "4小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['楊惠欣', '加油加油加油，辛苦了', '4小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油加油加油，辛苦了\n", "已添加留言: 加油加油加油，辛苦了...\n", "找到時間: 4小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 楊惠欣\n", "已添加使用者: 楊惠欣\n", "最後一行值: 4小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 27 個元素 ---\n", "原始文本: 蔡佩玲\n", "我同意罷免\n", "4小時\n", "讚\n", "回覆...\n", "清理後文本: 蔡佩玲\n", "我同意罷免\n", "4小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['蔡佩玲', '我\\ue030同意罷免', '4小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 我同意罷免\n", "已添加留言: 我同意罷免...\n", "找到時間: 4小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 蔡佩玲\n", "已添加使用者: 蔡佩玲\n", "最後一行值: 4小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 28 個元素 ---\n", "原始文本: <PERSON><PERSON><PERSON>\n", "元之加油\n", "4小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON><PERSON><PERSON>\n", "元之加油\n", "4小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON><PERSON><PERSON>', '元之加油', '4小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 元之加油\n", "已添加留言: 元之加油...\n", "找到時間: 4小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON><PERSON><PERSON>\n", "已添加使用者: <PERSON><PERSON><PERSON>\n", "最後一行值: 4小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 29 個元素 ---\n", "原始文本: 林碧燕\n", "加油。\n", "4小時\n", "讚\n", "回覆...\n", "清理後文本: 林碧燕\n", "加油。\n", "4小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['林碧燕', '加油。', '4小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油。\n", "已添加留言: 加油。...\n", "找到時間: 4小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 林碧燕\n", "已添加使用者: 林碧燕\n", "最後一行值: 4小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 30 個元素 ---\n", "原始文本: 陳旺興\n", "雜質的立委，指的是，不專業，沒有水準素質，口出惡言，斷章取義，打架，聽口令集合按同意，按什麼法什麼事情他們也不知道，要把台灣毀掉，要把台灣給共產黨，他們錢賺飽了，也跑了，拜託罷免救台灣，以後廢除不分區立委，他們是三不管地帶，非常恐怖可以做滿4年立委。\n", "5小時\n", "讚\n", "回覆...\n", "清理後文本: 陳旺興\n", "雜質的立委，指的是，不專業，沒有水準素質，口出惡言，斷章取義，打架，聽口令集合按同意，按什麼法什麼事情他們也不知道，要把台灣毀掉，要把台灣給共產黨，他們錢賺飽了，也跑了，拜託罷免救台灣，以後廢除不分區立委，他們是三不管地帶，非常恐怖可以做滿4年立委。\n", "5小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['陳旺興', '雜質的立委，指的是，不專業，沒有水準素質，口出惡言，斷章取義，打架，聽口令集合按同意，按什麼法什麼事情他們也不知道，要把台灣毀掉，要把台灣給共產黨，他們錢賺飽了，也跑了，拜託罷免救台灣，以後廢除不分區立委，他們是三不管地帶，非常恐怖可以做滿4年立委。', '5小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 雜質的立委，指的是，不專業，沒有水準素質，口出惡言，斷章取義，打架，聽口令集合按同意，按什麼法什麼事情他們也不知道，要把台灣毀掉，要把台灣給共產黨，他們錢賺飽了，也跑了，拜託罷免救台灣，以後廢除不分區立委，他們是三不管地帶，非常恐怖可以做滿4年立委。\n", "已添加留言: 雜質的立委，指的是，不專業，沒有水準素質，口出惡言，斷章取義，打架，聽口令集合按同意，按什麼法什麼事情他們也不知道，要把台灣毀掉，要把台灣給共產黨，他們錢賺飽了，也跑了，拜託罷免救台灣，以後廢除不分區...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 陳旺興\n", "已添加使用者: 陳旺興\n", "最後一行值: 5小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 31 個元素 ---\n", "原始文本: 許瑞霞\n", "加油\n", "5小時\n", "讚\n", "回覆...\n", "清理後文本: 許瑞霞\n", "加油\n", "5小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['許瑞霞', '加油', '5小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 許瑞霞\n", "已添加使用者: 許瑞霞\n", "最後一行值: 5小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 32 個元素 ---\n", "原始文本: 柯味珍\n", "請慎防綠中選會做票，加油\n", "5小時\n", "讚\n", "回覆...\n", "清理後文本: 柯味珍\n", "請慎防綠中選會做票，加油\n", "5小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['柯味珍', '請慎防綠中選會做票，加油', '5小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 請慎防綠中選會做票，加油\n", "已添加留言: 請慎防綠中選會做票，加油...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 柯味珍\n", "已添加使用者: 柯味珍\n", "最後一行值: 5小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 33 個元素 ---\n", "原始文本: <PERSON>\n", "元之加油加油！反惡罷！\n", "5小時\n", "讚\n", "回覆\n", "已編輯...\n", "清理後文本: <PERSON>\n", "元之加油加油！反惡罷！\n", "5小時\n", "\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '元之加油加油！反惡罷！', '5小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 元之加油加油！反惡罷！\n", "已添加留言: 元之加油加油！反惡罷！...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 5小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 34 個元素 ---\n", "原始文本: 林金裕\n", "加油\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: 林金裕\n", "加油\n", "6小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['林金裕', '加油', '6小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 林金裕\n", "已添加使用者: 林金裕\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 35 個元素 ---\n", "原始文本: 許地申\n", "加油！\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: 許地申\n", "加油！\n", "6小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['許地申', '加油！', '6小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油！\n", "已添加留言: 加油！...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 許地申\n", "已添加使用者: 許地申\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 36 個元素 ---\n", "原始文本: <PERSON>\n", "加油！\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "加油！\n", "6小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '加油！', '6小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油！\n", "已添加留言: 加油！...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 37 個元素 ---\n", "原始文本: 張明杰\n", "加油\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: 張明杰\n", "加油\n", "6小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['張明杰', '加油', '6小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 張明杰\n", "已添加使用者: 張明杰\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 38 個元素 ---\n", "原始文本: 張櫻櫻\n", "元之加油辛苦了⋯⋯\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: 張櫻櫻\n", "元之加油辛苦了⋯⋯\n", "6小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['張櫻櫻', '元之加油辛苦了⋯⋯', '6小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 元之加油辛苦了⋯⋯\n", "已添加留言: 元之加油辛苦了⋯⋯...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 張櫻櫻\n", "已添加使用者: 張櫻櫻\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 39 個元素 ---\n", "原始文本: 楊珍\n", "建議“不同意罷免”一定要更突出明顯，加油！\n", "726 不同意罷免\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: 楊珍\n", "建議“不同意罷免”一定要更突出明顯，加油！\n", "726 不同意罷免\n", "6小時\n", "\n", "...\n", "分割後行數: 4\n", "各行內容: ['楊珍', '建議“不同意罷免”一定要更突出明顯，加油！', '726 不同意罷免', '6小時']\n", "包含時間的行索引: [3]\n", "多行模式，留言內容: 建議“不同意罷免”一定要更突出明顯，加油！ 726 不同意罷免\n", "已添加留言: 建議“不同意罷免”一定要更突出明顯，加油！ 726 不同意罷免...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 楊珍\n", "已添加使用者: 楊珍\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 40 個元素 ---\n", "原始文本: 梁哲嘉\n", "記得對工讀生好一點喔\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: 梁哲嘉\n", "記得對工讀生好一點喔\n", "6小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['梁哲嘉', '記得對工讀生好一點喔', '6小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 記得對工讀生好一點喔\n", "已添加留言: 記得對工讀生好一點喔...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 梁哲嘉\n", "已添加使用者: 梁哲嘉\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 41 個元素 ---\n", "原始文本: <PERSON>\n", "大罷免大成功\n", "7小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "大罷免大成功\n", "7小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '大罷免大成功', '7小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 大罷免大成功\n", "已添加留言: 大罷免大成功...\n", "找到時間: 7小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 7小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 42 個元素 ---\n", "原始文本: 李春梅\n", "元之委員，加油\n", "請大家7月26號出來，投不同意罷免，感恩\n", "7小時\n", "讚\n", "回覆\n", "2...\n", "清理後文本: 李春梅\n", "元之委員，加油\n", "請大家7月26號出來，投不同意罷免，感恩\n", "7小時\n", "\n", "\n", "2...\n", "分割後行數: 5\n", "各行內容: ['李春梅', '元之委員，加油', '請大家7月26號出來，投不同意罷免，感恩', '7小時', '2']\n", "包含時間的行索引: [3]\n", "多行模式，留言內容: 元之委員，加油 請大家7月26號出來，投不同意罷免，感恩\n", "已添加留言: 元之委員，加油 請大家7月26號出來，投不同意罷免，感恩...\n", "找到時間: 7小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 李春梅\n", "已添加使用者: 李春梅\n", "最後一行值: 2\n", "數字格式，按讚數: 2\n", "已添加按讚數: 2\n", "\n", "=== 提取完成 ===\n", "留言數量: 41\n", "時間數量: 41\n", "按讚數數量: 41\n", "使用者數量: 41\n", "範例 1:\n", "  使用者: 張繼慈\n", "  留言: 葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "  時間: 2025-07-08\n", "  按讚: 40\n", "範例 2:\n", "  使用者: 林賢明\n", "  留言: 委員加油加油\n", "  時間: 2025-07-05\n", "  按讚: 13\n", "範例 3:\n", "  使用者: 孫振文\n", "  留言: 帥葉元之委員 真的需要國民黨立委職務\n", "  時間: 2025-07-08\n", "  按讚: 0\n", "\n", "開始組織評論結構...\n", "=== 開始組織評論 ===\n", "輸入資料統計:\n", "  留言數量: 41\n", "  時間數量: 41\n", "  按讚數數量: 41\n", "  使用者數量: 41\n", "✅ JSON 已儲存為 333.json\n"]}], "source": ["double_click_on_comment1(driver)\n", "double_click_on_comment2(driver)\n", "comb_text = process_combined_text(driver)\n", "print(comb_text)\n", "commt_ct = get_last_comment_count(driver)\n", "fnct = commt_ct + 1550\n", "scroll_to_bottom_kk(driver, fnct, fnct/10)\n", "for i in range(500):\n", "    print(f\"第 {i+1} 次執行 click_reply_elements\")\n", "    click_all_reply_buttons(driver)\n", "data = extract_comments(driver,comb_text, filename='333.json')\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 開始提取評論 ===\n", "貼文資訊: 77\n", "檔案名稱: comments_data1.json\n", "正在尋找評論元素...\n", "找到 55 個評論元素\n", "\n", "--- 處理第 1 個元素 ---\n", "原始文本: 張繼慈\n", "葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "5小時\n", "讚\n", "回覆\n", "40...\n", "清理後文本: 張繼慈\n", "葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "5小時\n", "\n", "\n", "40...\n", "分割後行數: 4\n", "各行內容: ['張繼慈', '葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！', '5小時', '40']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "已添加留言: 葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 張繼慈\n", "已添加使用者: 張繼慈\n", "最後一行值: 40\n", "數字格式，按讚數: 40\n", "已添加按讚數: 40\n", "\n", "--- 處理第 2 個元素 ---\n", "原始文本: 林賢明\n", "委員加油加油\n", "3天\n", "讚\n", "回覆\n", "13...\n", "清理後文本: 林賢明\n", "委員加油加油\n", "3天\n", "\n", "\n", "13...\n", "分割後行數: 4\n", "各行內容: ['林賢明', '委員加油加油', '3天', '13']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 委員加油加油\n", "已添加留言: 委員加油加油...\n", "找到時間: 3天\n", "格式化時間: 2025-07-05\n", "使用者名稱: 林賢明\n", "已添加使用者: 林賢明\n", "最後一行值: 13\n", "數字格式，按讚數: 13\n", "已添加按讚數: 13\n", "\n", "--- 處理第 3 個元素 ---\n", "原始文本: <PERSON>\n", "什麼? 還有限制 板橋戶籍喔\n", "38分鐘\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "什麼? 還有限制 板橋戶籍喔\n", "38分鐘\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON> Sheng', '什麼? 還有限制 板橋戶籍喔', '38分鐘']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 什麼? 還有限制 板橋戶籍喔\n", "已添加留言: 什麼? 還有限制 板橋戶籍喔...\n", "找到時間: 38分鐘\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 38分鐘\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 4 個元素 ---\n", "原始文本: 信毅\n", "如果7月26號他們沒有被罷免掉，將來在立法院會比現在的囂張脫序的行為超過10倍以上\n", "44分鐘\n", "讚\n", "回覆...\n", "清理後文本: 信毅\n", "如果7月26號他們沒有被罷免掉，將來在立法院會比現在的囂張脫序的行為超過10倍以上\n", "44分鐘\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['信毅', '如果7月26號他們沒有被罷免掉，將來在立法院會比現在的囂張脫序的行為超過10倍以上', '44分鐘']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 如果7月26號他們沒有被罷免掉，將來在立法院會比現在的囂張脫序的行為超過10倍以上\n", "已添加留言: 如果7月26號他們沒有被罷免掉，將來在立法院會比現在的囂張脫序的行為超過10倍以上...\n", "找到時間: 44分鐘\n", "格式化時間: 2025-07-08\n", "使用者名稱: 信毅\n", "已添加使用者: 信毅\n", "最後一行值: 44分鐘\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 5 個元素 ---\n", "原始文本: 李碧雲\n", "55分鐘\n", "讚\n", "回覆...\n", "清理後文本: 李碧雲\n", "55分鐘\n", "\n", "...\n", "分割後行數: 2\n", "各行內容: ['李碧雲', '55分鐘']\n", "行數不足3，清空處理\n", "包含時間的行索引: []\n", "無時間模式，留言內容: \n", "留言內容為空，跳過\n", "未找到時間資訊\n", "未找到使用者名稱\n", "最後一行值: \n", "按讚數為 None，跳過\n", "\n", "--- 處理第 6 個元素 ---\n", "原始文本: <PERSON>\n", "這個好！遇到青鳥隨便抽一張讓他們閉嘴\n", "59分鐘\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "這個好！遇到青鳥隨便抽一張讓他們閉嘴\n", "59分鐘\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '這個好！遇到青鳥隨便抽一張讓他們閉嘴', '59分鐘']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 這個好！遇到青鳥隨便抽一張讓他們閉嘴\n", "已添加留言: 這個好！遇到青鳥隨便抽一張讓他們閉嘴...\n", "找到時間: 59分鐘\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 59分鐘\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 7 個元素 ---\n", "原始文本: 黃郁翔\n", "加油啊！\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: 黃郁翔\n", "加油啊！\n", "1小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['黃郁翔', '加油啊！', '1小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油啊！\n", "已添加留言: 加油啊！...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 黃郁翔\n", "已添加使用者: 黃郁翔\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 8 個元素 ---\n", "原始文本: 胡素眞\n", "反罷免，票投不同意，加油囉！\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: 胡素眞\n", "反罷免，票投不同意，加油囉！\n", "1小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['胡素眞', '反罷免，票投不同意，加油囉！', '1小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 反罷免，票投不同意，加油囉！\n", "已添加留言: 反罷免，票投不同意，加油囉！...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 胡素眞\n", "已添加使用者: 胡素眞\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 9 個元素 ---\n", "原始文本: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "加油！不同意霸免+1\n", "1小時\n", "讚\n", "回覆\n", "已編輯...\n", "清理後文本: HsinTsung Tsai\n", "加油！不同意霸免+1\n", "1小時\n", "\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['HsinTsung Tsai', '加油！不同意霸免+1', '1小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油！不同意霸免+1\n", "已添加留言: 加油！不同意霸免+1...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON><PERSON><PERSON><PERSON>g Tsai\n", "已添加使用者: HsinTsung Tsai\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 10 個元素 ---\n", "原始文本: 彭怡翔\n", "罷起來，貫老闆消失\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: 彭怡翔\n", "罷起來，貫老闆消失\n", "1小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['彭怡翔', '罷起來，貫老闆消失', '1小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 罷起來，貫老闆消失\n", "已添加留言: 罷起來，貫老闆消失...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 彭怡翔\n", "已添加使用者: 彭怡翔\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 11 個元素 ---\n", "原始文本: 頭號粉絲\n", "廖馨心\n", "太棒了，我想要\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: \n", "廖馨心\n", "太棒了，我想要\n", "1小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['廖馨心', '太棒了，我想要', '1小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 太棒了，我想要\n", "已添加留言: 太棒了，我想要...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 廖馨心\n", "已添加使用者: 廖馨心\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 12 個元素 ---\n", "原始文本: 高美文\n", "南部人很想要\n", "太可惜了！\n", "1小時\n", "讚\n", "回覆...\n", "清理後文本: 高美文\n", "南部人很想要\n", "太可惜了！\n", "1小時\n", "\n", "...\n", "分割後行數: 4\n", "各行內容: ['高美文', '南部人很想要', '太可惜了！', '1小時']\n", "包含時間的行索引: [3]\n", "多行模式，留言內容: 南部人很想要 太可惜了！\n", "已添加留言: 南部人很想要 太可惜了！...\n", "找到時間: 1小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 高美文\n", "已添加使用者: 高美文\n", "最後一行值: 1小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 13 個元素 ---\n", "原始文本: <PERSON>\n", "7/26出門投「不同意罷免」，支持認真優質的委員為選民服務，加油\n", "2小時\n", "讚\n", "回覆\n", "2...\n", "清理後文本: <PERSON>\n", "7/26出門投「不同意罷免」，支持認真優質的委員為選民服務，加油\n", "2小時\n", "\n", "\n", "2...\n", "分割後行數: 4\n", "各行內容: ['<PERSON>', '7/26出門投「不同意罷免」，支持認真優質的委員為選民服務，加油', '2小時', '2']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 7/26出門投「不同意罷免」，支持認真優質的委員為選民服務，加油\n", "已添加留言: 7/26出門投「不同意罷免」，支持認真優質的委員為選民服務，加油...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 2\n", "數字格式，按讚數: 2\n", "已添加按讚數: 2\n", "\n", "--- 處理第 14 個元素 ---\n", "原始文本: 陳喜龍\n", "加油加油，支持反罷免，藍白立委一席都不能少\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 陳喜龍\n", "加油加油，支持反罷免，藍白立委一席都不能少\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['陳喜龍', '加油加油，支持反罷免，藍白立委一席都不能少', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油加油，支持反罷免，藍白立委一席都不能少\n", "已添加留言: 加油加油，支持反罷免，藍白立委一席都不能少...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 陳喜龍\n", "已添加使用者: 陳喜龍\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 15 個元素 ---\n", "原始文本: 李蘾\n", "求川普別在726前公布對台關稅，否則大罷免可能功虧一簣！\n", "4小時\n", "讚\n", "回覆...\n", "清理後文本: 李蘾\n", "求川普別在726前公布對台關稅，否則大罷免可能功虧一簣！\n", "4小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['李蘾', '求川普別在726前公布對台關稅，否則大罷免可能功虧一簣！', '4小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 求川普別在726前公布對台關稅，否則大罷免可能功虧一簣！\n", "已添加留言: 求川普別在726前公布對台關稅，否則大罷免可能功虧一簣！...\n", "找到時間: 4小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 李蘾\n", "已添加使用者: 李蘾\n", "最後一行值: 4小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 16 個元素 ---\n", "原始文本: 胡了\n", "加油\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 胡了\n", "加油\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['胡了', '加油', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 胡了\n", "已添加使用者: 胡了\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 17 個元素 ---\n", "原始文本: 安信懷\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "2小時\n", "讚\n", "回覆\n", "3...\n", "清理後文本: 安信懷\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "2小時\n", "\n", "\n", "3...\n", "分割後行數: 15\n", "各行內容: ['安信懷', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '0726反惡罷教訓賴！', '0726反惡霸民進黨！', '2小時', '3']\n", "包含時間的行索引: [13]\n", "多行模式，留言內容: 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！\n", "已添加留言: 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726反惡罷教訓賴！ 0726反惡霸民進黨！ 0726...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 安信懷\n", "已添加使用者: 安信懷\n", "最後一行值: 3\n", "數字格式，按讚數: 3\n", "已添加按讚數: 3\n", "\n", "--- 處理第 18 個元素 ---\n", "原始文本: 安信懷\n", "現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因：\n", "1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。\n", "民進黨認為只要鼓動他們40%的選民中25%投下同意罷免票，而藍白和中間選民可能因為厭惡罷免或對此罷免通過要件不清楚而未出來投反對票，民進黨即可穩操勝券，經由罷免將立院結構翻轉，重回過去他們ㄧ黨獨大可以為所欲為的狀態。\n", "...\n", "清理後文本: 安信懷\n", "現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因：\n", "1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。\n", "民進黨認為只要鼓動他們40%的選民中25%投下同意罷免票，而藍白和中間選民可能因為厭惡罷免或對此罷免通過要件不清楚而未出來投反對票，民進黨即可穩操勝券，經由罷免將立院結構翻轉，重回過去他們ㄧ黨獨大可以為所欲為的狀態。\n", "...\n", "分割後行數: 7\n", "各行內容: ['安信懷', '現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因：', '1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。', '民進黨認為只要鼓動他們40%的選民中25%投下同意罷免票，而藍白和中間選民可能因為厭惡罷免或對此罷免通過要件不清楚而未出來投反對票，民進黨即可穩操勝券，經由罷免將立院結構翻轉，重回過去他們ㄧ黨獨大可以為所欲為的狀態。', '所以，如果你反對大罷免，不是坐在家裡不投票，而是要出來投下‘’不同意罷免票‘’。', '2小時', '2']\n", "包含時間的行索引: [5]\n", "多行模式，留言內容: 現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因： 1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。 民進黨認為只要鼓動他們40%的選民中25%投下同意罷免票，而藍白和中間選民可能因為厭惡罷免或對此罷免通過要件不清楚而未出來投反對票，民進黨即可穩操勝券，經由罷免將立院結構翻轉，重回過去他們ㄧ黨獨大可以為所欲為的狀態。 所以，如果你反對大罷免，不是坐在家裡不投票，而是要出來投下‘’不同意罷免票‘’。\n", "已添加留言: 現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因： 1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。 民進黨認為只要鼓動他們40...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 安信懷\n", "已添加使用者: 安信懷\n", "最後一行值: 2\n", "數字格式，按讚數: 2\n", "已添加按讚數: 2\n", "\n", "--- 處理第 19 個元素 ---\n", "原始文本: <PERSON>\n", "加油 挺住\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "加油 挺住\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '加油 挺住', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油 挺住\n", "已添加留言: 加油 挺住...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 20 個元素 ---\n", "原始文本: 洪沛榆\n", "委員加油，反惡霸，不同意霸免\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 洪沛榆\n", "委員加油，反惡霸，不同意霸免\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['洪沛榆', '委員加油，反惡霸，不同意霸免', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 委員加油，反惡霸，不同意霸免\n", "已添加留言: 委員加油，反惡霸，不同意霸免...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 洪沛榆\n", "已添加使用者: 洪沛榆\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 21 個元素 ---\n", "原始文本: 黃國隆\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 黃國隆\n", "2小時\n", "\n", "...\n", "分割後行數: 2\n", "各行內容: ['黃國隆', '2小時']\n", "行數不足3，清空處理\n", "包含時間的行索引: []\n", "無時間模式，留言內容: \n", "留言內容為空，跳過\n", "未找到時間資訊\n", "未找到使用者名稱\n", "最後一行值: \n", "按讚數為 None，跳過\n", "\n", "--- 處理第 22 個元素 ---\n", "原始文本: 張皓祐\n", "委員加油不同意罷免\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 張皓祐\n", "委員加油不同意罷免\n", "2小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['張皓祐', '委員加油不同意罷免', '2小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 委員加油不同意罷免\n", "已添加留言: 委員加油不同意罷免...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 張皓祐\n", "已添加使用者: 張皓祐\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 23 個元素 ---\n", "原始文本: 陳盈泰\n", "元之辛苦了\n", "7/26大家出來投不同意罷免加油加油\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 陳盈泰\n", "元之辛苦了\n", "7/26大家出來投不同意罷免加油加油\n", "2小時\n", "\n", "...\n", "分割後行數: 4\n", "各行內容: ['陳盈泰', '元之辛苦了', '7/26大家出來投不同意罷免加油加油', '2小時']\n", "包含時間的行索引: [3]\n", "多行模式，留言內容: 元之辛苦了 7/26大家出來投不同意罷免加油加油\n", "已添加留言: 元之辛苦了 7/26大家出來投不同意罷免加油加油...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 陳盈泰\n", "已添加使用者: 陳盈泰\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 24 個元素 ---\n", "原始文本: 楊承翰\n", "誰掏空了台灣\n", "▍洗錢兩千億的涂誠文 跑了\n", "▍洗錢兩百億的郭哲敏 跑了\n", "▍洗錢百億的賭王林秉文三百萬交保，也跑了\n", "▍炒作TDR的股市禿鷹鐘文智判刑30年 這個月也跑了\n", "▍掏空台鹽11的綠高官陳啟昱地檢署讓他無保請回 隔天就跑了\n", "▍如興老闆 陳仕修\n", "詐騙國發基金14億\n", "也跑了\n", "▍獵雷艦弊案主嫌陳偉志詐騙公股銀行兩百億五百萬交保 也跑了\n", "▍▍▍唯一查不到貪污金流的柯文哲\n", "「繼續延押、羈押禁見」\n", "...\n", "清理後文本: 楊承翰\n", "誰掏空了台灣\n", "▍洗錢兩千億的涂誠文 跑了\n", "▍洗錢兩百億的郭哲敏 跑了\n", "▍洗錢百億的賭王林秉文三百萬交保，也跑了\n", "▍炒作TDR的股市禿鷹鐘文智判刑30年 這個月也跑了\n", "▍掏空台鹽11的綠高官陳啟昱地檢署讓他無保請回 隔天就跑了\n", "▍如興老闆 陳仕修\n", "詐騙國發基金14億\n", "也跑了\n", "▍獵雷艦弊案主嫌陳偉志詐騙公股銀行兩百億五百萬交保 也跑了\n", "▍▍▍唯一查不到貪污金流的柯文哲\n", "「繼續延押、羈押禁見」\n", "...\n", "分割後行數: 35\n", "各行內容: ['楊承翰', '誰掏空了台灣', '▍洗錢兩千億的涂誠文 跑了', '▍洗錢兩百億的郭哲敏 跑了', '▍洗錢百億的賭王林秉文三百萬交保，也跑了', '▍炒作TDR的股市禿鷹鐘文智判刑30年 這個月也跑了', '▍掏空台鹽11的綠高官陳啟昱地檢署讓他無保請回 隔天就跑了', '▍如興老闆 陳仕修', '詐騙國發基金14億', '也跑了', '▍獵雷艦弊案主嫌陳偉志詐騙公股銀行兩百億五百萬交保 也跑了', '▍▍▍唯一查不到貪污金流的柯文哲', '「繼續延押、羈押禁見」', '另外：', '如興弊案15 億元', '新竹棒球場12 億元', '台鹽綠能11 億元', '鍾文智案4.9 億元', '就業安定基金0.7 億元', '潤寅詐貸案400 億元', '獵雷艦案200 億元', '力暘光電91 億元', '聯合再生70 億元', '遠航超貸案60 億元', '體育協會40 億元', '三立王牌交易所詐騙案22.6 億元', '大同炒股案12.5 億元', '桃園八德農地案10 億元', '超思雞蛋5.2 億元', '大創案4 億元', '永豐金超貸案3 億元', '吳乃仁1.7 億元', '弊案金額總計 963.6 億元', '掏空台灣總共3300多億,全民買單', '2小時']\n", "包含時間的行索引: [34]\n", "多行模式，留言內容: 誰掏空了台灣 ▍洗錢兩千億的涂誠文 跑了 ▍洗錢兩百億的郭哲敏 跑了 ▍洗錢百億的賭王林秉文三百萬交保，也跑了 ▍炒作TDR的股市禿鷹鐘文智判刑30年 這個月也跑了 ▍掏空台鹽11的綠高官陳啟昱地檢署讓他無保請回 隔天就跑了 ▍如興老闆 陳仕修 詐騙國發基金14億 也跑了 ▍獵雷艦弊案主嫌陳偉志詐騙公股銀行兩百億五百萬交保 也跑了 ▍▍▍唯一查不到貪污金流的柯文哲 「繼續延押、羈押禁見」 另外： 如興弊案15 億元 新竹棒球場12 億元 台鹽綠能11 億元 鍾文智案4.9 億元 就業安定基金0.7 億元 潤寅詐貸案400 億元 獵雷艦案200 億元 力暘光電91 億元 聯合再生70 億元 遠航超貸案60 億元 體育協會40 億元 三立王牌交易所詐騙案22.6 億元 大同炒股案12.5 億元 桃園八德農地案10 億元 超思雞蛋5.2 億元 大創案4 億元 永豐金超貸案3 億元 吳乃仁1.7 億元 弊案金額總計 963.6 億元 掏空台灣總共3300多億,全民買單\n", "已添加留言: 誰掏空了台灣 ▍洗錢兩千億的涂誠文 跑了 ▍洗錢兩百億的郭哲敏 跑了 ▍洗錢百億的賭王林秉文三百萬交保，也跑了 ▍炒作TDR的股市禿鷹鐘文智判刑30年 這個月也跑了 ▍掏空台鹽11的綠高官陳啟昱地檢署...\n", "找到時間: 2小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 楊承翰\n", "已添加使用者: 楊承翰\n", "最後一行值: 2小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 25 個元素 ---\n", "原始文本: 鄭宙王\n", "2小時\n", "讚\n", "回覆...\n", "清理後文本: 鄭宙王\n", "2小時\n", "\n", "...\n", "分割後行數: 2\n", "各行內容: ['鄭宙王', '2小時']\n", "行數不足3，清空處理\n", "包含時間的行索引: []\n", "無時間模式，留言內容: \n", "留言內容為空，跳過\n", "未找到時間資訊\n", "未找到使用者名稱\n", "最後一行值: \n", "按讚數為 None，跳過\n", "\n", "--- 處理第 26 個元素 ---\n", "原始文本: 謝雲雲\n", "加油加油\n", "5小時\n", "讚\n", "回覆\n", "6...\n", "清理後文本: 謝雲雲\n", "加油加油\n", "5小時\n", "\n", "\n", "6...\n", "分割後行數: 4\n", "各行內容: ['謝雲雲', '加油加油', '5小時', '6']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 加油加油\n", "已添加留言: 加油加油...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 謝雲雲\n", "已添加使用者: 謝雲雲\n", "最後一行值: 6\n", "數字格式，按讚數: 6\n", "已添加按讚數: 6\n", "\n", "--- 處理第 27 個元素 ---\n", "原始文本: 林圓圓\n", "元之委員是板橋人的精神支柱！7/26懇賜不同意罷免，讓板橋更進步熱鬧\n", "5小時\n", "讚\n", "回覆\n", "14...\n", "清理後文本: 林圓圓\n", "元之委員是板橋人的精神支柱！7/26懇賜不同意罷免，讓板橋更進步熱鬧\n", "5小時\n", "\n", "\n", "14...\n", "分割後行數: 4\n", "各行內容: ['林圓圓', '元之委員是板橋人的精神支柱！7/26懇賜不同意罷免，讓板橋更進步熱鬧', '5小時', '14']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 元之委員是板橋人的精神支柱！7/26懇賜不同意罷免，讓板橋更進步熱鬧\n", "已添加留言: 元之委員是板橋人的精神支柱！7/26懇賜不同意罷免，讓板橋更進步熱鬧...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 林圓圓\n", "已添加使用者: 林圓圓\n", "最後一行值: 14\n", "數字格式，按讚數: 14\n", "已添加按讚數: 14\n", "\n", "--- 處理第 28 個元素 ---\n", "原始文本: 林金裕\n", "加油\n", "6小時\n", "讚\n", "回覆\n", "4...\n", "清理後文本: 林金裕\n", "加油\n", "6小時\n", "\n", "\n", "4...\n", "分割後行數: 4\n", "各行內容: ['林金裕', '加油', '6小時', '4']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 林金裕\n", "已添加使用者: 林金裕\n", "最後一行值: 4\n", "數字格式，按讚數: 4\n", "已添加按讚數: 4\n", "\n", "--- 處理第 29 個元素 ---\n", "原始文本: 鍾雨潔\n", "葉委員加油\n", "不同意罷免\n", "6小時\n", "讚\n", "回覆\n", "12...\n", "清理後文本: 鍾雨潔\n", "葉委員加油\n", "不同意罷免\n", "6小時\n", "\n", "\n", "12...\n", "分割後行數: 5\n", "各行內容: ['鍾雨潔', '葉委員加油', '不同意罷免', '6小時', '12']\n", "包含時間的行索引: [3]\n", "多行模式，留言內容: 葉委員加油 不同意罷免\n", "已添加留言: 葉委員加油 不同意罷免...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 鍾雨潔\n", "已添加使用者: 鍾雨潔\n", "最後一行值: 12\n", "數字格式，按讚數: 12\n", "已添加按讚數: 12\n", "\n", "--- 處理第 30 個元素 ---\n", "原始文本: 鄧正武\n", "加油\n", "6小時\n", "讚\n", "回覆\n", "6...\n", "清理後文本: 鄧正武\n", "加油\n", "6小時\n", "\n", "\n", "6...\n", "分割後行數: 4\n", "各行內容: ['鄧正武', '加油', '6小時', '6']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 鄧正武\n", "已添加使用者: 鄧正武\n", "最後一行值: 6\n", "數字格式，按讚數: 6\n", "已添加按讚數: 6\n", "\n", "--- 處理第 31 個元素 ---\n", "原始文本: Choco Chlor\n", "加油，不同意罷免\n", "6小時\n", "讚\n", "回覆\n", "19...\n", "清理後文本: Choco Chlor\n", "加油，不同意罷免\n", "6小時\n", "\n", "\n", "19...\n", "分割後行數: 4\n", "各行內容: ['Choco Chlor', '加油，不同意罷免', '6小時', '19']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 加油，不同意罷免\n", "已添加留言: 加油，不同意罷免...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: Choco Chlor\n", "已添加使用者: Choco Chlor\n", "最後一行值: 19\n", "數字格式，按讚數: 19\n", "已添加按讚數: 19\n", "\n", "--- 處理第 32 個元素 ---\n", "原始文本: 張繼慈\n", "葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "5小時\n", "讚\n", "回覆\n", "40...\n", "清理後文本: 張繼慈\n", "葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "5小時\n", "\n", "\n", "40...\n", "分割後行數: 4\n", "各行內容: ['張繼慈', '葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！', '5小時', '40']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "已添加留言: 葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 張繼慈\n", "已添加使用者: 張繼慈\n", "最後一行值: 40\n", "數字格式，按讚數: 40\n", "已添加按讚數: 40\n", "\n", "--- 處理第 33 個元素 ---\n", "原始文本: 吳素珠\n", "加油加油\n", "3小時\n", "讚\n", "回覆...\n", "清理後文本: 吳素珠\n", "加油加油\n", "3小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['吳素珠', '加油加油', '3小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油加油\n", "已添加留言: 加油加油...\n", "找到時間: 3小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 吳素珠\n", "已添加使用者: 吳素珠\n", "最後一行值: 3小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 34 個元素 ---\n", "原始文本: <PERSON>\n", "委員太優秀了啦！\n", "3小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "委員太優秀了啦！\n", "3小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '委員太優秀了啦！', '3小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 委員太優秀了啦！\n", "已添加留言: 委員太優秀了啦！...\n", "找到時間: 3小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 3小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 35 個元素 ---\n", "原始文本: <PERSON> Chang\n", "您辛苦了正義永遠站在你這一邊感謝喔加油加油委員加油加油加油\n", "3小時\n", "讚\n", "回覆\n", "4...\n", "清理後文本: <PERSON> Chang\n", "您辛苦了正義永遠站在你這一邊感謝喔加油加油委員加油加油加油\n", "3小時\n", "\n", "\n", "4...\n", "分割後行數: 4\n", "各行內容: ['<PERSON> Chang', '您辛苦了正義永遠站在你這一邊感謝喔加油加油委員加油加油加油', '3小時', '4']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 您辛苦了正義永遠站在你這一邊感謝喔加油加油委員加油加油加油\n", "已添加留言: 您辛苦了正義永遠站在你這一邊感謝喔加油加油委員加油加油加油...\n", "找到時間: 3小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: Apple Chang\n", "已添加使用者: Apple Chang\n", "最後一行值: 4\n", "數字格式，按讚數: 4\n", "已添加按讚數: 4\n", "\n", "--- 處理第 36 個元素 ---\n", "原始文本: 林世昌\n", "3小時\n", "讚\n", "回覆...\n", "清理後文本: 林世昌\n", "3小時\n", "\n", "...\n", "分割後行數: 2\n", "各行內容: ['林世昌', '3小時']\n", "行數不足3，清空處理\n", "包含時間的行索引: []\n", "無時間模式，留言內容: \n", "留言內容為空，跳過\n", "未找到時間資訊\n", "未找到使用者名稱\n", "最後一行值: \n", "按讚數為 None，跳過\n", "\n", "--- 處理第 37 個元素 ---\n", "原始文本: <PERSON>\n", "加油\n", "3小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "加油\n", "3小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '加油', '3小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 3小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 3小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 38 個元素 ---\n", "原始文本: <PERSON>\n", "加油\n", "4小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON>\n", "加油\n", "4小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON>', '加油', '4小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 4小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>\n", "已添加使用者: <PERSON>\n", "最後一行值: 4小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 39 個元素 ---\n", "原始文本: 蘇淑媛\n", "投不同意罷免\n", "4小時\n", "讚\n", "回覆...\n", "清理後文本: 蘇淑媛\n", "投不同意罷免\n", "4小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['蘇淑媛', '投不同意罷免', '4小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 投不同意罷免\n", "已添加留言: 投不同意罷免...\n", "找到時間: 4小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 蘇淑媛\n", "已添加使用者: 蘇淑媛\n", "最後一行值: 4小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 40 個元素 ---\n", "原始文本: <PERSON><PERSON><PERSON><PERSON> Jack\n", "想要+1\n", "4小時\n", "讚\n", "回覆...\n", "清理後文本: Tzseng Jack\n", "想要+1\n", "4小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['Tzseng Jack', '想要+1', '4小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 想要+1\n", "已添加留言: 想要+1...\n", "找到時間: 4小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: Tzseng Jack\n", "已添加使用者: Tzseng Jack\n", "最後一行值: 4小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 41 個元素 ---\n", "原始文本: 李顯彬\n", "5小時\n", "讚\n", "回覆\n", "2...\n", "清理後文本: 李顯彬\n", "5小時\n", "\n", "\n", "2...\n", "分割後行數: 3\n", "各行內容: ['李顯彬', '5小時', '2']\n", "包含時間的行索引: [1]\n", "3行模式，留言內容: 5小時\n", "已添加留言: 5小時...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 李顯彬\n", "已添加使用者: 李顯彬\n", "最後一行值: 2\n", "數字格式，按讚數: 2\n", "已添加按讚數: 2\n", "\n", "--- 處理第 42 個元素 ---\n", "原始文本: 謝金令\n", "加油，7/26不同意罷免\n", "5小時\n", "讚\n", "回覆\n", "8...\n", "清理後文本: 謝金令\n", "加油，7/26不同意罷免\n", "5小時\n", "\n", "\n", "8...\n", "分割後行數: 4\n", "各行內容: ['謝金令', '加油，7/26不同意罷免', '5小時', '8']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 加油，7/26不同意罷免\n", "已添加留言: 加油，7/26不同意罷免...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 謝金令\n", "已添加使用者: 謝金令\n", "最後一行值: 8\n", "數字格式，按讚數: 8\n", "已添加按讚數: 8\n", "\n", "--- 處理第 43 個元素 ---\n", "原始文本: 黃錦松\n", "7/26票投不同意罷免\n", "5小時\n", "讚\n", "回覆\n", "3...\n", "清理後文本: 黃錦松\n", "7/26票投不同意罷免\n", "5小時\n", "\n", "\n", "3...\n", "分割後行數: 4\n", "各行內容: ['黃錦松', '7/26票投不同意罷免', '5小時', '3']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 7/26票投不同意罷免\n", "已添加留言: 7/26票投不同意罷免...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 黃錦松\n", "已添加使用者: 黃錦松\n", "最後一行值: 3\n", "數字格式，按讚數: 3\n", "已添加按讚數: 3\n", "\n", "--- 處理第 44 個元素 ---\n", "原始文本: 黃錦松\n", "元之委員加油加油\n", "5小時\n", "讚\n", "回覆\n", "2...\n", "清理後文本: 黃錦松\n", "元之委員加油加油\n", "5小時\n", "\n", "\n", "2...\n", "分割後行數: 4\n", "各行內容: ['黃錦松', '元之委員加油加油', '5小時', '2']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 元之委員加油加油\n", "已添加留言: 元之委員加油加油...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 黃錦松\n", "已添加使用者: 黃錦松\n", "最後一行值: 2\n", "數字格式，按讚數: 2\n", "已添加按讚數: 2\n", "\n", "--- 處理第 45 個元素 ---\n", "原始文本: 黃錦松\n", "5小時\n", "讚\n", "回覆\n", "2...\n", "清理後文本: 黃錦松\n", "5小時\n", "\n", "\n", "2...\n", "分割後行數: 3\n", "各行內容: ['黃錦松', '5小時', '2']\n", "包含時間的行索引: [1]\n", "3行模式，留言內容: 5小時\n", "已添加留言: 5小時...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 黃錦松\n", "已添加使用者: 黃錦松\n", "最後一行值: 2\n", "數字格式，按讚數: 2\n", "已添加按讚數: 2\n", "\n", "--- 處理第 46 個元素 ---\n", "原始文本: <PERSON><PERSON>\n", "委員加油！\n", "5小時\n", "讚\n", "回覆\n", "2...\n", "清理後文本: <PERSON><PERSON>\n", "委員加油！\n", "5小時\n", "\n", "\n", "2...\n", "分割後行數: 4\n", "各行內容: ['<PERSON><PERSON> <PERSON>', '委員加油！', '5小時', '2']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 委員加油！\n", "已添加留言: 委員加油！...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON><PERSON>\n", "已添加使用者: <PERSON><PERSON>\n", "最後一行值: 2\n", "數字格式，按讚數: 2\n", "已添加按讚數: 2\n", "\n", "--- 處理第 47 個元素 ---\n", "原始文本: 陳永興\n", "5小時\n", "讚\n", "回覆\n", "2...\n", "清理後文本: 陳永興\n", "5小時\n", "\n", "\n", "2...\n", "分割後行數: 3\n", "各行內容: ['陳永興', '5小時', '2']\n", "包含時間的行索引: [1]\n", "3行模式，留言內容: 5小時\n", "已添加留言: 5小時...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 陳永興\n", "已添加使用者: 陳永興\n", "最後一行值: 2\n", "數字格式，按讚數: 2\n", "已添加按讚數: 2\n", "\n", "--- 處理第 48 個元素 ---\n", "原始文本: 柯順華\n", "郷長下午好 加油加油\n", "5小時\n", "讚\n", "回覆...\n", "清理後文本: 柯順華\n", "郷長下午好 加油加油\n", "5小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['柯順華', '郷長下午好 加油加油', '5小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 郷長下午好 加油加油\n", "已添加留言: 郷長下午好 加油加油...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 柯順華\n", "已添加使用者: 柯順華\n", "最後一行值: 5小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 49 個元素 ---\n", "原始文本: 柯順榮\n", "委員辛苦了 加油\n", "5小時\n", "讚\n", "回覆\n", "3...\n", "清理後文本: 柯順榮\n", "委員辛苦了 加油\n", "5小時\n", "\n", "\n", "3...\n", "分割後行數: 4\n", "各行內容: ['柯順榮', '委員辛苦了 加油', '5小時', '3']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 委員辛苦了 加油\n", "已添加留言: 委員辛苦了 加油...\n", "找到時間: 5小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 柯順榮\n", "已添加使用者: 柯順榮\n", "最後一行值: 3\n", "數字格式，按讚數: 3\n", "已添加按讚數: 3\n", "\n", "--- 處理第 50 個元素 ---\n", "原始文本: <PERSON>i\n", "葉委員 加油！\n", "6小時\n", "讚\n", "回覆\n", "8...\n", "清理後文本: <PERSON>i\n", "葉委員 加油！\n", "6小時\n", "\n", "\n", "8...\n", "分割後行數: 4\n", "各行內容: ['Li O<PERSON>', '葉委員 加油！', '6小時', '8']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 葉委員 加油！\n", "已添加留言: 葉委員 加油！...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON>i\n", "已添加使用者: <PERSON>i\n", "最後一行值: 8\n", "數字格式，按讚數: 8\n", "已添加按讚數: 8\n", "\n", "--- 處理第 51 個元素 ---\n", "原始文本: <PERSON><PERSON><PERSON>\n", "可惜搬家了，好想要\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON><PERSON><PERSON>\n", "可惜搬家了，好想要\n", "6小時\n", "\n", "...\n", "分割後行數: 3\n", "各行內容: ['<PERSON><PERSON><PERSON>', '可惜搬家了，好想要', '6小時']\n", "包含時間的行索引: [2]\n", "3行模式，留言內容: 可惜搬家了，好想要\n", "已添加留言: 可惜搬家了，好想要...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON><PERSON><PERSON>\n", "已添加使用者: <PERSON><PERSON><PERSON>\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 52 個元素 ---\n", "原始文本: 邱鴻炎\n", "加油 加油 加油\n", "6小時\n", "讚\n", "回覆\n", "3...\n", "清理後文本: 邱鴻炎\n", "加油 加油 加油\n", "6小時\n", "\n", "\n", "3...\n", "分割後行數: 4\n", "各行內容: ['邱鴻炎', '加油 加油 加油', '6小時', '3']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 加油 加油 加油\n", "已添加留言: 加油 加油 加油...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 邱鴻炎\n", "已添加使用者: 邱鴻炎\n", "最後一行值: 3\n", "數字格式，按讚數: 3\n", "已添加按讚數: 3\n", "\n", "--- 處理第 53 個元素 ---\n", "原始文本: <PERSON><PERSON>\n", "7/26投不同意罷免\n", "●賴清德規避「國會改革 打貪揭弊 監督制衡」遮遮掩掩閃閃躲躲為哪樁？急欲奪回立法院主導權，顯然窮途末路進退維谷頻頻出賤招，只許罷藍不許罷綠獨裁霸道\n", "●民進黨執政預算細目給查嗎？禁得起查嗎？國人合理懷疑必有貓膩，政府該給國人一個交代\n", "●賴清德是雜質爐渣廢棄物最該被清除罷免自行下台去吧！\n", "6小時\n", "讚\n", "回覆...\n", "清理後文本: <PERSON><PERSON>\n", "7/26投不同意罷免\n", "●賴清德規避「國會改革 打貪揭弊 監督制衡」遮遮掩掩閃閃躲躲為哪樁？急欲奪回立法院主導權，顯然窮途末路進退維谷頻頻出賤招，只許罷藍不許罷綠獨裁霸道\n", "●民進黨執政預算細目給查嗎？禁得起查嗎？國人合理懷疑必有貓膩，政府該給國人一個交代\n", "●賴清德是雜質爐渣廢棄物最該被清除罷免自行下台去吧！\n", "6小時\n", "\n", "...\n", "分割後行數: 6\n", "各行內容: ['<PERSON><PERSON> Chen', '7/26投不同意罷免', '●賴清德規避「國會改革 打貪揭弊 監督制衡」遮遮掩掩閃閃躲躲為哪樁？急欲奪回立法院主導權，顯然窮途末路進退維谷頻頻出賤招，只許罷藍不許罷綠獨裁霸道', '●民進黨執政預算細目給查嗎？禁得起查嗎？國人合理懷疑必有貓膩，政府該給國人一個交代', '●賴清德是雜質爐渣廢棄物最該被清除罷免自行下台去吧！', '6小時']\n", "包含時間的行索引: [5]\n", "多行模式，留言內容: 7/26投不同意罷免 ●賴清德規避「國會改革 打貪揭弊 監督制衡」遮遮掩掩閃閃躲躲為哪樁？急欲奪回立法院主導權，顯然窮途末路進退維谷頻頻出賤招，只許罷藍不許罷綠獨裁霸道 ●民進黨執政預算細目給查嗎？禁得起查嗎？國人合理懷疑必有貓膩，政府該給國人一個交代 ●賴清德是雜質爐渣廢棄物最該被清除罷免自行下台去吧！\n", "已添加留言: 7/26投不同意罷免 ●賴清德規避「國會改革 打貪揭弊 監督制衡」遮遮掩掩閃閃躲躲為哪樁？急欲奪回立法院主導權，顯然窮途末路進退維谷頻頻出賤招，只許罷藍不許罷綠獨裁霸道 ●民進黨執政預算細目給查嗎？禁...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: <PERSON><PERSON>\n", "已添加使用者: <PERSON><PERSON>\n", "最後一行值: 6小時\n", "包含時間單位，按讚數設為 0\n", "已添加按讚數: 0\n", "\n", "--- 處理第 54 個元素 ---\n", "原始文本: 頭號粉絲\n", "郭 民安\n", "7月26日我一定投不同意罷免票，反惡罷。\n", "6小時\n", "讚\n", "回覆\n", "5...\n", "清理後文本: \n", "郭 民安\n", "7月26日我一定投不同意罷免票，反惡罷。\n", "6小時\n", "\n", "\n", "5...\n", "分割後行數: 4\n", "各行內容: ['郭 民安', '7月26日我一定投不同意罷免票，反惡罷。', '6小時', '5']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 7月26日我一定投不同意罷免票，反惡罷。\n", "已添加留言: 7月26日我一定投不同意罷免票，反惡罷。...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 郭 民安\n", "已添加使用者: 郭 民安\n", "最後一行值: 5\n", "數字格式，按讚數: 5\n", "已添加按讚數: 5\n", "\n", "--- 處理第 55 個元素 ---\n", "原始文本: 楊忠明\n", "加油\n", "6小時\n", "讚\n", "回覆\n", "2...\n", "清理後文本: 楊忠明\n", "加油\n", "6小時\n", "\n", "\n", "2...\n", "分割後行數: 4\n", "各行內容: ['楊忠明', '加油', '6小時', '2']\n", "包含時間的行索引: [2]\n", "多行模式，留言內容: 加油\n", "已添加留言: 加油...\n", "找到時間: 6小時\n", "格式化時間: 2025-07-08\n", "使用者名稱: 楊忠明\n", "已添加使用者: 楊忠明\n", "最後一行值: 2\n", "數字格式，按讚數: 2\n", "已添加按讚數: 2\n", "\n", "=== 提取完成 ===\n", "留言數量: 51\n", "時間數量: 51\n", "按讚數數量: 51\n", "使用者數量: 51\n", "範例 1:\n", "  使用者: 張繼慈\n", "  留言: 葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "  時間: 2025-07-08\n", "  按讚: 40\n", "範例 2:\n", "  使用者: 林賢明\n", "  留言: 委員加油加油\n", "  時間: 2025-07-05\n", "  按讚: 13\n", "範例 3:\n", "  使用者: <PERSON>\n", "  留言: 什麼? 還有限制 板橋戶籍喔\n", "  時間: 2025-07-08\n", "  按讚: 0\n", "\n", "開始組織評論結構...\n", "=== 開始組織評論 ===\n", "輸入資料統計:\n", "  留言數量: 51\n", "  時間數量: 51\n", "  按讚數數量: 51\n", "  使用者數量: 51\n", "✅ JSON 已儲存為 comments_data1.json\n"]}], "source": ["\n", "def extract_comments(driver, post_info, filename='12.json'):\n", "    \"\"\"\n", "    從網頁中提取評論，將評論、時間、按讚數和使用者名稱分別添加到各自的列表中，並儲存為 JSON 文件。\n", "    \n", "    參數:\n", "        driver: Selenium WebDriver 物件，用於與瀏覽器交互。\n", "        filename: 儲存 JSON 文件的名稱，預設為 '12.json'。\n", "\n", "    返回:\n", "        dict: 包含 `comments_list`, `times_list`, `likes_list`, `User_list` 的字典。\n", "    \"\"\"\n", "    print(f\"=== 開始提取評論 ===\")\n", "    print(f\"貼文資訊: {post_info}\")\n", "    print(f\"檔案名稱: {filename}\")\n", "    \n", "    comments_list = []\n", "    times_list = []\n", "    likes_list = []\n", "    User_list = []\n", "\n", "    try:\n", "        # 使用 XPath 定位包含指定類名的評論元素\n", "        print(\"正在尋找評論元素...\")\n", "        elements =  driver.find_elements(\n", "    By.XPATH,\n", "    \"//div[contains(@class, 'x1n2onr6') and contains(@class, 'x1g0dm76') and contains(@class, 'x1iorvi4')]\"\n", ")\n", "        print(f\"找到 {len(elements)} 個評論元素\")\n", "        \n", "        # 遍歷元素並清理文本後添加到各列表中\n", "        for i, element in enumerate(elements):\n", "            print(f\"\\n--- 處理第 {i+1} 個元素 ---\")\n", "            text = element.text\n", "            print(f\"原始文本: {text[:200]}...\")  # 只顯示前200字元\n", "            \n", "            likes = None\n", "            user = None\n", "\n", "            # 清除特定的無用文本\n", "            text = re.sub(r'\\s*·\\s*', '', text)\n", "            for phrase in ['追蹤', '讚', '回覆', '已編輯', '頭號粉絲', '翻譯年糕']:\n", "                text = text.replace(phrase, '')\n", "            print(f\"清理後文本: {text[:200]}...\")\n", "\n", "            # 根據行分割並過濾空行\n", "            text_lines = [line for line in text.splitlines() if line]\n", "            print(f\"分割後行數: {len(text_lines)}\")\n", "            print(f\"各行內容: {text_lines}\")\n", "            \n", "            if len(text_lines) < 3:\n", "                print(\"行數不足3，清空處理\")\n", "                text_lines = []  # 若行數不足3，清空\n", "\n", "            # 確定包含時間的行索引\n", "            indices_with_days = [index for index, item in enumerate(text_lines) if re.search(r'(\\d+\\s*[天週分小時])', item)]\n", "            print(f\"包含時間的行索引: {indices_with_days}\")\n", "        \n", "            # 處理留言內容\n", "            if len(text_lines) == 3:\n", "                comment_text = text_lines[1]\n", "                print(f\"3行模式，留言內容: {comment_text}\")\n", "            else:\n", "                if indices_with_days:\n", "                    comment_text = \" \".join(text_lines[1:indices_with_days[-1]])  # 合併為一條留言\n", "                    print(f\"多行模式，留言內容: {comment_text}\")\n", "                else:\n", "                    comment_text = \" \".join(text_lines)\n", "                    print(f\"無時間模式，留言內容: {comment_text}\")\n", "        \n", "            if comment_text:\n", "                comments_list.append(comment_text)  # 非空才加入\n", "                print(f\"已添加留言: {comment_text[:100]}...\")\n", "            else:\n", "                print(\"留言內容為空，跳過\")\n", "                \n", "            # 處理時間\n", "            if indices_with_days:\n", "                first_time = text_lines[indices_with_days[-1]]\n", "                print(f\"找到時間: {first_time}\")\n", "                calculated_date = calculate_date(first_time)\n", "                formatted_date = calculated_date.strftime('%Y-%m-%d')\n", "                times_list.append(formatted_date)\n", "                print(f\"格式化時間: {formatted_date}\")\n", "            else:\n", "                print(\"未找到時間資訊\")\n", "                \n", "            # 處理使用者名稱\n", "            if text_lines:\n", "                user = text_lines[0]\n", "                print(f\"使用者名稱: {user}\")\n", "            if user:\n", "                User_list.append(user)\n", "                print(f\"已添加使用者: {user}\")\n", "            else:\n", "                print(\"未找到使用者名稱\")\n", "\n", "            # 處理按讚數\n", "            last_time_value = text_lines[-1] if text_lines else \"\"\n", "            print(f\"最後一行值: {last_time_value}\")\n", "            if last_time_value:\n", "                if re.search(r'[天分週小時]', last_time_value):\n", "                    likes = 0  # 包含時間單位則按讚數設為 0\n", "                    print(\"包含時間單位，按讚數設為 0\")\n", "                elif last_time_value.isdigit():\n", "                    likes = int(last_time_value)\n", "                    print(f\"數字格式，按讚數: {likes}\")\n", "                else:\n", "                    likes = None\n", "                    print(\"無法解析按讚數\")\n", "            if likes is not None:\n", "                likes_list.append(likes)\n", "                print(f\"已添加按讚數: {likes}\")\n", "            else:\n", "                print(\"按讚數為 None，跳過\")\n", "\n", "        print(f\"\\n=== 提取完成 ===\")\n", "        print(f\"留言數量: {len(comments_list)}\")\n", "        print(f\"時間數量: {len(times_list)}\")\n", "        print(f\"按讚數數量: {len(likes_list)}\")\n", "        print(f\"使用者數量: {len(User_list)}\")\n", "        \n", "        # 顯示前幾個結果作為範例\n", "        for i in range(min(3, len(comments_list))):\n", "            print(f\"範例 {i+1}:\")\n", "            print(f\"  使用者: {User_list[i] if i < len(User_list) else 'N/A'}\")\n", "            print(f\"  留言: {comments_list[i] if i < len(comments_list) else 'N/A'}\")\n", "            print(f\"  時間: {times_list[i] if i < len(times_list) else 'N/A'}\")\n", "            print(f\"  按讚: {likes_list[i] if i < len(likes_list) else 'N/A'}\")\n", "\n", "        # 使用 organize_comments 將結果儲存為 JSON 文件\n", "        print(\"\\n開始組織評論結構...\")\n", "        organized_data = organize_comments(comments_list, times_list, likes_list, User_list, post_info, save_to_file=True, filename=filename)\n", "        return organized_data\n", "        \n", "    except Exception as e:\n", "        print(f\"抓取內容失敗: {e}\")\n", "        import traceback\n", "        print(f\"詳細錯誤: {traceback.format_exc()}\")\n", "\n", "def organize_comments(comments_list, times_list, likes_list, User_list, post_info, save_to_file=False, filename=\"comments_structure.json\"):\n", "    \"\"\"\n", "    將留言組織為單層平鋪結構，並儲存為簡化 JSON 格式。\n", "\n", "    格式範例：\n", "    {\n", "        \"標題\": \"Re: [貼文標題]\",\n", "        \"留言內容\": \"留言文字\",\n", "        \"情感標籤\": \"\",\n", "        \"情緒\": \"\",\n", "        \"日期\": \"2025-07-08\",\n", "        \"用戶\": \"使用者名稱\",\n", "        \"上文留言\": \"被回覆的使用者名稱\"（如果有）\n", "    }\n", "    \"\"\"\n", "    print(f\"=== 開始組織評論 ===\")\n", "    print(f\"輸入資料統計:\")\n", "    print(f\"  留言數量: {len(comments_list)}\")\n", "    print(f\"  時間數量: {len(times_list)}\")\n", "    print(f\"  按讚數數量: {len(likes_list)}\")\n", "    print(f\"  使用者數量: {len(User_list)}\")\n", "\n", "    flat_comment_list = []\n", "\n", "    for i, comment in enumerate(comments_list):\n", "        user = User_list[i] if i < len(User_list) else \"\"\n", "        time_str = times_list[i] if i < len(times_list) else \"\"\n", "        reply_to = \"\"\n", "\n", "        # 嘗試找出「上文留言」：若留言內容中包含其他用戶\n", "        for other_user in User_list:\n", "            if other_user in comment and other_user != user:\n", "                reply_to = other_user\n", "                break\n", "\n", "        flat_comment = {\n", "            \"標題\": f\"{post_info}\",\n", "            \"留言內容\": comment,\n", "            \"情感標籤\": \"\",\n", "            \"情緒\": \"\",\n", "            \"日期\": time_str,\n", "            \"用戶\": user,\n", "            \"上文留言\": f\"{post_info}\"\n", "        }\n", "        flat_comment_list.append(flat_comment)\n", "\n", "    if save_to_file:\n", "        if os.path.exists(filename):\n", "            with open(filename, \"r\", encoding=\"utf-8\") as f:\n", "                existing_data = json.load(f)\n", "            existing_data.extend(flat_comment_list)\n", "        else:\n", "            existing_data = flat_comment_list\n", "\n", "        with open(filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(existing_data, f, ensure_ascii=False, indent=2)\n", "\n", "        print(f\"✅ JSON 已儲存為 {filename}\")\n", "\n", "    return flat_comment_list\n", "\n", "#scrape_and_save_to_csv(driver, 'output32.csv')\n", "def calculate_date(time_string):\n", "    # 获取当前时间\n", "    now = datetime.now()\n", "   \n", "\n", "    # 使用正则表达式匹配时间类型和数值\n", "    week_match = re.search(r'(\\d+)\\s*週', time_string)\n", "    day_match = re.search(r'(\\d+)\\s*天', time_string)\n", "    hour_match = re.search(r'(\\d+)\\s*時', time_string)\n", "    minute_match = re.search(r'(\\d+)\\s*分', time_string)\n", "\n", "    # 初始化用于存储最终时间的变量\n", "    calculated_date = now\n", "\n", "    # 根据匹配到的时间类型调整时间\n", "    if week_match:\n", "        weeks = int(week_match.group(1))\n", "        calculated_date = now - timed<PERSON><PERSON>(weeks=weeks)\n", "    elif day_match:\n", "        days = int(day_match.group(1))\n", "        calculated_date = now - timed<PERSON>ta(days=days)\n", "    elif hour_match:\n", "        hours = int(hour_match.group(1))\n", "        calculated_date = now - timedelta(hours=hours)\n", "    elif minute_match:\n", "        minutes = int(minute_match.group(1))\n", "        calculated_date = now - <PERSON><PERSON><PERSON>(minutes=minutes)\n", "\n", "    \n", "    # 返回最终计算后的时间\n", "    return calculated_date\n", "\n", "\n", "data = extract_comments(driver,\"77\", filename='comments_data1.json')"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💬 留言 1: <PERSON>\n", "什麼? 還有限制 板橋戶籍喔\n", "33分鐘\n", "讚\n", "回覆\n", "💬 留言 2: 信毅\n", "如果7月26號他們沒有被罷免掉，將來在立法院會比現在的囂張脫序的行為超過10倍以上\n", "39分鐘\n", "讚\n", "回覆\n", "💬 留言 3: 李碧雲\n", "50分鐘\n", "讚\n", "回覆\n", "💬 留言 4: <PERSON>\n", "這個好！遇到青鳥隨便抽一張讓他們閉嘴\n", "54分鐘\n", "讚\n", "回覆\n", "💬 留言 5: 黃郁翔\n", "加油啊！\n", "1小時\n", "讚\n", "回覆\n", "💬 留言 6: 胡素眞\n", "反罷免，票投不同意，加油囉！\n", "1小時\n", "讚\n", "回覆\n", "💬 留言 7: <PERSON><PERSON><PERSON><PERSON><PERSON>sai\n", "加油！不同意霸免+1\n", "1小時\n", "讚\n", "回覆\n", "已編輯\n", "💬 留言 8: 彭怡翔\n", "罷起來，貫老闆消失\n", "1小時\n", "讚\n", "回覆\n", "💬 留言 9: 頭號粉絲\n", "廖馨心\n", "太棒了，我想要\n", "1小時\n", "讚\n", "回覆\n", "💬 留言 10: 高美文\n", "南部人很想要\n", "太可惜了！\n", "1小時\n", "讚\n", "回覆\n", "💬 留言 11: <PERSON>\n", "7/26出門投「不同意罷免」，支持認真優質的委員為選民服務，加油\n", "2小時\n", "讚\n", "回覆\n", "2\n", "💬 留言 12: 陳喜龍\n", "加油加油，支持反罷免，藍白立委一席都不能少\n", "2小時\n", "讚\n", "回覆\n", "💬 留言 13: 李蘾\n", "求川普別在726前公布對台關稅，否則大罷免可能功虧一簣！\n", "4小時\n", "讚\n", "回覆\n", "Do<PERSON> Tang\n", "李蘾\n", "就是要讓大罷免功虧一潰，癩缺德為了討好牠的川普老爸把台灣所有的資源產業免費送過去米國，根本不管台灣人的死活\n", "2小時\n", "讚\n", "回覆\n", "\n", "回覆李蘾......\n", "💬 留言 14: 胡了\n", "加油\n", "2小時\n", "讚\n", "回覆\n", "💬 留言 15: 安信懷\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "0726反惡罷教訓賴！\n", "0726反惡霸民進黨！\n", "2小時\n", "讚\n", "回覆\n", "3\n", "💬 留言 16: 安信懷\n", "現行選罷法有關罷免案的立法缺陷是這次民進黨竭盡全力鼓動大罷免的重要原因：\n", "1.罷免案只要有選舉人數中1／4（25%）同意罷免且反對罷免票數低於同意罷免票數，罷免案即會成功。\n", "民進黨認為只要鼓動他們40%的選民中25%投下同意罷免票，而藍白和中間選民可能因為厭惡罷免或對此罷免通過要件不清楚而未出來投反對票，民進黨即可穩操勝券，經由罷免將立院結構翻轉，重回過去他們ㄧ黨獨大可以為所欲為的狀態。\n", "所以，如果你反對大罷免，不是坐在家裡不投票，而是要出來投下‘’不同意罷免票‘’。\n", "2小時\n", "讚\n", "回覆\n", "2\n", "💬 留言 17: <PERSON>\n", "加油 挺住\n", "2小時\n", "讚\n", "回覆\n", "💬 留言 18: 洪沛榆\n", "委員加油，反惡霸，不同意霸免\n", "2小時\n", "讚\n", "回覆\n", "💬 留言 19: 黃國隆\n", "2小時\n", "讚\n", "回覆\n", "💬 留言 20: 張皓祐\n", "委員加油不同意罷免\n", "2小時\n", "讚\n", "回覆\n", "💬 留言 21: 陳盈泰\n", "元之辛苦了\n", "7/26大家出來投不同意罷免加油加油\n", "2小時\n", "讚\n", "回覆\n", "💬 留言 22: 楊承翰\n", "誰掏空了台灣\n", "▍洗錢兩千億的涂誠文 跑了\n", "▍洗錢兩百億的郭哲敏 跑了\n", "▍洗錢百億的賭王林秉文三百萬交保，也跑了\n", "▍炒作TDR的股市禿鷹鐘文智判刑30年 這個月也跑了\n", "▍掏空台鹽11的綠高官陳啟昱地檢署讓他無保請回 隔天就跑了\n", "▍如興老闆 陳仕修\n", "詐騙國發基金14億\n", "也跑了\n", "▍獵雷艦弊案主嫌陳偉志詐騙公股銀行兩百億五百萬交保 也跑了\n", "▍▍▍唯一查不到貪污金流的柯文哲\n", "「繼續延押、羈押禁見」\n", "另外：\n", "如興弊案15 億元\n", "新竹棒球場12 億元\n", "台鹽綠能11 億元\n", "鍾文智案4.9 億元\n", "就業安定基金0.7 億元\n", "潤寅詐貸案400 億元\n", "獵雷艦案200 億元\n", "力暘光電91 億元\n", "聯合再生70 億元\n", "遠航超貸案60 億元\n", "體育協會40 億元\n", "三立王牌交易所詐騙案22.6 億元\n", "大同炒股案12.5 億元\n", "桃園八德農地案10 億元\n", "超思雞蛋5.2 億元\n", "大創案4 億元\n", "永豐金超貸案3 億元\n", "吳乃仁1.7 億元\n", "弊案金額總計 963.6 億元\n", "掏空台灣總共3300多億,全民買單\n", "2小時\n", "讚\n", "回覆\n", "💬 留言 23: 鄭宙王\n", "2小時\n", "讚\n", "回覆\n", "💬 留言 24: 謝雲雲\n", "加油加油\n", "5小時\n", "讚\n", "回覆\n", "6\n", "作者\n", "葉元之．板橋\n", "謝雲雲\n", "謝謝啦\n", "2小時\n", "讚\n", "回覆\n", "查看全部2則回覆\n", "\n", "回覆謝雲雲......\n", "💬 留言 25: 林圓圓\n", "元之委員是板橋人的精神支柱！7/26懇賜不同意罷免，讓板橋更進步熱鬧\n", "5小時\n", "讚\n", "回覆\n", "14\n", "作者\n", "葉元之．板橋\n", "林圓圓\n", "感謝\n", "2小時\n", "讚\n", "回覆\n", "2\n", "\n", "回覆林圓圓......\n", "💬 留言 26: 林金裕\n", "加油\n", "6小時\n", "讚\n", "回覆\n", "4\n", "作者\n", "葉元之．板橋\n", "林金裕\n", "感謝\n", "2小時\n", "讚\n", "回覆\n", "\n", "回覆林金裕......\n", "💬 留言 27: 鍾雨潔\n", "葉委員加油\n", "不同意罷免\n", "5小時\n", "讚\n", "回覆\n", "12\n", "作者\n", "葉元之．板橋\n", "鍾雨潔\n", "謝謝\n", "2小時\n", "讚\n", "回覆\n", "\n", "回覆鍾雨潔......\n", "💬 留言 28: 鄧正武\n", "加油\n", "6小時\n", "讚\n", "回覆\n", "6\n", "作者\n", "葉元之．板橋\n", "鄧正武\n", "感恩\n", "2小時\n", "讚\n", "回覆\n", "\n", "回覆鄧正武......\n", "💬 留言 29: <PERSON><PERSON> Chlor\n", "加油，不同意罷免\n", "6小時\n", "讚\n", "回覆\n", "19\n", "葉元之．板橋已回覆\n", "  ·\n", "1則回覆\n", "2小時\n", "💬 留言 30: 張繼慈\n", "葉元之委員協助我抓到詐騙集團兩個車手，阻止歹徒奪去我的房地產，非常感謝葉元之委員！\n", "5小時\n", "讚\n", "回覆\n", "40\n", "作者\n", "葉元之．板橋\n", "張繼慈\n", "應該的！\n", "2小時\n", "讚\n", "回覆\n", "17\n", "Купала Иван\n", "張繼慈\n", "歐里桑你真會掰、真會唬爛，應該是高賽吃了不少？哈哈！\n", "19分鐘\n", "讚\n", "回覆\n", "\n", "回覆張繼慈......\n", "💬 留言 31: 吳素珠\n", "加油加油\n", "3小時\n", "讚\n", "回覆\n", "💬 留言 32: <PERSON>\n", "委員太優秀了啦！\n", "3小時\n", "讚\n", "回覆\n", "💬 留言 33: <PERSON>\n", "您辛苦了正義永遠站在你這一邊感謝喔加油加油委員加油加油加油\n", "3小時\n", "讚\n", "回覆\n", "4\n", "💬 留言 34: 林世昌\n", "3小時\n", "讚\n", "回覆\n", "💬 留言 35: <PERSON>\n", "加油\n", "3小時\n", "讚\n", "回覆\n", "💬 留言 36: <PERSON>\n", "加油\n", "4小時\n", "讚\n", "回覆\n", "💬 留言 37: 蘇淑媛\n", "投不同意罷免\n", "4小時\n", "讚\n", "回覆\n", "💬 留言 38: <PERSON><PERSON><PERSON><PERSON> <PERSON>\n", "想要+1\n", "4小時\n", "讚\n", "回覆\n", "💬 留言 39: 李顯彬\n", "5小時\n", "讚\n", "回覆\n", "2\n", "💬 留言 40: 謝金令\n", "加油，7/26不同意罷免\n", "5小時\n", "讚\n", "回覆\n", "8\n", "💬 留言 41: 黃錦松\n", "7/26票投不同意罷免\n", "5小時\n", "讚\n", "回覆\n", "3\n", "💬 留言 42: 黃錦松\n", "元之委員加油加油\n", "5小時\n", "讚\n", "回覆\n", "2\n", "💬 留言 43: 黃錦松\n", "5小時\n", "讚\n", "回覆\n", "2\n", "💬 留言 44: <PERSON><PERSON>\n", "委員加油！\n", "5小時\n", "讚\n", "回覆\n", "2\n", "💬 留言 45: 陳永興\n", "5小時\n", "讚\n", "回覆\n", "2\n", "💬 留言 46: 柯順華\n", "郷長下午好 加油加油\n", "5小時\n", "讚\n", "回覆\n", "💬 留言 47: 柯順榮\n", "委員辛苦了 加油\n", "5小時\n", "讚\n", "回覆\n", "3\n", "💬 留言 48: <PERSON><PERSON>\n", "葉委員 加油！\n", "6小時\n", "讚\n", "回覆\n", "8\n", "💬 留言 49: <PERSON><PERSON><PERSON>\n", "可惜搬家了，好想要\n", "6小時\n", "讚\n", "回覆\n", "💬 留言 50: 邱鴻炎\n", "加油 加油 加油\n", "6小時\n", "讚\n", "回覆\n", "3\n", "💬 留言 51: <PERSON><PERSON>\n", "7/26投不同意罷免\n", "●賴清德規避「國會改革 打貪揭弊 監督制衡」遮遮掩掩閃閃躲躲為哪樁？急欲奪回立法院主導權，顯然窮途末路進退維谷頻頻出賤招，只許罷藍不許罷綠獨裁霸道\n", "●民進黨執政預算細目給查嗎？禁得起查嗎？國人合理懷疑必有貓膩，政府該給國人一個交代\n", "●賴清德是雜質爐渣廢棄物最該被清除罷免自行下台去吧！\n", "6小時\n", "讚\n", "回覆\n", "💬 留言 52: 頭號粉絲\n", "郭 民安\n", "7月26日我一定投不同意罷免票，反惡罷。\n", "6小時\n", "讚\n", "回覆\n", "5\n", "💬 留言 53: 楊忠明\n", "加油\n", "6小時\n", "讚\n", "回覆\n", "2\n", "\n", "✅ 共擷取到 53 筆留言\n"]}], "source": ["contents = driver.find_elements(\n", "    By.XPATH,\n", "    \"//div[contains(@class, 'x18xomjl') and contains(@class, 'xbcz3fp')]\"\n", ")\n", "for i, content in enumerate(contents):\n", "        print(f\"💬 留言 {i+1}: {content.text}\")\n", "\n", "print(f\"\\n✅ 共擷取到 {len(contents)} 筆留言\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}