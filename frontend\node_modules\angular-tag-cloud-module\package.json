{"name": "angular-tag-cloud-module", "version": "19.0.0", "license": "MIT", "peerDependencies": {"@angular/common": "^19.0.0", "@angular/core": "^19.0.0"}, "repository": {"type": "git", "url": "https://github.com/d-koppenhagen/angular-tag-cloud-module.git", "directory": "projects/angular-tag-cloud-module"}, "dependencies": {"tslib": "^2.3.0"}, "module": "fesm2022/angular-tag-cloud-module.mjs", "typings": "index.d.ts", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/angular-tag-cloud-module.mjs"}}, "sideEffects": false}