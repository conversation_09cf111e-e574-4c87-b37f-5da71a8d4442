<div class="politician-detail-container">
  <!-- 標題區 -->
  <div class="profile-header-wrapper">
    <div class="profile-header">
      <div class="avatar-container">
        <img *ngIf="data?.image_url" [src]="data.image_url" cAvatar>
      </div>
      <div class="profile-info">
        <h1 class="profile-name">{{ data?.name }}</h1>
        <span cBadge color="danger" class="profile-badge">{{ party }}</span>
        <div class="profile-meta" *ngIf="district">
          <i class="cil-location-pin"></i>
          <span>{{ district }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 主要內容區 -->
  <div class="content-container">
    <!-- 左側基本資料 -->
    <div class="sidebar-container">
      <div cCard class="info-card">


      <!-- 罷免進度 -->
      <div *ngIf="recallData" cCard class="info-card recall-card">
        <div cCardHeader class="recall-header">
          <i class="cil-warning"></i>
          <span>罷免進度</span>
        </div>
        <div cCardBody>
          <div class="recall-progress">
            <div class="progress-item">
              <div class="progress-label">目前收件</div>
              <div class="progress-value">{{ recallData["目前收件"] }}</div>
            </div>
            <div class="progress-item">
              <div class="progress-label">罷免門檻</div>
              <div class="progress-value">{{ recallData["罷免門檻"] }}</div>
            </div>
            <!-- 正負面聲量比值顯示 - 黑白雙色長條圖 -->
            <div class="sentiment-ratio-container">
              <div class="sentiment-ratio-header">
                <span class="ratio-label">網路聲量比值</span>
                <span class="ratio-total">{{ getTotalUsers() }} 總聲量</span>
              </div>

              <!-- 雙色長條圖 -->
              <div class="dual-color-bar">
                <!-- 正面聲量 (白色) -->
                <div
                  class="positive-bar"
                  [style.width.%]="getOpposePercentage()">
                </div>
                <!-- 負面聲量 (黑色) -->
                <div
                  class="negative-bar"
                  [style.width.%]="getSupportPercentage()">
                </div>

                <!-- 左側文字 (正面) -->
                <div class="bar-text-left">
                  {{ getOpposePercentage() }}% 反對罷免
                  <div class="stat-value positive">{{ positiveCount.toLocaleString() }}人</div>
                </div>

                <!-- 右側文字 (負面) -->
                <div class="bar-text-right">
                  {{ getSupportPercentage() }}% 支持罷免
                  <div class="stat-value negative">{{ negativeCount.toLocaleString() }}人</div>
                </div>
              </div>

              
            </div>
            <div class="progress-item">
              <div class="progress-label">行政區</div>
              <div class="progress-value">{{ recallData["行政區"] }}</div>
            </div>
            <div class="progress-item">
              <div class="progress-label">罷免狀態</div>
              <div class="progress-value status">
                {{ recallData["狀態"] || '網路聲量調查' }}
              </div>
            </div>
            <div class="social-links" *ngIf="recallData['社群連結']">
              <span>社群連結：</span>
              <a *ngFor="let key of objectKeys(recallData['社群連結'])" [href]="recallData['社群連結'][key]" target="_blank">{{ key }}</a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右側圖表區 -->
    <div class="main-container">
      <!-- 時間範圍選擇器 - 優化版本 -->
      <div cCard class="time-filter-card-modern">
        <div cCardHeader class="modern-header">
          <div class="header-content">
            <i class="cil-calendar me-2"></i>
            <span class="header-title">時間範圍篩選</span>
            <div class="loading-indicator" *ngIf="isLoadingTimeData">
              <div class="spinner-border spinner-border-sm text-primary me-2"></div>
              <small class="text-muted">載入中...</small>
            </div>
          </div>
        </div>
        <div cCardBody class="modern-body">
          <!-- 快速篩選按鈕 - 移到最上方 -->
          <div class="quick-filters-modern">
            <div class="filter-label">
              <i class="cil-clock me-1"></i>
              <span>快速選擇：</span>
            </div>
            <div class="filter-buttons">
              <button
                cButton
                [ngClass]="currentFilter === 'week' ? 'btn-primary' : 'btn-outline-primary'"
                size="sm"
                (click)="setQuickFilter('week')"
                [disabled]="isLoadingTimeData"
                class="filter-btn">
                <i class="cil-calendar-check me-1"></i>
                最近一週
              </button>
              <button
                cButton
                [ngClass]="currentFilter === '2weeks' ? 'btn-primary' : 'btn-outline-primary'"
                size="sm"
                (click)="setQuickFilter('2weeks')"
                [disabled]="isLoadingTimeData"
                class="filter-btn">
                <i class="cil-calendar me-1"></i>
                最近兩週
              </button>
              <button
                cButton
                [ngClass]="currentFilter === 'month' ? 'btn-primary' : 'btn-outline-primary'"
                size="sm"
                (click)="setQuickFilter('month')"
                [disabled]="isLoadingTimeData"
                class="filter-btn">
                <i class="cil-calendar me-1"></i>
                最近一個月
              </button>
              <button
                cButton
                [ngClass]="currentFilter === '3months' ? 'btn-primary' : 'btn-outline-primary'"
                size="sm"
                (click)="setQuickFilter('3months')"
                [disabled]="isLoadingTimeData"
                class="filter-btn">
                <i class="cil-calendar-range me-1"></i>
                最近三個月
              </button>
              <button
                cButton
                [ngClass]="currentFilter === '6months' ? 'btn-primary' : 'btn-outline-primary'"
                size="sm"
                (click)="setQuickFilter('6months')"
                [disabled]="isLoadingTimeData"
                class="filter-btn">
                <i class="cil-calendar-range me-1"></i>
                最近六個月
              </button>
              <button
                cButton
                [ngClass]="currentFilter === '1year' ? 'btn-primary' : 'btn-outline-primary'"
                size="sm"
                (click)="setQuickFilter('1year')"
                [disabled]="isLoadingTimeData"
                class="filter-btn">
                <i class="cil-calendar-range me-1"></i>
                最近一年
              </button>
            </div>
          </div>

          <!-- 篩選資訊 -->
          <div class="filter-info-modern" *ngIf="startDate && endDate">
            <div class="info-card">
              <i class="cil-info me-2 text-info"></i>
              <span class="info-text">
                顯示期間：<strong>{{ formatDateRange(startDate, endDate) }}</strong>
                <span class="text-muted ms-2">({{ getDateRangeDays() }} 天)</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="charts-row">
        <!-- 正負面留言比例 -->
        <div cCard class="chart-card">
          <div cCardHeader>
            <i class="cil-chart-pie"></i>
            <span>罷免民意分析 (總計: {{ positiveCount + negativeCount }}人)</span>
            <span *ngIf="isLoadingTimeData" class="spinner-border spinner-border-sm ms-2"></span>
          </div>
          <div cCardBody style="overflow: visible; position: relative;">
            <div style="display: flex; align-items: center; justify-content: center; height: 350px;">
              <!-- 圓餅圖容器 -->
              <div style="width: 300px; height: 300px; position: relative;">
                <c-chart [data]="sentimentChartData" type="doughnut" [options]="doughnutOptions" style="height: 100%; width: 100%;"></c-chart>
              </div>
            </div>
            <!-- 自定義百分比顯示 - 右下角 -->
            <div class="custom-chart-stats">
              <div class="stat-item">
                <span class="color-dot oppose"></span>
                <span class="stat-text">反對 {{ getOpposePercentage() }}%</span>
              </div>
              <div class="stat-item">
                <span class="color-dot support"></span>
                <span class="stat-text">支持 {{ getSupportPercentage() }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 情緒雷達圖 
        <div cCard class="chart-card">
          <div cCardHeader>
            <i class="cil-graph"></i>
            <span>情緒雷達圖</span>
          </div>
          <div cCardBody style="overflow: visible; position: relative;">
            <div style="display: flex; align-items: center; justify-content: center; height: 450px;">
               雷達圖容器 - 增大尺寸 
              <div style="width: 500px; height: 450px; position: relative;">
                <c-chart [data]="radarChartData" type="radar" [options]="radarChartOptions" style="height: 100%; width: 100%;"></c-chart>
              </div>
            </div>
          </div>
        </div>-->
        <div cRow>
      <div cCol xs="12">
        <div cCard class="mb-4">
          <div cCardHeader>
            <i class="cil-text-size"></i>
            <span>熱門關鍵字</span>
            <div class="loading-indicator" *ngIf="isLoadingTimeData">
              <div class="spinner-border spinner-border-sm text-primary ms-2"></div>
              <small class="text-muted">載入中...</small>
            </div>
          </div>
          <div cCardBody style="max-height: 600px; overflow: hidden;">
            <div *ngIf="!isLoadingTimeData && wordCloudData.length > 0" class="wordcloud-container">
              <angular-tag-cloud
                [data]="wordCloudData"
                [width]="wordCloudOptions.width"
                [height]="wordCloudOptions.height"
                [overflow]="wordCloudOptions.overflow"
                [zoomOnHover]="wordCloudOptions.zoomOnHover"
                [realignOnResize]="wordCloudOptions.realignOnResize"
                [randomizeAngle]="wordCloudOptions.randomizeAngle"
                (clicked)="onWordCloudClick($event)">
              </angular-tag-cloud>
            </div>
            <div *ngIf="!isLoadingTimeData && wordCloudData.length === 0" class="no-data-placeholder">
              <p class="text-muted text-center">暫無關鍵字數據</p>
            </div>
            <div *ngIf="isLoadingTimeData" class="loading-placeholder">
              <div class="spinner-border text-primary"></div>
              <p class="mt-3 text-muted">正在載入關鍵字數據...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
      </div>

    </div>

    <!-- 罷免支持度趨勢 - 獨立一列 -->
    <div cRow>
      <div cCol xs="12">
        <div cCard class="mb-4">
          <div cCardHeader>
            <i class="cil-chart-line"></i>
            <span>罷免支持度趨勢</span>
            <div class="loading-indicator" *ngIf="isLoadingTimeData">
              <div class="spinner-border spinner-border-sm text-primary ms-2"></div>
              <small class="text-muted">載入中...</small>
            </div>
          </div>
          <div cCardBody style="overflow-x: auto; overflow-y: visible;">
            <div *ngIf="!isLoadingTimeData" style="display: flex; align-items: center; justify-content: center; height: 280px;">
              <div style="min-width: 600px; width: 100%; height: 250px; position: relative;">
                <c-chart [data]="demoLineChartData" type="line" [options]="lineChartOptions" style="height: 100%; width: 100%;"></c-chart>
              </div>
            </div>
            <div *ngIf="isLoadingTimeData" class="loading-placeholder">
              <div class="spinner-border text-primary"></div>
              <p class="mt-3 text-muted">正在載入趨勢數據...</p>
            </div>
          </div>
        </div>
      </div>
    </div>

     <!-- 回到主畫面按鈕 -->
  <button class="back-to-main-btn" (click)="goToMainPage()" title="回到主畫面">
    <i class="cil-home"></i>
    <span class="btn-text">回到主畫面</span>
  </button>
</div>
    
  </div>
</div>

<ng-template #loading>
  <div class="loading-container">
    <div cSpinner color="primary" size="lg"></div>
    <p>資料載入中...</p>
  </div>
</ng-template>
