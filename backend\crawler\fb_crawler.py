from dotenv import load_dotenv
load_dotenv()
import os
import re
import json
import time
import random
import logging
import traceback
from datetime import datetime, timedelta
import traceback
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('fb_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fb_crawler')

# Facebook 帳號密碼 (建議使用環境變數或設定檔管理)
FB_EMAIL = os.environ.get('FB_EMAIL')
FB_PASSWORD = os.environ.get('FB_PASSWORD')

# -------------------- 基礎操作函數 --------------------
def wait_for_element(driver, by, value, timeout=20):
    """等待元素出現"""
    logger.info(f"等待元素 {by}: {value} (超時: {timeout}秒)")
    try:
        element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
        logger.info(f"成功找到元素 {by}: {value}")
        return element
    except TimeoutException:
        logger.warning(f"等待元素 {by}: {value} 超時")
        # 嘗試直接查找元素，看是否能找到但不可見
        try:
            element = driver.find_element(by, value)
            logger.info(f"元素存在但可能不可見: {by}: {value}")
            return element
        except NoSuchElementException:
            logger.error(f"元素不存在: {by}: {value}")
            return None
    except Exception as e:
        logger.error(f"等待元素時發生錯誤 {by}: {value}: {e}")
        return None

def scroll_down(driver, pixels=2000, interval=2):
    """向下滾動指定像素"""
    driver.execute_script(f"window.scrollBy(0, {pixels});")
    time.sleep(interval)

def click_element_by_xpath(driver, xpath):
    """使用 XPath 點擊元素"""
    try:
        element = driver.find_element(By.XPATH, xpath)
        driver.execute_script("arguments[0].click();", element)
        return True
    except NoSuchElementException:
        print(f"找不到 XPath: {xpath} 的元素")
        return False
    except Exception as e:
        print(f"點擊 XPath: {xpath} 的元素失敗: {e}")
        return False

def get_elements_by_xpath(driver, xpath):
    """使用 XPath 獲取多個元素"""
    try:
        return driver.find_elements(By.XPATH, xpath)
    except Exception as e:
        print(f"獲取 XPath: {xpath} 的多個元素失敗: {e}")
        return []

# -------------------- Facebook 特定操作函數 --------------------

def fb_login(driver, email, password):
    """登入 Facebook"""
    # 首先檢查是否已經登入
    if is_logged_in(driver):
        logger.info("已經登入 Facebook，無需再次登入")
        return True
    
    logger.info("開始 Facebook 登入流程")
    login_url = 'https://www.facebook.com/?stype=lo&flo=1&deoia=1&jlou=AffOCHL-l0XnC2k68NOvDfMhV_gVYHwCg9E7AhEIZoEJjO5kjS15RlgAT9vEKrUrTkgraIQLBDZanSM1W1KrvXdEIXNKgbybnct2e4NaCMP-gg&smuh=3643&lh=Ac_9HO-wmWZRb1iyP5A'
    driver.get(login_url)
    time.sleep(5)

    # 清除可能的彈窗和 cookies 提示
    try:
        buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '允許') or contains(text(), '接受') or contains(text(), 'Accept') or contains(text(), 'Allow')]")
        for button in buttons:
            try:
                button.click()
                logger.info("已點擊接受/允許按鈕")
                time.sleep(1)
            except:
                pass
    except:
        pass

    # 尋找登入表單
    email_input = wait_for_element(driver, By.ID, 'email')
    password_input = wait_for_element(driver, By.ID, 'pass')
    
    if not email_input or not password_input:
        logger.warning("找不到登入框，嘗試其他選擇器")
        # 嘗試其他選擇器
        email_input = driver.find_elements(By.CSS_SELECTOR, "input[name='email']")
        password_input = driver.find_elements(By.CSS_SELECTOR, "input[name='pass']")
        
        if email_input and password_input:
            email_input = email_input[0]
            password_input = password_input[0]
        else:
            logger.error("無法找到登入框")
            return False
    
    # 清除並填寫登入表單
    email_input.clear()
    email_input.send_keys(email)
    password_input.clear()
    password_input.send_keys(password)
    
    # 提交登入表單
    try:
        login_button = driver.find_element(By.CSS_SELECTOR, "button[name='login']")
        login_button.click()
        logger.info("已點擊登入按鈕")
    except:
        try:
            password_input.send_keys(Keys.RETURN)
            logger.info("已使用 Enter 鍵提交登入表單")
        except Exception as e:
            logger.error(f"提交登入表單失敗: {e}")
            return False
    
    # 等待登入完成
    wait_time = 65  # 初始等待時間
    logger.info(f"等待登入完成，等待 {wait_time} 秒")
    time.sleep(wait_time)
    
    # 檢查是否登入成功
    if is_logged_in(driver):
        logger.info("登入成功")
        return True
    else:
        # 如果登入失敗，等待人工介入
        logger.warning("自動登入似乎失敗，等待人工介入完成登入")
        for i in range(3):  # 再等待 3 次，每次 30 秒
            time.sleep(30)
            if is_logged_in(driver):
                logger.info("檢測到人工介入後登入成功")
                return True
        
        logger.error("登入失敗，即使等待人工介入後仍未成功")
        return False

def find_profile_link(driver, name):
    """搜尋人名並取得標記為「立法委員」的個人頁面連結，優先選擇粉絲數最多的"""
    search_url = f'https://www.facebook.com/search/people/?q={name}'
    driver.get(search_url)
    time.sleep(5)
    try:
        # 尋找所有可能的搜尋結果
        search_results = driver.find_elements(By.XPATH, "//div[contains(@class, 'x1yztbdb')]")
        logger.info(f"找到 {len(search_results)} 個搜尋結果")
        
        # 存儲所有候選人
        candidates = []
        
        # 首先尋找標記為「立法委員」的結果
        for result in search_results:
            try:
                # 檢查是否包含「立法委員」標籤
                legislator_tag = result.find_elements(By.XPATH, ".//span[contains(text(), '立法委員')]")
                if legislator_tag:
                    # 檢查是否包含粉絲數信息
                    fans_info = result.find_elements(By.XPATH, ".//span[contains(text(), '位粉絲') or contains(text(), '萬位粉絲')]")
                    fans_count = 0
                    if fans_info:
                        fans_text = fans_info[0].text
                        logger.info(f"找到粉絲信息: {fans_text}")
                        # 提取粉絲數字
                        import re
                        match = re.search(r'([\d,.]+)\s*(?:萬)?位粉絲', fans_text)
                        if match:
                            count_str = match.group(1).replace(',', '')
                            if '萬' in fans_text:
                                fans_count = float(count_str) * 10000
                            else:
                                fans_count = float(count_str)
                    
                    # 找到連結
                    link_element = result.find_element(By.XPATH, ".//a[contains(@href, '/profile.php') or contains(@href, '/people/') or contains(@href, '/' + name) or contains(@href, 'facebook.com')]")
                    if link_element:
                        link = link_element.get_attribute('href')
                        candidates.append((link, fans_count, "立法委員"))
                        logger.info(f"找到立法委員頁面: {link}，粉絲數: {fans_count}")
            except Exception as e:
                logger.error(f"處理立法委員搜尋結果時出錯: {e}")
                continue
        
        # 如果找不到立法委員，尋找「公眾人物」
        if not candidates:
            for result in search_results:
                try:
                    public_figure_tag = result.find_elements(By.XPATH, ".//span[contains(text(), '公眾人物')]")
                    if public_figure_tag:
                        fans_info = result.find_elements(By.XPATH, ".//span[contains(text(), '位粉絲') or contains(text(), '萬位粉絲')]")
                        fans_count = 0
                        if fans_info:
                            fans_text = fans_info[0].text
                            logger.info(f"找到粉絲信息: {fans_text}")
                            match = re.search(r'([\d,.]+)\s*(?:萬)?位粉絲', fans_text)
                            if match:
                                count_str = match.group(1).replace(',', '')
                                if '萬' in fans_text:
                                    fans_count = float(count_str) * 10000
                                else:
                                    fans_count = float(count_str)
                        
                        link_element = result.find_element(By.XPATH, ".//a[contains(@href, '/profile.php') or contains(@href, '/people/') or contains(@href, '/' + name) or contains(@href, 'facebook.com')]")
                        if link_element:
                            link = link_element.get_attribute('href')
                            candidates.append((link, fans_count, "公眾人物"))
                            logger.info(f"找到公眾人物頁面: {link}，粉絲數: {fans_count}")
                except Exception as e:
                    logger.error(f"處理公眾人物搜尋結果時出錯: {e}")
                    continue
        
        # 如果仍然找不到，嘗試名字完全匹配且有粉絲數的結果
        if not candidates:
            for result in search_results:
                try:
                    name_element = result.find_element(By.XPATH, ".//span[contains(@class, 'x193iq5w')]")
                    if name_element and name_element.text.strip() == name:
                        fans_info = result.find_elements(By.XPATH, ".//span[contains(text(), '位粉絲') or contains(text(), '萬位粉絲')]")
                        fans_count = 0
                        if fans_info:
                            fans_text = fans_info[0].text
                            logger.info(f"找到粉絲信息: {fans_text}")
                            match = re.search(r'([\d,.]+)\s*(?:萬)?位粉絲', fans_text)
                            if match:
                                count_str = match.group(1).replace(',', '')
                                if '萬' in fans_text:
                                    fans_count = float(count_str) * 10000
                                else:
                                    fans_count = float(count_str)
                        
                        link_element = result.find_element(By.XPATH, ".//a[contains(@href, '/profile.php') or contains(@href, '/people/') or contains(@href, '/' + name) or contains(@href, 'facebook.com')]")
                        if link_element:
                            link = link_element.get_attribute('href')
                            candidates.append((link, fans_count, "名字匹配"))
                            logger.info(f"找到名字匹配的頁面: {link}，粉絲數: {fans_count}")
                except Exception:
                    continue
        
        # 如果還是找不到，就選擇第一個結果
        if not candidates and search_results:
            try:
                link_element = search_results[0].find_element(By.XPATH, ".//a[contains(@href, '/profile.php') or contains(@href, '/people/') or contains(@href, '/' + name) or contains(@href, 'facebook.com')]")
                if link_element:
                    link = link_element.get_attribute('href')
                    candidates.append((link, 0, "第一個結果"))
                    logger.info(f"使用第一個搜尋結果: {link}")
            except Exception as e:
                logger.error(f"嘗試獲取第一個搜尋結果時出錯: {e}")
        
        # 按粉絲數排序並選擇最佳結果
        if candidates:
            # 按粉絲數從大到小排序
            candidates.sort(key=lambda x: x[1], reverse=True)
            best_link, fans_count, category = candidates[0]
            logger.info(f"選擇最佳結果: {best_link}，類型: {category}，粉絲數: {fans_count}")
            return best_link
        
        # 最後嘗試 - 使用舊的方法
        logger.warning(f"找不到 {name} 的適合頁面，嘗試使用舊方法")
        profile_link_element = wait_for_element(driver, By.XPATH, "//a[contains(@href, '/profile.php') or contains(@href, '/people/') or contains(@href, '/' + name) or contains(@href, 'facebook.com')]")
        if profile_link_element:
            link = profile_link_element.get_attribute('href')
            logger.info(f"使用舊方法獲取連結: {link}")
            return link
    except Exception as e:
        logger.error(f'搜尋 {name} 的 Facebook 個人頁面時發生錯誤: {e}')
    
    logger.error(f"無法找到 {name} 的 Facebook 頁面")
    return None

def navigate_to_posts_tab(driver):
    """導航到個人頁面的「貼文」分頁"""
    try:
        posts_tab = wait_for_element(driver, By.XPATH, "//a[contains(@href, '/posts')]")
        if posts_tab:
            posts_tab.click()
            time.sleep(3)
            return True
    except Exception:
        print("找不到或無法點擊「貼文」分頁")
    return False

def click_and_print_tab_elements(driver):
    """尋找並點擊包含 '則留言' 的元素"""
    try:
        # 嘗試直接使用 XPath 找到包含「則留言」的元素
        logger.info("嘗試直接尋找包含「則留言」的元素")
        comments_elements = driver.find_elements(By.XPATH, "//*[contains(text(),'則留言')]")
        if comments_elements:
            for element in comments_elements:
                try:
                    logger.info(f"找到「則留言」元素: {element.text}")
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(1)
                    driver.execute_script("arguments[0].click();", element)
                    logger.info("成功點擊「則留言」元素")
                    return True
                except Exception as e:
                    logger.error(f"點擊「則留言」元素失敗: {e}")
                    continue
        
        # 如果上面的方法失敗，嘗試使用更多不同的 CSS 選擇器
        selectors = [
            ".html-div.xdj266r.x11i5rnm.xat24cr.x1mh8g0r.xexx8yu.x4uap5.x18d9i69.xkhd6sd",
            "div[role='button']",
            "span[role='button']",
            "div.x1i10hfl",
            "div.x78zum5"
        ]
        
        for selector in selectors:
            logger.info(f"嘗試使用選擇器: {selector}")
            elements = driver.find_elements(By.CSS_SELECTOR, selector)
            logger.info(f"找到 {len(elements)} 個元素")
            
            for element in elements:
                try:
                    text = element.text.strip()
                    if '則留言' in text:
                        logger.info(f"找到「則留言」元素: {text}")
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                        time.sleep(1)
                        driver.execute_script("arguments[0].click();", element)
                        logger.info("成功點擊「則留言」元素")
                        return True
                except Exception as e:
                    logger.error(f"處理元素時發生錯誤: {e}")
                    continue
        
        # 如果仍然找不到，使用原始的方法
        logger.warning("無法直接找到「則留言」元素，嘗試使用 Tab 鍵導航")
        elements = driver.find_elements(By.CSS_SELECTOR, "div[role='button']")
        if elements:
            element = elements[0]
            action = ActionChains(driver)
            action.context_click(element).perform()  # 右鍵點擊
            for _ in range(100):  # 假設最多搜尋 100 次 Tab 鍵
                action.send_keys(Keys.TAB).perform()
                time.sleep(0.1)  # 稍微等待，讓焦點移動完成

                active_element = driver.switch_to.active_element
                try:
                    if active_element.text and '則留言' in active_element.text:
                        try:
                            logger.info(f"通過 Tab 鍵找到「則留言」元素: {active_element.text}")
                            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", active_element)
                            active_element.click()
                            logger.info("成功點擊「則留言」元素")
                            return True
                        except Exception as e:
                            logger.error(f"點擊「則留言」元素失敗: {e}")
                            continue
                except Exception:
                    continue
        
        logger.error("無法找到或點擊「則留言」元素")
        return False

    except Exception as e:
        logger.error(f"click_and_print_tab_elements 發生錯誤: {e}")
        return False

def double_click_on_comment1(driver):
    """點擊 '最相關' 排序"""
    try:
        element = wait_for_element(driver, By.XPATH, "//*[contains(text(), '最相關')]", timeout=10)
        if element:
            ActionChains(driver).click(element).perform()
            return True
    except Exception as e:
        print(f"點擊 '最相關' 元素失敗: {e}")
        return False

def double_click_on_comment2(driver):
    """點擊 '所有留言' 選項"""
    try:
        element = wait_for_element(driver, By.XPATH, "//*[contains(text(), '所有留言')]", timeout=10)
        if element:
            ActionChains(driver).click(element).perform()
            return True
    except Exception as e:
        print(f"點擊 '所有留言' 元素失敗: {e}")
        return False

def process_combined_text(driver):
    """抓取並處理貼文主要內容和時間文本"""
    try:
        # 獲取貼文內容
        elements = driver.find_elements(By.CSS_SELECTOR, '.x193iq5w.xeuugli.x13faqbe.x1vvkbs.x1xmvt09.x1lliihq.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.xudqn12.x3x7a5m.x6prxxf.xvq8zen.xo1l8bm.xzsf02u.x1yc453h')
        all_text_split = []
        for element in elements:
            text = element.text.strip()
            split_text = re.split(r'\n+', text)
            all_text_split.extend(split_text)

        combined_text = ''.join(filter(None, all_text_split)) # 過濾空字串後合併
        combined_text = combined_text.replace('…… 查看更多', '')
        
        # 尋找貼文時間
        post_time = None
        
        # 嘗試方法1：尋找時間戳元素
        try:
            time_elements = driver.find_elements(By.XPATH, "//span[contains(@class, 'x4k7w5x') and contains(@class, 'x1h91t0o')]")
            for time_element in time_elements:
                try:
                    time_text = time_element.text.strip()
                    if re.search(r'\d+\s*(?:分鐘|小時|天|週|月|年)前|昨天|前天|\d{4}年\d{1,2}月\d{1,2}日|\d{1,2}月\d{1,2}日', time_text):
                        logger.info(f"找到貼文時間元素: {time_text}")
                        post_time = time_text
                        break
                except:
                    continue
        except Exception as e:
            logger.error(f"尋找時間戳元素失敗: {e}")
        
        # 嘗試方法2：使用aria-label屬性
        if not post_time:
            try:
                time_elements = driver.find_elements(By.CSS_SELECTOR, "a[aria-label*='小時'], a[aria-label*='分鐘'], a[aria-label*='天'], a[aria-label*='週'], a[aria-label*='月']")
                if time_elements:
                    aria_label = time_elements[0].get_attribute("aria-label")
                    logger.info(f"從aria-label找到時間: {aria_label}")
                    post_time = aria_label
            except Exception as e:
                logger.error(f"從aria-label獲取時間失敗: {e}")
        
        # 嘗試方法3：使用JavaScript獲取所有可能包含時間的元素
        if not post_time:
            try:
                potential_time_elements = driver.execute_script("""
                    return Array.from(document.querySelectorAll('*')).filter(el => {
                        const text = el.innerText;
                        return text && (
                            text.match(/\\d+\\s*(?:分鐘|小時|天|週|月|年)前/) ||
                            text.match(/昨天|前天/) ||
                            text.match(/\\d{4}年\\d{1,2}月\\d{1,2}日/) ||
                            text.match(/\\d{1,2}月\\d{1,2}日/)
                        );
                    });
                """)
                
                if potential_time_elements:
                    post_time = potential_time_elements[0].text.strip()
                    logger.info(f"使用JavaScript找到時間: {post_time}")
            except Exception as e:
                logger.error(f"使用JavaScript獲取時間失敗: {e}")
        
        # 將時間轉換為標準格式
        if post_time:
            standardized_time = standardize_time(post_time)
            combined_text = f"[發布時間: {standardized_time}] {combined_text}"
            logger.info(f"標準化時間: {standardized_time}")
        
        return combined_text
    except Exception as e:
        logger.error(f"處理貼文內容失敗: {e}")
        return ""

def standardize_time(time_string):
    """將各種時間格式轉換為標準的 YYYY-MM-DD 格式"""
    now = datetime.now()
    standardized_date = now
    
    # 處理「X分鐘前」、「X小時前」、「X天前」、「X週前」、「X月前」、「X年前'
    minute_match = re.search(r'(\d+)\s*分鐘前', time_string)
    hour_match = re.search(r'(\d+)\s*小時前', time_string)
    day_match = re.search(r'(\d+)\s*天前', time_string)
    week_match = re.search(r'(\d+)\s*週前', time_string)
    month_match = re.search(r'(\d+)\s*月前', time_string)
    year_match = re.search(r'(\d+)\s*年前', time_string)
    
    # 處理「昨天」、「前天」
    yesterday_match = re.search(r'昨天', time_string)
    day_before_yesterday_match = re.search(r'前天', time_string)
    
    # 處理確切日期格式「YYYY年MM月DD日」和「MM月DD日」
    full_date_match = re.search(r'(\d{4})年(\d{1,2})月(\d{1,2})日', time_string)
    short_date_match = re.search(r'(\d{1,2})月(\d{1,2})日', time_string)
    
    if minute_match:
        standardized_date = now - timedelta(minutes=int(minute_match.group(1)))
    elif hour_match:
        standardized_date = now - timedelta(hours=int(hour_match.group(1)))
    elif day_match:
        standardized_date = now - timedelta(days=int(day_match.group(1)))
    elif week_match:
        standardized_date = now - timedelta(weeks=int(week_match.group(1)))
    elif month_match:
        # 近似計算，一個月按30天計
        standardized_date = now - timedelta(days=int(month_match.group(1)) * 30)
    elif year_match:
        # 近似計算，一年按365天計
        standardized_date = now - timedelta(days=int(year_match.group(1)) * 365)
    elif yesterday_match:
        standardized_date = now - timedelta(days=1)
    elif day_before_yesterday_match:
        standardized_date = now - timedelta(days=2)
    elif full_date_match:
        year = int(full_date_match.group(1))
        month = int(full_date_match.group(2))
        day = int(full_date_match.group(3))
        try:
            standardized_date = datetime(year, month, day)
        except ValueError:
            logger.error(f"無效的日期: {year}-{month}-{day}")
    elif short_date_match:
        month = int(short_date_match.group(1))
        day = int(short_date_match.group(2))
        try:
            # 對於沒有年份的日期，假設是當前年份
            standardized_date = datetime(now.year, month, day)
            # 如果計算出的日期在未來，則假設是去年的日期
            if standardized_date > now:
                standardized_date = datetime(now.year - 1, month, day)
        except ValueError:
            logger.error(f"無效的日期: {now.year}-{month}-{day}")
    
    return standardized_date.strftime('%Y-%m-%d')

def extract_comments(driver, post_info, person_name):
    """提取評論並儲存為 JSON"""
    comments_list = []
    times_list = []
    User_list = []
    likes_list = []

    try:
        # 檢查貼文信息是否包含時間
        post_time = None
        time_match = re.search(r'\[發布時間: ([\d-]+)\]', post_info)
        if time_match:
            post_time = time_match.group(1)
            logger.info(f"從貼文信息中提取到發布時間: {post_time}")
        
        # 獲取所有評論元素
        logger.info("開始獲取評論元素")
        elements = driver.find_elements(By.XPATH, "//*[contains(@class, 'x1r8uery') and contains(@class, 'x1iyjqo2') and contains(@class, 'x6ikm8r') and contains(@class, 'x10wlt62') and contains(@class, 'x1pi30zi')]")
        logger.info(f"找到 {len(elements)} 個可能的評論元素")

        for element in elements:
            try:
                text = element.text
                if not text.strip():
                    continue
                
                # 記錄原始文本以便調試
                logger.debug(f"原始評論文本: {text}")
                
                # 清理文本
                text = re.sub(r'\s*·\s*', '', text)
                for phrase in ['追蹤', '讚', '回覆', '已編輯', '頭號粉絲', '翻譯年糕']:
                    text = text.replace(phrase, '')

                # 分割成行
                text_lines = [line for line in text.splitlines() if line]
                if len(text_lines) < 2:  # 至少需要用戶名和評論
                    continue

                user = text_lines[0]
                comment_text = " ".join(text_lines[1:])

                # 提取時間
                time_patterns = [
                    r'(\d+\s*分鐘前)',
                    r'(\d+\s*小時前)',
                    r'(\d+\s*天前)',
                    r'(\d+\s*週前)',
                    r'(\d+\s*月前)',
                    r'(\d+\s*年前)',
                    r'(昨天|前天)',
                    r'(\d{4}年\d{1,2}月\d{1,2}日)',
                    r'(\d{1,2}月\d{1,2}日)'
                ]
                
                comment_time = None
                for pattern in time_patterns:
                    match = re.search(pattern, text)
                    if match:
                        time_text = match.group(1)
                        comment_time = standardize_time(time_text)
                        logger.info(f"提取到評論時間: {time_text} => {comment_time}")
                        break
                
                # 如果找不到評論時間，則使用貼文時間
                if not comment_time:
                    comment_time = post_time if post_time else datetime.now().strftime('%Y-%m-%d')
                    logger.info(f"使用默認時間: {comment_time}")

                # 提取按讚數
                likes = 0
                likes_match = re.search(r'(\d+)\s*個讚', text)
                if likes_match:
                    likes = int(likes_match.group(1))
                    logger.info(f"提取到按讚數: {likes}")

                # 儲存評論信息
                if comment_text and user:
                    comments_list.append(comment_text)
                    times_list.append(comment_time)
                    User_list.append(user)
                    likes_list.append(likes)
                    logger.info(f"成功提取評論 - 用戶: {user}, 時間: {comment_time}, 讚數: {likes}")
            except Exception as e:
                logger.error(f"處理單個評論時出錯: {e}")
                continue        # 整理並保存數據 - 立即保存當前貼文的資料
        logger.info(f"共提取到 {len(comments_list)} 條評論，立即保存")
        if comments_list:
            file_path = organize_comments(comments_list, times_list, likes_list, User_list, post_info, person_name, save_to_file=True)
            logger.info(f"已成功保存貼文及評論數據到 {file_path}")
    except Exception as e:
        logger.error(f"提取評論過程中發生錯誤: {e}")
        logger.error(traceback.format_exc())

def organize_comments(comments_list, times_list, likes_list, User_list, post_info, person_name, save_to_file=False):
    """
    將貼文和留言組織成結構，並根據需要保存為 JSON 文件，
    檔案名包含人名並儲存在 fbdata 資料夾中。
    
    Returns:
        若 save_to_file=True，返回保存的文件路徑，否則返回資料結構
    """
    data_structure = {
        "貼文內容": post_info,
        "留言": []
    }
    for i, comment in enumerate(comments_list):
        data_structure["留言"].append({
            "用戶名": User_list[i],
            "留言時間": times_list[i],
            "留言內容": comment,
            "按讚數": likes_list[i] if i < len(likes_list) else 0
        })

    if save_to_file:
        output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'facebook')
        filename = f"{person_name}.json"
        filepath = os.path.join(output_dir, filename)
        os.makedirs(output_dir, exist_ok=True)

        existing_data = []
        if os.path.exists(filepath):
            try:
                with open(filepath, "r", encoding="utf-8") as f:
                    existing_data = json.load(f)
            except json.JSONDecodeError:
                existing_data = []
                
        existing_data.append(data_structure)

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=4)
        logger.info(f"JSON 文件已更新為 {filepath}")
        return filepath

    return data_structure

def is_logged_in(driver):
    """檢查是否已登入 Facebook"""
    try:
        # 檢查是否有用戶導航欄，這通常是登入後才會顯示
        user_nav = driver.find_elements(By.XPATH, "//div[@aria-label='帳號' or @aria-label='Account' or contains(@aria-label, '用戶')]")
        if user_nav:
            logger.info("檢測到用戶導航欄，已登入")
            return True
        
        # 檢查是否有個人資料連結，這也是登入後才會顯示
        profile_link = driver.find_elements(By.XPATH, "//a[contains(@href, '/profile.php') or contains(@href, '/me')]")
        if profile_link:
            logger.info("檢測到個人資料連結，已登入")
            return True
        
        # 檢查是否有登入表單，如果有則表示未登入
        login_form = driver.find_elements(By.ID, "email") or driver.find_elements(By.ID, "pass")
        if login_form:
            logger.info("檢測到登入表單，未登入")
            return False
        
        # 嘗試查找其他登入後才會出現的元素
        create_post = driver.find_elements(By.XPATH, "//div[contains(text(), '在想什麼？') or contains(text(), 'What')]")
        if create_post:
            logger.info("檢測到動態發佈框，已登入")
            return True
        
        logger.warning("無法確定登入狀態，預設為未登入")
        return False
    except Exception as e:
        logger.error(f"檢查登入狀態時發生錯誤: {e}")
        return False

def scroll_to_bottom_kk(driver, max_scrolls=50):
    """滾动到頁面底部，直到加載所有內容或達到最大滾動次數"""
    logger.info(f"開始滾動頁面，最大滾動次數: {max_scrolls}")
    last_height = driver.execute_script("return document.body.scrollHeight")
    scroll_count = 0
    
    while scroll_count < max_scrolls:
        # 滾動到頁面底部
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(random.uniform(1.0, 2.5))  # 等待頁面加載
        
        # 計算新的滾動高度
        new_height = driver.execute_script("return document.body.scrollHeight")
        
        # 如果高度沒有變化，則停止滾動
        if new_height == last_height:
            logger.info("頁面高度未變化，停止滾動")
            break
            
        last_height = new_height
        scroll_count += 1
        
        # 每10次滾動輸出一次日誌
        if scroll_count % 10 == 0:
            logger.info(f"已滾動 {scroll_count} 次")
    
    logger.info(f"滾動完成，共滾動 {scroll_count} 次")
    return scroll_count

def click_display_block_right_0px_Chang(driver):
    """點擊所有「查看更多回覆」按鈕，展開全部回覆"""
    try:
        logger.info("開始點擊「查看更多回覆」按鈕")
        # 尋找所有可能的「查看更多回覆」元素
        selectors = [
            "//div[contains(text(), '查看更多回覆') or contains(text(), '查看回覆') or contains(text(), 'View more replies')]",
            "//span[contains(text(), '查看更多回覆') or contains(text(), '查看回覆') or contains(text(), 'View more replies')]",
            "//div[contains(@role, 'button')][contains(text(), '查看') and contains(text(), '回覆')]",
            "//span[contains(@role, 'button')][contains(text(), '查看') and contains(text(), '回覆')]"
        ]
        
        total_clicked = 0
        max_attempts = 20  # 最多嘗試點擊次數
        
        for _ in range(max_attempts):
            clicked = False
            
            for selector in selectors:
                elements = driver.find_elements(By.XPATH, selector)
                logger.info(f"找到 {len(elements)} 個「查看更多回覆」元素")
                
                for element in elements:
                    try:
                        # 滾動到元素位置
                        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                        time.sleep(0.5)
                        
                        # 點擊元素
                        driver.execute_script("arguments[0].click();", element)
                        logger.info("成功點擊一個「查看更多回覆」按鈕")
                        time.sleep(random.uniform(1.0, 2.0))  # 等待展開
                        
                        clicked = True
                        total_clicked += 1
                    except Exception as e:
                        logger.warning(f"點擊「查看更多回覆」按鈕失敗: {e}")
                        continue
            
            # 如果沒有點擊任何元素，則跳出循環
            if not clicked:
                logger.info("沒有更多「查看更多回覆」按鈕可點擊")
                break
        
        logger.info(f"總共點擊了 {total_clicked} 個「查看更多回覆」按鈕")
        return total_clicked > 0
    except Exception as e:
        logger.error(f"點擊「查看更多回覆」按鈕時發生錯誤: {e}")
        return False

def get_last_comment_count(driver):
    """獲取貼文的留言數量"""
    try:
        # 嘗試多種方式獲取留言數
        comment_count_patterns = [
            "//span[contains(text(), '則留言')]",
            "//div[contains(text(), '則留言')]",
            "//span[contains(text(), 'comments')]",
            "//div[contains(text(), 'comments')]"
        ]
        
        for pattern in comment_count_patterns:
            elements = driver.find_elements(By.XPATH, pattern)
            if elements:
                for element in elements:
                    text = element.text.strip()
                    match = re.search(r'(\d+)[^\d]*(?:則留言|comments)', text, re.IGNORECASE)
                    if match:
                        count = int(match.group(1))
                        logger.info(f"找到留言數: {count}")
                        return count
        
        # 如果通過文本無法找到，嘗試其他方法
        elements = driver.find_elements(By.CSS_SELECTOR, "div[role='article']")
        if elements:
            for element in elements:
                try:
                    reply_bar = element.find_element(By.XPATH, ".//div[contains(@aria-label, '回覆') or contains(@aria-label, 'reply')]")
                    if reply_bar:
                        aria_label = reply_bar.get_attribute("aria-label")
                        match = re.search(r'(\d+)', aria_label)
                        if match:
                            count = int(match.group(1))
                            logger.info(f"從aria-label找到留言數: {count}")
                            return count
                except:
                    pass
        
        logger.warning("無法獲取留言數量，使用默認值")
        return 10  # 默認值
    except Exception as e:
        logger.error(f"獲取留言數量時發生錯誤: {e}")
        return 10  # 默認值

def crawl_facebook(name, output_dir='./data/facebook', headless=True):
    """主要爬蟲函數"""
    options = webdriver.ChromeOptions()
    if headless:
        #options.add_argument('--headless')
        options.add_argument('--mute-audio')

    options.add_argument('--window-size=1920,1080')
    options.add_argument('--disable-notifications')  # 禁用通知
    options.add_argument('--disable-popup-blocking')  # 禁用彈窗阻擋
    options.add_argument('--disable-extensions')  # 禁用擴展
    options.add_argument('--disable-infobars')  # 禁用信息欄
    
    driver = None
    try:
        driver = webdriver.Chrome(service=webdriver.chrome.service.Service(ChromeDriverManager().install()), options=options)
        
        # 嘗試登入
        login_success = fb_login(driver, FB_EMAIL, FB_PASSWORD)
        if not login_success:
            logger.error(f"無法登入 Facebook，停止爬取 {name} 的資料")
            return False
        
        # 搜尋人物頁面
        logger.info(f"開始搜尋 {name} 的 Facebook 頁面")
        profile_link = find_profile_link(driver, name)
        if profile_link:
            logger.info(f"成功找到 {name} 的頁面: {profile_link}")
            driver.get(profile_link)
            time.sleep(5)
            
            # 導航到貼文分頁
            if navigate_to_posts_tab(driver):
                logger.info(f"成功導航到 {name} 的貼文分頁")
            else:
                logger.warning(f"無法找到 {name} 的貼文分頁，嘗試使用當前頁面")
            
            # 初始滾動
            driver.execute_script("window.scrollBy(0,1500);")
            time.sleep(3)
            
            # 爬取貼文和留言
            posts_processed = 0
            max_posts = 50  # 限制處理的貼文數
            
            for _ in range(max_posts):
                try:
                    time.sleep(random.uniform(2, 4))
                    comment_clicked = click_and_print_tab_elements(driver)
                    
                    if comment_clicked:
                        logger.info(f"成功點擊第 {posts_processed + 1} 個貼文的留言")
                        time.sleep(random.uniform(2, 3))
                        
                        # 嘗試排序留言
                        if double_click_on_comment1(driver):
                            logger.info("成功點擊「最相關」排序")
                            time.sleep(random.uniform(2, 3))
                        else:
                            logger.warning("無法點擊「最相關」排序")
                        
                        if double_click_on_comment2(driver):
                            logger.info("成功點擊「所有留言」選項")
                            time.sleep(random.uniform(2, 3))
                        else:
                            logger.warning("無法點擊「所有留言」選項")
                        
                        # 處理貼文內容
                        post_text = process_combined_text(driver)
                        logger.info(f"取得貼文內容: {post_text[:50]}...")
                        
                        # 嘗試展開回覆
                        click_display_block_right_0px_Chang(driver)                        
                        time.sleep(random.uniform(2, 3))
                        
                        # 獲取留言數並滾動
                        comment_count = get_last_comment_count(driver)
                        if comment_count is not None:
                            logger.info(f"貼文有 {comment_count} 則留言")
                            scroll_to_bottom_kk(driver, min(comment_count + 50, 500))
                            time.sleep(random.uniform(2, 3))
                        
                        # 提取評論
                        extract_comments(driver, post_text, name)
                        posts_processed += 1
                        
                        # 每處理 5 個貼文，滾動一次
                        if posts_processed % 5 == 0:
                            logger.info("已處理 5 個貼文，執行一次滾動")
                            scroll_to_bottom_kk(driver, 1)
                            time.sleep(random.uniform(2, 3))
                except Exception as e:
                    logger.error(f"處理貼文時發生錯誤: {e}")
                    continue
        
        logger.info("爬取完成")
        return True
    except Exception as e:
        logger.error(f"爬蟲過程中發生錯誤: {e}")
        return False
    finally:
        if driver:
            driver.quit()

def extract_post_urls(driver, name, max_posts=50):
    """
    提取 Facebook 個人頁面的所有貼文 URL 和時間
    並保存到 JSON 文件，以便後續進行爬取
    
    Args:
        driver (WebDriver): Selenium WebDriver 實例
        name (str): 要爬取的人名
        max_posts (int): 最大爬取的貼文數量
    
    Returns:
        list: 貼文 URL 和時間的列表
    """
    logger.info(f"開始提取 {name} 的貼文 URL，最大數量: {max_posts}")
    
    # 確保 href/facebook 目錄存在
    href_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'href', 'facebook')
    os.makedirs(href_dir, exist_ok=True)
    
    # 準備用於保存的文件路徑
    current_date = datetime.now().strftime('%Y%m%d')
    json_file = os.path.join(href_dir, f"{name}.json")
    
    # 檢查是否已有提取的 URL
    if os.path.exists(json_file):
        logger.info(f"找到現有的 URL 文件: {json_file}")
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                posts_data = json.load(f)
                logger.info(f"從文件加載了 {len(posts_data)} 個貼文 URL")
                # 如果已經有足夠的 URL，直接返回
                if len(posts_data) >= max_posts:
                    logger.info(f"已有足夠的 URL ({len(posts_data)} >= {max_posts})，跳過提取")
                    return posts_data
        except Exception as e:
            logger.error(f"讀取現有 URL 文件失敗: {e}")
            posts_data = []
    else:
        posts_data = []
    
    # 導航到貼文頁面
    if navigate_to_posts_tab(driver):
        logger.info(f"成功導航到 {name} 的貼文分頁")
    else:
        logger.warning(f"無法找到 {name} 的貼文分頁，嘗試使用當前頁面")
    
    # 初始滾動
    scroll_count = 0
    posts_found = len(posts_data)
    
    # 保存已處理的 URL，避免重複
    processed_urls = set(item['url'] for item in posts_data)
    
    # 滾動頁面直到找到足夠的貼文或達到最大滾動次數
    while posts_found < max_posts and scroll_count < 200:
        # 滾動頁面
        driver.execute_script("window.scrollBy(0, 800);")
        time.sleep(random.uniform(1.0, 2.0))
        scroll_count += 1
        
        # 每 10 次滾動輸出一次日誌
        if scroll_count % 10 == 0:
            logger.info(f"已滾動 {scroll_count} 次，找到 {posts_found} 個貼文")
        
        try:
            # 查找所有可能是貼文的元素
            post_elements = driver.find_elements(By.CSS_SELECTOR, "div[role='article']")
            
            for post in post_elements:
                try:
                    # 嘗試獲取貼文鏈接
                    url_elements = post.find_elements(By.XPATH, ".//a[contains(@href, '/posts/') or contains(@href, '/videos/') or contains(@href, '/photo/') or contains(@href, '/permalink/')]")
                    
                    for url_element in url_elements:
                        url = url_element.get_attribute('href')
                        
                        # 確保是貼文 URL (過濾掉頁面上的其他鏈接)
                        if url and any(x in url for x in ['/posts/', '/videos/', '/photo/', '/permalink/']):
                            # 過濾掉已處理的 URL
                            if url in processed_urls:
                                continue
                            
                            # 保存到已處理集合
                            processed_urls.add(url)
                            
                            # 嘗試獲取貼文時間
                            time_text = ""
                            try:
                                time_elements = post.find_elements(By.XPATH, ".//a[contains(@href, '/posts/') or contains(@href, '/videos/')]//span[contains(@class, 'x4k7w5x') and contains(@class, 'x1h91t0o')]")
                                
                                if not time_elements:
                                    time_elements = post.find_elements(By.XPATH, ".//span[contains(@class, 'x4k7w5x') and contains(@class, 'x1h91t0o')]")
                                
                                if time_elements:
                                    time_text = time_elements[0].text.strip()
                                    
                                    # 如果時間文本為空，嘗試獲取 aria-label
                                    if not time_text:
                                        time_text = time_elements[0].get_attribute("aria-label") or ""
                            except Exception as e:
                                logger.warning(f"獲取貼文時間失敗: {e}")
                            
                            # 標準化時間
                            post_time = standardize_time(time_text) if time_text else datetime.now().strftime('%Y-%m-%d')
                              # 將 URL 和時間保存到列表
                            posts_data.append({
                                'url': url,
                                'time': post_time,
                                'processed': False  # 標記為未處理
                            })
                            
                            posts_found = len(posts_data)
                            
                            # 每發現一個新 URL 就立即保存到 JSON 文件
                            try:
                                with open(json_file, 'w', encoding='utf-8') as f:
                                    json.dump(posts_data, f, ensure_ascii=False, indent=4)
                                    logger.info(f"發現新 URL，立即保存，當前共有 {posts_found} 個 URL")
                            except Exception as e:
                                logger.error(f"保存 URL 到 JSON 文件失敗: {e}")
                            
                            # 如果找到足夠的貼文，則停止尋找
                            if posts_found >= max_posts:
                                break
                
                except Exception as e:
                    logger.error(f"處理單個貼文元素時出錯: {e}")
                    continue
            
            # 保存當前的進度
            if scroll_count % 5 == 0 and posts_data:
                try:
                    with open(json_file, 'w', encoding='utf-8') as f:
                        json.dump(posts_data, f, ensure_ascii=False, indent=4)
                        logger.info(f"已保存 {len(posts_data)} 個貼文 URL 到文件")
                except Exception as e:
                    logger.error(f"保存 URL 文件失敗: {e}")
                    
        except Exception as e:
            logger.error(f"尋找貼文元素時出錯: {e}")
    
    # 最終保存所有找到的 URL
    if posts_data:
        try:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(posts_data, f, ensure_ascii=False, indent=4)
                logger.info(f"已最終保存 {len(posts_data)} 個貼文 URL 到文件")
        except Exception as e:
            logger.error(f"最終保存 URL 文件失敗: {e}")
    
    logger.info(f"URL 提取完成，共找到 {len(posts_data)} 個貼文 URL")
    return posts_data

def process_post_urls(driver, name):
    """
    處理已提取的貼文 URL，獲取每個貼文的內容和留言
    
    Args:
        driver (WebDriver): Selenium WebDriver 實例
        name (str): 要爬取的人名
    
    Returns:
        int: 成功處理的貼文數量
    """
    logger.info(f"開始處理 {name} 的貼文 URL")
    
    # 準備讀取 URL 文件
    href_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'href', 'facebook')
    current_date = datetime.now().strftime('%Y%m%d')
    json_file = os.path.join(href_dir, f"{name}.json")
    
    # 檢查文件是否存在
    if not os.path.exists(json_file):
        logger.error(f"找不到 URL 文件: {json_file}")
        return 0
    
    # 讀取 URL 數據
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            posts_data = json.load(f)
            logger.info(f"從文件加載了 {len(posts_data)} 個貼文 URL")
    except Exception as e:
        logger.error(f"讀取 URL 文件失敗: {e}")
        return 0
    
    # 開始處理每個 URL
    processed_count = 0
    
    for post_index, post in enumerate(posts_data):
        # 檢查是否已處理
        if post.get('processed', False):
            logger.info(f"跳過已處理的貼文: {post['url']}")
            processed_count += 1
            continue
        
        url = post['url']
        logger.info(f"處理第 {post_index + 1}/{len(posts_data)} 個貼文: {url}")
        
        try:
            # 導航到貼文頁面
            driver.get(url)
            time.sleep(random.uniform(3.0, 5.0))
            
            # 處理貼文內容
            post_text = process_combined_text(driver)
            logger.info(f"取得貼文內容: {post_text[:50]}...")
            
            # 嘗試展開留言
            comment_clicked = click_and_print_tab_elements(driver)
            
            if comment_clicked:
                logger.info(f"成功點擊貼文的留言")
                time.sleep(random.uniform(2.0, 3.0))
                
                # 嘗試排序留言
                if double_click_on_comment1(driver):
                    logger.info("成功點擊「最相關」排序")
                    time.sleep(random.uniform(2.0, 3.0))
                else:
                    logger.warning("無法點擊「最相關」排序")
                
                if double_click_on_comment2(driver):
                    logger.info("成功點擊「所有留言」選項")
                    time.sleep(random.uniform(2.0, 3.0))
                else:
                    logger.warning("無法點擊「所有留言」選項")
                
                # 嘗試展開回覆
                click_display_block_right_0px_Chang(driver)                        
                time.sleep(random.uniform(2.0, 3.0))
                
                # 獲取留言數並滾动
                comment_count = get_last_comment_count(driver)
                if comment_count is not None:
                    logger.info(f"貼文有 {comment_count} 則留言")
                    # 滾动到底部以加載更多留言
                    scroll_to_bottom_kk(driver, min(comment_count + 5, 50))
                
                # 提取留言
                extract_comments(driver, post_text, name)
                
                # 標記為已處理
                post['processed'] = True
                processed_count += 1
                
                # 每處理 5 個貼文保存一次進度
                if processed_count % 5 == 0:
                    try:
                        with open(json_file, 'w', encoding='utf-8') as f:
                            json.dump(posts_data, f, ensure_ascii=False, indent=4)
                            logger.info(f"已保存處理進度，完成 {processed_count}/{len(posts_data)} 個貼文")
                    except Exception as e:
                        logger.error(f"保存處理進度失敗: {e}")
            else:
                logger.warning(f"無法點擊貼文的留言，跳過: {url}")
        
        except Exception as e:
            logger.error(f"處理貼文時出錯: {e}")
            logger.error(traceback.format_exc())
            continue
        
        # 隨機等待，避免被封鎖
        time.sleep(random.uniform(2.0, 5.0))
    
    # 最終保存處理進度
    try:
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(posts_data, f, ensure_ascii=False, indent=4)
            logger.info(f"已完成所有貼文處理，共處理 {processed_count}/{len(posts_data)} 個貼文")
    except Exception as e:
        logger.error(f"最終保存處理進度失敗: {e}")
    
    return processed_count

def crawl_facebook_with_urls(name, output_dir=None, headless=True, max_posts=50, driver=None, quit_driver=True, url_map=None):
    """
    使用兩步驟策略爬取 Facebook :
    1. 先提取所有貼文 URL
    2. 再逐一處理每個 URL，獲取內容和留言
    
    Args:
        name (str): 要爬取的人名
        output_dir (str): 輸出目錄
        headless (bool): 是否使用無頭模式
        max_posts (int): 最大爬取的貼文數量
        driver (WebDriver, optional): 外部傳入的 WebDriver 實例，如果為 None 則創建新的
        quit_driver (bool): 是否在函數結束時關閉瀏覽器，當需要持續使用同一個瀏覽器時設為 False
        url_map (dict, optional): 立委名稱到 Facebook 頁面 URL 的對照表，如果為 None 則使用默認對照表
    
    Returns:
        dict: 爬取結果，包含是否成功以及處理的貼文數量，以及 WebDriver 實例（如果 quit_driver=False）
    """
    logger.info(f"開始使用優化流程爬取 {name} 的 Facebook 資料")
    
    # 設定預設輸出目錄
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data', 'facebook')
    os.makedirs(output_dir, exist_ok=True)
    
    # 如果沒有提供 driver，則創建新的
    new_driver_created = False
    if driver is None:
        new_driver_created = True
        options = webdriver.ChromeOptions()
        if headless:
            options.add_argument('--headless')
            options.add_argument('--mute-audio')

        options.add_argument('--window-size=1920,1080')
        options.add_argument('--disable-notifications')  # 禁用通知
        options.add_argument('--disable-popup-blocking')  # 禁用彈窗阻擋
        options.add_argument('--disable-extensions')  # 禁用擴展
        options.add_argument('--disable-infobars')  # 禁用信息欄
        
        driver = webdriver.Chrome(service=webdriver.chrome.service.Service(ChromeDriverManager().install()), options=options)
    
    result = {"success": False, "processed": 0}
    
    try:
        # 如果是新創建的 driver，需要嘗試登入
        if new_driver_created:
            login_success = fb_login(driver, FB_EMAIL, FB_PASSWORD)
            if not login_success:
                logger.error(f"無法登入 Facebook，停止爬蟲 {name} 的資料")
                result["reason"] = "登入失敗"
                return result
        
        # 使用對照表直接獲取頁面 URL，如果沒有則使用搜尋
        if url_map is None:
            url_map = load_legislator_urls()
        
        profile_link = url_map.get(name)
        
        # 如果對照表中沒有，則使用搜尋
        if not profile_link:
            logger.info(f"對照表中找不到 {name} 的 URL，嘗試搜尋")
            profile_link = find_profile_link(driver, name)
            
            # 如果找到了 URL，可以考慮更新對照表
            if profile_link:
                logger.info(f"通過搜尋找到 {name} 的頁面 URL: {profile_link}，可以添加到對照表")
        
        if profile_link:
            logger.info(f"使用頁面 URL: {profile_link}")
            driver.get(profile_link)
            time.sleep(5)
            
            # 步驟 1: 提取所有貼文 URL
            posts_data = extract_post_urls(driver, name, max_posts)
            
            if not posts_data:
                logger.warning(f"未找到 {name} 的貼文 URL，爬取結束")
                result["reason"] = "無貼文URL"
                return result
            
            # 步驟 2: 處理每個 URL，獲取內容和留言
            processed_count = process_post_urls(driver, name)
            
            logger.info(f"完成 {name} 的 Facebook 爬取，共處理 {processed_count} 個貼文")
            result["success"] = processed_count > 0
            result["processed"] = processed_count
            return result
        else:
            logger.error(f"找不到 {name} 的 Facebook 頁面")
            result["reason"] = "找不到頁面"
            return result
            
    except Exception as e:
        logger.error(f"爬取 {name} 的 Facebook 資料時發生錯誤: {e}")
        logger.error(traceback.format_exc())
        result["reason"] = str(e)
        return result
    finally:
        # 如果指定要關閉瀏覽器且瀏覽器是由此函數創建的，則關閉它
        if quit_driver and new_driver_created and driver:
            driver.quit()
            logger.info(f"已關閉瀏覽器")
        elif not quit_driver:
            logger.info(f"保留瀏覽器會話以供後續使用")
            result["driver"] = driver

def crawl_multiple_legislators(legislators, max_posts=30, headless=True, url_map_file=None):
    """
    使用單一瀏覽器會話連續爬取多位立委的 Facebook 資料
    
    Args:
        legislators (list): 要爬取的立委名單
        max_posts (int): 每位立委最大爬取的貼文數量
        headless (bool): 是否使用無頭模式
        url_map_file (str, optional): 立委 URL 對照表文件路徑，如果為 None 則使用默認對照表
    
    Returns:
        dict: 每位立委的爬取結果
    """
    logger.info(f"開始連續爬取 {len(legislators)} 位立委的 Facebook 資料")
    
    # 載入立委 URL 對照表
    url_map = load_legislator_urls(url_map_file)
    logger.info(f"已載入立委 URL 對照表，包含 {len(url_map)} 位立委")
    
    results = {}
    driver = None
    
    try:
        # 設置 Chrome 選項
        options = webdriver.ChromeOptions()
        if headless:
            options.add_argument('--headless')
            options.add_argument('--mute-audio')
            
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--disable-notifications')  # 禁用通知
        options.add_argument('--disable-popup-blocking')  # 禁用彈窗阻擋
        options.add_argument('--disable-extensions')  # 禁用擴展
        options.add_argument('--disable-infobars')  # 禁用信息欄
        
        # 初始化 WebDriver (只初始化一次)
        driver = webdriver.Chrome(service=webdriver.chrome.service.Service(ChromeDriverManager().install()), options=options)
        
        # 嘗試登入 (只登入一次)
        login_success = fb_login(driver, FB_EMAIL, FB_PASSWORD)
        if not login_success:
            logger.error(f"無法登入 Facebook，停止爬蟲任務")
            return results
        
        # 依次處理每位立委
        for legislator in legislators:
            try:
                logger.info(f"開始爬取 {legislator} 的 Facebook 資料")
                
                # 使用 URL 對照表直接訪問頁面，而不是搜尋
                result = crawl_facebook_with_urls(
                    legislator, 
                    headless=headless, 
                    max_posts=max_posts, 
                    driver=driver, 
                    quit_driver=False,  # 不關閉瀏覽器
                    url_map=url_map  # 使用 URL 對照表
                )
                
                results[legislator] = result
                
                # 在處理下一位立委之前稍作等待
                time.sleep(random.uniform(3.0, 5.0))
            except Exception as e:
                results[legislator] = {"success": False, "processed": 0, "reason": str(e)}
                logger.error(f"爬取 {legislator} 的 Facebook 資料時發生錯誤: {e}")
                logger.error(traceback.format_exc())
        
        logger.info(f"連續爬取任務完成")
        return results
    
    except Exception as e:
        logger.error(f"執行連續爬取任務時發生錯誤: {e}")
        logger.error(traceback.format_exc())
        return results
    
    finally:
        # 關閉瀏覽器
        if driver:
            driver.quit()

def daily_crawler(legislators, target_date=None, url_map_file=None):
    """每日爬蟲，用於爬取指定日期的 Facebook 資料
    
    參數:
        legislators (list): 要爬取的立委名單
        target_date (str, optional): 要爬取的日期，格式為 'YYYY-MM-DD'。默認為 None (即爬取最新數據)。
        url_map_file (str, optional): 立委 URL 對照表文件路徑，如果為 None 則使用默認對照表
    
    返回:
        dict: 每位立委的爬取結果
    """
    logger.info(f"開始爬取 {target_date or '最新'} 的 Facebook 資料，共 {len(legislators)} 位立委")
    
    # 使用連續爬蟲功能爬取資料（一個瀏覽器會話處理所有立委）
    results = crawl_multiple_legislators(
        legislators, 
        max_posts=10,  # 每位立委爬取的最大貼文數量
        headless=True,  # 是否使用無頭模式
        url_map_file=url_map_file  # 使用 URL 對照表
    )
    
    # 輸出爬蟲結果摘要
    success_count = sum(1 for name, result in results.items() if result.get("success", False))
    total_processed = sum(result.get("processed", 0) for result in results.values())
    
    logger.info(f"Facebook 爬蟲完成: {success_count}/{len(legislators)} 位立委成功，共處理 {total_processed} 個貼文")
    
    return results

def load_legislator_urls(url_file=None):
    """
    載入立委名稱到 Facebook 頁面 URL 的對照表
    如果提供了文件路徑，則從文件中讀取；否則返回默認的對照表
    
    Args:
        url_file (str, optional): JSON 文件路徑，包含立委名稱到 URL 的映射。默認為 None。
        
    Returns:
        dict: 立委名稱到 Facebook 頁面 URL 的對照表
    """
    # 默認的對照表 (可根據實際情況擴充)
    default_urls = {
        "馬文君": "https://www.facebook.com/MaWenChun",
        "葉元之": "https://www.facebook.com/profile.php?id=100044232406141",
        "林淑芬": "https://www.facebook.com/lin.s.fen",
        "王婉諭": "https://www.facebook.com/wanyu.kao.1",
        # 可以添加更多立委的 URL
    }
    
    if url_file and os.path.exists(url_file):
        try:
            with open(url_file, 'r', encoding='utf-8') as f:
                logger.info(f"從 {url_file} 載入立委 URL 對照表")
                url_map = json.load(f)
                return url_map
        except Exception as e:
            logger.error(f"載入立委 URL 對照表文件失敗: {e}")
            logger.info("使用默認 URL 對照表")
            return default_urls
    else:
        if url_file:
            logger.warning(f"找不到立委 URL 對照表文件 {url_file}，使用默認對照表")
        return default_urls

def save_legislator_urls(url_map, url_file):
    """
    保存立委名稱到 Facebook 頁面 URL 的對照表
    
    Args:
        url_map (dict): 立委名稱到 URL 的映射
        url_file (str): 保存的 JSON 文件路徑
    
    Returns:
        bool: 是否成功保存
    """
    try:
        # 確保目錄存在
        os.makedirs(os.path.dirname(url_file), exist_ok=True)
        
        with open(url_file, 'w', encoding='utf-8') as f:
            json.dump(url_map, f, ensure_ascii=False, indent=4)
            logger.info(f"已保存立委 URL 對照表到 {url_file}")
        return True
    except Exception as e:
        logger.error(f"保存立委 URL 對照表失敗: {e}")
        return False

def crawl_facebook_with_pool(name, webdriver_pool, last_crawled_time=None, max_threads=1):
    """
    使用 WebDriver 池爬取 Facebook 資料
    
    參數:
        name: 要搜尋的立委名稱
        webdriver_pool: WebDriver 池實例
        last_crawled_time: 上次爬取的時間點
        max_threads: 最大線程數（Facebook 建議使用 1 避免限制）
    
    Returns:
        str: 輸出檔案路徑，失敗時返回 None
    """
    print(f"🚀 使用 WebDriver 池爬取 Facebook: {name}")
    
    try:
        # 設定輸出目錄
        current_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(current_dir, 'data', 'facebook')
        os.makedirs(output_dir, exist_ok=True)
        
        # 設定輸出檔案路徑
        out_path = os.path.join(output_dir, f'{name}.json')
        
        # 檢查環境變數
        if not FB_EMAIL or not FB_PASSWORD:
            print("❌ 缺少 Facebook 帳號或密碼環境變數")
            return None
        
        # 使用 WebDriver 池中的 driver
        with webdriver_pool.get_driver() as driver:
            print(f"🔐 使用帳號 {FB_EMAIL} 登入 Facebook...")
            
            # 執行登入
            if not fb_login(driver, FB_EMAIL, FB_PASSWORD):
                print("❌ Facebook 登入失敗")
                return None
            
            print(f"🔍 搜尋立委: {name}")
            
            # 使用現有的 crawl_facebook 函數
            result = crawl_facebook(name, output_dir, headless=True)
            
            if result:
                print(f"✅ Facebook 爬取成功")
                return out_path
            else:
                print(f"⚠️  Facebook 爬取無結果")
                return None
                
    except Exception as e:
        print(f"❌ Facebook 爬取失敗: {e}")
        return None