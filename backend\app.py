from flask import Flask, jsonify, request, abort
import json
import os
import requests
import threading
import time
from flask_cors import CORS
from src.legislator import legislator_app, ensure_database_indexes
from dotenv import load_dotenv
from config import get_config
from cls_data_to_mongo import main as cls_data_to_mongo
import logging
from datetime import datetime
from src.visitor_counter import visitor_counter_bp  # 導入訪問計數器藍圖

# 設定爬蟲排程的日誌
logging.basicConfig(level=logging.INFO)

# 先載入環境變數
load_dotenv()

# 檢測是否在 Render 環境中運行，並在創建 Flask 應用前設置環境
if 'RENDER' in os.environ or 'IS_RENDER' in os.environ:
    os.environ['IS_RENDER'] = 'true'
    # 在 Render 環境中強制使用 production 配置
    os.environ['FLASK_CONFIG'] = 'production'
    print("🚀 檢測到 Render 環境，強制設置為 production 配置")
else:
    print(f"使用 {os.environ.get('FLASK_CONFIG', 'development')} 配置")

# 確保生產環境使用正確的 MongoDB URI
if os.environ.get('FLASK_CONFIG') == 'production' and not os.environ.get('RAILWAY_URI'):
    print("⚠️ 警告: 生產環境未設置 RAILWAY_URI 環境變數")

# 創建 Flask 應用並載入配置
app = Flask(__name__)
config_class = get_config()
app.config.from_object(config_class)


CORS(app,
     origins=[
         "http://localhost:4200",
         "https://legislative-recall-frontend.netlify.app"
     ],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     allow_headers=['Content-Type', 'Authorization'],
     supports_credentials=True)




app.register_blueprint(legislator_app, url_prefix='/api/legislators')

# 註冊訪問統計藍圖
app.register_blueprint(visitor_counter_bp, url_prefix='/api/visitor')

@app.route('/')
def health_check():
    return jsonify({
        "status": "healthy",
        "message": "Legislative Recall Backend API is running",
        "endpoints": {
            "legislators": "/api/legislators",
            "legislator_detail": "/api/legislators/<name>",
            "recall_list": "/api/legislators/recall",
            "visitor_count": "/api/visitor_counter/count"
        }
    })

@app.route('/health')
def health():
    return jsonify({"status": "healthy"})




if __name__ == '__main__':
    app.run(debug=app.config['DEBUG'], port=5001)
