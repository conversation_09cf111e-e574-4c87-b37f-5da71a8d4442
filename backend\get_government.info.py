import requests
from bs4 import BeautifulSoup
import time
from urllib.parse import urljoin
import json
import os

def get_legislator_base_info():
    """
    爬取立法院現任立法委員的基本資訊：頭像 URL、個人頁面 URL 和名稱。
    """
    list_page_url = "https://www.ly.gov.tw/Pages/List.aspx?nodeid=109"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    try:
        response = requests.get(list_page_url, headers=headers, verify=False) # 禁用 SSL 驗證
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        legislator_list = []
        base_url = "https://www.ly.gov.tw/"

        legislator_containers = soup.find_all('div', class_='col-xs-4 col-sm-3 col-md-3 col-lg-15')

        for container in legislator_containers:
            img_tag = container.find('img', class_='img-thumbnail')
            raw_image_url = img_tag['src'] if img_tag and img_tag.get('src') else None
            image_url = urljoin(base_url, raw_image_url) if raw_image_url else None

            name_div = container.find('div', class_='caption').find('div', class_='legislatorname')
            name = name_div.text.strip() if name_div else "N/A"

            person_url_tag = container.find('a')
            person_url = person_url_tag['href'] if person_url_tag and person_url_tag.get('href') else None

            legislator_list.append({
                "name": name,
                "image_url": image_url,
                "person_url": person_url,
                "constituency": "N/A", # 初始化
                "party": "N/A",      # 初始化
                "education": [],      # 初始化
            })

        return legislator_list

    except requests.exceptions.RequestException as e:
        print(f"爬取立委基本資訊時發生網路請求錯誤：{e}")
        return []
    except Exception as e:
        print(f"解析立委基本資訊網頁時發生錯誤：{e}")
        return []
    finally:
        time.sleep(1)

def get_legislator_details(person_relative_url):
    """
    爬取立法委員個人網頁的詳細資訊，如選區、學經歷、電話、政黨等。
    """
    if not person_relative_url:
        print("未提供個人網頁 URL。")
        return {}
    list_page_base_url = "https://www.ly.gov.tw/Pages/List.aspx?nodeid=109" # 用於解析相對路徑
    full_url = urljoin(list_page_base_url, person_relative_url)

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    details = {}
    try:
        print(f"正在爬取詳細資料：{full_url}")
        response = requests.get(full_url, headers=headers, verify=False, timeout=10) # 禁用 SSL 驗證
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        # 提取選區
        constituency_span = soup.find('div', class_='info-left')
        if constituency_span:
            details['constituency'] = constituency_span.text.strip()
            print(f"選區：{details['constituency']}")
        else:
            details['constituency'] = "N/A"

        # 提取政黨 (從個人頁面嘗試提取，如果找不到則留空)
        party_text_div = soup.find('div', class_='info-party')
        details['party'] = party_text_div.text.strip() if party_text_div else "N/A"

       
    except requests.exceptions.RequestException as e:
        print(f"爬取個人網頁 {full_url} 時發生網路請求錯誤：{e}")
    except Exception as e:
        print(f"解析個人網頁 {full_url} 時發生錯誤：{e}")
    finally:
        time.sleep(0.5)

    return details

def clean_data(legislator_data):
    # 處理 constituency 字串
    if 'constituency' in legislator_data and isinstance(legislator_data['constituency'], str):
        constituency_parts = [part.strip() for part in legislator_data['constituency'].split('\n') if part.strip()]
        legislator_data['constituency'] = constituency_parts

    # 移除 party 鍵
    if 'party' in legislator_data:
        del legislator_data['party']

if __name__ == "__main__":
    legislator_base_info_list = get_legislator_base_info()
    all_legislators_data = []

    if legislator_base_info_list:
        for base_info in legislator_base_info_list:
            legislator_data = base_info.copy()
            if legislator_data['person_url']:
                detailed_info = get_legislator_details(legislator_data['person_url'])
                legislator_data.update(detailed_info)
            clean_data(legislator_data) # 在這裡調用 clean_data 函數
            all_legislators_data.append(legislator_data)
            print(f"已處理立委：{legislator_data['name']}")
            print("-" * 20)

        output_dir = './data'
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        output_filename = './data/now_legislator_info.json'
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(all_legislators_data, f, ensure_ascii=False, indent=4)
        print(f"所有立委詳細資料已保存到 {output_filename}")
    else:
        print("未能成功爬取立委基本資訊。")