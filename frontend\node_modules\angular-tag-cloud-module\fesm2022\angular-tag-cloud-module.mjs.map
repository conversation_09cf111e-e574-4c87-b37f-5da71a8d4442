{"version": 3, "file": "angular-tag-cloud-module.mjs", "sources": ["../../../projects/angular-tag-cloud-module/src/lib/tag-cloud.component.ts", "../../../projects/angular-tag-cloud-module/src/public-api.ts", "../../../projects/angular-tag-cloud-module/src/angular-tag-cloud-module.ts"], "sourcesContent": ["import {\n  Component,\n  ElementRef,\n  Renderer2,\n  HostListener,\n  input,\n  output,\n  effect,\n  inject,\n  model,\n  computed,\n  ChangeDetectionStrategy\n} from '@angular/core';\n\nimport {\n  CloudData,\n  CloudOptions,\n  ZoomOnHoverOptions,\n} from './tag-cloud.interfaces';\n\ninterface CloudOptionsInternal extends CloudOptions {\n  /**\n   * setting the aspect ratio. This value is calculated by the given width and height\n   */\n  aspectRatio: number;\n  center: {\n    x: number;\n    y: number;\n  };\n}\n\nconst DEFAULT_HEIGHT = 400;\nconst DEFAULT_WIDTH = 1;\nconst DEFAULT_STEP = 1;\n\n@Component({\n  standalone: true,\n  selector: 'angular-tag-cloud, ng-tag-cloud, ngtc',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  template: '',\n  styleUrls: ['./tag-cloud.component.scss'],\n})\nexport class TagCloudComponent {\n  data = input<CloudData[]>([]);\n  width = input<number>();\n  height = input<number>();\n  step = input<number>();\n  overflow = input<boolean>();\n  strict = input<boolean>();\n  zoomOnHover = input<ZoomOnHoverOptions>();\n  realignOnResize = input<boolean>();\n  randomizeAngle = input<boolean>();\n  background = input<string>();\n  font = input<string>();\n  delay = input<number>();\n  config = input<CloudOptions>({});\n  log = input<'warn' | 'debug' | false>();\n\n  clicked = output<CloudData>();\n  // dataChanges = output<SimpleChanges>();\n  afterInit = output<void>();\n  afterChecked = output<void>();\n\n  private localConfig = computed(() => {\n    const config = this.config()\n    const localConfig: CloudOptions = {\n      ...config, // override default width params in config object\n      width: this.width() || config.width || 500,\n      height: this.height() || config.height || 300,\n      overflow: this.overflow() ?? (config.overflow || true),\n      strict: this.strict() ?? (config.strict || false),\n      zoomOnHover: this.zoomOnHover() || config.zoomOnHover || {\n        transitionTime: 0,\n        scale: 1,\n        delay: 0,\n      },\n      realignOnResize: this.realignOnResize() ?? (config.realignOnResize || false),\n      randomizeAngle: this.randomizeAngle() ?? (config.randomizeAngle || false),\n      step: this.step() || config.step || 2.0,\n      log: this.log() || config.log || false,\n      delay: this.delay() || config.delay,\n      background: this.background() || config.background,\n      font: this.font() || config.font\n    };\n    return localConfig;\n  });\n\n  public cloudDataHtmlElements: HTMLElement[] = [];\n  private dataArr: CloudData[] = [];\n\n  private options!: CloudOptionsInternal;\n  private timeoutId: number | undefined;\n\n  get calculatedWidth(): number {\n    let width = this.localConfig().width || this.width() || DEFAULT_WIDTH;\n    if (\n      this.el.nativeElement.parentNode.offsetWidth > 0 &&\n      width <= 1 &&\n      width > 0\n    ) {\n      width = this.el.nativeElement.parentNode.offsetWidth * width;\n    }\n    return width;\n  }\n\n  get calculatedHeight(): number {\n    let height = this.localConfig().height || this.height() || DEFAULT_HEIGHT;\n    if (\n      this.el.nativeElement.parentNode.offsetHeight > 0 &&\n      height <= 1 &&\n      height > 0\n    ) {\n      height = this.el.nativeElement.parentNode.offsetHeight * height;\n    }\n    return height;\n  }\n\n  private el = inject(ElementRef);\n  private r2 = inject(Renderer2);\n\n  @HostListener('window:resize', ['$event']) onResize(event: any) {\n    this.logMessage('debug', 'rezisze triggered');\n    window.clearTimeout(this.timeoutId);\n    this.timeoutId = window.setTimeout(() => {\n      if (this.options.realignOnResize) {\n        this.reDraw();\n      }\n    }, 200);\n  }\n\n  constructor() {\n    const el = this.el.nativeElement;\n    effect(() => {\n      // this.logMessage('debug', 'ngOnChanges fired', changes);\n      // set default values\n      const config = this.localConfig();\n      this.logMessage('warn', 'cloud configuration', config);\n\n      // set the basic font style if property is provided\n      if (config.font) {\n        this.r2.setStyle(el, 'font', config.font);\n      }\n\n      // set a background image if property is provided\n      if (config.background) {\n        this.r2.setStyle(el, 'background', config.background);\n      }\n      this.reDraw();\n    });\n  }\n\n  // ngAfterContentInit() {\n  //   this.afterInit?.emit();\n  //   this.logMessage('debug', 'afterInit emitted');\n  // }\n\n  // ngAfterContentChecked() {\n  //   this.afterChecked?.emit();\n  //   this.logMessage('debug', 'afterChecked emitted');\n  // }\n\n  /**\n   * re-draw the word cloud\n   * @param changes the change set\n   */\n  reDraw() {\n    // this.dataChanges?.emit(changes);\n    this.afterChecked?.emit();\n    this.logMessage('debug', 'dataChanges emitted');\n    this.cloudDataHtmlElements = [];\n\n    // check if data is not null or empty\n    if (!this.data()) {\n      console.error(\n        'angular-tag-cloud: No data passed. Please pass an Array of CloudData',\n      );\n      return;\n    }\n\n    // values changed, reset cloud\n    this.el.nativeElement.innerHTML = '';\n\n    // set value changes\n    if (this.data()) {\n      this.dataArr = this.data();\n    }\n\n    // set options\n    this.options = {\n      ...this.localConfig(),\n      aspectRatio: this.calculatedWidth / this.calculatedHeight,\n      width: this.calculatedWidth,\n      height: this.calculatedHeight,\n      center: {\n        x: this.calculatedWidth / 2.0,\n        y: this.calculatedHeight / 2.0,\n      },\n    };\n\n    // set the dimensions\n    this.r2.setStyle(this.el.nativeElement, 'width', this.options.width + 'px');\n    this.r2.setStyle(\n      this.el.nativeElement,\n      'height',\n      this.options.height + 'px',\n    );\n\n    // draw the cloud\n    this.drawWordCloud();\n    this.logMessage('debug', 'reDraw finished');\n  }\n\n  /**\n   * helper to generate a descriptive string for an entry to use when sorting alphabetically\n   * @param entry the cloud entry to be used\n   */\n  private descriptiveEntry(entry: CloudData): string {\n    let description = entry.text;\n    description += entry.color ? `-${entry.color}` : '';\n    description += entry.external ? `-${entry.external}` : '';\n    description += entry.link ? `-${entry.link}` : '';\n    description += entry.rotate ? `-${entry.rotate}` : '';\n    return description;\n  }\n\n  /**\n   * proceed draw the cloud\n   */\n  private drawWordCloud() {\n    // Sort alphabetically to ensure that, all things being equal, words are placed uniformly\n    this.dataArr.sort((a, b) =>\n      this.descriptiveEntry(a).localeCompare(this.descriptiveEntry(b)),\n    );\n    // Sort this._dataArr from the word with the highest weight to the one with the lowest\n    this.dataArr.sort((a, b) => b.weight - a.weight);\n    // place fixed elements first\n    const elementsWithFixedPositions = this.dataArr.filter(\n      (item) => item.position,\n    );\n    const elementsWithRandomPositions = this.dataArr.filter(\n      (item) => !item.position,\n    );\n    elementsWithFixedPositions.forEach((elem, index) => {\n      this.drawWord(index, elem);\n    });\n    elementsWithRandomPositions.forEach((elem, index) => {\n      this.drawWord(index, elem);\n    });\n  }\n\n  /**\n   * Helper function to test if an element overlaps others\n   * @param rect the DOM rectangle that represents the element's bounds\n   */\n  private hitTest(rect: DOMRect): boolean {\n    // Check elements for overlap one by one, stop and return false as soon as an overlap is found\n    for (const item of this.cloudDataHtmlElements) {\n      if (this.overlapping(rect, item)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Pairwise overlap detection\n   * @param rect the DOM rectangle that represents the element's bounds\n   * @param e2 the second element for overlap detection\n   */\n  private overlapping(rect: DOMRect, e2: HTMLElement) {\n    const { offsetLeft, offsetTop, offsetWidth, offsetHeight } = e2;\n    const offsetRight = offsetLeft + offsetWidth;\n    const offsetBottom = offsetTop + offsetHeight;\n\n    const overlap = !(\n      rect.right < offsetLeft ||\n      rect.left > offsetRight ||\n      rect.bottom < offsetTop ||\n      rect.top > offsetBottom\n    );\n    return overlap;\n  }\n\n  /**\n   * Check if min(weight) > max(weight) otherwise use default\n   * @param word the particular word configuration\n   */\n  private getWeightForWord(word: CloudData): number {\n    let weight = 5;\n    if (this.dataArr[0].weight > this.dataArr[this.dataArr.length - 1].weight) {\n      // check if strict mode is active\n      if (!this.options.strict) {\n        // Linearly map the original weight to a discrete scale from 1 to 10\n        weight =\n          Math.round(\n            ((word.weight - this.dataArr[this.dataArr.length - 1].weight) /\n              (this.dataArr[0].weight -\n                this.dataArr[this.dataArr.length - 1].weight)) *\n            9.0,\n          ) + 1;\n      } else {\n        // use given value for weigth directly\n        // fallback to 10\n        if (word.weight > 10) {\n          weight = 10;\n          this.logMessage(\n            'warn',\n            `[TagCloud strict] Weight property ${word.weight} > 10. Fallback to 10 as you are using strict mode`,\n            word,\n          );\n        } else if (word.weight < 1) {\n          // fallback to 1\n          weight = 1;\n          this.logMessage(\n            'warn',\n            `[TagCloud strict] Given weight property ${word.weight} < 1. Fallback to 1 as you are using strict mode`,\n            word,\n          );\n        } else if (word.weight % 1 !== 0) {\n          // round if given value is not an integer\n          weight = Math.round(word.weight);\n          this.logMessage(\n            'warn',\n            `[TagCloud strict] Given weight property ${word.weight} is not an integer. Rounded value to ${weight}`,\n            word,\n          );\n        } else {\n          weight = word.weight;\n        }\n      }\n    }\n    return weight;\n  }\n\n  /**\n   * change the HTMLElements color style\n   * @param el the HTML element\n   * @param color the CSS color value\n   */\n  private setWordColor(el: HTMLElement, color: string) {\n    this.r2.setStyle(el, 'color', color);\n  }\n\n  /**\n   * Add a tooltip to the element\n   * @param el the HTML element\n   * @param tooltip the tooltip text\n   */\n  private setTooltip(el: HTMLElement, tooltip: string) {\n    this.r2.addClass(el, 'tooltip');\n    const tooltipSpan = this.r2.createElement('span');\n    tooltipSpan.className = 'tooltiptext';\n    const text = this.r2.createText(tooltip);\n    tooltipSpan.appendChild(text);\n    el.appendChild(tooltipSpan);\n  }\n\n  /**\n   * change the HTMLElements rotation style\n   * @param el the HTML element\n   * @param deg the rotation value (degrees)\n   */\n  private setWordRotation(el: HTMLElement, deg?: number): string {\n    const transformString = deg ? `rotate(${deg}deg)` : '';\n    this.r2.setStyle(el, 'transform', transformString);\n    return transformString;\n  }\n\n  /**\n   * wrap the given node into an HTML anchor element\n   * @param node the HTML node that should be wrapped\n   * @param word the particular word configuration\n   */\n  private wrapNodeIntoAnchorElement(\n    node: HTMLElement,\n    word: CloudData,\n  ): HTMLAnchorElement {\n    const wordLink: HTMLAnchorElement = this.r2.createElement('a');\n    wordLink.href = word.link || '';\n\n    if (word.external !== undefined && word.external) {\n      wordLink.target = '_blank';\n    }\n\n    wordLink.appendChild(node);\n    return wordLink;\n  }\n\n  /**\n   * wrap the given node into an HTML anchor element\n   * @param node the HTML node that should be wrapped\n   * @param word the particular word configuration\n   */\n  private applyZoomStyle(\n    node: HTMLElement,\n    el: HTMLElement,\n    link: string | undefined,\n    transformString: string,\n  ) {\n    if (this.options.zoomOnHover && this.options.zoomOnHover.scale !== 1) {\n      if (!this.options.zoomOnHover.transitionTime) {\n        this.options.zoomOnHover.transitionTime = 0;\n      }\n      if (!this.options.zoomOnHover.scale) {\n        this.options.zoomOnHover.scale = 1;\n      }\n\n      el.onmouseover = () => {\n        if (this.options.zoomOnHover?.transitionTime) {\n          this.r2.setStyle(\n            el,\n            'transition',\n            `transform ${this.options.zoomOnHover.transitionTime}s`,\n          );\n        }\n        if (this.options.zoomOnHover?.scale) {\n          this.r2.setStyle(\n            el,\n            'transform',\n            `scale(${this.options.zoomOnHover.scale}) ${transformString}`,\n          );\n        }\n        if (this.options.zoomOnHover?.delay) {\n          this.r2.setStyle(\n            el,\n            'transition-delay',\n            `${this.options.zoomOnHover.delay}s`,\n          );\n        }\n        if (this.options.zoomOnHover?.color) {\n          link\n            ? this.r2.setStyle(node, 'color', this.options.zoomOnHover.color)\n            : this.r2.setStyle(el, 'color', this.options.zoomOnHover.color);\n        }\n      };\n\n      el.onmouseout = () => {\n        this.r2.setStyle(el, 'transform', `none ${transformString}`);\n        if (this.options.zoomOnHover?.color) {\n          link\n            ? this.r2.removeStyle(node, 'color')\n            : this.r2.removeStyle(el, 'color');\n        }\n      };\n    }\n  }\n\n  /**\n   * Place the word at a calculated position\n   * @param wordSpan The HTML Span element to be placed\n   * @param word The word to be placed\n   * @param index The index of the element\n   */\n  private setPosition(\n    wordSpan: HTMLSpanElement,\n    word: CloudData,\n    index: number,\n  ) {\n    let angle = this.options.randomizeAngle ? 6.28 * Math.random() : 0;\n    let radius = 0;\n    // Save a reference to the style property, for better performance\n    const wordStyle = wordSpan.style;\n    wordStyle.position = 'absolute';\n\n    const useFixedPosition: boolean = Boolean(\n      word.position && word.position.left && word.position.top,\n    );\n\n    const width = wordSpan.offsetWidth;\n    const height = wordSpan.offsetHeight;\n    let left =\n      useFixedPosition && word.position?.left\n        ? word.position.left\n        : this.options.center.x - width / 2;\n    let top =\n      useFixedPosition && word.position?.top\n        ? word.position.top\n        : this.options.center.y - height / 2;\n\n    // place the first word\n    wordStyle.left = left + 'px';\n    wordStyle.top = top + 'px';\n\n    // delayed appearance\n    if (this.options.delay) {\n      wordSpan.classList.add('tag-animation-delay');\n      // add custom css properties\n      wordStyle.setProperty(\n        '--tag-animation-delay',\n        `${this.options.delay * index}ms`,\n      );\n    }\n\n    // default case: place randomly\n    if (!useFixedPosition) {\n      // do not place the first word always right in the middle\n      if (index === 0) {\n        wordStyle.left =\n          left + (Math.random() - 0.5) * 2 * (this.calculatedWidth / 5) + 'px';\n        wordStyle.top =\n          top +\n          (Math.random() - 0.5) * 2 * (this.calculatedHeight / 5) +\n          '30px';\n      } else {\n        while (\n          this.options.width &&\n          this.options.height &&\n          wordSpan.offsetHeight &&\n          wordSpan.offsetWidth &&\n          this.hitTest(new DOMRect(left, top, wordSpan.offsetWidth, wordSpan.offsetHeight))\n        ) {\n          radius += this.options.step || DEFAULT_STEP;\n          angle +=\n            (index % 2 === 0 ? 1 : -1) * (this.options.step || DEFAULT_STEP);\n\n          left =\n            this.options.center.x -\n            width / 2.0 +\n            radius * Math.cos(angle) * this.options.aspectRatio;\n          top = this.options.center.y + radius * Math.sin(angle) - height / 2.0;\n        }\n\n        wordStyle.left = left + 'px';\n        wordStyle.top = top + 'px';\n      }\n    }\n\n    // Don't render word if part of it would be outside the container\n    if (\n      !this.options.overflow &&\n      (left < 0 ||\n        top < 0 ||\n        left + width > this.calculatedWidth ||\n        top + height > this.calculatedHeight)\n    ) {\n      this.logMessage(\n        'warn',\n        \"Word did not fit into the cloud and overflow is set to 'false'. The element will be removed\",\n        wordSpan,\n      );\n      wordSpan.remove();\n      return;\n    }\n  }\n\n  /**\n   * Methods to draw a word, by moving it in spiral until it finds a suitable empty place.\n   * This will be iterated on each word.\n   * @param index the index number for the word\n   * @param word the particular word configuration\n   */\n  private drawWord(index: number, word: CloudData) {\n    let wordSpan: HTMLSpanElement;\n\n    // get calculated word weight\n    const weight: number = this.getWeightForWord(word);\n\n    // Create a new span and insert node.\n    wordSpan = this.r2.createElement('span');\n    wordSpan.className = `w${weight}`;\n\n    // emit onclick event\n    wordSpan.onclick = () => {\n      this.clicked?.emit(word);\n    };\n\n    // Put the word (and its tooltip) in foreground when cursor is above\n    wordSpan.onmouseenter = () => {\n      wordSpan.style.zIndex = '2';\n    };\n\n    // Otherwise, restore standard priority\n    wordSpan.onmouseleave = () => {\n      wordSpan.style.zIndex = '1';\n    };\n\n    // append word text\n    let node = this.r2.createText(word.text);\n\n    // set color\n    if (word.color) this.setWordColor(wordSpan, word.color);\n\n    // rotate word possibly\n    const transformString = this.setWordRotation(wordSpan, word.rotate);\n\n    // Append href if there's a link along with the tag\n    if (word.link) node = this.wrapNodeIntoAnchorElement(node, word);\n\n    // set zoomOption\n    if (this.options.zoomOnHover && this.options.zoomOnHover.scale !== 1) {\n      this.applyZoomStyle(node, wordSpan, word.link, transformString);\n    }\n\n    wordSpan.appendChild(node);\n    this.r2.appendChild(this.el.nativeElement, wordSpan);\n\n    // add tooltip if provided\n    if (word.tooltip) this.setTooltip(wordSpan, word.tooltip);\n\n    // set a unique id\n    wordSpan.id = `angular-tag-cloud-item-${index}`;\n\n    // define the elements position\n    this.setPosition(wordSpan, word, index);\n\n    this.logMessage('debug', 'Adds new word <span>', wordSpan);\n    this.cloudDataHtmlElements.push(wordSpan);\n\n    this.logMessage('debug', 'Placed words', this.cloudDataHtmlElements);\n  }\n\n  /**\n   * Log messages to console\n   * @param level the log level\n   * @param args extra args to be logged\n   */\n  private logMessage(level: 'warn' | 'debug' | false, ...args: any) {\n    if (!this.localConfig()) {\n      return;\n    }\n    if (this.localConfig().log === 'debug') {\n      console.log(`[AngularTagCloudModule ${level}]`, ...args);\n    } else if (this.localConfig().log === 'warn' && level === 'warn') {\n      console.warn(`[AngularTagCloudModule ${level}]`, ...args);\n    }\n  }\n}\n", "/*\n * Public API Surface of angular-tag-cloud-module\n */\n\nexport * from './lib/tag-cloud.interfaces';\nexport * from './lib/tag-cloud.component';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": ";;;AA+BA,MAAM,cAAc,GAAG,GAAG;AAC1B,MAAM,aAAa,GAAG,CAAC;AACvB,MAAM,YAAY,GAAG,CAAC;MAST,iBAAiB,CAAA;AAmD5B,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,aAAa;QACrE,IACE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC;AAChD,YAAA,KAAK,IAAI,CAAC;YACV,KAAK,GAAG,CAAC,EACT;AACA,YAAA,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,GAAG,KAAK;;AAE9D,QAAA,OAAO,KAAK;;AAGd,IAAA,IAAI,gBAAgB,GAAA;AAClB,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,cAAc;QACzE,IACE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC;AACjD,YAAA,MAAM,IAAI,CAAC;YACX,MAAM,GAAG,CAAC,EACV;AACA,YAAA,MAAM,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY,GAAG,MAAM;;AAEjE,QAAA,OAAO,MAAM;;AAM4B,IAAA,QAAQ,CAAC,KAAU,EAAA;AAC5D,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,mBAAmB,CAAC;AAC7C,QAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,MAAK;AACtC,YAAA,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;gBAChC,IAAI,CAAC,MAAM,EAAE;;SAEhB,EAAE,GAAG,CAAC;;AAGT,IAAA,WAAA,GAAA;AAvFA,QAAA,IAAA,CAAA,IAAI,GAAG,KAAK,CAAc,EAAE,CAAC;QAC7B,IAAK,CAAA,KAAA,GAAG,KAAK,EAAU;QACvB,IAAM,CAAA,MAAA,GAAG,KAAK,EAAU;QACxB,IAAI,CAAA,IAAA,GAAG,KAAK,EAAU;QACtB,IAAQ,CAAA,QAAA,GAAG,KAAK,EAAW;QAC3B,IAAM,CAAA,MAAA,GAAG,KAAK,EAAW;QACzB,IAAW,CAAA,WAAA,GAAG,KAAK,EAAsB;QACzC,IAAe,CAAA,eAAA,GAAG,KAAK,EAAW;QAClC,IAAc,CAAA,cAAA,GAAG,KAAK,EAAW;QACjC,IAAU,CAAA,UAAA,GAAG,KAAK,EAAU;QAC5B,IAAI,CAAA,IAAA,GAAG,KAAK,EAAU;QACtB,IAAK,CAAA,KAAA,GAAG,KAAK,EAAU;AACvB,QAAA,IAAA,CAAA,MAAM,GAAG,KAAK,CAAe,EAAE,CAAC;QAChC,IAAG,CAAA,GAAA,GAAG,KAAK,EAA4B;QAEvC,IAAO,CAAA,OAAA,GAAG,MAAM,EAAa;;QAE7B,IAAS,CAAA,SAAA,GAAG,MAAM,EAAQ;QAC1B,IAAY,CAAA,YAAA,GAAG,MAAM,EAAQ;AAErB,QAAA,IAAA,CAAA,WAAW,GAAG,QAAQ,CAAC,MAAK;AAClC,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;AAC5B,YAAA,MAAM,WAAW,GAAiB;gBAChC,GAAG,MAAM;gBACT,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,IAAI,GAAG;gBAC1C,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,MAAM,CAAC,MAAM,IAAI,GAAG;AAC7C,gBAAA,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;AACtD,gBAAA,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,MAAM,CAAC,MAAM,IAAI,KAAK,CAAC;gBACjD,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,MAAM,CAAC,WAAW,IAAI;AACvD,oBAAA,cAAc,EAAE,CAAC;AACjB,oBAAA,KAAK,EAAE,CAAC;AACR,oBAAA,KAAK,EAAE,CAAC;AACT,iBAAA;AACD,gBAAA,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE,KAAK,MAAM,CAAC,eAAe,IAAI,KAAK,CAAC;AAC5E,gBAAA,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,MAAM,CAAC,cAAc,IAAI,KAAK,CAAC;gBACzE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG;gBACvC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,GAAG,IAAI,KAAK;gBACtC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK;gBACnC,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,MAAM,CAAC,UAAU;gBAClD,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,MAAM,CAAC;aAC7B;AACD,YAAA,OAAO,WAAW;AACpB,SAAC,CAAC;QAEK,IAAqB,CAAA,qBAAA,GAAkB,EAAE;QACxC,IAAO,CAAA,OAAA,GAAgB,EAAE;AA6BzB,QAAA,IAAA,CAAA,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC;AACvB,QAAA,IAAA,CAAA,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC;AAa5B,QAAA,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa;QAChC,MAAM,CAAC,MAAK;;;AAGV,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE;YACjC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,qBAAqB,EAAE,MAAM,CAAC;;AAGtD,YAAA,IAAI,MAAM,CAAC,IAAI,EAAE;AACf,gBAAA,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC;;;AAI3C,YAAA,IAAI,MAAM,CAAC,UAAU,EAAE;AACrB,gBAAA,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC;;YAEvD,IAAI,CAAC,MAAM,EAAE;AACf,SAAC,CAAC;;;;;;;;;;AAaJ;;;AAGG;IACH,MAAM,GAAA;;AAEJ,QAAA,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE;AACzB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,qBAAqB,CAAC;AAC/C,QAAA,IAAI,CAAC,qBAAqB,GAAG,EAAE;;AAG/B,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE;AAChB,YAAA,OAAO,CAAC,KAAK,CACX,sEAAsE,CACvE;YACD;;;QAIF,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,SAAS,GAAG,EAAE;;AAGpC,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;AACf,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE;;;QAI5B,IAAI,CAAC,OAAO,GAAG;YACb,GAAG,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,WAAW,EAAE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB;YACzD,KAAK,EAAE,IAAI,CAAC,eAAe;YAC3B,MAAM,EAAE,IAAI,CAAC,gBAAgB;AAC7B,YAAA,MAAM,EAAE;AACN,gBAAA,CAAC,EAAE,IAAI,CAAC,eAAe,GAAG,GAAG;AAC7B,gBAAA,CAAC,EAAE,IAAI,CAAC,gBAAgB,GAAG,GAAG;AAC/B,aAAA;SACF;;QAGD,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;QAC3E,IAAI,CAAC,EAAE,CAAC,QAAQ,CACd,IAAI,CAAC,EAAE,CAAC,aAAa,EACrB,QAAQ,EACR,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAC3B;;QAGD,IAAI,CAAC,aAAa,EAAE;AACpB,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC;;AAG7C;;;AAGG;AACK,IAAA,gBAAgB,CAAC,KAAgB,EAAA;AACvC,QAAA,IAAI,WAAW,GAAG,KAAK,CAAC,IAAI;AAC5B,QAAA,WAAW,IAAI,KAAK,CAAC,KAAK,GAAG,CAAA,CAAA,EAAI,KAAK,CAAC,KAAK,CAAE,CAAA,GAAG,EAAE;AACnD,QAAA,WAAW,IAAI,KAAK,CAAC,QAAQ,GAAG,CAAA,CAAA,EAAI,KAAK,CAAC,QAAQ,CAAE,CAAA,GAAG,EAAE;AACzD,QAAA,WAAW,IAAI,KAAK,CAAC,IAAI,GAAG,CAAA,CAAA,EAAI,KAAK,CAAC,IAAI,CAAE,CAAA,GAAG,EAAE;AACjD,QAAA,WAAW,IAAI,KAAK,CAAC,MAAM,GAAG,CAAA,CAAA,EAAI,KAAK,CAAC,MAAM,CAAE,CAAA,GAAG,EAAE;AACrD,QAAA,OAAO,WAAW;;AAGpB;;AAEG;IACK,aAAa,GAAA;;AAEnB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KACrB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CACjE;;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;;AAEhD,QAAA,MAAM,0BAA0B,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CACpD,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,CACxB;AACD,QAAA,MAAM,2BAA2B,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CACrD,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CACzB;QACD,0BAA0B,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;AACjD,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC;AAC5B,SAAC,CAAC;QACF,2BAA2B,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAI;AAClD,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC;AAC5B,SAAC,CAAC;;AAGJ;;;AAGG;AACK,IAAA,OAAO,CAAC,IAAa,EAAA;;AAE3B,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC7C,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;AAChC,gBAAA,OAAO,IAAI;;;AAGf,QAAA,OAAO,KAAK;;AAGd;;;;AAIG;IACK,WAAW,CAAC,IAAa,EAAE,EAAe,EAAA;QAChD,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,EAAE;AAC/D,QAAA,MAAM,WAAW,GAAG,UAAU,GAAG,WAAW;AAC5C,QAAA,MAAM,YAAY,GAAG,SAAS,GAAG,YAAY;QAE7C,MAAM,OAAO,GAAG,EACd,IAAI,CAAC,KAAK,GAAG,UAAU;YACvB,IAAI,CAAC,IAAI,GAAG,WAAW;YACvB,IAAI,CAAC,MAAM,GAAG,SAAS;AACvB,YAAA,IAAI,CAAC,GAAG,GAAG,YAAY,CACxB;AACD,QAAA,OAAO,OAAO;;AAGhB;;;AAGG;AACK,IAAA,gBAAgB,CAAC,IAAe,EAAA;QACtC,IAAI,MAAM,GAAG,CAAC;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE;;AAEzE,YAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;;gBAExB,MAAM;oBACJ,IAAI,CAAC,KAAK,CACR,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM;AAC1D,yBAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM;AACrB,4BAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;wBACjD,GAAG,CACJ,GAAG,CAAC;;iBACF;;;AAGL,gBAAA,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE;oBACpB,MAAM,GAAG,EAAE;AACX,oBAAA,IAAI,CAAC,UAAU,CACb,MAAM,EACN,CAAA,kCAAA,EAAqC,IAAI,CAAC,MAAM,CAAA,kDAAA,CAAoD,EACpG,IAAI,CACL;;AACI,qBAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;;oBAE1B,MAAM,GAAG,CAAC;AACV,oBAAA,IAAI,CAAC,UAAU,CACb,MAAM,EACN,CAAA,wCAAA,EAA2C,IAAI,CAAC,MAAM,CAAA,gDAAA,CAAkD,EACxG,IAAI,CACL;;qBACI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;;oBAEhC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;AAChC,oBAAA,IAAI,CAAC,UAAU,CACb,MAAM,EACN,CAA2C,wCAAA,EAAA,IAAI,CAAC,MAAM,wCAAwC,MAAM,CAAA,CAAE,EACtG,IAAI,CACL;;qBACI;AACL,oBAAA,MAAM,GAAG,IAAI,CAAC,MAAM;;;;AAI1B,QAAA,OAAO,MAAM;;AAGf;;;;AAIG;IACK,YAAY,CAAC,EAAe,EAAE,KAAa,EAAA;QACjD,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC;;AAGtC;;;;AAIG;IACK,UAAU,CAAC,EAAe,EAAE,OAAe,EAAA;QACjD,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,CAAC;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC;AACjD,QAAA,WAAW,CAAC,SAAS,GAAG,aAAa;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;AACxC,QAAA,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC;AAC7B,QAAA,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC;;AAG7B;;;;AAIG;IACK,eAAe,CAAC,EAAe,EAAE,GAAY,EAAA;AACnD,QAAA,MAAM,eAAe,GAAG,GAAG,GAAG,CAAU,OAAA,EAAA,GAAG,CAAM,IAAA,CAAA,GAAG,EAAE;QACtD,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,WAAW,EAAE,eAAe,CAAC;AAClD,QAAA,OAAO,eAAe;;AAGxB;;;;AAIG;IACK,yBAAyB,CAC/B,IAAiB,EACjB,IAAe,EAAA;QAEf,MAAM,QAAQ,GAAsB,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC;QAC9D,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE;QAE/B,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;AAChD,YAAA,QAAQ,CAAC,MAAM,GAAG,QAAQ;;AAG5B,QAAA,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC;AAC1B,QAAA,OAAO,QAAQ;;AAGjB;;;;AAIG;AACK,IAAA,cAAc,CACpB,IAAiB,EACjB,EAAe,EACf,IAAwB,EACxB,eAAuB,EAAA;AAEvB,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,EAAE;YACpE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc,EAAE;gBAC5C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc,GAAG,CAAC;;YAE7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE;gBACnC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC;;AAGpC,YAAA,EAAE,CAAC,WAAW,GAAG,MAAK;gBACpB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,cAAc,EAAE;AAC5C,oBAAA,IAAI,CAAC,EAAE,CAAC,QAAQ,CACd,EAAE,EACF,YAAY,EACZ,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc,CAAA,CAAA,CAAG,CACxD;;gBAEH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE;oBACnC,IAAI,CAAC,EAAE,CAAC,QAAQ,CACd,EAAE,EACF,WAAW,EACX,CAAA,MAAA,EAAS,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAK,EAAA,EAAA,eAAe,CAAE,CAAA,CAC9D;;gBAEH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE;AACnC,oBAAA,IAAI,CAAC,EAAE,CAAC,QAAQ,CACd,EAAE,EACF,kBAAkB,EAClB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAA,CAAA,CAAG,CACrC;;gBAEH,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE;oBACnC;AACE,0BAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK;AAChE,0BAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC;;AAErE,aAAC;AAED,YAAA,EAAE,CAAC,UAAU,GAAG,MAAK;AACnB,gBAAA,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,WAAW,EAAE,CAAA,KAAA,EAAQ,eAAe,CAAA,CAAE,CAAC;gBAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE;oBACnC;0BACI,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO;0BACjC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,EAAE,OAAO,CAAC;;AAExC,aAAC;;;AAIL;;;;;AAKG;AACK,IAAA,WAAW,CACjB,QAAyB,EACzB,IAAe,EACf,KAAa,EAAA;QAEb,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC;QAClE,IAAI,MAAM,GAAG,CAAC;;AAEd,QAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK;AAChC,QAAA,SAAS,CAAC,QAAQ,GAAG,UAAU;QAE/B,MAAM,gBAAgB,GAAY,OAAO,CACvC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CACzD;AAED,QAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW;AAClC,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY;QACpC,IAAI,IAAI,GACN,gBAAgB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjC,cAAE,IAAI,CAAC,QAAQ,CAAC;AAChB,cAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC;QACvC,IAAI,GAAG,GACL,gBAAgB,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjC,cAAE,IAAI,CAAC,QAAQ,CAAC;AAChB,cAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC;;AAGxC,QAAA,SAAS,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI;AAC5B,QAAA,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI;;AAG1B,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AACtB,YAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC;;AAE7C,YAAA,SAAS,CAAC,WAAW,CACnB,uBAAuB,EACvB,CAAG,EAAA,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA,EAAA,CAAI,CAClC;;;QAIH,IAAI,CAAC,gBAAgB,EAAE;;AAErB,YAAA,IAAI,KAAK,KAAK,CAAC,EAAE;AACf,gBAAA,SAAS,CAAC,IAAI;oBACZ,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,IAAI;AACtE,gBAAA,SAAS,CAAC,GAAG;oBACX,GAAG;AACH,wBAAA,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AACvD,wBAAA,MAAM;;iBACH;AACL,gBAAA,OACE,IAAI,CAAC,OAAO,CAAC,KAAK;oBAClB,IAAI,CAAC,OAAO,CAAC,MAAM;AACnB,oBAAA,QAAQ,CAAC,YAAY;AACrB,oBAAA,QAAQ,CAAC,WAAW;oBACpB,IAAI,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC,EACjF;oBACA,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,YAAY;oBAC3C,KAAK;wBACH,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,YAAY,CAAC;oBAElE,IAAI;AACF,wBAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACrB,4BAAA,KAAK,GAAG,GAAG;AACX,4BAAA,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;oBACrD,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,GAAG;;AAGvE,gBAAA,SAAS,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI;AAC5B,gBAAA,SAAS,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI;;;;AAK9B,QAAA,IACE,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;aACrB,IAAI,GAAG,CAAC;AACP,gBAAA,GAAG,GAAG,CAAC;AACP,gBAAA,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,eAAe;gBACnC,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,EACvC;YACA,IAAI,CAAC,UAAU,CACb,MAAM,EACN,6FAA6F,EAC7F,QAAQ,CACT;YACD,QAAQ,CAAC,MAAM,EAAE;YACjB;;;AAIJ;;;;;AAKG;IACK,QAAQ,CAAC,KAAa,EAAE,IAAe,EAAA;AAC7C,QAAA,IAAI,QAAyB;;QAG7B,MAAM,MAAM,GAAW,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;;QAGlD,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC;AACxC,QAAA,QAAQ,CAAC,SAAS,GAAG,CAAI,CAAA,EAAA,MAAM,EAAE;;AAGjC,QAAA,QAAQ,CAAC,OAAO,GAAG,MAAK;AACtB,YAAA,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC;AAC1B,SAAC;;AAGD,QAAA,QAAQ,CAAC,YAAY,GAAG,MAAK;AAC3B,YAAA,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;AAC7B,SAAC;;AAGD,QAAA,QAAQ,CAAC,YAAY,GAAG,MAAK;AAC3B,YAAA,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;AAC7B,SAAC;;AAGD,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;;QAGxC,IAAI,IAAI,CAAC,KAAK;YAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC;;AAGvD,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC;;QAGnE,IAAI,IAAI,CAAC,IAAI;YAAE,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,IAAI,CAAC;;AAGhE,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,EAAE;AACpE,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC;;AAGjE,QAAA,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC;AAC1B,QAAA,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC;;QAGpD,IAAI,IAAI,CAAC,OAAO;YAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC;;AAGzD,QAAA,QAAQ,CAAC,EAAE,GAAG,CAA0B,uBAAA,EAAA,KAAK,EAAE;;QAG/C,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC;QAEvC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,CAAC;AAC1D,QAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC;QAEzC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC;;AAGtE;;;;AAIG;AACK,IAAA,UAAU,CAAC,KAA+B,EAAE,GAAG,IAAS,EAAA;AAC9D,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YACvB;;QAEF,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,OAAO,EAAE;YACtC,OAAO,CAAC,GAAG,CAAC,CAA0B,uBAAA,EAAA,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC;;AACnD,aAAA,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,EAAE;YAChE,OAAO,CAAC,IAAI,CAAC,CAA0B,uBAAA,EAAA,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC;;;+GArkBlD,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA,CAAA;AAAjB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,EAAA,IAAA,EAAA,iBAAiB,++DAHlB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,8tCAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,CAAA,CAAA;;4FAGD,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAP7B,SAAS;AACI,YAAA,IAAA,EAAA,CAAA,EAAA,UAAA,EAAA,IAAI,YACN,uCAAuC,EAAA,eAAA,EAChC,uBAAuB,CAAC,MAAM,YACrC,EAAE,EAAA,MAAA,EAAA,CAAA,8tCAAA,CAAA,EAAA;wDAiF+B,QAAQ,EAAA,CAAA;sBAAlD,YAAY;uBAAC,eAAe,EAAE,CAAC,QAAQ,CAAC;;;ACxH3C;;AAEG;;ACFH;;AAEG;;;;"}