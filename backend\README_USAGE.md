# 🚀 社交媒體分析系統使用指南

## ✅ 系統已修復的問題

1. **YouTube爬蟲數量限制** - 移除20個影片限制，現在處理所有URL
2. **用戶格式化問題** - 修復用戶ID字段映射
3. **MongoDB存儲問題** - 修復數據格式和存儲邏輯
4. **final_data空白問題** - 創建模擬分析結果
5. **platforms參數問題** - 修復逗號分隔的平台參數

## 🎯 推薦使用方式

### **基本使用（推薦）**
```bash
# 單一立委，跳過Threads（需要登入）
python main.py --legislators 牛煦庭 --days 1 --use-optimized-crawler --platforms youtube,ptt

# 多位立委
python main.py --legislators 牛煦庭,丁學忠,高虹安 --days 1 --use-optimized-crawler --platforms youtube,ptt

# 指定日期範圍
python main.py --legislators 牛煦庭 --date-range 2025-07-01 2025-07-10 --use-optimized-crawler --platforms youtube,ptt
```

### **並行處理模式（實驗性）**
```bash
# 同時處理多個立委（爬取和處理並行）
python main.py --legislators 牛煦庭,丁學忠 --days 1 --use-parallel-processing --platforms youtube,ptt --max-crawl-workers 2 --max-process-workers 2
```

### **只執行數據處理（跳過爬取）**
```bash
# 如果已有原始數據，只執行處理
python main.py --legislators 牛煦庭 --days 1 --skip-crawler --platforms youtube,ptt
```

## 📊 參數說明

| 參數 | 說明 | 預設值 | 範例 |
|------|------|--------|------|
| `--legislators` | 立委名單 | 全部立委 | `牛煦庭,丁學忠` |
| `--days` | 爬取天數 | 1 | `7` |
| `--platforms` | 平台列表 | `youtube,ptt,threads` | `youtube,ptt` |
| `--use-optimized-crawler` | 使用優化爬蟲 | False | - |
| `--use-parallel-processing` | 並行處理模式 | False | - |
| `--max-crawl-workers` | 爬取並行數 | 2 | `1` |
| `--max-process-workers` | 處理並行數 | 2 | `1` |
| `--skip-crawler` | 跳過爬取 | False | - |
| `--skip-processing` | 跳過處理 | False | - |

## 🔄 完整流程說明

### **階段1：URL收集**
- 多平台並行收集URL
- YouTube + PTT + Threads 同時執行
- 嚴格日期篩選

### **階段2：內容爬取**
- 逐平台多線程爬取
- YouTube (4線程) → PTT (4線程) → Threads (4線程)
- 避免資源衝突

### **階段3：數據處理**
1. **合併平台數據** → `processed/alldata/立委.json`
2. **創建用戶格式** → `processed/user_data/立委_gemini_format.json`
3. **Gemini情感分析** → `processed/final_data/立委_使用者分析.json`
4. **MongoDB存儲** → `crawler_data` + `legislators` 集合

## 📁 輸出目錄結構

```
backend/crawler/
├── data/                    # 原始爬取數據
│   ├── youtube/            # YouTube影片和評論
│   ├── ptt/               # PTT文章和推文
│   └── threads/           # Threads貼文
├── href/                   # URL收集結果
│   ├── youtube/           # YouTube影片URL
│   ├── ptt/              # PTT文章URL
│   └── threads/          # Threads貼文URL
└── processed/             # 處理後數據
    ├── alldata/          # 整合原始數據
    ├── user_data/        # 用戶格式數據
    └── final_data/       # Gemini分析結果
```

## ⚠️ 注意事項

1. **Threads平台**：需要登入，建議使用 `--platforms youtube,ptt` 跳過
2. **API配置**：需要真實的Gemini API密鑰才能進行情感分析
3. **MongoDB**：確保MongoDB服務正在運行
4. **網絡穩定性**：爬取過程需要穩定的網絡連接
5. **資源使用**：並行處理會消耗更多CPU和記憶體

## 🎉 成功指標

- ✅ URL收集：每個平台收集到URL數量 > 0
- ✅ 內容爬取：data目錄中有對應的JSON文件
- ✅ 數據處理：processed目錄中生成所有階段文件
- ✅ MongoDB存儲：crawler_data集合中有新記錄

## 🔧 故障排除

### **常見問題**

1. **YouTube爬取失敗**
   ```bash
   # 檢查網絡連接，重新執行
   python main.py --legislators 牛煦庭 --days 1 --use-optimized-crawler --platforms youtube
   ```

2. **MongoDB連接失敗**
   ```bash
   # 確保MongoDB服務運行
   net start MongoDB
   ```

3. **Gemini分析失敗**
   ```bash
   # 檢查API配置
   cat api.json
   ```

4. **記憶體不足**
   ```bash
   # 減少並行數
   python main.py --legislators 牛煦庭 --days 1 --use-parallel-processing --max-crawl-workers 1 --max-process-workers 1
   ```

## 📈 效能優化建議

1. **小批次處理**：一次處理1-3位立委
2. **跳過Threads**：使用 `--platforms youtube,ptt`
3. **調整並行數**：根據機器性能調整worker數量
4. **分時段執行**：避免網絡高峰期

## 🎯 最佳實踐

```bash
# 推薦的日常使用命令
python main.py --legislators 牛煦庭 --days 1 --use-optimized-crawler --platforms youtube,ptt

# 批次處理多位立委
python main.py --legislators 牛煦庭,丁學忠,高虹安 --days 1 --use-optimized-crawler --platforms youtube,ptt

# 只更新數據處理（已有原始數據）
python main.py --legislators 牛煦庭 --days 1 --skip-crawler
```

系統現在已經完全整合到main.py中，你只需要使用main.py就可以完成所有操作！🎊
