#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試YouTube修復腳本
直接測試YouTube爬蟲的修復效果
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def test_youtube_url_collection():
    """測試YouTube URL收集修復"""
    logger.info("🧪 測試YouTube URL收集修復...")
    
    try:
        from crawler.yt_crawler import search_youtube_videos
        
        # 測試參數
        politician_name = "牛煦庭"
        cutoff_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        logger.info(f"測試立委: {politician_name}")
        logger.info(f"截止日期: {cutoff_date}")
        
        # 執行URL收集
        url_file, video_data = search_youtube_videos(
            name=politician_name,
            cutoff_date=cutoff_date,
            headless=True
        )
        
        logger.info(f"收集結果: {len(video_data)} 個影片")
        
        # 檢查結果
        if video_data:
            logger.info("📊 收集到的影片詳情:")
            for i, video in enumerate(video_data[:5]):  # 只顯示前5個
                logger.info(f"   {i+1}. 時間: {video.get('time', 'Unknown')}")
                logger.info(f"      日期: {video.get('date', 'Unknown')}")
                logger.info(f"      post_time: {video.get('post_time', 'Missing')}")
                logger.info(f"      URL: {video.get('url', 'Unknown')[:50]}...")
            
            # 檢查post_time字段
            has_post_time = all('post_time' in video for video in video_data)
            logger.info(f"post_time字段檢查: {'✅ 全部包含' if has_post_time else '❌ 部分缺失'}")
            
            # 檢查日期篩選效果
            today = datetime.now().strftime('%Y-%m-%d')
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            
            today_count = sum(1 for v in video_data if v.get('date') == today)
            yesterday_count = sum(1 for v in video_data if v.get('date') == yesterday)
            
            logger.info(f"日期分布: 今天({today}): {today_count}個, 昨天({yesterday}): {yesterday_count}個")
            
            if len(video_data) > 2:  # 應該收集到更多影片
                logger.info("✅ YouTube URL收集修復測試通過")
                return True
            else:
                logger.warning(f"⚠️ 收集到的影片數量較少: {len(video_data)}個")
                return False
        else:
            logger.error("❌ 沒有收集到任何影片")
            return False
            
    except Exception as e:
        logger.error(f"❌ YouTube URL收集測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_href_file_structure():
    """測試href文件結構"""
    logger.info("🧪 測試href文件結構...")
    
    try:
        href_file = os.path.join(current_dir, 'crawler', 'href', 'youtube', '牛煦庭.json')
        
        if os.path.exists(href_file):
            with open(href_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"href文件包含 {len(data)} 個項目")
            
            if data:
                # 檢查第一個項目的結構
                first_item = data[0]
                logger.info(f"第一個項目結構: {list(first_item.keys())}")
                
                # 檢查是否包含post_time字段
                has_post_time = 'post_time' in first_item
                logger.info(f"post_time字段: {'✅ 存在' if has_post_time else '❌ 缺失'}")
                
                if has_post_time:
                    logger.info(f"post_time值: {first_item['post_time']}")
                
                # 檢查所有項目
                all_have_post_time = all('post_time' in item for item in data if isinstance(item, dict))
                logger.info(f"所有項目post_time字段: {'✅ 完整' if all_have_post_time else '❌ 不完整'}")
                
                return has_post_time
            else:
                logger.warning("⚠️ href文件為空")
                return False
        else:
            logger.warning("⚠️ href文件不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ href文件結構測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    logger.info("🚀 開始測試YouTube修復...")
    
    tests = [
        ("YouTube URL收集修復", test_youtube_url_collection),
        ("href文件結構", test_href_file_structure)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"測試: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"測試 {test_name} 時發生異常: {e}")
            results[test_name] = False
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("YouTube修復測試總結")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed >= 1:  # 至少1個測試通過
        logger.info("🎉 YouTube修復基本成功！")
        return 0
    else:
        logger.warning("⚠️ YouTube修復需要進一步檢查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
