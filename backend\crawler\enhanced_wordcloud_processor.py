#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
強化文字雲處理器
解決斷詞問題，移除人名，優化關鍵詞提取
"""

import os
import sys
import re
import json
import jieba
import jieba.posseg as pseg
from collections import Counter
from typing import List, Dict, Tuple

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

class EnhancedWordCloudProcessor:
    """強化文字雲處理器"""
    
    def __init__(self):
        self.stopwords = self._load_enhanced_stopwords()
        self.person_names = self._load_person_names()
        self.political_terms = self._load_political_terms()
        self._initialize_jieba()
    
    def _load_enhanced_stopwords(self) -> set:
        """載入增強的停用詞"""
        stopwords = set()
        
        # 基本停用詞
        basic_stopwords = {
            '的', '了', '是', '我', '也', '和', '就', '都', '不', '在', '會', '要', '很',
            '有', '這', '那', '個', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十',
            '他', '她', '它', '們', '你', '您', '我們', '你們', '他們', '她們',
            '但', '而', '或', '與', '及', '以', '為', '於', '從', '到', '由', '對',
            '可以', '可能', '應該', '必須', '需要', '想要', '希望', '覺得', '認為',
            '什麼', '怎麼', '為什麼', '哪裡', '什麼時候', '誰', '哪個', '多少',
            '這樣', '那樣', '如此', '這麼', '那麼', '非常', '特別', '尤其',
            '因為', '所以', '如果', '雖然', '但是', '然而', '不過', '而且', '另外',
            '首先', '其次', '最後', '總之', '因此', '所以', '然後', '接著',
            '比較', '相對', '絕對', '完全', '幾乎', '差不多', '大概', '可能',
            '真的', '確實', '當然', '其實', '實際', '基本', '主要', '重要',
            '一些', '一點', '一下', '一直', '一起', '一樣', '一定', '一般',
            '現在', '以前', '之前', '以後', '之後', '目前', '當時', '那時',
            '今天', '昨天', '明天', '今年', '去年', '明年', '這次', '上次', '下次'
        }
        stopwords.update(basic_stopwords)
        
        # 網路用語停用詞
        internet_stopwords = {
            '哈哈', '呵呵', '嗯嗯', '喔喔', '哦哦', '嘿嘿', '嘻嘻',
            '讚', '推', '頂', '樓主', '原po', 'po', 'Po', 'PO',
            '回覆', '留言', '評論', '分享', '轉發', '按讚',
            '網友', '鄉民', '大家', '各位', '朋友們',
            '影片', '文章', '新聞', '報導', '消息', '資訊',
            '連結', '網址', '來源', '出處', '引用',
            '更新', '編輯', '修改', '刪除', '發布', '上傳'
        }
        stopwords.update(internet_stopwords)
        
        # 政治相關但無意義的詞
        political_noise = {
            '立委', '議員', '市長', '縣長', '總統', '院長', '部長', '局長',
            '政府', '政治', '政策', '選舉', '投票', '民調', '支持', '反對',
            '黨', '政黨', '民進黨', '國民黨', '民眾黨', '時代力量',
            '立法院', '行政院', '監察院', '司法院', '考試院',
            '台灣', '中華民國', '中國', '大陸', '兩岸',
            '民眾', '人民', '國民', '市民', '鄉親', '同胞'
        }
        stopwords.update(political_noise)
        
        return stopwords
    
    def _load_person_names(self) -> set:
        """載入人名詞典"""
        person_names = set()
        
        # 常見姓氏
        surnames = {
            '王', '李', '張', '劉', '陳', '楊', '黃', '趙', '周', '吳',
            '林', '郭', '何', '高', '羅', '鄭', '梁', '謝', '宋', '唐',
            '許', '韓', '馮', '鄧', '曹', '彭', '曾', '蕭', '田', '董',
            '袁', '潘', '蔣', '蔡', '余', '杜', '葉', '程', '魏', '蘇',
            '呂', '丁', '任', '沈', '姚', '盧', '姜', '崔', '鐘', '譚',
            '陸', '汪', '范', '金', '石', '廖', '賈', '夏', '韋', '方',
            '白', '鄒', '孟', '熊', '秦', '邱', '江', '尹', '薛', '閻',
            '段', '雷', '侯', '龍', '史', '陶', '黎', '賀', '顧', '毛',
            '郝', '龔', '邵', '萬', '錢', '嚴', '覃', '武', '戴', '莫',
            '孔', '向', '湯', '洪', '施', '馬', '溫', '康', '賴', '易'
        }
        person_names.update(surnames)
        
        # 政治人物全名（從立委名單中提取）
        politicians = {
            '丁學忠', '牛煦庭', '王鴻薇', '江啟臣', '呂玉玲', '李彥秀',
            '林沛祥', '林思銘', '林德福', '邱若華', '邱鎮軍', '洪孟楷',
            '徐巧芯', '徐欣瑩', '馬文君', '張其祿', '張嘉郡', '梁文傑',
            '莊瑞雄', '陳玉珍', '陳建仁', '陳昭姿', '陳椒華', '陳雪生',
            '陳學聖', '曾銘宗', '游毓蘭', '蔡英文', '賴清德', '柯文哲',
            '侯友宜', '盧秀燕', '鄭文燦', '陳其邁', '黃偉哲', '林右昌'
        }
        person_names.update(politicians)
        
        return person_names
    
    def _load_political_terms(self) -> set:
        """載入政治術語詞典（這些詞有意義，應該保留）"""
        return {
            '罷免', '罷免案', '連署', '投票', '公投', '選舉',
            '貪污', '腐敗', '弊案', '醜聞', '爭議', '質疑',
            '改革', '政策', '法案', '提案', '修法', '立法',
            '預算', '審查', '監督', '問政', '質詢', '備詢',
            '民主', '自由', '人權', '法治', '憲政', '制衡',
            '透明', '公開', '負責', '究責', '下台', '辭職',
            '抗議', '示威', '遊行', '集會', '陳情', '請願',
            '經濟', '教育', '醫療', '環保', '交通', '住房',
            '就業', '薪資', '物價', '通膨', '景氣', '發展'
        }
    
    def _initialize_jieba(self):
        """初始化jieba分詞器"""
        # 添加政治術語到jieba詞典
        for term in self.political_terms:
            jieba.add_word(term)
        
        # 添加常見政治組合詞
        political_combinations = [
            '罷免案', '連署書', '投票率', '支持率', '民調結果',
            '政策執行', '預算審查', '法案通過', '質詢時間',
            '委員會', '公聽會', '記者會', '政見發表'
        ]
        
        for combo in political_combinations:
            jieba.add_word(combo)
    
    def extract_keywords(self, text_content: str, max_words: int = 50) -> List[Dict[str, any]]:
        """
        提取關鍵詞
        
        Args:
            text_content: 文本內容
            max_words: 最大詞數
            
        Returns:
            關鍵詞列表，格式：[{"text": "詞", "weight": 權重}, ...]
        """
        if not text_content or len(text_content.strip()) < 10:
            return []
        
        # 清理文本
        cleaned_text = self._clean_text(text_content)
        
        # 使用詞性標註分詞
        words = pseg.cut(cleaned_text)
        
        # 統計詞頻
        word_counter = Counter()
        
        for word, flag in words:
            word = word.strip()
            
            # 過濾條件
            if self._should_keep_word(word, flag):
                word_counter[word] += 1
        
        # 獲取高頻詞
        top_words = word_counter.most_common(max_words * 2)  # 先取更多，再篩選
        
        # 進一步篩選和格式化
        result = []
        for word, count in top_words:
            if len(result) >= max_words:
                break
                
            # 最終檢查
            if self._final_word_check(word, count):
                result.append({
                    "text": word,
                    "weight": count
                })
        
        return result
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        # 移除URL
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # 移除email
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)
        
        # 移除特殊符號，但保留中文標點
        text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\w\s，。！？；：「」『』（）【】]', ' ', text)
        
        # 移除多餘空白
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _should_keep_word(self, word: str, flag: str) -> bool:
        """判斷是否保留該詞"""
        # 長度檢查
        if len(word) < 2 or len(word) > 8:
            return False
        
        # 停用詞檢查
        if word in self.stopwords:
            return False
        
        # 人名檢查
        if word in self.person_names:
            return False
        
        # 純數字檢查
        if word.isdigit():
            return False
        
        # 純英文檢查（除非是重要縮寫）
        if re.match(r'^[a-zA-Z]+$', word) and len(word) < 3:
            return False
        
        # 詞性檢查 - 只保留有意義的詞性
        meaningful_flags = {
            'n', 'nr', 'ns', 'nt', 'nz',  # 名詞類
            'v', 'vd', 'vn',              # 動詞類
            'a', 'ad', 'an',              # 形容詞類
            'i',                          # 成語
            'l',                          # 習用語
        }
        
        if flag not in meaningful_flags:
            return False
        
        # 特殊字符檢查
        if re.search(r'[^\u4e00-\u9fff\w]', word):
            return False
        
        return True
    
    def _final_word_check(self, word: str, count: int) -> bool:
        """最終詞彙檢查"""
        # 頻次檢查
        if count < 2:
            return False
        
        # 避免重複的相似詞
        # 這裡可以添加更複雜的相似詞檢測邏輯
        
        # 避免無意義的組合
        meaningless_patterns = [
            r'^.{1}$',  # 單字
            r'^\d+$',   # 純數字
            r'^[a-zA-Z]{1,2}$',  # 短英文
        ]
        
        for pattern in meaningless_patterns:
            if re.match(pattern, word):
                return False
        
        return True

def process_legislator_wordcloud(legislator_name: str, text_data: List[str]) -> List[Dict[str, any]]:
    """
    處理單個立委的文字雲
    
    Args:
        legislator_name: 立委姓名
        text_data: 文本數據列表
        
    Returns:
        文字雲數據
    """
    processor = EnhancedWordCloudProcessor()
    
    # 合併所有文本
    combined_text = ' '.join(text_data)
    
    # 提取關鍵詞
    keywords = processor.extract_keywords(combined_text, max_words=50)
    
    print(f"✅ {legislator_name} 文字雲處理完成，提取 {len(keywords)} 個關鍵詞")
    
    return keywords

def main():
    """測試函數"""
    # 測試文本
    test_text = """
    牛煦庭立委在立法院質詢時表示，政府應該加強對於罷免案的監督機制。
    民眾對於這次的罷免連署活動反應熱烈，支持率持續上升。
    立法院預算審查委員會今天討論相關法案，多位立委發表意見。
    網友在PTT上討論這個議題，認為應該要有更透明的程序。
    """
    
    processor = EnhancedWordCloudProcessor()
    result = processor.extract_keywords(test_text)
    
    print("測試結果：")
    for item in result:
        print(f"  {item['text']}: {item['weight']}")

if __name__ == "__main__":
    main()
