import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """基礎配置類"""
    SECRET_KEY = os.environ.get('SECRET_KEY')
    GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')
    NEWSAPI_KEY = os.environ.get('NEWSAPI_KEY')
    
    # MongoDB 連線優化參數
    MONGODB_MAX_POOL_SIZE = int(os.environ.get('MONGODB_MAX_POOL_SIZE', 10))
    MONGODB_MAX_IDLE_TIME_MS = int(os.environ.get('MONGODB_MAX_IDLE_TIME_MS', 30000))
    MONGODB_CONNECT_TIMEOUT_MS = int(os.environ.get('MONGODB_CONNECT_TIMEOUT_MS', 30000))
    MONGODB_SOCKET_TIMEOUT_MS = int(os.environ.get('MONGODB_SOCKET_TIMEOUT_MS', 45000))
    MONGODB_SERVER_SELECTION_TIMEOUT_MS = int(os.environ.get('MONGODB_SERVER_SELECTION_TIMEOUT_MS', 30000))
    MONGODB_WAIT_QUEUE_TIMEOUT_MS = int(os.environ.get('MONGODB_WAIT_QUEUE_TIMEOUT_MS', 30000))
    MONGODB_HEARTBEAT_FREQUENCY_MS = int(os.environ.get('MONGODB_HEARTBEAT_FREQUENCY_MS', 10000))
    MONGODB_RETRY_WRITES = os.environ.get('MONGODB_RETRY_WRITES', 'true').lower() == 'true'
    MONGODB_RETRY_READS = os.environ.get('MONGODB_RETRY_READS', 'true').lower() == 'true'
    MONGODB_W = int(os.environ.get('MONGODB_W', 1))
    MONGODB_JOURNAL = os.environ.get('MONGODB_JOURNAL', 'true').lower() == 'true'
    MONGODB_APP_NAME = os.environ.get('MONGODB_APP_NAME', 'legislator_recall_backend')

    # 立委選舉區選舉人總數字典 (第11屆)
LEGISLATOR_ELECTORAL_COUNTS = {
    "馬文君": {"district": "南投縣第二選舉區", "electoral_count": 281438},
    "葉元之": {"district": "新北市第一選舉區", "electoral_count": 357901},
    "楊瓊瓔": {"district": "台中市第三選舉區", "electoral_count": 252658},
    "陳超明": {"district": "苗栗縣第一選舉區", "electoral_count": 280071},
    "顏寬恒": {"district": "台中市第二選舉區", "electoral_count": 285159},
    "廖偉翔": {"district": "台中市第一選舉區", "electoral_count": 300533},
    "傅崐萁": {"district": "花蓮縣選舉區", "electoral_count": 309036},
    "丁學忠": {"district": "雲林縣第一選舉區", "electoral_count": 258477},
    "江啟臣": {"district": "台中市第八選舉區", "electoral_count": 272308},
    "邱鎮軍": {"district": "苗栗縣第二選舉區", "electoral_count": 273088},
    "羅廷瑋": {"district": "台中市第六選舉區", "electoral_count": 282109},
    "黃健豪": {"district": "台中市第五選舉區", "electoral_count": 275662},
    "黃建賓": {"district": "台東縣選舉區", "electoral_count": 184863},
    "謝衣鳳": {"district": "彰化縣第三選舉區", "electoral_count": 268261},
    "游顥": {"district": "南投縣第一選舉區", "electoral_count": 273733},
    "牛煦庭": {"district": "桃園市第一選舉區", "electoral_count": 327159},
    "王鴻薇": {"district": "台北市第三選舉區", "electoral_count": 271768},
    "羅明才": {"district": "新北市第十一選舉區", "electoral_count": 281254},
    "林思銘": {"district": "新竹縣第二選舉區", "electoral_count": 295496},
    "萬美玲": {"district": "桃園市第二選舉區", "electoral_count": 325483},
    "呂玉玲": {"district": "桃園市第四選舉區", "electoral_count": 307816},
    "廖先翔": {"district": "新北市第十二選舉區", "electoral_count": 292729},
    "洪孟楷": {"district": "新北市第一選舉區", "electoral_count": 357901},
    "李彥秀": {"district": "台北市第四選舉區", "electoral_count": 261330},
    "鄭正鈐": {"district": "新竹市選舉區", "electoral_count": 356870},
    "徐巧芯": {"district": "台北市第七選舉區", "electoral_count": 238822},
    "羅智強": {"district": "台北市第五選舉區", "electoral_count": 240496},
    "徐欣瑩": {"district": "新竹縣第一選舉區", "electoral_count": 367078},
    "賴士葆": {"district": "台北市第八選舉區", "electoral_count": 281424},
    "林德福": {"district": "新北市第九選舉區", "electoral_count": 263332},
    "林沛祥": {"district": "基隆市選舉區", "electoral_count": 308092},
    "涂權吉": {"district": "桃園市第二選舉區", "electoral_count": 325483},
    "張智倫": {"district": "新北市第八選舉區", "electoral_count": 293711},
    "邱若華": {"district": "桃園市第六選舉區", "electoral_count": 254778},
    "魯明哲": {"district": "桃園市第三選舉區", "electoral_count": 326940},
    "高虹安": {"district": "新竹市選舉區", "electoral_count": 356870}
}

class DevelopmentConfig(Config):
    """本地開發環境配置"""
    DEBUG = True
    ENV = 'development'
    # 開發環境使用本地 MongoDB
    MONGODB_URI = 'mongodb://localhost:27017'
    MONGODB_DBNAME = 'legislator_recall'

class RailwayConfig(Config):
    """Railway MongoDB 生產環境配置"""
    DEBUG = False
    ENV = 'production'
    
    @property
    def MONGODB_URI(self):
        uri = os.environ.get('RAILWAY_URI')
        if not uri:
            # 預設連接字串
            uri = 'mongodb://mongo:<EMAIL>:11709'
            print("警告：使用預設的 Railway MongoDB 連接字串，請確認連接參數是否正確")
        return uri
    
    @property
    def MONGODB_DBNAME(self):
        return os.environ.get('RAILWAY_DBNAME', 'legislator_recall')

def get_config():
    """根據環境變數自動選擇配置
    
    - development: 本地開發環境
    - production: Railway 生產環境
    """
    # 每次重新讀取環境變數，避免使用緩存
    config = os.environ.get('FLASK_CONFIG', 'development').lower()
    
    # 顯示當前使用的配置模式
    print(f"使用配置模式: {config}")
    
    # 在 Render 環境中強制使用 production
    if os.environ.get('IS_RENDER') == 'true':
        print("確認在 Render 環境中，強制使用 production 配置")
        return RailwayConfig
    
    # 判斷是否使用生產環境
    if config == 'production':
        return RailwayConfig
    else:
        # 預設使用開發環境配置
        return DevelopmentConfig

def get_mongo_config():
    """
    根據當前配置獲取 MongoDB 連接資訊
    自動根據環境選擇本地或 Fly.io 數據庫
    並返回優化的連線參數
    """
    config = get_config()()  # 獲取配置實例

    mongodb_uri = config.MONGODB_URI
    mongodb_dbname = config.MONGODB_DBNAME

    # 添加 MongoDB 連線優化參數
    mongo_options = {
        'maxPoolSize': config.MONGODB_MAX_POOL_SIZE,
        'maxIdleTimeMS': config.MONGODB_MAX_IDLE_TIME_MS,
        'connectTimeoutMS': config.MONGODB_CONNECT_TIMEOUT_MS,
        'socketTimeoutMS': config.MONGODB_SOCKET_TIMEOUT_MS,
        'serverSelectionTimeoutMS': config.MONGODB_SERVER_SELECTION_TIMEOUT_MS,
        'waitQueueTimeoutMS': config.MONGODB_WAIT_QUEUE_TIMEOUT_MS,
        'heartbeatFrequencyMS': config.MONGODB_HEARTBEAT_FREQUENCY_MS,
        'retryWrites': config.MONGODB_RETRY_WRITES,
        'retryReads': config.MONGODB_RETRY_READS,
        'w': config.MONGODB_W,
        'journal': config.MONGODB_JOURNAL,
        'appName': config.MONGODB_APP_NAME
    }    # 輸出連接信息（僅在開發環境）
    if config.ENV == 'development':
        print(f"MongoDB 連接: {config.ENV} 環境")
        print(f"MongoDB URI: {mongodb_uri}")
        print(f"MongoDB 資料庫: {mongodb_dbname}")
    else:
        print(f"MongoDB 連接: 正式環境 (Railway)")
        print(f"MongoDB 資料庫: {mongodb_dbname}")

    return {
        'MONGODB_URI': mongodb_uri,
        'MONGODB_DBNAME': mongodb_dbname,
        'MONGODB_OPTIONS': mongo_options
    }