{"cells": [{"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "from sklearn.metrics import accuracy_score\n", "\n", "# 設定檔案路徑\n", "DATA_PATHS = {\n", "    \"人工標註數據\": \"data/0323/驗證-myself.json\",\n", "    \"Gemini 預測數據\": \"data/0323/驗證-gemini2.json\",\n", "    \"BERT 預測數據\": \"data/0323/驗證-bert.json\"\n", "}\n", "\n", "REQUIRED_COLUMNS = {\"標題\", \"留言內容\", \"情感標籤\", \"情緒\"}\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功處理 90 筆資料\n", "\n", "true_data 範例:\n", "{\n", "  \"context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"reply\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"label\": \"POSITIVE\",\n", "  \"emotion\": \"anger\"\n", "}\n", "\n", "predict_bert_data 範例:\n", "{\n", "  \"context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"reply\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"label\": \"POSITIVE\",\n", "  \"emotion\": \"anger\"\n", "}\n"]}, {"data": {"text/plain": ["[{'context': '2/1「立委就職滿一年」 罷免團體衝刺連署',\n", "  'reply': 'TVBS新聞 \\u202a@TVBSNEWS01\\u202c 可以先罷免爛總桶嗎',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之',\n", "  'reply': '徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305',\n", "  'reply': '#許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '徐巧芯掃街拜年遇抗議！民眾怒撕春聯\\u3000衝罷免站連署',\n", "  'reply': '#鏡新聞 中指可以比出來啊～',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】',\n", "  'reply': '三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '自由爆新聞》糗！罷綠慘團滅､罷免藍\"全壘打\"！他不認中共威脅網傻眼轟！(柯文哲/川普)',\n", "  'reply': '國民黨吃台灣賣台灣。',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3',\n", "  'reply': '黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '被罷免並非不可能！傅崐萁與徐巧芯「仇恨值飆升」成綠營摧毀戰上「兩大指標性山頭」？【關鍵時刻】\\u202a@ebcCTime\\u202c',\n", "  'reply': '請支持國共合作',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑',\n", "  'reply': '民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '徐巧芯掃街拜年遇抗議！民眾怒撕春聯\\u3000衝罷免站連署',\n", "  'reply': '#鏡新聞 醒醒',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」',\n", "  'reply': '三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免案第一階段送件！ 二階遭曝連署時間短、難度高',\n", "  'reply': '霸到底救國家\\n霸到底救台灣\\n霸到底救民主\\n霸到底救人民',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞',\n", "  'reply': '國民黨在恐嚇選民，真的好可怕',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16',\n", "  'reply': '行政院可以不接受。让国民党倒阁，解散国会。\\n国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞',\n", "  'reply': '千萬別被他騙了!!!霸定了舉手部隊。',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷韓像失戀的女人?葉元之.王定宇火線交鋒!',\n", "  'reply': '廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '自由爆新聞》12藍委陷\"罷免危險區\"！花蓮想變！他揭\"1狀況\"傅崐萁會怕！(王毅/王大陸)',\n", "  'reply': '中共又再胡說八道，鬼扯，。',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '\"我們也是保台但年輕人不接受\"! 葉元之委屈解釋\"九二共識\"? \"他\"挨轟不切實際...│鄭弘儀 主持│【鄭知道了。先知道】20200114│三立iNEWS',\n", "  'reply': '原汁沒開玩笑 是在裝瘋賣傻\\n人一藍 腦就殘',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片\" 反制大罷免潮 朱立倫:啟動反廢死公投應戰│新聞一把抓20250308│三立新聞台',\n", "  'reply': '死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台',\n", "  'reply': '累就滾回家',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305',\n", "  'reply': '#許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\\n但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '寶傑驚喊「徐巧芯沒別條路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑',\n", "  'reply': '遷户口準備中\\n徐阿花你等著',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！',\n", "  'reply': '罷免葉元之就是罷免陳玉珍',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台',\n", "  'reply': '那張衰臉真是原汁',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台',\n", "  'reply': '無恥的人，還敢公然鬼扯，罷定了!',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免藍委遍地開花！葉元之.涂權吉.羅廷瑋恐成危險名單 賴中強揭罷免可能適用舊法 諷藍白弄巧成拙！民團拚2/3提案送出連署書',\n", "  'reply': '三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台',\n", "  'reply': '領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\"盯梢\" 淡水分局:依法行政.保持中立',\n", "  'reply': '三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '發燒新聞／葉元之被問罷免急閃躲\\u3000雙手合十求饒：大年初一別這樣',\n", "  'reply': '葉猿之可惡至極，非罷不可 。',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '自由爆新聞》糗！罷綠慘團滅､罷免藍\"全壘打\"！他不認中共威脅網傻眼轟！(柯文哲/川普)',\n", "  'reply': '除惡務盡，一戰改變台灣政治生態，台派加油！',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞',\n", "  'reply': '這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【3Q來客訴】台美建交就像伴侶結婚?!葉元之大戰3Q：「台美需要實質關係」',\n", "  'reply': '以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\\n馬來西亞華人都是穴今',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '自由爆新聞》\"罷免謝國樑\"進度曝光！藍立院\"奪權之戰\"？分析示警！(黃仁勳/基隆山崩)',\n", "  'reply': '國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '寶傑驚喊「徐巧芯沒別條路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑',\n", "  'reply': '人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【一刀未剪】來~元之讓你說！ 葉元之稱罷免是民進黨發起 「先射箭再畫靶」成為鎖定目標連開玩笑都不行 還被青鳥修理？ 簡舒培揪過去「這點」打臉 雙方吵翻',\n", "  'reply': '【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\"盯梢\" 淡水分局:依法行政.保持中立',\n", "  'reply': '三立新聞網 SETN.com 还有民众党',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免陳柏惟宣傳片(唐鳳舞版本)',\n", "  'reply': '元之你是在做復健嗎?',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免陳柏惟宣傳片(唐鳳舞版本)',\n", "  'reply': '朝聖! 柿子要挑軟的吃',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3',\n", "  'reply': '黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '青鳥發威藍白膽寒？元之罷總召！傅修選罷法被撤案！黨團爆內鬨？【全國第一勇】2024.05.31',\n", "  'reply': '建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '《鄉民大學問EP.80》字幕版',\n", "  'reply': '#王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台',\n", "  'reply': '葉元之從頭到尾鬼扯',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞',\n", "  'reply': '傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免潮燒新北！葉元之也遭殃\\u3000葉：搞鬥爭秀下限',\n", "  'reply': '#鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【#週末大爆卦 上】 還是完蛋了!罷免案分AB案送件!吳沛憶樂極生悲?他爆\"這南部綠委\"罷免案恐是自導自演?20250209 \\u202a@大新聞大爆卦HotNewsTalk\\u202c',\n", "  'reply': '真不相信李蟾蜍會是一個中立的X X',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3',\n", "  'reply': '黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '發燒新聞／葉元之被問罷免急閃躲\\u3000雙手合十求饒：大年初一別這樣',\n", "  'reply': '葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│\\u202a@NewTaiwan\\u202c',\n", "  'reply': '葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│\\u202a@NewTaiwan\\u202c',\n", "  'reply': '國民黨出葉元之這種貨色\\n不倒才怪',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！',\n", "  'reply': '笑噴～真的要出來把沒用的破嘴51席全罷掉',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '自由爆新聞》12藍委陷\"罷免危險區\"！花蓮想變！他揭\"1狀況\"傅崐萁會怕！(王毅/王大陸)',\n", "  'reply': '國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； \\n1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16',\n", "  'reply': '中共在逼了他們為何要這麼急',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之',\n", "  'reply': '看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】',\n", "  'reply': '三立新聞網 SETN.com 青年軍 : \"什麼~要坐牢 ?!  黨主席, 救救我 ! \"\\n朱立倫 : \"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\"\\n青年軍 : \"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \"\\n朱立倫 : \"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \"',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！',\n", "  'reply': '葉原之是立委? 我以為他是政論節目通告咖',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '徐巧芯掃街拜年遇抗議！民眾怒撕春聯\\u3000衝罷免站連署',\n", "  'reply': '#鏡新聞 支持罷免徐三八！',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」',\n", "  'reply': '三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之',\n", "  'reply': '為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【一刀未剪】三種罷免一次滿足?! \"早餐火腿蛋吐司 吃完罷免葉元之\"歌曲一出眾人笑翻 溫朗東AI創作罷樑曲獲好評 喊話徐巧芯:不要急! 你的歌一定最精彩│【焦點人物大現場】20240617│三立新聞台',\n", "  'reply': '自己的國家自己救 !',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免潮燒新北！葉元之也遭殃\\u3000葉：搞鬥爭秀下限',\n", "  'reply': '#鏡新聞 葉李的說話水準素質堪憂',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台',\n", "  'reply': '這屆藍白真的有史以來最爛的',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│\\u202a@NewTaiwan\\u202c',\n", "  'reply': '賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '徐巧芯掃街拜年遇抗議！民眾怒撕春聯\\u3000衝罷免站連署',\n", "  'reply': '#鏡新聞 請問秀中指算不算帶著仇恨?',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│\\u202a@NewTaiwan\\u202c',\n", "  'reply': '元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '談到罷免紅了眼眶？\\u3000葉元之「曝光原因」怒批交通部 \\u202a@ChinaTimes\\u202c',\n", "  'reply': '感覺想專政',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免陳柏惟宣傳片(唐鳳舞版本)',\n", "  'reply': '鳳姐也要「刪Q」？',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之',\n", "  'reply': '這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '《鄉民大學問EP.48》重點版',\n", "  'reply': '黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews \\u202a@hance63\\u202c 支持罷免爛藍白立委',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之',\n", "  'reply': '所以该节目到底是蓝是绿？',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3',\n", "  'reply': '黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16',\n", "  'reply': '大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑',\n", "  'reply': '輸不起就不要出來選嗎',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '正面迎戰罷免潮！ 徐巧芯「超前部署」反罷 嗆綠輸不起！\\u202a@newsebc\\u202c',\n", "  'reply': '亂七八糟，真的大搞綠色恐怖，肅清異己',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之',\n", "  'reply': '凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│\\u202a@NewTaiwan\\u202c',\n", "  'reply': '葉之再凹就聽不下去了.你令人討厭耶.',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞',\n", "  'reply': '加油！罷免藍營立委',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│\\u202a@NewTaiwan\\u202c',\n", "  'reply': '主持人要加油加油',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3',\n", "  'reply': '黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免陳柏惟宣傳片(唐鳳舞版本)',\n", "  'reply': '破產都要把牠罷下來！',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '民團號召罷免27藍委 葉元之:那總統也一起重選! 狙擊27藍委! 2月任期滿1年 民團將發動罷免│記者 許芷瑄 林柏翰│台灣要聞20250102│三立iNEWS',\n", "  'reply': '美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免民調危險了?!葉元之曝\"自救計劃\":有則改之無則加勉',\n", "  'reply': '元之委員加油',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3',\n", "  'reply': '黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '「春眠不覺曉、文山賴士葆」大罷免開花！學生詩諷藍委－民視新聞',\n", "  'reply': '往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台',\n", "  'reply': '審預算沒看內容還能表決，這立委6歲小孩都能當',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '自由爆新聞》12藍委陷\"罷免危險區\"！花蓮想變！他揭\"1狀況\"傅崐萁會怕！(王毅/王大陸)',\n", "  'reply': '如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台',\n", "  'reply': '哇，清大有這種老師教憲法！悲哀啊！',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【94要客訴】沒在怕？綠營將反制「罷免江啟臣」？葉元之：要罷就罷！',\n", "  'reply': '不止罷免江啟程。\\n凡是國民黨議員。立委。縣市長。\\n都該被罷免。\\n趕出台灣。',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'},\n", " {'context': '【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│\\u202a@NewTaiwan\\u202c',\n", "  'reply': '罷候',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '立院外罷免投票「徐巧芯、葉元之人氣旺」！青鳥投票「黃國昌被貼滿」\\u202a@newsebc\\u202c',\n", "  'reply': '抗議的不知道自己在抗什麼',\n", "  'label': 'NEGATIVE',\n", "  'emotion': 'anger'},\n", " {'context': '民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑',\n", "  'reply': '我們直接罷掉不適任的總統！更快！',\n", "  'label': 'POSITIVE',\n", "  'emotion': 'anger'}]"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "\n", "def split_data(input_file):\n", "    \"\"\"\n", "    將資料分為 true 和 predict_bert 兩種格式\n", "    :param input_file: 輸入的JSON檔案路徑\n", "    :return: 包含 true_data 和 predict_bert_data 的字典\n", "    \"\"\"\n", "    try:\n", "        # 讀取JSON檔案\n", "        with open(input_file, \"r\", encoding=\"utf-8\") as f:\n", "            data = json.load(f)\n", "        \n", "        # 初始化結果列表\n", "        true_data = []\n", "        predict_bert_data = []\n", "        \n", "        # 處理每一筆資料\n", "        for item in data:\n", "            # 創建 true 格式的資料\n", "            true_item = {\n", "                \"context\": item[\"context\"],\n", "                \"reply\": item[\"reply\"],\n", "                \"label\": item[\"true_sentiment\"],\n", "                \"emotion\": item[\"true_emotion\"]\n", "            }\n", "            true_data.append(true_item)\n", "            \n", "            # 創建 predict_bert 格式的資料\n", "            predict_item = {\n", "                \"context\": item[\"context\"],\n", "                \"reply\": item[\"reply\"],\n", "                \"label\": item[\"predicted_sentiment\"],\n", "                \"emotion\": item[\"predicted_emotion\"]\n", "            }\n", "            predict_bert_data.append(predict_item)\n", "        \n", "        # 顯示處理結果\n", "        print(f\"成功處理 {len(data)} 筆資料\")\n", "        print(\"\\ntrue_data 範例:\")\n", "        print(json.dumps(true_data[0], ensure_ascii=False, indent=2))\n", "        print(\"\\npredict_bert_data 範例:\")\n", "        print(json.dumps(predict_bert_data[0], ensure_ascii=False, indent=2))\n", "        \n", "        # 返回處理後的資料\n", "        return {\n", "            \"true_data\": true_data,\n", "            \"predict_bert_data\": predict_bert_data\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"發生錯誤：{str(e)}\")\n", "        return None\n", "\n", "# 使用範例\n", "input_file = r\"data\\100_model_predict.json\"\n", "result = split_data(input_file)\n", "\n", "# 如果你需要存取處理後的資料\n", "if result:\n", "    true_data = result[\"true_data\"]\n", "    predict_bert_data = result[\"predict_bert_data\"]\n", "    \n", "    # 可以在這裡使用這些資料進行後續處理\n", "predict_bert_data"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功處理 85 筆資料\n", "\n", "true_data 範例:\n", "{\n", "  \"context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"reply\": \"TVBS新聞 ‪@BSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"label\": \"POSITIVE\",\n", "  \"emotion\": \"anger\"\n", "}\n", "成功處理 60 筆資料\n", "\n", "true_data 範例:\n", "{\n", "  \"context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"reply\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"label\": \"POSITIVE\",\n", "  \"emotion\": \"anger\"\n", "}\n", "成功處理 90 筆資料\n", "\n", "true_data 範例:\n", "{\n", "  \"context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"reply\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"label\": \"POSITIVE\",\n", "  \"emotion\": \"disgust\"\n", "}\n", "成功處理 82 筆資料\n", "\n", "true_data 範例:\n", "{\n", "  \"context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"reply\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"label\": \"POSITIVE\",\n", "  \"emotion\": \"disgust\"\n", "}\n", "成功處理 89 筆資料\n", "\n", "true_data 範例:\n", "{\n", "  \"context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"reply\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"label\": \"POSITIVE\",\n", "  \"emotion\": \"anger\"\n", "}\n", "成功處理 88 筆資料\n", "\n", "true_data 範例:\n", "{\n", "  \"context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"reply\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"label\": \"POSITIVE\",\n", "  \"emotion\": \"disgust\"\n", "}\n"]}], "source": ["import json\n", "\n", "def split_data(input_file):\n", "    \"\"\"\n", "    將資料分為 true 和 predict_bert 兩種格式\n", "    :param input_file: 輸入的JSON檔案路徑\n", "    :return: 包含 true_data 和 predict_bert_data 的字典\n", "    \"\"\"\n", "    try:\n", "        # 讀取JSON檔案\n", "        with open(input_file, \"r\", encoding=\"utf-8\") as f:\n", "            data = json.load(f)\n", "        \n", "        # 初始化結果列表\n", "        true_data = []\n", "    \n", "        \n", "        # 處理每一筆資料\n", "        for item in data:\n", "            # 創建 true 格式的資料\n", "            true_item = {\n", "                \"context\": item[\"標題\"],\n", "                \"reply\": item[\"留言內容\"],\n", "                \"label\": item[\"情感標籤\"],\n", "                \"emotion\": item[\"情緒\"]\n", "            }\n", "            true_data.append(true_item)\n", "            \n", "     \n", "        \n", "        # 顯示處理結果\n", "        print(f\"成功處理 {len(data)} 筆資料\")\n", "        print(\"\\ntrue_data 範例:\")\n", "        print(json.dumps(true_data[0], ensure_ascii=False, indent=2))\n", "     \n", "        \n", "        # 返回處理後的資料\n", "        return {\n", "            \"true_data\": true_data,\n", "            \"predict_bert_data\": predict_bert_data\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"發生錯誤：{str(e)}\")\n", "        return None\n", "\n", "# 使用範例\n", "# 創建6個不同規模的資料集\n", "input_files = [\n", "    r\"data\\0409\\gemini_ba_20.json\",\n", "    r\"data\\0409\\gemini_ba_50.json\",\n", "    r\"data\\0409\\gemini_ba_100.json\",\n", "    r\"data\\0409\\gemini_em_ba_20.json\",\n", "    r\"data\\0409\\gemini_em_ba_50-003.json\",\n", "    r\"data\\0409\\gemini_em_ba_100.json\"\n", "]\n", "\n", "ba_20_file = r\"data\\0409\\gemini_ba_20.json\"\n", "ba_20_data = split_data(ba_20_file)\n", "\n", "ba_50_file = r\"data\\0409\\gemini_ba_50.json\"\n", "ba_50_data = split_data(ba_50_file)\n", "\n", "ba_100_file = r\"data\\0409\\gemini_ba_100.json\"\n", "ba_100_data = split_data(ba_100_file)\n", "\n", "em_ba_20_file = r\"data\\0409\\gemini_em_ba_20.json\"\n", "em_ba_20_data = split_data(em_ba_20_file)\n", "\n", "em_ba_50_file = r\"data\\0409\\gemini_em_ba_50-003.json\"\n", "em_ba_50_data = split_data(em_ba_50_file)\n", "\n", "em_ba_100_file = r\"data\\0409\\gemini_em_ba_100.json\"\n", "em_ba_100_data = split_data(em_ba_100_file)\n", "\n", "\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "各模型準確性比較：\n", "------------------------------------------------------------------------------------------------------------------------\n", "模型名稱            Label準確率        Emotion準確率      兩者都正確           樣本數        Label錯誤數     Emotion錯誤數  \n", "------------------------------------------------------------------------------------------------------------------------\n", "ba_20           0.5412        0.2235        0.1176        85         39           66          \n", "ba_50           0.4833        0.3167        0.1833        60         31           41          \n", "ba_100          0.6444        0.2889        0.2111        90         32           64          \n", "em_ba_20        0.4512        0.2317        0.1098        82         45           63          \n", "em_ba_50        0.6067        0.2022        0.1461        89         35           71          \n", "em_ba_100       0.4318        0.1932        0.0568        88         50           71          \n", "predict_bert_data 0.6000        0.3667        0.2333        90         36           57          \n", "\n", "詳細比對結果：\n", "--------------------------------------------------------------------------------\n", "\n", "ba_20 的比對結果：\n", "--------------------------------------------------\n", "\n", "第 1 筆資料：\n", "Context: 2/1「立委就職滿一年」 罷免團體衝刺連署\n", "Reply: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: anger ✓\n", "\n", "第 2 筆資料：\n", "Context: 鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\n", "Reply: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "True Label: POSITIVE vs Predicted Label: NEGATIVE ✗\n", "True Emotion: anger vs Predicted Emotion: anger ✓\n", "\n", "第 3 筆資料：\n", "Context: 【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\n", "Reply: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: sadness vs Predicted Emotion: anger ✗\n", "\n", "第 4 筆資料：\n", "Context: 徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\n", "Reply: #鏡新聞 中指可以比出來啊～\n", "True Label: NEGATIVE vs Predicted Label: NEGATIVE ✓\n", "True Emotion: fear vs Predicted Emotion: anger ✗\n", "\n", "第 5 筆資料：\n", "Context: 罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\n", "Reply: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "True Label: NEGATIVE vs Predicted Label: NEGATIVE ✓\n", "True Emotion: disgust vs Predicted Emotion: anger ✗\n", "\n", "ba_50 的比對結果：\n", "--------------------------------------------------\n", "\n", "第 1 筆資料：\n", "Context: 2/1「立委就職滿一年」 罷免團體衝刺連署\n", "Reply: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: anger ✓\n", "\n", "第 2 筆資料：\n", "Context: 鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\n", "Reply: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: anger ✓\n", "\n", "第 3 筆資料：\n", "Context: 【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\n", "Reply: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "True Label: POSITIVE vs Predicted Label: NEGATIVE ✗\n", "True Emotion: sadness vs Predicted Emotion: anger ✗\n", "\n", "第 4 筆資料：\n", "Context: 徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\n", "Reply: #鏡新聞 中指可以比出來啊～\n", "True Label: NEGATIVE vs Predicted Label: NEGATIVE ✓\n", "True Emotion: fear vs Predicted Emotion: anger ✗\n", "\n", "第 5 筆資料：\n", "Context: 罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\n", "Reply: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "True Label: NEGATIVE vs Predicted Label: POSITIVE ✗\n", "True Emotion: disgust vs Predicted Emotion: trust ✗\n", "\n", "ba_100 的比對結果：\n", "--------------------------------------------------\n", "\n", "第 1 筆資料：\n", "Context: 2/1「立委就職滿一年」 罷免團體衝刺連署\n", "Reply: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: disgust ✗\n", "\n", "第 2 筆資料：\n", "Context: 鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\n", "Reply: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "True Label: POSITIVE vs Predicted Label: NEGATIVE ✗\n", "True Emotion: anger vs Predicted Emotion: disgust ✗\n", "\n", "第 3 筆資料：\n", "Context: 【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\n", "Reply: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "True Label: POSITIVE vs Predicted Label: NEGATIVE ✗\n", "True Emotion: sadness vs Predicted Emotion: anger ✗\n", "\n", "第 4 筆資料：\n", "Context: 徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\n", "Reply: #鏡新聞 中指可以比出來啊～\n", "True Label: NEGATIVE vs Predicted Label: POSITIVE ✗\n", "True Emotion: fear vs Predicted Emotion: disgust ✗\n", "\n", "第 5 筆資料：\n", "Context: 罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\n", "Reply: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "True Label: NEGATIVE vs Predicted Label: NEGATIVE ✓\n", "True Emotion: disgust vs Predicted Emotion: anger ✗\n", "\n", "em_ba_20 的比對結果：\n", "--------------------------------------------------\n", "\n", "第 1 筆資料：\n", "Context: 2/1「立委就職滿一年」 罷免團體衝刺連署\n", "Reply: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: disgust ✗\n", "\n", "第 2 筆資料：\n", "Context: 鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\n", "Reply: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "True Label: POSITIVE vs Predicted Label: NEGATIVE ✗\n", "True Emotion: anger vs Predicted Emotion: sadness ✗\n", "\n", "第 3 筆資料：\n", "Context: 【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\n", "Reply: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: sadness vs Predicted Emotion: anger ✗\n", "\n", "第 4 筆資料：\n", "Context: 徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\n", "Reply: #鏡新聞 中指可以比出來啊～\n", "True Label: NEGATIVE vs Predicted Label: POSITIVE ✗\n", "True Emotion: fear vs Predicted Emotion: disgust ✗\n", "\n", "第 5 筆資料：\n", "Context: 罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\n", "Reply: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "True Label: NEGATIVE vs Predicted Label: NEGATIVE ✓\n", "True Emotion: disgust vs Predicted Emotion: anger ✗\n", "\n", "em_ba_50 的比對結果：\n", "--------------------------------------------------\n", "\n", "第 1 筆資料：\n", "Context: 2/1「立委就職滿一年」 罷免團體衝刺連署\n", "Reply: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: anger ✓\n", "\n", "第 2 筆資料：\n", "Context: 鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\n", "Reply: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "True Label: POSITIVE vs Predicted Label: NEGATIVE ✗\n", "True Emotion: anger vs Predicted Emotion: joy ✗\n", "\n", "第 3 筆資料：\n", "Context: 【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\n", "Reply: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: sadness vs Predicted Emotion: anger ✗\n", "\n", "第 4 筆資料：\n", "Context: 徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\n", "Reply: #鏡新聞 中指可以比出來啊～\n", "True Label: NEGATIVE vs Predicted Label: NEGATIVE ✓\n", "True Emotion: fear vs Predicted Emotion: anger ✗\n", "\n", "第 5 筆資料：\n", "Context: 罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\n", "Reply: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "True Label: NEGATIVE vs Predicted Label: NEGATIVE ✓\n", "True Emotion: disgust vs Predicted Emotion: anger ✗\n", "\n", "em_ba_100 的比對結果：\n", "--------------------------------------------------\n", "\n", "第 1 筆資料：\n", "Context: 2/1「立委就職滿一年」 罷免團體衝刺連署\n", "Reply: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: disgust ✗\n", "\n", "第 2 筆資料：\n", "Context: 鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\n", "Reply: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: anger ✓\n", "\n", "第 3 筆資料：\n", "Context: 【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\n", "Reply: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: sadness vs Predicted Emotion: joy ✗\n", "\n", "第 4 筆資料：\n", "Context: 徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\n", "Reply: #鏡新聞 中指可以比出來啊～\n", "True Label: NEGATIVE vs Predicted Label: POSITIVE ✗\n", "True Emotion: fear vs Predicted Emotion: anger ✗\n", "\n", "第 5 筆資料：\n", "Context: 罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\n", "Reply: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "True Label: NEGATIVE vs Predicted Label: NEGATIVE ✓\n", "True Emotion: disgust vs Predicted Emotion: anger ✗\n", "\n", "predict_bert_data 的比對結果：\n", "--------------------------------------------------\n", "\n", "第 1 筆資料：\n", "Context: 2/1「立委就職滿一年」 罷免團體衝刺連署\n", "Reply: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: anger ✓\n", "\n", "第 2 筆資料：\n", "Context: 鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\n", "Reply: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: anger vs Predicted Emotion: anger ✓\n", "\n", "第 3 筆資料：\n", "Context: 【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\n", "Reply: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "True Label: POSITIVE vs Predicted Label: POSITIVE ✓\n", "True Emotion: sadness vs Predicted Emotion: anger ✗\n", "\n", "第 4 筆資料：\n", "Context: 徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\n", "Reply: #鏡新聞 中指可以比出來啊～\n", "True Label: NEGATIVE vs Predicted Label: POSITIVE ✗\n", "True Emotion: fear vs Predicted Emotion: anger ✗\n", "\n", "第 5 筆資料：\n", "Context: 罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\n", "Reply: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "True Label: NEGATIVE vs Predicted Label: NEGATIVE ✓\n", "True Emotion: disgust vs Predicted Emotion: anger ✗\n"]}], "source": ["import os\n", "import json\n", "from datetime import datetime\n", "\n", "def save_incorrect_predictions(true_data, predict_data, model_name):\n", "    \"\"\"\n", "    儲存錯誤的預測結果，分別儲存 label 和 emotion 的錯誤\n", "    :param true_data: 真實資料列表\n", "    :param predict_data: 預測資料列表\n", "    :param model_name: 模型名稱\n", "    \"\"\"\n", "    label_incorrect = []\n", "    emotion_incorrect = []\n", "    \n", "    for true, pred in zip(true_data, predict_data):\n", "        # 檢查 label 錯誤\n", "        if true[\"label\"] != pred[\"label\"]:\n", "            label_prediction = {\n", "                \"context\": true[\"context\"],\n", "                \"reply\": true[\"reply\"],\n", "                \"true_label\": true[\"label\"],\n", "                \"predicted_label\": pred[\"label\"]\n", "            }\n", "            label_incorrect.append(label_prediction)\n", "        \n", "        # 檢查 emotion 錯誤\n", "        if true[\"emotion\"] != pred[\"emotion\"]:\n", "            emotion_prediction = {\n", "                \"context\": true[\"context\"],\n", "                \"reply\": true[\"reply\"],\n", "                \"true_emotion\": true[\"emotion\"],\n", "                \"predicted_emotion\": pred[\"emotion\"]\n", "            }\n", "            emotion_incorrect.append(emotion_prediction)\n", "    \n", "    # 建立目錄\n", "    result_dir = \"data/0409/result\"\n", "    os.makedirs(result_dir, exist_ok=True)\n", "    \n", "    # 產生時間戳記\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 儲存 label 錯誤檔案\n", "    label_filename = f\"{result_dir}/{model_name}_L_{timestamp}.json\"\n", "    with open(label_filename, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(label_incorrect, f, ensure_ascii=False, indent=2)\n", "    \n", "    # 儲存 emotion 錯誤檔案\n", "    emotion_filename = f\"{result_dir}/{model_name}_E_{timestamp}.json\"\n", "    with open(emotion_filename, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(emotion_incorrect, f, ensure_ascii=False, indent=2)\n", "    \n", "    return len(label_incorrect), len(emotion_incorrect)\n", "\n", "def calculate_accuracy(true_data, predict_data):\n", "    \"\"\"\n", "    計算預測資料的準確性\n", "    :param true_data: 真實資料列表\n", "    :param predict_data: 預測資料列表\n", "    :return: 準確率字典\n", "    \"\"\"\n", "    total = len(predict_data)\n", "    label_correct = 0\n", "    emotion_correct = 0\n", "    both_correct = 0\n", "    \n", "    for true, pred in zip(true_data, predict_data):\n", "        # 比對 label\n", "        if true[\"label\"] == pred[\"label\"]:\n", "            label_correct += 1\n", "        \n", "        # 比對 emotion\n", "        if true[\"emotion\"] == pred[\"emotion\"]:\n", "            emotion_correct += 1\n", "        \n", "        # 比對兩者都正確\n", "        if (true[\"label\"] == pred[\"label\"] and \n", "            true[\"emotion\"] == pred[\"emotion\"]):\n", "            both_correct += 1\n", "    \n", "    return {\n", "        \"label_accuracy\": label_correct / total,\n", "        \"emotion_accuracy\": emotion_correct / total,\n", "        \"both_accuracy\": both_correct / total,\n", "        \"total_samples\": total\n", "    }\n", "    \n", "def compare_all_models(true_data):\n", "    \"\"\"\n", "    比較所有模型的準確性\n", "    :param true_data: 真實資料\n", "    \"\"\"\n", "    # 定義所有要比較的模型資料\n", "    models = {\n", "        \"ba_20\": ba_20_data[\"true_data\"],\n", "        \"ba_50\": ba_50_data[\"true_data\"],\n", "        \"ba_100\": ba_100_data[\"true_data\"],\n", "        \"em_ba_20\": em_ba_20_data[\"true_data\"],\n", "        \"em_ba_50\": em_ba_50_data[\"true_data\"],\n", "        \"em_ba_100\": em_ba_100_data[\"true_data\"],\n", "        \"predict_bert_data\": predict_bert_data\n", "    }\n", "    \n", "    results = {}\n", "    label_incorrect_counts = {}\n", "    emotion_incorrect_counts = {}\n", "    \n", "    # 計算準確性並儲存錯誤預測\n", "    for model_name, predict_data in models.items():\n", "        results[model_name] = calculate_accuracy(true_data, predict_data)\n", "        label_count, emotion_count = save_incorrect_predictions(true_data, predict_data, model_name)\n", "        label_incorrect_counts[model_name] = label_count\n", "        emotion_incorrect_counts[model_name] = emotion_count\n", "    \n", "    # 顯示結果\n", "    print(\"\\n各模型準確性比較：\")\n", "    print(\"-\" * 120)\n", "    print(f\"{'模型名稱':<15} {'Label準確率':<15} {'Emotion準確率':<15} {'兩者都正確':<15} {'樣本數':<10} {'Label錯誤數':<12} {'Emotion錯誤數':<12}\")\n", "    print(\"-\" * 120)\n", "    \n", "    for model_name, result in results.items():\n", "        print(f\"{model_name:<15} {result['label_accuracy']:.4f}        {result['emotion_accuracy']:.4f}        {result['both_accuracy']:.4f}        {result['total_samples']:<10} {label_incorrect_counts[model_name]:<12} {emotion_incorrect_counts[model_name]:<12}\")\n", "    \n", "    # 顯示詳細的比對結果\n", "    print(\"\\n詳細比對結果：\")\n", "    print(\"-\" * 80)\n", "    for model_name, predict_data in models.items():\n", "        print(f\"\\n{model_name} 的比對結果：\")\n", "        print(\"-\" * 50)\n", "        for i, (true, pred) in enumerate(zip(true_data[:5], predict_data[:5])):  # 只顯示前5筆\n", "            print(f\"\\n第 {i+1} 筆資料：\")\n", "            print(f\"Context: {true['context']}\")\n", "            print(f\"Reply: {true['reply']}\")\n", "            print(f\"True Label: {true['label']} vs Predicted Label: {pred['label']} {'✓' if true['label'] == pred['label'] else '✗'}\")\n", "            print(f\"True Emotion: {true['emotion']} vs Predicted Emotion: {pred['emotion']} {'✓' if true['emotion'] == pred['emotion'] else '✗'}\")\n", "\n", "# 使用範例\n", "\n", "compare_all_models(true_data)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已成功提取資料並儲存至：C:\\Users\\<USER>\\Desktop\\儲存庫\\GEMINI\\data\\extracted_data_20250420_202848.json\n", "共處理 90 筆資料\n"]}], "source": ["import json\n", "import os\n", "from datetime import datetime\n", "\n", "def extract_and_save_json():\n", "    # 設定檔案路徑\n", "    input_file = r\"C:\\Users\\<USER>\\Desktop\\儲存庫\\GEMINI\\data\\100_model_predict.json\"\n", "    \n", "    # 設定輸出目錄和檔案名稱\n", "    output_dir = r\"C:\\Users\\<USER>\\Desktop\\儲存庫\\GEMINI\\data\"\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_file = os.path.join(output_dir, f\"extracted_data_{timestamp}.json\")\n", "    \n", "    # 確保輸出目錄存在\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    # 讀取原始JSON檔案\n", "    try:\n", "        with open(input_file, 'r', encoding='utf-8') as file:\n", "            data = json.load(file)\n", "        \n", "        # 創建新的資料結構\n", "        extracted_data = []\n", "        \n", "        # 判斷資料結構\n", "        if isinstance(data, list):\n", "            items = data\n", "        elif isinstance(data, dict) and 'data' in data:\n", "            items = data['data']\n", "        else:\n", "            print(\"無法識別JSON結構，請檢查原始檔案格式\")\n", "            return\n", "        \n", "        # 提取所需欄位\n", "        for item in items:\n", "            extracted_item = {\n", "                \"context\": item.get(\"context\", \"\"),\n", "                \"reply\": item.get(\"reply\", \"\"),\n", "                \"true_sentiment\": item.get(\"true_sentiment\", item.get(\"true_label\", \"\")),\n", "                \"true_emotion\": item.get(\"true_emotion\", \"\")\n", "            }\n", "            extracted_data.append(extracted_item)\n", "        \n", "        # 儲存新的JSON檔案\n", "        with open(output_file, 'w', encoding='utf-8') as file:\n", "            json.dump(extracted_data, file, ensure_ascii=False, indent=2)\n", "        \n", "        print(f\"已成功提取資料並儲存至：{output_file}\")\n", "        print(f\"共處理 {len(extracted_data)} 筆資料\")\n", "        \n", "    except Exception as e:\n", "        print(f\"處理過程中發生錯誤：{str(e)}\")\n", "\n", "if __name__ == \"__main__\":\n", "    extract_and_save_json()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["總資料筆數: 90\n", "\n", "資料已儲存至以下檔案:\n", "所有資料: C:\\Users\\<USER>\\Desktop\\儲存庫\\GEMINI\\data\\bert_data.json\n", "\n", "資料範例:\n", "{\n", "  \"標題\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"留言內容\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"情感標籤\": \"POSITIVE\",\n", "  \"情緒\": \"anger\"\n", "}\n"]}], "source": ["import json\n", "import os\n", "from datetime import datetime\n", "\n", "# 設定檔案路徑\n", "input_file = r\"C:\\Users\\<USER>\\Desktop\\儲存庫\\GEMINI\\data\\100_model_predict.json\"\n", "\n", "# 確認檔案存在\n", "if not os.path.exists(input_file):\n", "    print(f\"檔案不存在：{input_file}\")\n", "else:\n", "    try:\n", "        # 讀取JSON檔案\n", "        with open(input_file, 'r', encoding='utf-8') as file:\n", "            data = json.load(file)\n", "        \n", "        # 初始化結果列表\n", "        correct_data = []\n", "        \n", "        # 處理每一筆資料\n", "        for item in data:\n", "            # 提取所需欄位\n", "            context = item.get(\"context\", \"\")\n", "            reply = item.get(\"reply\", \"\")\n", "            true_label = item.get(\"predicted_sentiment\", \"\")\n", "            true_emotion = item.get(\"predicted_emotion\", \"\")\n", "            \n", "            # 標準格式的資料項\n", "            correct_item = {\n", "                \"標題\": context,\n", "                \"留言內容\": reply,\n", "                \"情感標籤\": true_label,\n", "                \"情緒\": true_emotion\n", "            }\n", "            correct_data.append(correct_item)\n", "        \n", "        # 設定輸出目錄\n", "        output_dir = r\"C:\\Users\\<USER>\\Desktop\\儲存庫\\GEMINI\\data\"\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        \n", "        # 產生時間戳記\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        \n", "        # 儲存所有正確資料\n", "        all_file = os.path.join(output_dir, f\"bert_data.json\")\n", "        with open(all_file, 'w', encoding='utf-8') as file:\n", "            json.dump(correct_data, file, ensure_ascii=False, indent=2)\n", "        \n", "        # 顯示結果統計\n", "        print(f\"總資料筆數: {len(data)}\")\n", "        print(f\"\\n資料已儲存至以下檔案:\")\n", "        print(f\"所有資料: {all_file}\")\n", "        \n", "        # 顯示範例\n", "        if correct_data:\n", "            print(\"\\n資料範例:\")\n", "            print(json.dumps(correct_data[0], ensure_ascii=False, indent=2))\n", "    \n", "    except Exception as e:\n", "        print(f\"處理過程中發生錯誤: {str(e)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0409\\gemini_em_ba_50-006.j<PERSON>, 資料筆數: 90\n", "\n", "開始比較相同內容的資料...\n", "正確資料集: 90 筆\n", "預測資料集: 90 筆\n", "共同存在的內容: 88 筆\n", "\n", "儲存錯誤預測...\n", "已儲存 Label 錯誤檔案: result\\em_ba_50_L_20250424_031226.json (共 17 筆)\n", "已儲存 Emotion 錯誤檔案: result\\em_ba_50_E_20250424_031226.j<PERSON> (共 59 筆)\n", "\n", "評估結果摘要已儲存至：result\\em_ba_50_evaluation_summary_20250424_031226.json\n", "\n", "=== 最終比對數據 ===\n", "總樣本數: 88\n", "Label 準確率: 0.8068\n", "Emotion 準確率: 0.3295\n", "整體準確率 (Label和Emotion都正確): 0.2955\n", "Label 錯誤數: 17\n", "Emotion 錯誤數: 59\n"]}], "source": ["import os\n", "import json\n", "from datetime import datetime\n", "\n", "def load_json_file(file_path):\n", "    \"\"\"讀取JSON檔案\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as file:\n", "            data = json.load(file)\n", "            print(f\"成功載入檔案: {file_path}, 資料筆數: {len(data)}\")\n", "            return data\n", "    except Exception as e:\n", "        print(f\"讀取檔案 {file_path} 時發生錯誤：{str(e)}\")\n", "        return None\n", "\n", "def create_content_index(data):\n", "    \"\"\"以內容為鍵創建索引\"\"\"\n", "    index = {}\n", "    for item in data:\n", "        # 取得標題和留言內容\n", "        title = item.get(\"標題\", item.get(\"context\", \"\"))\n", "        content = item.get(\"留言內容\", item.get(\"reply\", \"\"))\n", "        \n", "        # 創建唯一鍵\n", "        key = (title, content)\n", "        \n", "        # 儲存項目\n", "        index[key] = item\n", "    \n", "    return index\n", "\n", "def compare_data_sets(correct_data, predict_data):\n", "    \"\"\"比較兩個資料集，只比對標題和留言內容相同的項目\"\"\"\n", "    # 創建以內容為鍵的索引\n", "    correct_index = create_content_index(correct_data)\n", "    predict_index = create_content_index(predict_data)\n", "    \n", "    # 找出兩個資料集中都存在的內容\n", "    common_keys = set(correct_index.keys()) & set(predict_index.keys())\n", "    \n", "    print(f\"正確資料集: {len(correct_index)} 筆\")\n", "    print(f\"預測資料集: {len(predict_index)} 筆\")\n", "    print(f\"共同存在的內容: {len(common_keys)} 筆\")\n", "    \n", "    # 比較結果\n", "    comparisons = []\n", "    label_correct = 0\n", "    emotion_correct = 0\n", "    both_correct = 0\n", "    \n", "    for key in common_keys:\n", "        true_item = correct_index[key]\n", "        pred_item = predict_index[key]\n", "        \n", "        # 提取標籤和情緒\n", "        true_label = true_item.get(\"情感標籤\", true_item.get(\"label\", \"\"))\n", "        true_emotion = true_item.get(\"情緒\", true_item.get(\"emotion\", \"\"))\n", "        pred_label = pred_item.get(\"情感標籤\", pred_item.get(\"label\", \"\"))\n", "        pred_emotion = pred_item.get(\"情緒\", pred_item.get(\"emotion\", \"\"))\n", "        \n", "        # 比較結果\n", "        label_match = true_label == pred_label\n", "        emotion_match = true_emotion == pred_emotion\n", "        \n", "        if label_match:\n", "            label_correct += 1\n", "        if emotion_match:\n", "            emotion_correct += 1\n", "        if label_match and emotion_match:\n", "            both_correct += 1\n", "        \n", "        comparisons.append({\n", "            \"context\": key[0],  # 標題\n", "            \"reply\": key[1],    # 留言內容\n", "            \"true_label\": true_label,\n", "            \"pred_label\": pred_label,\n", "            \"label_match\": label_match,\n", "            \"true_emotion\": true_emotion,\n", "            \"pred_emotion\": pred_emotion,\n", "            \"emotion_match\": emotion_match,\n", "            \"both_match\": label_match and emotion_match\n", "        })\n", "    \n", "    # 計算準確率\n", "    total = len(common_keys)\n", "    accuracy = {\n", "        \"label_accuracy\": label_correct / total if total > 0 else 0,\n", "        \"emotion_accuracy\": emotion_correct / total if total > 0 else 0,\n", "        \"both_accuracy\": both_correct / total if total > 0 else 0,\n", "        \"total_samples\": total,\n", "        \"comparison_results\": comparisons\n", "    }\n", "    \n", "    return accuracy\n", "\n", "def save_incorrect_predictions(comparison_results, model_name):\n", "    \"\"\"儲存錯誤的預測結果\"\"\"\n", "    label_incorrect = []\n", "    emotion_incorrect = []\n", "    \n", "    for result in comparison_results:\n", "        # 標籤錯誤\n", "        if not result[\"label_match\"]:\n", "            label_incorrect.append({\n", "                \"context\": result[\"context\"],\n", "                \"reply\": result[\"reply\"],\n", "                \"true_label\": result[\"true_label\"],\n", "                \"predicted_label\": result[\"pred_label\"]\n", "            })\n", "        \n", "        # 情緒錯誤\n", "        if not result[\"emotion_match\"]:\n", "            emotion_incorrect.append({\n", "                \"context\": result[\"context\"],\n", "                \"reply\": result[\"reply\"],\n", "                \"true_emotion\": result[\"true_emotion\"],\n", "                \"predicted_emotion\": result[\"pred_emotion\"]\n", "            })\n", "    \n", "    # 建立目錄\n", "    result_dir = \"result\"\n", "    os.makedirs(result_dir, exist_ok=True)\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 儲存錯誤檔案\n", "    label_filename = os.path.join(result_dir, f\"{model_name}_L_{timestamp}.json\")\n", "    with open(label_filename, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(label_incorrect, f, ensure_ascii=False, indent=2)\n", "    \n", "    emotion_filename = os.path.join(result_dir, f\"{model_name}_E_{timestamp}.json\")\n", "    with open(emotion_filename, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(emotion_incorrect, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"已儲存 Label 錯誤檔案: {label_filename} (共 {len(label_incorrect)} 筆)\")\n", "    print(f\"已儲存 Emotion 錯誤檔案: {emotion_filename} (共 {len(emotion_incorrect)} 筆)\")\n", "    \n", "    return len(label_incorrect), len(emotion_incorrect)\n", "\n", "def evaluate_model(correct_data_path, model_path, model_name):\n", "    \"\"\"評估模型準確度\"\"\"\n", "    # 載入正確資料\n", "    correct_data = load_json_file(correct_data_path)\n", "    if not correct_data:\n", "        print(\"無法載入正確資料，程序終止\")\n", "        return\n", "    \n", "    # 載入預測資料\n", "    model_data = load_json_file(model_path)\n", "    if not model_data:\n", "        print(\"無法載入預測資料，程序終止\")\n", "        return\n", "    \n", "    # 比較資料集\n", "    print(\"\\n開始比較相同內容的資料...\")\n", "    accuracy = compare_data_sets(correct_data, model_data)\n", "    \n", "    # 儲存錯誤預測\n", "    print(\"\\n儲存錯誤預測...\")\n", "    label_count, emotion_count = save_incorrect_predictions(\n", "        accuracy[\"comparison_results\"], model_name\n", "    )\n", "    \n", "    # 記錄錯誤數量\n", "    accuracy_result = {\n", "        \"label_accuracy\": accuracy[\"label_accuracy\"],\n", "        \"emotion_accuracy\": accuracy[\"emotion_accuracy\"],\n", "        \"both_accuracy\": accuracy[\"both_accuracy\"],\n", "        \"total_samples\": accuracy[\"total_samples\"],\n", "        \"label_incorrect\": label_count,\n", "        \"emotion_incorrect\": emotion_count\n", "    }\n", "    \n", "    # 儲存評估結果摘要\n", "    result_dir = \"result\"\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    summary_file = os.path.join(result_dir, f\"{model_name}_evaluation_summary_{timestamp}.json\")\n", "    \n", "    with open(summary_file, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(accuracy_result, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"\\n評估結果摘要已儲存至：{summary_file}\")\n", "    \n", "    # 顯示最終比對數據\n", "    print(\"\\n=== 最終比對數據 ===\")\n", "    print(f\"總樣本數: {accuracy_result['total_samples']}\")\n", "    print(f\"Label 準確率: {accuracy_result['label_accuracy']:.4f}\")\n", "    print(f\"Emotion 準確率: {accuracy_result['emotion_accuracy']:.4f}\")\n", "    print(f\"整體準確率 (Label和Emotion都正確): {accuracy_result['both_accuracy']:.4f}\")\n", "    print(f\"Label 錯誤數: {label_count}\")\n", "    print(f\"Emotion 錯誤數: {emotion_count}\")\n", "\n", "def main():\n", "    # 設定檔案路徑\n", "    correct_data_path = \"data\\\\correct_data.json\"\n", "    model_path = \"data\\\\0409\\\\gemini_em_ba_50-006.json\"\n", "    model_name = \"em_ba_50\"\n", "    \n", "    # 評估模型\n", "    evaluate_model(correct_data_path, model_path, model_name)\n", "\n", "\n", "main()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "找到 12 個模型檔案待評估\n", "\n", "===== 評估模型: ba_100 =====\n", "成功載入檔案: data\\0424\\ba_100.json, 資料筆數: 89\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\ba_100_missing_in_predict_20250424_031957.json (共 18 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\ba_100_missing_in_correct_20250424_031957.json (共 17 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 89 筆\n", "共同存在的留言: 72 筆\n", "僅存在於正確資料的留言: 18 筆\n", "僅存在於預測資料的留言: 17 筆\n", "\n", "評估結果:\n", "總樣本數: 72\n", "Label準確率: 0.6389\n", "Emotion準確率: 0.3472\n", "整體準確率: 0.2222\n", "Label錯誤數: 26\n", "Emotion錯誤數: 47\n", "模型名稱: ba_100\n", "檔案路徑: data\\0424\\ba_100.json\n", "缺少在預測中的項目數: 18\n", "缺少在正確資料中的項目數: 17\n", "\n", "===== 評估模型: ba_20 =====\n", "成功載入檔案: data\\0424\\ba_20.j<PERSON>, 資料筆數: 81\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\ba_20_missing_in_predict_20250424_031957.json (共 28 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\ba_20_missing_in_correct_20250424_031957.json (共 19 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 81 筆\n", "共同存在的留言: 62 筆\n", "僅存在於正確資料的留言: 28 筆\n", "僅存在於預測資料的留言: 19 筆\n", "\n", "評估結果:\n", "總樣本數: 62\n", "Label準確率: 0.5000\n", "Emotion準確率: 0.3710\n", "整體準確率: 0.1452\n", "Label錯誤數: 31\n", "Emotion錯誤數: 39\n", "模型名稱: ba_20\n", "檔案路徑: data\\0424\\ba_20.json\n", "缺少在預測中的項目數: 28\n", "缺少在正確資料中的項目數: 19\n", "\n", "===== 評估模型: ba_50 =====\n", "成功載入檔案: data\\0424\\ba_50.j<PERSON>, 資料筆數: 73\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\ba_50_missing_in_predict_20250424_031957.j<PERSON> (共 34 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\ba_50_missing_in_correct_20250424_031957.json (共 17 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 73 筆\n", "共同存在的留言: 56 筆\n", "僅存在於正確資料的留言: 34 筆\n", "僅存在於預測資料的留言: 17 筆\n", "\n", "評估結果:\n", "總樣本數: 56\n", "Label準確率: 0.6250\n", "Emotion準確率: 0.4107\n", "整體準確率: 0.2679\n", "Label錯誤數: 21\n", "Emotion錯誤數: 33\n", "模型名稱: ba_50\n", "檔案路徑: data\\0424\\ba_50.json\n", "缺少在預測中的項目數: 34\n", "缺少在正確資料中的項目數: 17\n", "\n", "===== 評估模型: bert =====\n", "成功載入檔案: data\\0424\\bert.json, 資料筆數: 90\n", "\n", "儲存不匹配的項目...\n", "正確資料集: 90 筆\n", "預測資料集: 90 筆\n", "共同存在的留言: 90 筆\n", "僅存在於正確資料的留言: 0 筆\n", "僅存在於預測資料的留言: 0 筆\n", "\n", "評估結果:\n", "總樣本數: 90\n", "Label準確率: 0.5111\n", "Emotion準確率: 0.3667\n", "整體準確率: 0.1889\n", "Label錯誤數: 44\n", "Emotion錯誤數: 57\n", "模型名稱: bert\n", "檔案路徑: data\\0424\\bert.json\n", "缺少在預測中的項目數: 0\n", "缺少在正確資料中的項目數: 0\n", "\n", "===== 評估模型: gemini_2 =====\n", "成功載入檔案: data\\0424\\gemini_2.0-006.j<PERSON>, 資料筆數: 90\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\gemini_2_missing_in_predict_20250424_031957.json (共 2 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\gemini_2_missing_in_correct_20250424_031957.json (共 2 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 90 筆\n", "共同存在的留言: 88 筆\n", "僅存在於正確資料的留言: 2 筆\n", "僅存在於預測資料的留言: 2 筆\n", "\n", "評估結果:\n", "總樣本數: 88\n", "Label準確率: 0.8068\n", "Emotion準確率: 0.3295\n", "整體準確率: 0.2955\n", "Label錯誤數: 17\n", "Emotion錯誤數: 59\n", "模型名稱: gemini_2\n", "檔案路徑: data\\0424\\gemini_2.0-006.json\n", "缺少在預測中的項目數: 2\n", "缺少在正確資料中的項目數: 2\n", "\n", "===== 評估模型: gemini_2 =====\n", "成功載入檔案: data\\0424\\gemini_2.0FLASH.json, 資料筆數: 90\n", "\n", "儲存不匹配的項目...\n", "正確資料集: 90 筆\n", "預測資料集: 90 筆\n", "共同存在的留言: 90 筆\n", "僅存在於正確資料的留言: 0 筆\n", "僅存在於預測資料的留言: 0 筆\n", "\n", "評估結果:\n", "總樣本數: 90\n", "Label準確率: 0.7667\n", "Emotion準確率: 0.3333\n", "整體準確率: 0.2444\n", "Label錯誤數: 21\n", "Emotion錯誤數: 60\n", "模型名稱: gemini_2\n", "檔案路徑: data\\0424\\gemini_2.0FLASH.json\n", "缺少在預測中的項目數: 0\n", "缺少在正確資料中的項目數: 0\n", "\n", "===== 評估模型: le_ba_100 =====\n", "成功載入檔案: data\\0424\\le_ba_100.j<PERSON>, 資料筆數: 89\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\le_ba_100_missing_in_predict_20250424_031957.json (共 13 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\le_ba_100_missing_in_correct_20250424_031957.json (共 12 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 89 筆\n", "共同存在的留言: 77 筆\n", "僅存在於正確資料的留言: 13 筆\n", "僅存在於預測資料的留言: 12 筆\n", "\n", "評估結果:\n", "總樣本數: 77\n", "Label準確率: 0.5844\n", "Emotion準確率: 0.3896\n", "整體準確率: 0.2857\n", "Label錯誤數: 32\n", "Emotion錯誤數: 47\n", "模型名稱: le_ba_100\n", "檔案路徑: data\\0424\\le_ba_100.json\n", "缺少在預測中的項目數: 13\n", "缺少在正確資料中的項目數: 12\n", "\n", "===== 評估模型: le_ba_20 =====\n", "成功載入檔案: data\\0424\\le_ba_20.j<PERSON>, 資料筆數: 58\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\le_ba_20_missing_in_predict_20250424_031957.j<PERSON> (共 46 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\le_ba_20_missing_in_correct_20250424_031957.j<PERSON> (共 14 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 58 筆\n", "共同存在的留言: 44 筆\n", "僅存在於正確資料的留言: 46 筆\n", "僅存在於預測資料的留言: 14 筆\n", "\n", "評估結果:\n", "總樣本數: 44\n", "Label準確率: 0.4773\n", "Emotion準確率: 0.3636\n", "整體準確率: 0.1818\n", "Label錯誤數: 23\n", "Emotion錯誤數: 28\n", "模型名稱: le_ba_20\n", "檔案路徑: data\\0424\\le_ba_20.json\n", "缺少在預測中的項目數: 46\n", "缺少在正確資料中的項目數: 14\n", "\n", "===== 評估模型: le_ba_50 =====\n", "成功載入檔案: data\\0424\\le_ba_50.j<PERSON>, 資料筆數: 80\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\le_ba_50_missing_in_predict_20250424_031957.j<PERSON> (共 19 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\le_ba_50_missing_in_correct_20250424_031957.json (共 9 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 80 筆\n", "共同存在的留言: 71 筆\n", "僅存在於正確資料的留言: 19 筆\n", "僅存在於預測資料的留言: 9 筆\n", "\n", "評估結果:\n", "總樣本數: 71\n", "Label準確率: 0.6338\n", "Emotion準確率: 0.3803\n", "整體準確率: 0.2113\n", "Label錯誤數: 26\n", "Emotion錯誤數: 44\n", "模型名稱: le_ba_50\n", "檔案路徑: data\\0424\\le_ba_50.json\n", "缺少在預測中的項目數: 19\n", "缺少在正確資料中的項目數: 9\n", "\n", "===== 評估模型: new_ba_100 =====\n", "成功載入檔案: data\\0424\\new_ba_100.json, 資料筆數: 90\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\new_ba_100_missing_in_predict_20250424_031957.json (共 11 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\new_ba_100_missing_in_correct_20250424_031957.json (共 11 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 90 筆\n", "共同存在的留言: 79 筆\n", "僅存在於正確資料的留言: 11 筆\n", "僅存在於預測資料的留言: 11 筆\n", "\n", "評估結果:\n", "總樣本數: 79\n", "Label準確率: 0.6076\n", "Emotion準確率: 0.4177\n", "整體準確率: 0.2911\n", "Label錯誤數: 31\n", "Emotion錯誤數: 46\n", "模型名稱: new_ba_100\n", "檔案路徑: data\\0424\\new_ba_100.json\n", "缺少在預測中的項目數: 11\n", "缺少在正確資料中的項目數: 11\n", "\n", "===== 評估模型: new_ba_20 =====\n", "成功載入檔案: data\\0424\\new_ba_20.j<PERSON>, 資料筆數: 89\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\new_ba_20_missing_in_predict_20250424_031957.j<PERSON> (共 25 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\new_ba_20_missing_in_correct_20250424_031957.j<PERSON> (共 24 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 89 筆\n", "共同存在的留言: 65 筆\n", "僅存在於正確資料的留言: 25 筆\n", "僅存在於預測資料的留言: 24 筆\n", "\n", "評估結果:\n", "總樣本數: 65\n", "Label準確率: 0.4769\n", "Emotion準確率: 0.2769\n", "整體準確率: 0.1538\n", "Label錯誤數: 34\n", "Emotion錯誤數: 47\n", "模型名稱: new_ba_20\n", "檔案路徑: data\\0424\\new_ba_20.json\n", "缺少在預測中的項目數: 25\n", "缺少在正確資料中的項目數: 24\n", "\n", "===== 評估模型: new_ba_50 =====\n", "成功載入檔案: data\\0424\\new_ba_50.j<PERSON>, 資料筆數: 90\n", "\n", "儲存不匹配的項目...\n", "已儲存在正確資料但不存在於預測資料的項目: result2\\new_ba_50_missing_in_predict_20250424_031957.j<PERSON> (共 14 筆)\n", "已儲存在預測資料但不存在於正確資料的項目: result2\\new_ba_50_missing_in_correct_20250424_031957.j<PERSON> (共 14 筆)\n", "正確資料集: 90 筆\n", "預測資料集: 90 筆\n", "共同存在的留言: 76 筆\n", "僅存在於正確資料的留言: 14 筆\n", "僅存在於預測資料的留言: 14 筆\n", "\n", "評估結果:\n", "總樣本數: 76\n", "Label準確率: 0.6579\n", "Emotion準確率: 0.3553\n", "整體準確率: 0.2237\n", "Label錯誤數: 26\n", "Emotion錯誤數: 49\n", "模型名稱: new_ba_50\n", "檔案路徑: data\\0424\\new_ba_50.json\n", "缺少在預測中的項目數: 14\n", "缺少在正確資料中的項目數: 14\n", "\n", "\n", "========= 所有模型評估結果比較 =========\n", "| 模型名稱 | 總樣本數 | Label準確率 | Emotion準確率 | 整體準確率 | Label錯誤數 | Emotion錯誤數 | 缺少在預測中 | 缺少在正確資料中 |\n", "| --- | --- | --- | --- | --- | --- | --- | --- | --- |\n", "| new_ba_100 | 79 | 0.6076 | 0.4177 | 0.2911 | 31 | 46 | 11 | 11 |\n", "| ba_50 | 56 | 0.6250 | 0.4107 | 0.2679 | 21 | 33 | 34 | 17 |\n", "| le_ba_100 | 77 | 0.5844 | 0.3896 | 0.2857 | 32 | 47 | 13 | 12 |\n", "| le_ba_50 | 71 | 0.6338 | 0.3803 | 0.2113 | 26 | 44 | 19 | 9 |\n", "| ba_20 | 62 | 0.5000 | 0.3710 | 0.1452 | 31 | 39 | 28 | 19 |\n", "| bert | 90 | 0.5111 | 0.3667 | 0.1889 | 44 | 57 | 0 | 0 |\n", "| le_ba_20 | 44 | 0.4773 | 0.3636 | 0.1818 | 23 | 28 | 46 | 14 |\n", "| new_ba_50 | 76 | 0.6579 | 0.3553 | 0.2237 | 26 | 49 | 14 | 14 |\n", "| ba_100 | 72 | 0.6389 | 0.3472 | 0.2222 | 26 | 47 | 18 | 17 |\n", "| gemini_2 | 90 | 0.7667 | 0.3333 | 0.2444 | 21 | 60 | 0 | 0 |\n", "| gemini_2 | 88 | 0.8068 | 0.3295 | 0.2955 | 17 | 59 | 2 | 2 |\n", "| new_ba_20 | 65 | 0.4769 | 0.2769 | 0.1538 | 34 | 47 | 25 | 24 |\n", "\n", "最佳模型: new_ba_100\n", "情緒準確率: 0.4177\n", "檔案路徑: data\\0424\\new_ba_100.json\n"]}], "source": ["import os\n", "import json\n", "from datetime import datetime\n", "\n", "def load_json_file(file_path):\n", "    \"\"\"讀取JSON檔案\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as file:\n", "            data = json.load(file)\n", "            print(f\"成功載入檔案: {file_path}, 資料筆數: {len(data)}\")\n", "            return data\n", "    except Exception as e:\n", "        print(f\"讀取檔案 {file_path} 時發生錯誤：{str(e)}\")\n", "        return None\n", "\n", "def create_content_index(data):\n", "    \"\"\"僅以留言內容為鍵創建索引\"\"\"\n", "    index = {}\n", "    for item in data:\n", "        # 僅取得留言內容作為鍵\n", "        content = item.get(\"留言內容\", item.get(\"reply\", \"\"))\n", "        \n", "        # 創建唯一鍵 - 只用留言內容\n", "        key = content\n", "        \n", "        # 儲存項目\n", "        index[key] = item\n", "    \n", "    return index\n", "\n", "def save_mismatched_items(correct_data, predict_data, model_name):\n", "    \"\"\"儲存不匹配的項目到result2目錄\"\"\"\n", "    # 創建以留言內容為鍵的索引\n", "    correct_index = create_content_index(correct_data)\n", "    predict_index = create_content_index(predict_data)\n", "    \n", "    # 找出只存在於正確資料但不存在於預測資料的項目\n", "    only_in_correct = set(correct_index.keys()) - set(predict_index.keys())\n", "    # 找出只存在於預測資料但不存在於正確資料的項目\n", "    only_in_predict = set(predict_index.keys()) - set(correct_index.keys())\n", "    \n", "    # 準備資料\n", "    missing_in_predict = [correct_index[key] for key in only_in_correct]\n", "    missing_in_correct = [predict_index[key] for key in only_in_predict]\n", "    \n", "    # 建立目錄\n", "    result_dir = \"result2\"\n", "    os.makedirs(result_dir, exist_ok=True)\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 儲存錯誤檔案\n", "    if missing_in_predict:\n", "        missing_pred_filename = os.path.join(result_dir, f\"{model_name}_missing_in_predict_{timestamp}.json\")\n", "        with open(missing_pred_filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(missing_in_predict, f, ensure_ascii=False, indent=2)\n", "        print(f\"已儲存在正確資料但不存在於預測資料的項目: {missing_pred_filename} (共 {len(missing_in_predict)} 筆)\")\n", "    \n", "    if missing_in_correct:\n", "        missing_correct_filename = os.path.join(result_dir, f\"{model_name}_missing_in_correct_{timestamp}.json\")\n", "        with open(missing_correct_filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(missing_in_correct, f, ensure_ascii=False, indent=2)\n", "        print(f\"已儲存在預測資料但不存在於正確資料的項目: {missing_correct_filename} (共 {len(missing_in_correct)} 筆)\")\n", "    \n", "    return len(missing_in_predict), len(missing_in_correct)\n", "\n", "def evaluate_model_accuracy(correct_data, predict_data):\n", "    \"\"\"評估模型準確度並返回結果\"\"\"\n", "    # 創建以留言內容為鍵的索引\n", "    correct_index = create_content_index(correct_data)\n", "    predict_index = create_content_index(predict_data)\n", "    \n", "    # 找出兩個資料集中都存在的留言內容\n", "    common_keys = set(correct_index.keys()) & set(predict_index.keys())\n", "    \n", "    print(f\"正確資料集: {len(correct_index)} 筆\")\n", "    print(f\"預測資料集: {len(predict_index)} 筆\")\n", "    print(f\"共同存在的留言: {len(common_keys)} 筆\")\n", "    print(f\"僅存在於正確資料的留言: {len(set(correct_index.keys()) - common_keys)} 筆\")\n", "    print(f\"僅存在於預測資料的留言: {len(set(predict_index.keys()) - common_keys)} 筆\")\n", "    \n", "    # 比較結果\n", "    label_correct = 0\n", "    emotion_correct = 0\n", "    both_correct = 0\n", "    \n", "    label_incorrect = []\n", "    emotion_incorrect = []\n", "    \n", "    for key in common_keys:\n", "        true_item = correct_index[key]\n", "        pred_item = predict_index[key]\n", "        \n", "        # 提取標籤和情緒\n", "        true_label = true_item.get(\"情感標籤\", true_item.get(\"label\", \"\"))\n", "        true_emotion = true_item.get(\"情緒\", true_item.get(\"true_emotion\", true_item.get(\"emotion\", \"\")))\n", "        pred_label = pred_item.get(\"情感標籤\", pred_item.get(\"label\", \"\"))\n", "        pred_emotion = pred_item.get(\"情緒\", pred_item.get(\"predicted_emotion\", pred_item.get(\"emotion\", \"\")))\n", "        \n", "        # 提取標題或上下文\n", "        context = true_item.get(\"標題\", true_item.get(\"context\", \"\"))\n", "        \n", "        # 比較結果\n", "        label_match = true_label == pred_label\n", "        emotion_match = true_emotion == pred_emotion\n", "        \n", "        if label_match:\n", "            label_correct += 1\n", "        else:\n", "            label_incorrect.append({\n", "                \"context\": context,\n", "                \"reply\": key,\n", "                \"true_label\": true_label,\n", "                \"predicted_label\": pred_label\n", "            })\n", "            \n", "        if emotion_match:\n", "            emotion_correct += 1\n", "        else:\n", "            emotion_incorrect.append({\n", "                \"context\": context,\n", "                \"reply\": key,\n", "                \"true_emotion\": true_emotion,\n", "                \"predicted_emotion\": pred_emotion\n", "            })\n", "            \n", "        if label_match and emotion_match:\n", "            both_correct += 1\n", "    \n", "    # 計算準確率\n", "    total = len(common_keys)\n", "    if total > 0:\n", "        label_accuracy = label_correct / total\n", "        emotion_accuracy = emotion_correct / total\n", "        both_accuracy = both_correct / total\n", "    else:\n", "        label_accuracy = emotion_accuracy = both_accuracy = 0\n", "    \n", "    return {\n", "        \"總樣本數\": total,\n", "        \"Label準確率\": f\"{label_accuracy:.4f}\",\n", "        \"Emotion準確率\": f\"{emotion_accuracy:.4f}\",\n", "        \"整體準確率\": f\"{both_accuracy:.4f}\",\n", "        \"Label錯誤數\": len(label_incorrect),\n", "        \"Emotion錯誤數\": len(emotion_incorrect),\n", "        \"label_incorrect\": label_incorrect,\n", "        \"emotion_incorrect\": emotion_incorrect\n", "    }\n", "\n", "def evaluate_all_models(correct_data_path, models_dir):\n", "    \"\"\"評估指定目錄中的所有模型檔案\"\"\"\n", "    # 載入正確資料\n", "    correct_data = load_json_file(correct_data_path)\n", "    if not correct_data:\n", "        print(\"無法載入正確資料，程序終止\")\n", "        return\n", "    \n", "    # 獲取資料夾中所有 JSON 檔案\n", "    model_paths = [os.path.join(models_dir, f) for f in os.listdir(models_dir) if f.endswith('.json')]\n", "    \n", "    if not model_paths:\n", "        print(f\"在 {models_dir} 目錄中未找到任何 JSON 檔案\")\n", "        return\n", "    \n", "    print(f\"找到 {len(model_paths)} 個模型檔案待評估\")\n", "    \n", "    results = []\n", "    \n", "    # 評估每個模型\n", "    for model_path in model_paths:\n", "        # 從檔案路徑提取模型全名（保留完整檔名，僅移除副檔名）\n", "        model_name = os.path.basename(model_path).split('.')[0]\n", "        \n", "        print(f\"\\n===== 評估模型: {model_name} =====\")\n", "        \n", "        # 載入預測資料\n", "        model_data = load_json_file(model_path)\n", "        if not model_data:\n", "            print(f\"無法載入模型 {model_name} 的資料，跳過\")\n", "            continue\n", "        \n", "        # 儲存不匹配的項目\n", "        print(\"\\n儲存不匹配的項目...\")\n", "        missing_in_predict, missing_in_correct = save_mismatched_items(correct_data, model_data, model_name)\n", "        \n", "        # 評估準確度\n", "        result = evaluate_model_accuracy(correct_data, model_data)\n", "        \n", "        # 添加模型名稱和檔案路徑\n", "        result[\"模型名稱\"] = model_name\n", "        result[\"檔案路徑\"] = model_path\n", "        result[\"缺少在預測中的項目數\"] = missing_in_predict\n", "        result[\"缺少在正確資料中的項目數\"] = missing_in_correct\n", "        \n", "        # 移除不需要顯示在表格中的大型列表\n", "        result_display = result.copy()\n", "        if \"label_incorrect\" in result_display:\n", "            del result_display[\"label_incorrect\"]\n", "        if \"emotion_incorrect\" in result_display:\n", "            del result_display[\"emotion_incorrect\"]\n", "        \n", "        # 顯示評估結果\n", "        print(\"\\n評估結果:\")\n", "        for key, value in result_display.items():\n", "            print(f\"{key}: {value}\")\n", "        \n", "        # 加入結果列表\n", "        results.append(result)\n", "    \n", "    # 顯示所有模型的比較表格\n", "    print(\"\\n\\n========= 所有模型評估結果比較 =========\")\n", "    \n", "    if not results:\n", "        print(\"沒有可比較的模型結果\")\n", "        return\n", "    \n", "    # 顯示表頭\n", "    headers = [\"模型名稱\", \"總樣本數\", \"Label準確率\", \"Emotion準確率\", \"整體準確率\", \"Label錯誤數\", \"Emotion錯誤數\", \"缺少在預測中\", \"缺少在正確資料中\"]\n", "    print(\"| \" + \" | \".join(headers) + \" |\")\n", "    print(\"| \" + \" | \".join([\"---\" for _ in headers]) + \" |\")\n", "    \n", "    # 按情緒準確率排序結果（降序）\n", "    sorted_results = sorted(results, key=lambda x: float(x[\"Emotion準確率\"]), reverse=True)\n", "    \n", "    # 顯示各模型數據\n", "    for result in sorted_results:\n", "        row = [\n", "            result[\"模型名稱\"], \n", "            str(result[\"總樣本數\"]), \n", "            result[\"Label準確率\"], \n", "            result[\"Emotion準確率\"], \n", "            result[\"整體準確率\"], \n", "            str(result[\"Label錯誤數\"]), \n", "            str(result[\"Emotion錯誤數\"]),\n", "            str(result[\"缺少在預測中的項目數\"]),\n", "            str(result[\"缺少在正確資料中的項目數\"])\n", "        ]\n", "        print(\"| \" + \" | \".join(row) + \" |\")\n", "    \n", "    # 找出最佳模型\n", "    best_model = sorted_results[0]\n", "    print(f\"\\n最佳模型: {best_model['模型名稱']}\")\n", "    print(f\"情緒準確率: {best_model['Emotion準確率']}\")\n", "    print(f\"檔案路徑: {best_model['檔案路徑']}\")\n", "\n", "def main():\n", "    \"\"\"主函數\"\"\"\n", "    # 設定檔案路徑\n", "    correct_data_path = \"data\\\\correct_data.json\"\n", "    \n", "    # 指定包含多個模型檔案的資料夾\n", "    models_dir = \"data\\\\0424\"\n", "    \n", "    # 評估所有模型\n", "    evaluate_all_models(correct_data_path, models_dir)\n", "\n", "# 執行主程式\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["找到 12 個模型檔案待評估\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\ba_100.json, 資料筆數: 89\n", "\n", "===== 開始評估模型: ba_100 =====\n", "精確匹配項目數: 77\n", "未匹配的正確項目數: 13\n", "未匹配的預測項目數: 12\n", "高相似度匹配項目數: 8\n", "總匹配項目數: 85\n", "\n", "===== 近似但未匹配的例子 =====\n", "\n", "例子 1:\n", "正確內容: 遷户口準備中\n", "徐阿花你等著\n", "預測內容: 遷戶口準備中\\n徐阿花你等著\n", "相似度: 0.8800\n", "標準化後(正確): 遷户口準備中徐阿花你等著\n", "標準化後(預測): 遷戶口準備中n徐阿花你等著\n", "已儲存在正確資料但未匹配的項目: result2\\ba_100_missing_in_predict_20250424_132531.json (共 5 筆)\n", "已儲存在預測資料但未匹配的項目: result2\\ba_100_missing_in_correct_20250424_132531.json (共 4 筆)\n", "已儲存情緒預測錯誤的項目: result2\\ba_100_emotion_errors_20250424_132531.json (共 55 筆)\n", "\n", "評估結果:\n", "總樣本數: 85\n", "Label準確率: 0.6471\n", "Emotion準確率: 0.3529\n", "整體準確率: 0.2235\n", "Label錯誤數: 30\n", "Emotion錯誤數: 55\n", "模型名稱: ba_100\n", "檔案路徑: data\\0424\\ba_100.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\ba_20.j<PERSON>, 資料筆數: 81\n", "\n", "===== 開始評估模型: ba_20 =====\n", "精確匹配項目數: 66\n", "未匹配的正確項目數: 24\n", "未匹配的預測項目數: 15\n", "高相似度匹配項目數: 11\n", "總匹配項目數: 77\n", "\n", "===== 近似但未匹配的例子 =====\n", "\n", "例子 1:\n", "正確內容: 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\n", "馬來西亞華人都是穴今\n", "預測內容: 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\n", "馬來西亞華人都是騙吃騙喝的意思。\n", "相似度: 0.8916\n", "標準化後(正確): 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎馬來西亞華人都是穴今\n", "標準化後(預測): 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎馬來西亞華人都是騙吃騙喝的意思\n", "\n", "例子 2:\n", "正確內容: 真不相信李蟾蜍會是一個中立的X X\n", "預測內容: 真不相信李蟾蓉會是一個中立的騙子。\n", "相似度: 0.8125\n", "標準化後(正確): 真不相信李蟾蜍會是一個中立的xx\n", "標準化後(預測): 真不相信李蟾蓉會是一個中立的騙子\n", "已儲存在正確資料但未匹配的項目: result2\\ba_20_missing_in_predict_20250424_132531.json (共 13 筆)\n", "已儲存在預測資料但未匹配的項目: result2\\ba_20_missing_in_correct_20250424_132531.json (共 4 筆)\n", "已儲存情緒預測錯誤的項目: result2\\ba_20_emotion_errors_20250424_132531.json (共 49 筆)\n", "\n", "評估結果:\n", "總樣本數: 77\n", "Label準確率: 0.5584\n", "Emotion準確率: 0.3636\n", "整體準確率: 0.1558\n", "Label錯誤數: 34\n", "Emotion錯誤數: 49\n", "模型名稱: ba_20\n", "檔案路徑: data\\0424\\ba_20.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\ba_50.j<PERSON>, 資料筆數: 73\n", "\n", "===== 開始評估模型: ba_50 =====\n", "精確匹配項目數: 57\n", "未匹配的正確項目數: 33\n", "未匹配的預測項目數: 16\n", "高相似度匹配項目數: 12\n", "總匹配項目數: 69\n", "\n", "===== 近似但未匹配的例子 =====\n", "\n", "例子 1:\n", "正確內容: 行政院可以不接受。让国民党倒阁，解散国会。\n", "国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。\n", "預測內容: 行政院可以不接受。让国民党倒閣，解散国会。\\n国安局应調查傅崐萁去見中共王滬寧，以反國安法，叛國罪起訴他。\n", "相似度: 0.8352\n", "標準化後(正確): 行政院可以不接受让国民党倒阁解散国会国安局应调查傅崐萁去见中共王滬宁以反国安法叛国罪起诉他\n", "標準化後(預測): 行政院可以不接受让国民党倒閣解散国会n国安局应調查傅崐萁去見中共王滬寧以反國安法叛國罪起訴他\n", "\n", "例子 2:\n", "正確內容: 除惡務盡，一戰改變台灣政治生態，台派加油！\n", "預測內容: 除惡務盡，一罷元之政治生態，台派加油！\n", "相似度: 0.7647\n", "標準化後(正確): 除惡務盡一戰改變台灣政治生態台派加油\n", "標準化後(預測): 除惡務盡一罷元之政治生態台派加油\n", "已儲存在正確資料但未匹配的項目: result2\\ba_50_missing_in_predict_20250424_132531.json (共 21 筆)\n", "已儲存在預測資料但未匹配的項目: result2\\ba_50_missing_in_correct_20250424_132531.json (共 4 筆)\n", "已儲存情緒預測錯誤的項目: result2\\ba_50_emotion_errors_20250424_132531.json (共 41 筆)\n", "\n", "評估結果:\n", "總樣本數: 69\n", "Label準確率: 0.6232\n", "Emotion準確率: 0.4058\n", "整體準確率: 0.2609\n", "Label錯誤數: 26\n", "Emotion錯誤數: 41\n", "模型名稱: ba_50\n", "檔案路徑: data\\0424\\ba_50.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\bert.json, 資料筆數: 90\n", "\n", "===== 開始評估模型: bert =====\n", "精確匹配項目數: 90\n", "未匹配的正確項目數: 0\n", "未匹配的預測項目數: 0\n", "高相似度匹配項目數: 0\n", "總匹配項目數: 90\n", "\n", "===== 近似但未匹配的例子 =====\n", "已儲存情緒預測錯誤的項目: result2\\bert_emotion_errors_20250424_132531.j<PERSON> (共 57 筆)\n", "\n", "評估結果:\n", "總樣本數: 90\n", "Label準確率: 0.5111\n", "Emotion準確率: 0.3667\n", "整體準確率: 0.1889\n", "Label錯誤數: 44\n", "Emotion錯誤數: 57\n", "模型名稱: bert\n", "檔案路徑: data\\0424\\bert.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\gemini_006.json, 資料筆數: 90\n", "\n", "===== 開始評估模型: gemini_006 =====\n", "精確匹配項目數: 90\n", "未匹配的正確項目數: 0\n", "未匹配的預測項目數: 0\n", "高相似度匹配項目數: 0\n", "總匹配項目數: 90\n", "\n", "===== 近似但未匹配的例子 =====\n", "已儲存情緒預測錯誤的項目: result2\\gemini_006_emotion_errors_20250424_132531.json (共 60 筆)\n", "\n", "評估結果:\n", "總樣本數: 90\n", "Label準確率: 0.8000\n", "Emotion準確率: 0.3333\n", "整體準確率: 0.2889\n", "Label錯誤數: 18\n", "Emotion錯誤數: 60\n", "模型名稱: gemini_006\n", "檔案路徑: data\\0424\\gemini_006.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\gemini_FLASH.json, 資料筆數: 90\n", "\n", "===== 開始評估模型: gemini_FLASH =====\n", "精確匹配項目數: 90\n", "未匹配的正確項目數: 0\n", "未匹配的預測項目數: 0\n", "高相似度匹配項目數: 0\n", "總匹配項目數: 90\n", "\n", "===== 近似但未匹配的例子 =====\n", "已儲存情緒預測錯誤的項目: result2\\gemini_FLASH_emotion_errors_20250424_132531.json (共 60 筆)\n", "\n", "評估結果:\n", "總樣本數: 90\n", "Label準確率: 0.7667\n", "Emotion準確率: 0.3333\n", "整體準確率: 0.2444\n", "Label錯誤數: 21\n", "Emotion錯誤數: 60\n", "模型名稱: gemini_FLASH\n", "檔案路徑: data\\0424\\gemini_FLASH.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\le_ba_100.j<PERSON>, 資料筆數: 89\n", "\n", "===== 開始評估模型: le_ba_100 =====\n", "精確匹配項目數: 79\n", "未匹配的正確項目數: 11\n", "未匹配的預測項目數: 10\n", "高相似度匹配項目數: 9\n", "總匹配項目數: 88\n", "\n", "===== 近似但未匹配的例子 =====\n", "已儲存在正確資料但未匹配的項目: result2\\le_ba_100_missing_in_predict_20250424_132531.json (共 2 筆)\n", "已儲存在預測資料但未匹配的項目: result2\\le_ba_100_missing_in_correct_20250424_132531.json (共 1 筆)\n", "已儲存情緒預測錯誤的項目: result2\\le_ba_100_emotion_errors_20250424_132531.json (共 54 筆)\n", "\n", "評估結果:\n", "總樣本數: 88\n", "Label準確率: 0.6136\n", "Emotion準確率: 0.3864\n", "整體準確率: 0.2955\n", "Label錯誤數: 34\n", "Emotion錯誤數: 54\n", "模型名稱: le_ba_100\n", "檔案路徑: data\\0424\\le_ba_100.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\le_ba_20.j<PERSON>, 資料筆數: 58\n", "\n", "===== 開始評估模型: le_ba_20 =====\n", "精確匹配項目數: 47\n", "未匹配的正確項目數: 43\n", "未匹配的預測項目數: 11\n", "高相似度匹配項目數: 8\n", "總匹配項目數: 55\n", "\n", "===== 近似但未匹配的例子 =====\n", "\n", "例子 1:\n", "正確內容: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "預測內容: 徐巧騙盧定面說你罷免ㄧ個我罷免你五個，大家來罷免徐巧騙盧定面，讓她再去罷免5個綠委，很滑算\n", "相似度: 0.8684\n", "標準化後(正確): 徐巧芯說你罷免ㄧ個我罷免你五個大家來罷免徐巧芯讓她再去罷免個綠委很滑算\n", "標準化後(預測): 徐巧騙盧定面說你罷免ㄧ個我罷免你五個大家來罷免徐巧騙盧定面讓她再去罷免個綠委很滑算\n", "\n", "例子 2:\n", "正確內容: 元之你是在做復健嗎?\n", "預測內容: 元之你是在做復騙嗎？\n", "相似度: 0.8889\n", "標準化後(正確): 元之你是在做復健嗎\n", "標準化後(預測): 元之你是在做復騙嗎\n", "\n", "例子 3:\n", "正確內容: 加油！罷免藍營立委\n", "預測內容: 加油！罷面藍營立委\n", "相似度: 0.8750\n", "標準化後(正確): 加油罷免藍營立委\n", "標準化後(預測): 加油罷面藍營立委\n", "已儲存在正確資料但未匹配的項目: result2\\le_ba_20_missing_in_predict_20250424_132531.j<PERSON> (共 35 筆)\n", "已儲存在預測資料但未匹配的項目: result2\\le_ba_20_missing_in_correct_20250424_132531.json (共 3 筆)\n", "已儲存情緒預測錯誤的項目: result2\\le_ba_20_emotion_errors_20250424_132531.json (共 35 筆)\n", "\n", "評估結果:\n", "總樣本數: 55\n", "Label準確率: 0.4909\n", "Emotion準確率: 0.3636\n", "整體準確率: 0.1818\n", "Label錯誤數: 28\n", "Emotion錯誤數: 35\n", "模型名稱: le_ba_20\n", "檔案路徑: data\\0424\\le_ba_20.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\le_ba_50.j<PERSON>, 資料筆數: 80\n", "\n", "===== 開始評估模型: le_ba_50 =====\n", "精確匹配項目數: 76\n", "未匹配的正確項目數: 14\n", "未匹配的預測項目數: 4\n", "高相似度匹配項目數: 3\n", "總匹配項目數: 79\n", "\n", "===== 近似但未匹配的例子 =====\n", "\n", "例子 1:\n", "正確內容: 國民黨在恐嚇選民，真的好可怕\n", "預測內容: 國民黨在恐嚇選民，越好可怕\n", "相似度: 0.8800\n", "標準化後(正確): 國民黨在恐嚇選民真的好可怕\n", "標準化後(預測): 國民黨在恐嚇選民越好可怕\n", "已儲存在正確資料但未匹配的項目: result2\\le_ba_50_missing_in_predict_20250424_132531.json (共 11 筆)\n", "已儲存在預測資料但未匹配的項目: result2\\le_ba_50_missing_in_correct_20250424_132531.json (共 1 筆)\n", "已儲存情緒預測錯誤的項目: result2\\le_ba_50_emotion_errors_20250424_132531.json (共 49 筆)\n", "\n", "評估結果:\n", "總樣本數: 79\n", "Label準確率: 0.6329\n", "Emotion準確率: 0.3797\n", "整體準確率: 0.2278\n", "Label錯誤數: 29\n", "Emotion錯誤數: 49\n", "模型名稱: le_ba_50\n", "檔案路徑: data\\0424\\le_ba_50.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\new_ba_100.json, 資料筆數: 90\n", "\n", "===== 開始評估模型: new_ba_100 =====\n", "精確匹配項目數: 80\n", "未匹配的正確項目數: 10\n", "未匹配的預測項目數: 10\n", "高相似度匹配項目數: 8\n", "總匹配項目數: 88\n", "\n", "===== 近似但未匹配的例子 =====\n", "已儲存在正確資料但未匹配的項目: result2\\new_ba_100_missing_in_predict_20250424_132531.json (共 2 筆)\n", "已儲存在預測資料但未匹配的項目: result2\\new_ba_100_missing_in_correct_20250424_132531.json (共 2 筆)\n", "已儲存情緒預測錯誤的項目: result2\\new_ba_100_emotion_errors_20250424_132531.json (共 51 筆)\n", "\n", "評估結果:\n", "總樣本數: 88\n", "Label準確率: 0.6250\n", "Emotion準確率: 0.4205\n", "整體準確率: 0.3068\n", "Label錯誤數: 33\n", "Emotion錯誤數: 51\n", "模型名稱: new_ba_100\n", "檔案路徑: data\\0424\\new_ba_100.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\new_ba_20.j<PERSON>, 資料筆數: 89\n", "\n", "===== 開始評估模型: new_ba_20 =====\n", "精確匹配項目數: 68\n", "未匹配的正確項目數: 22\n", "未匹配的預測項目數: 21\n", "高相似度匹配項目數: 15\n", "總匹配項目數: 83\n", "\n", "===== 近似但未匹配的例子 =====\n", "\n", "例子 1:\n", "正確內容: 行政院可以不接受。让国民党倒阁，解散国会。\n", "国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。\n", "預測內容: 行政院可以不接受。让国民党倒阁，解散国会。\\n國安局應調查傅崐萁去見中共王滬寧，以反國安法，叛國罪起訴他。\n", "相似度: 0.8132\n", "標準化後(正確): 行政院可以不接受让国民党倒阁解散国会国安局应调查傅崐萁去见中共王滬宁以反国安法叛国罪起诉他\n", "標準化後(預測): 行政院可以不接受让国民党倒阁解散国会n國安局應調查傅崐萁去見中共王滬寧以反國安法叛國罪起訴他\n", "\n", "例子 2:\n", "正確內容: 遷户口準備中\n", "徐阿花你等著\n", "預測內容: 遷戶口準備中\\n徐阿花你等著\n", "相似度: 0.8800\n", "標準化後(正確): 遷户口準備中徐阿花你等著\n", "標準化後(預測): 遷戶口準備中n徐阿花你等著\n", "\n", "例子 3:\n", "正確內容: 三立新聞網 SETN.com 还有民众党\n", "預測內容: 三立新聞網 SETN.com 還有民眾黨\n", "相似度: 0.8235\n", "標準化後(正確): 三立新聞網setncom还有民众党\n", "標準化後(預測): 三立新聞網setncom還有民眾黨\n", "已儲存在正確資料但未匹配的項目: result2\\new_ba_20_missing_in_predict_20250424_132531.json (共 7 筆)\n", "已儲存在預測資料但未匹配的項目: result2\\new_ba_20_missing_in_correct_20250424_132531.json (共 6 筆)\n", "已儲存情緒預測錯誤的項目: result2\\new_ba_20_emotion_errors_20250424_132531.json (共 60 筆)\n", "\n", "評估結果:\n", "總樣本數: 83\n", "Label準確率: 0.5301\n", "Emotion準確率: 0.2771\n", "整體準確率: 0.1446\n", "Label錯誤數: 39\n", "Emotion錯誤數: 60\n", "模型名稱: new_ba_20\n", "檔案路徑: data\\0424\\new_ba_20.json\n", "成功載入檔案: data\\correct_data.json, 資料筆數: 90\n", "成功載入檔案: data\\0424\\new_ba_50.j<PERSON>, 資料筆數: 90\n", "\n", "===== 開始評估模型: new_ba_50 =====\n", "精確匹配項目數: 79\n", "未匹配的正確項目數: 11\n", "未匹配的預測項目數: 11\n", "高相似度匹配項目數: 8\n", "總匹配項目數: 87\n", "\n", "===== 近似但未匹配的例子 =====\n", "\n", "例子 1:\n", "正確內容: 遷户口準備中\n", "徐阿花你等著\n", "預測內容: 遷戶口準備中\\n徐阿花你等著\n", "相似度: 0.8800\n", "標準化後(正確): 遷户口準備中徐阿花你等著\n", "標準化後(預測): 遷戶口準備中n徐阿花你等著\n", "已儲存在正確資料但未匹配的項目: result2\\new_ba_50_missing_in_predict_20250424_132531.json (共 3 筆)\n", "已儲存在預測資料但未匹配的項目: result2\\new_ba_50_missing_in_correct_20250424_132531.json (共 3 筆)\n", "已儲存情緒預測錯誤的項目: result2\\new_ba_50_emotion_errors_20250424_132531.json (共 57 筆)\n", "\n", "評估結果:\n", "總樣本數: 87\n", "Label準確率: 0.6782\n", "Emotion準確率: 0.3448\n", "整體準確率: 0.2184\n", "Label錯誤數: 28\n", "Emotion錯誤數: 57\n", "模型名稱: new_ba_50\n", "檔案路徑: data\\0424\\new_ba_50.json\n", "========== 所有模型評估結果比較 ==========\n", "| 模型名稱                 | 總樣本數   | Label準確率   | Emotion準確率 | 整體準確率      | Label錯誤數 | Emotion錯誤數 |\n", "|----------------------|--------|------------|------------|------------|----------|----------|\n", "| new_ba_100           | 88     | 0.6250     | 0.4205     | 0.3068     | 33       | 51       |\n", "| ba_50                | 69     | 0.6232     | 0.4058     | 0.2609     | 26       | 41       |\n", "| le_ba_100            | 88     | 0.6136     | 0.3864     | 0.2955     | 34       | 54       |\n", "| le_ba_50             | 79     | 0.6329     | 0.3797     | 0.2278     | 29       | 49       |\n", "| bert                 | 90     | 0.5111     | 0.3667     | 0.1889     | 44       | 57       |\n", "| ba_20                | 77     | 0.5584     | 0.3636     | 0.1558     | 34       | 49       |\n", "| le_ba_20             | 55     | 0.4909     | 0.3636     | 0.1818     | 28       | 35       |\n", "| ba_100               | 85     | 0.6471     | 0.3529     | 0.2235     | 30       | 55       |\n", "| new_ba_50            | 87     | 0.6782     | 0.3448     | 0.2184     | 28       | 57       |\n", "| gemini_006           | 90     | 0.8000     | 0.3333     | 0.2889     | 18       | 60       |\n", "| gemini_FLASH         | 90     | 0.7667     | 0.3333     | 0.2444     | 21       | 60       |\n", "| new_ba_20            | 83     | 0.5301     | 0.2771     | 0.1446     | 39       | 60       |\n", "|----------------------|--------|------------|------------|------------|----------|----------|\n", "\n", "最佳模型: new_ba_100\n", "情緒準確率: 0.4205\n", "檔案路徑: data\\0424\\new_ba_100.json\n"]}], "source": ["import os\n", "import json\n", "import re\n", "from datetime import datetime\n", "from difflib import SequenceMatcher\n", "\n", "def load_json_file(file_path):\n", "    \"\"\"讀取JSON檔案\"\"\"\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as file:\n", "            data = json.load(file)\n", "            print(f\"成功載入檔案: {file_path}, 資料筆數: {len(data)}\")\n", "            return data\n", "    except Exception as e:\n", "        print(f\"讀取檔案 {file_path} 時發生錯誤：{str(e)}\")\n", "        return None\n", "\n", "def deep_normalize_text(text):\n", "    \"\"\"全面標準化文字，處理各種可能的差異\"\"\"\n", "    if not text:\n", "        return \"\"\n", "    \n", "    # 轉換為字串類型\n", "    text = str(text)\n", "    \n", "    # 統一換行符號\n", "    text = text.replace('\\r\\n', '\\n').replace('\\r', '\\n')\n", "    \n", "    # 轉換全形字符到半形\n", "    full_width_chars = \"，。：；？！（）【】《》\"\"''　\"\n", "    half_width_chars = \",.::?!()[]<>\\\"\\\"''\\\"\"\n", "    for i in range(len(full_width_chars)):\n", "        text = text.replace(full_width_chars[i], half_width_chars[min(i, len(half_width_chars)-1)])\n", "    \n", "    # 移除URL、標籤、表情符號等\n", "    text = re.sub(r'https?://\\S+', '', text)\n", "    text = re.sub(r'#\\w+', '', text)\n", "    text = re.sub(r'@\\w+', '', text)\n", "    \n", "    # 移除所有空白字元（空格、換行、tab等）\n", "    text = re.sub(r'\\s+', '', text)\n", "    \n", "    # 移除標點符號和特殊字元\n", "    text = re.sub(r'[^\\w\\s\\u4e00-\\u9fff]', '', text)\n", "    \n", "    # 移除數字\n", "    text = re.sub(r'\\d+', '', text)\n", "    \n", "    # 轉為小寫\n", "    text = text.lower()\n", "    \n", "    return text\n", "\n", "def create_similarity_index(data):\n", "    \"\"\"創建以深度標準化處理後的留言內容為鍵的索引，並保存原始數據\"\"\"\n", "    index = {}\n", "    normalized_to_original = {}\n", "    original_to_normalized = {}\n", "    \n", "    for item in data:\n", "        # 取得留言內容\n", "        content = item.get(\"留言內容\", item.get(\"reply\", \"\"))\n", "        if not content:\n", "            continue\n", "            \n", "        # 深度標準化處理\n", "        normalized = deep_normalize_text(content)\n", "        if not normalized:\n", "            continue\n", "            \n", "        # 儲存映射關係\n", "        if normalized not in normalized_to_original:\n", "            normalized_to_original[normalized] = []\n", "        normalized_to_original[normalized].append(content)\n", "        original_to_normalized[content] = normalized\n", "        \n", "        # 儲存項目\n", "        if normalized not in index:\n", "            index[normalized] = []\n", "        index[normalized].append(item)\n", "    \n", "    return index, normalized_to_original, original_to_normalized\n", "\n", "def calculate_similarity(text1, text2):\n", "    \"\"\"計算兩個文本的相似度\"\"\"\n", "    return SequenceMatcher(None, text1, text2).ratio()\n", "\n", "def compare_and_match(correct_data, predict_data, high_similarity_threshold=0.9):\n", "    \"\"\"比較兩個數據集，嘗試匹配相似項目\"\"\"\n", "    # 創建標準化索引\n", "    correct_index, correct_norm_to_orig, correct_orig_to_norm = create_similarity_index(correct_data)\n", "    predict_index, predict_norm_to_orig, predict_orig_to_norm = create_similarity_index(predict_data)\n", "    \n", "    # 找出精確匹配的項目\n", "    exact_matches = set(correct_index.keys()) & set(predict_index.keys())\n", "    print(f\"精確匹配項目數: {len(exact_matches)}\")\n", "    \n", "    # 初始化匹配結果\n", "    matched_correct = set()\n", "    matched_predict = set()\n", "    matches = []  # 存儲匹配對\n", "    \n", "    # 處理精確匹配的項目\n", "    for key in exact_matches:\n", "        for correct_item in correct_index[key]:\n", "            correct_content = correct_item.get(\"留言內容\", correct_item.get(\"reply\", \"\"))\n", "            \n", "            for predict_item in predict_index[key]:\n", "                predict_content = predict_item.get(\"留言內容\", predict_item.get(\"reply\", \"\"))\n", "                \n", "                # 記錄匹配\n", "                matched_correct.add(correct_content)\n", "                matched_predict.add(predict_content)\n", "                matches.append((correct_item, predict_item))\n", "    \n", "    # 嘗試找出高相似度但未精確匹配的項目\n", "    unmatched_correct = [item for item in correct_data if item.get(\"留言內容\", item.get(\"reply\", \"\")) not in matched_correct]\n", "    unmatched_predict = [item for item in predict_data if item.get(\"留言內容\", item.get(\"reply\", \"\")) not in matched_predict]\n", "    \n", "    print(f\"未匹配的正確項目數: {len(unmatched_correct)}\")\n", "    print(f\"未匹配的預測項目數: {len(unmatched_predict)}\")\n", "    \n", "    # 尋找高相似度匹配\n", "    high_similarity_matches = 0\n", "    similarity_miss_examples = []\n", "    \n", "    for correct_item in unmatched_correct[:]:\n", "        correct_content = correct_item.get(\"留言內容\", correct_item.get(\"reply\", \"\"))\n", "        if not correct_content:\n", "            continue\n", "            \n", "        normalized_correct = deep_normalize_text(correct_content)\n", "        if not normalized_correct:\n", "            continue\n", "            \n", "        best_match = None\n", "        highest_similarity = 0\n", "        \n", "        for predict_item in unmatched_predict[:]:\n", "            predict_content = predict_item.get(\"留言內容\", predict_item.get(\"reply\", \"\"))\n", "            if not predict_content:\n", "                continue\n", "                \n", "            normalized_predict = deep_normalize_text(predict_content)\n", "            if not normalized_predict:\n", "                continue\n", "                \n", "            # 計算相似度\n", "            similarity = calculate_similarity(normalized_correct, normalized_predict)\n", "            \n", "            if similarity > highest_similarity:\n", "                highest_similarity = similarity\n", "                best_match = (predict_item, predict_content, similarity)\n", "        \n", "        # 如果找到高相似度匹配\n", "        if best_match and highest_similarity >= high_similarity_threshold:\n", "            predict_item, predict_content, similarity = best_match\n", "            \n", "            # 記錄匹配\n", "            matched_correct.add(correct_content)\n", "            matched_predict.add(predict_content)\n", "            matches.append((correct_item, predict_item))\n", "            \n", "            # 從未匹配列表中移除\n", "            unmatched_correct.remove(correct_item)\n", "            if predict_item in unmatched_predict:\n", "                unmatched_predict.remove(predict_item)\n", "                \n", "            high_similarity_matches += 1\n", "        \n", "        # 保存一些接近但未達閾值的例子，用於調試\n", "        elif best_match and highest_similarity >= 0.7:\n", "            predict_item, predict_content, similarity = best_match\n", "            similarity_miss_examples.append({\n", "                \"correct\": correct_content,\n", "                \"predict\": predict_content,\n", "                \"similarity\": similarity,\n", "                \"normalized_correct\": normalized_correct,\n", "                \"normalized_predict\": deep_normalize_text(predict_content)\n", "            })\n", "    \n", "    print(f\"高相似度匹配項目數: {high_similarity_matches}\")\n", "    print(f\"總匹配項目數: {len(matches)}\")\n", "    \n", "    # 顯示一些接近但未達閾值的例子\n", "    print(\"\\n===== 近似但未匹配的例子 =====\")\n", "    for i, example in enumerate(similarity_miss_examples[:3]):\n", "        print(f\"\\n例子 {i+1}:\")\n", "        print(f\"正確內容: {example['correct']}\")\n", "        print(f\"預測內容: {example['predict']}\")\n", "        print(f\"相似度: {example['similarity']:.4f}\")\n", "        print(f\"標準化後(正確): {example['normalized_correct']}\")\n", "        print(f\"標準化後(預測): {example['normalized_predict']}\")\n", "    \n", "    return matches, unmatched_correct, unmatched_predict\n", "\n", "def save_unmatched_items(unmatched_correct, unmatched_predict, model_name):\n", "    \"\"\"儲存未匹配的項目\"\"\"\n", "    # 建立目錄\n", "    result_dir = \"result2\"\n", "    os.makedirs(result_dir, exist_ok=True)\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 儲存未匹配的正確項目\n", "    if unmatched_correct:\n", "        filename = os.path.join(result_dir, f\"{model_name}_missing_in_predict_{timestamp}.json\")\n", "        with open(filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(unmatched_correct, f, ensure_ascii=False, indent=2)\n", "        print(f\"已儲存在正確資料但未匹配的項目: {filename} (共 {len(unmatched_correct)} 筆)\")\n", "    \n", "    # 儲存未匹配的預測項目\n", "    if unmatched_predict:\n", "        filename = os.path.join(result_dir, f\"{model_name}_missing_in_correct_{timestamp}.json\")\n", "        with open(filename, \"w\", encoding=\"utf-8\") as f:\n", "            json.dump(unmatched_predict, f, ensure_ascii=False, indent=2)\n", "        print(f\"已儲存在預測資料但未匹配的項目: {filename} (共 {len(unmatched_predict)} 筆)\")\n", "    \n", "    return len(unmatched_correct), len(unmatched_predict)\n", "\n", "def evaluate_matching_accuracy(matches):\n", "    \"\"\"評估匹配項目的準確度\"\"\"\n", "    # 初始化計數器\n", "    emotion_correct = 0\n", "    label_correct = 0\n", "    both_correct = 0\n", "    \n", "    emotion_errors = []\n", "    \n", "    # 評估每個匹配對\n", "    for correct_item, predict_item in matches:\n", "        # 提取標籤和情緒\n", "        true_label = correct_item.get(\"情感標籤\", correct_item.get(\"label\", \"\"))\n", "        true_emotion = correct_item.get(\"情緒\", correct_item.get(\"true_emotion\", correct_item.get(\"emotion\", \"\")))\n", "        pred_label = predict_item.get(\"情感標籤\", predict_item.get(\"label\", \"\"))\n", "        pred_emotion = predict_item.get(\"情緒\", predict_item.get(\"predicted_emotion\", predict_item.get(\"emotion\", \"\")))\n", "        \n", "        # 提取內容\n", "        context = correct_item.get(\"標題\", correct_item.get(\"context\", \"\"))\n", "        reply = correct_item.get(\"留言內容\", correct_item.get(\"reply\", \"\"))\n", "        \n", "        # 比較結果\n", "        label_match = true_label == pred_label\n", "        emotion_match = true_emotion == pred_emotion\n", "        \n", "        if label_match:\n", "            label_correct += 1\n", "        \n", "        if emotion_match:\n", "            emotion_correct += 1\n", "        else:\n", "            emotion_errors.append({\n", "                \"context\": context,\n", "                \"reply\": reply,\n", "                \"true_emotion\": true_emotion,\n", "                \"predicted_emotion\": pred_emotion\n", "            })\n", "        \n", "        if label_match and emotion_match:\n", "            both_correct += 1\n", "    \n", "    # 計算準確率\n", "    total = len(matches)\n", "    if total > 0:\n", "        label_accuracy = label_correct / total\n", "        emotion_accuracy = emotion_correct / total\n", "        both_accuracy = both_correct / total\n", "    else:\n", "        label_accuracy = emotion_accuracy = both_accuracy = 0\n", "    \n", "    return {\n", "        \"總樣本數\": total,\n", "        \"Label準確率\": f\"{label_accuracy:.4f}\",\n", "        \"Emotion準確率\": f\"{emotion_accuracy:.4f}\",\n", "        \"整體準確率\": f\"{both_accuracy:.4f}\",\n", "        \"Label錯誤數\": total - label_correct,\n", "        \"Emotion錯誤數\": total - emotion_correct,\n", "        \"emotion_errors\": emotion_errors\n", "    }\n", "\n", "def save_emotion_errors(emotion_errors, model_name):\n", "    \"\"\"儲存情緒預測錯誤的項目\"\"\"\n", "    if not emotion_errors:\n", "        return\n", "        \n", "    result_dir = \"result2\"\n", "    os.makedirs(result_dir, exist_ok=True)\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    filename = os.path.join(result_dir, f\"{model_name}_emotion_errors_{timestamp}.json\")\n", "    \n", "    with open(filename, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(emotion_errors, f, ensure_ascii=False, indent=2)\n", "        \n", "    print(f\"已儲存情緒預測錯誤的項目: {filename} (共 {len(emotion_errors)} 筆)\")\n", "\n", "def evaluate_model(correct_data_path, model_path, model_name):\n", "    \"\"\"評估單一模型\"\"\"\n", "    # 載入資料\n", "    correct_data = load_json_file(correct_data_path)\n", "    predict_data = load_json_file(model_path)\n", "    \n", "    if not correct_data or not predict_data:\n", "        print(\"載入資料失敗，無法評估模型\")\n", "        return None\n", "    \n", "    print(f\"\\n===== 開始評估模型: {model_name} =====\")\n", "    \n", "    # 比較和匹配數據\n", "    matches, unmatched_correct, unmatched_predict = compare_and_match(correct_data, predict_data)\n", "    \n", "    # 儲存未匹配項目\n", "    missing_in_predict, missing_in_correct = save_unmatched_items(unmatched_correct, unmatched_predict, model_name)\n", "    \n", "    # 評估匹配項目的準確度\n", "    result = evaluate_matching_accuracy(matches)\n", "    \n", "    # 儲存情緒預測錯誤的項目\n", "    save_emotion_errors(result.get(\"emotion_errors\", []), model_name)\n", "    \n", "    # 移除不需要顯示的大型列表\n", "    if \"emotion_errors\" in result:\n", "        del result[\"emotion_errors\"]\n", "    \n", "    # 添加模型信息\n", "    result[\"模型名稱\"] = model_name\n", "    result[\"檔案路徑\"] = model_path\n", "    result[\"缺少在預測中的項目數\"] = missing_in_predict\n", "    result[\"缺少在正確資料中的項目數\"] = missing_in_correct\n", "    \n", "    # 顯示評估結果\n", "    print(\"\\n評估結果:\")\n", "    for key, value in result.items():\n", "        if key not in [\"缺少在預測中的項目數\", \"缺少在正確資料中的項目數\"]:\n", "            print(f\"{key}: {value}\")\n", "    \n", "    return result\n", "\n", "def print_formatted_results(results):\n", "    \"\"\"打印格式化的評估結果表格\"\"\"\n", "    if not results:\n", "        print(\"沒有可顯示的評估結果\")\n", "        return\n", "        \n", "    # 排序結果（按情緒準確率降序）\n", "    sorted_results = sorted(results, key=lambda x: float(x[\"Emotion準確率\"]), reverse=True)\n", "    \n", "    # 欄位標題和寬度 - 移除不需要的欄位\n", "    columns = {\n", "        \"模型名稱\": 20,  # 增加寬度以確保顯示完整的模型名稱\n", "        \"總樣本數\": 6,\n", "        \"Label準確率\": 10,\n", "        \"Emotion準確率\": 10,\n", "        \"整體準確率\": 10,\n", "        \"Label錯誤數\": 8,\n", "        \"Emotion錯誤數\": 8\n", "    }\n", "    \n", "    # 表頭\n", "    print(\"========== 所有模型評估結果比較 ==========\")\n", "    header = \"| \" + \" | \".join([f\"{col:<{width}}\" for col, width in columns.items()]) + \" |\"\n", "    print(header)\n", "    \n", "    # 分隔線\n", "    separator = \"|-\" + \"-|-\".join([\"-\" * width for width in columns.values()]) + \"-|\"\n", "    print(separator)\n", "    \n", "    # 資料列\n", "    for result in sorted_results:\n", "        row_data = [\n", "            result[\"模型名稱\"],  # 使用完整模型名稱\n", "            str(result[\"總樣本數\"]),\n", "            result[\"Label準確率\"],\n", "            result[\"Emotion準確率\"],\n", "            result[\"整體準確率\"],\n", "            str(result[\"Label錯誤數\"]),\n", "            str(result[\"Emotion錯誤數\"])\n", "        ]\n", "        \n", "        row = \"| \" + \" | \".join([f\"{data:<{width}}\" for data, (col, width) in zip(row_data, columns.items())]) + \" |\"\n", "        print(row)\n", "    \n", "    # 結尾分隔線\n", "    print(separator)\n", "\n", "def evaluate_all_models(correct_data_path, models_dir):\n", "    \"\"\"評估指定目錄中的所有模型檔案\"\"\"\n", "    # 獲取資料夾中所有 JSON 檔案\n", "    model_paths = [os.path.join(models_dir, f) for f in os.listdir(models_dir) if f.endswith('.json')]\n", "    \n", "    if not model_paths:\n", "        print(f\"在 {models_dir} 目錄中未找到任何 JSON 檔案\")\n", "        return\n", "    \n", "    print(f\"找到 {len(model_paths)} 個模型檔案待評估\")\n", "    \n", "    results = []\n", "    \n", "    # 評估每個模型\n", "    for model_path in model_paths:\n", "        # 從檔案路徑提取模型完整名稱，保留原始檔案名稱\n", "        model_name = os.path.basename(model_path).split('.')[0]\n", "        \n", "        # 評估模型\n", "        result = evaluate_model(correct_data_path, model_path, model_name)\n", "        \n", "        if result:\n", "            results.append(result)\n", "    \n", "    # 顯示格式化的結果表格\n", "    print_formatted_results(results)\n", "    \n", "    # 找出最佳模型\n", "    if results:\n", "        best_model = max(results, key=lambda x: float(x[\"Emotion準確率\"]))\n", "        print(f\"\\n最佳模型: {best_model['模型名稱']}\")\n", "        print(f\"情緒準確率: {best_model['Emotion準確率']}\")\n", "        print(f\"檔案路徑: {best_model['檔案路徑']}\")\n", "\n", "def main():\n", "    \"\"\"主函數\"\"\"\n", "    # 設定檔案路徑\n", "    correct_data_path = \"data\\\\correct_data.json\"\n", "    \n", "    # 指定包含多個模型檔案的資料夾\n", "    models_dir = \"data\\\\0424\"\n", "    \n", "    # 評估所有模型\n", "    evaluate_all_models(correct_data_path, models_dir)\n", "\n", "# 執行主程式\n", "if __name__ == \"__main__\":\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}