#!/usr/bin/env python3
"""
社交媒體資料分析主程式

完整流程：
1. 爬蟲資料收集 (YT, PTT, Threads) - 預設不包含 Facebook
2. 資料合併整理 (all_data)
3. 用戶資料統計 (USER)
4. Gemini 情感分析 (final_data)
5. MongoDB 存儲與更新
6. 立委統計資料生成

常用指令範例：

基本用法：
    python main.py                          # 預設抓取一年資料，所有平台（除了FB）
    python main.py --days 1                 # 抓取一天資料
    python main.py --days 30                # 抓取一個月資料
    python main.py --days 7                 # 抓取一週資料

日期範圍：
    python main.py --date-range 2024-01-01 2024-12-31    # 指定完整年度
    python main.py --date-range 2024-06-01 2024-06-30    # 指定月份
    python main.py --date-range 2024-07-01 2024-07-03    # 指定幾天

選擇特定平台：
    python main.py --platforms youtube ptt              # 只爬 YouTube 和 PTT
    python main.py --platforms youtube                  # 只爬 YouTube
    python main.py --platforms ptt threads             # 只爬 PTT 和 Threads
    python main.py --platforms facebook                # 只爬 Facebook（需手動啟用）

選擇特定立委：
    python main.py --legislators 高虹安                 # 只處理高虹安的資料
    python main.py --legislators 高虹安 柯文哲          # 處理多個立委
    python main.py --legislators 高虹安 --days 30      # 處理特定立委的30天資料

跳過特定步驟：
    python main.py --skip-crawler                       # 跳過爬蟲，處理現有資料
    python main.py --skip-analysis                      # 跳過 Gemini 分析
    python main.py --skip-crawler --skip-analysis       # 只做資料整理和 MongoDB 更新

強制重新處理：
    python main.py --force-reprocess                    # 強制重新處理所有資料
    python main.py --force-reprocess --legislators 高虹安  # 強制重新處理特定立委

效能調整：
    python main.py --batch-size 10                      # Gemini 分析批次大小 (預設: 5)
    python main.py --batch-size 1 --legislators 高虹安    # 單一立委小批次處理

備份和報告：
    python main.py --enable-backup                      # 啟用 MongoDB 備份
    python main.py --save-report                        # 儲存詳細執行報告
    python main.py --enable-backup --save-report        # 同時啟用備份和報告

安靜模式：
    python main.py --quiet                              # 減少輸出訊息
    python main.py --quiet --days 1                     # 安靜模式處理一天資料

驗證模式：
    python main.py --validate-only                      # 只檢查設定，不執行流程

組合用法（實用範例）：
    # 重新分析特定立委的最近資料
    python main.py --days 7 --legislators 高虹安 --platforms youtube ptt --save-report

    # 重新處理一月份的現有資料（跳過爬蟲階段）
    python main.py --date-range 2024-01-01 2024-01-31 --skip-crawler --force-reprocess

    # 每日例行更新（啟用備份，安靜模式）
    python main.py --days 1 --enable-backup --quiet

    # 測試 Facebook 爬蟲（需手動在 config.py 啟用）
    python main.py --days 1 --platforms facebook --legislators 高虹安

    # 只做 Gemini 分析，不爬蟲
    python main.py --skip-crawler --legislators 高虹安 --batch-size 10

    # 強制重新處理所有立委的近一個月資料
    python main.py --days 30 --force-reprocess --enable-backup

每日維護建議：
    python main.py --days 1 --quiet                     # 每日例行更新
    python main.py --days 7 --enable-backup --quiet     # 每週備份更新
    python main.py --validate-only                      # 定期檢查系統狀態

流水線處理（推薦）：
    python main.py --days 400 --pipeline-mode optimized --enable-backup    # 全立委400天資料流水線
    python main.py --days 1 --pipeline-mode optimized --quiet   --legislators 高虹安     # 全立委每日資料流水線（適合定時任務）

疑難排解：
    python main.py --validate-only                      # 檢查系統設定
    python main.py --days 1 --legislators 高虹安        # 測試單一立委
    python main.py --skip-crawler --skip-analysis       # 只測試資料整理功能
    python main.py --quiet --legislators 高虹安 --days 1 --platforms youtube  # 最小測試

平台說明：
    - youtube: YouTube 留言（穩定）
    - ptt: PTT 討論（穩定）
    - threads: Meta Threads（穩定）
    - facebook: Facebook 貼文（預設關閉，需手動啟用）

注意事項：
    1. 預設不包含 Facebook 爬蟲，如需使用請修改 config.py
    2. Gemini API 有頻率限制，建議使用適當的 batch-size
    3. 大量資料處理建議啟用 --enable-backup
    4. 每日例行更新建議使用 --quiet 模式
    5. 測試新功能建議先用單一立委和短時間範圍
    
    '黃仁', '黃國書', '黃建豪', '楊瓊瓔', '萬美玲',
    '葉元之', '廖先翔', '廖偉翔', '劉建國', '歐陽立委', '蔡培慧', '蔡適應', '賴士葆',
    '賴惠員', '鄭正鈐', '魯明哲', '顏寬恒', '羅廷瑋', '羅明才', '羅智強', '謝衣鳳',
    '高虹安'


    清理進程taskkill /f /im chrome.exe /t 2>nul & taskkill /f /im chromedriver.exe /t 2>nul & echo "Chrome進程清理完成"
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timedelta
from typing import List, Optional

# 設置路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
crawler_dir = os.path.join(current_dir, 'crawler')
sys.path.append(current_dir)
sys.path.append(crawler_dir)

# 導入並行處理相關模組
import threading
from queue import Queue, Empty
from concurrent.futures import ThreadPoolExecutor, as_completed

# 導入我們的核心模組
from crawler.multi_platform_crawler import MultiPlatformCrawler
from crawler.user_data_processor import process_legislators_data
from crawler.gemini_emo_user import analyze_legislators_emotions, analyze_legislators_emotions_incremental
from crawler.data_to_mongo_v2 import DataToMongo
from crawler.integrated_crawler_manager import IntegratedCrawlerManager

# 立委列表
ALL_LEGISLATORS = [
    '丁學忠', '牛煦庭', '王鴻薇', '江啟臣', '呂玉玲', '李彥秀', '林沛祥', '林思銘',
    '林德福', '邱若華', '邱鎮軍', '洪孟楷', '徐巧芯', '徐欣瑩', '馬文君', '張其祿',
    '張嘉郡', '梁文傑', '莊瑞雄', '陳玉珍', '陳建仁', '陳昭姿', '陳椒華', '陳雪生',
    '陳學聖', '曾銘宗', '游毓蘭'
]

# 預設平台列表（不包含 Facebook）
DEFAULT_PLATFORMS = ['youtube', 'ptt', 'threads']

def setup_logging():
    """設置日誌系統"""
    logs_dir = os.path.join(current_dir, 'logs')
    os.makedirs(logs_dir, exist_ok=True)
    
    log_filename = datetime.now().strftime("main_%Y%m%d_%H%M%S.log")
    log_filepath = os.path.join(logs_dir, log_filename)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filepath, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def validate_setup():
    """驗證系統設定"""
    logger = logging.getLogger(__name__)
    logger.info("驗證系統設定...")
    
    # 檢查必要目錄
    required_dirs = [
        os.path.join(current_dir, 'logs'),
        os.path.join(current_dir, 'crawler', 'data'),
        os.path.join(current_dir, 'crawler', 'href'),
        os.path.join(current_dir, 'crawler', 'processed'),
        os.path.join(current_dir, 'crawler', 'backups')
    ]
    
    for dir_path in required_dirs:
        os.makedirs(dir_path, exist_ok=True)
        if not os.path.exists(dir_path):
            logger.error(f"無法創建目錄: {dir_path}")
            return False
    
    # 檢查爬蟲模組
    try:
        from crawler import multi_platform_crawler, user_data_processor, gemini_emo_user, data_to_mongo_v2
        logger.info("所有必要模組導入成功")
    except ImportError as e:
        logger.error(f"模組導入失敗: {e}")
        return False
    
    logger.info("設定驗證通過")
    return True

def parse_arguments():
    """解析命令列參數"""
    parser = argparse.ArgumentParser(
        description='社交媒體資料分析主程式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例用法：
  python main.py --days 30                    # 抓取最近30天資料
  python main.py --platforms youtube ptt      # 只爬取特定平台
  python main.py --legislators 高虹安         # 只處理特定立委
  python main.py --skip-crawler              # 跳過爬蟲，處理現有資料
  python main.py --validate-only             # 只驗證設定
        """
    )
    
    # 基本參數
    parser.add_argument('--days', type=int, default=365,
                        help='抓取天數 (預設: 365)')
    parser.add_argument('--date-range', nargs=2, metavar=('START', 'END'),
                        help='指定日期範圍 (格式: YYYY-MM-DD)')
    
    # 平台選擇
    parser.add_argument('--platforms', type=str,
                        default='youtube,ptt,threads',
                        help='選擇爬蟲平台，用逗號分隔 (預設: youtube,ptt,threads)')
    
    # 立委選擇
    parser.add_argument('--legislators', nargs='+',
                        help='指定要處理的立委 (預設: 所有立委)')
    
    # 流程控制
    parser.add_argument('--skip-crawler', action='store_true',
                        help='跳過爬蟲階段')
    parser.add_argument('--skip-analysis', action='store_true',
                        help='跳過 Gemini 分析階段')
    parser.add_argument('--skip-processing', action='store_true',
                        help='跳過資料處理階段')
    parser.add_argument('--force-reprocess', action='store_true',
                        help='強制重新處理所有資料')
    
    # 效能調整
    parser.add_argument('--batch-size', type=int, default=5,
                        help='Gemini 分析批次大小 (預設: 5)')
    
    # 其他選項
    parser.add_argument('--enable-backup', action='store_true',
                        help='啟用 MongoDB 備份')
    parser.add_argument('--save-report', action='store_true',
                        help='儲存詳細執行報告')
    parser.add_argument('--quiet', action='store_true',
                        help='安靜模式，減少輸出')
    parser.add_argument('--validate-only', action='store_true',
                        help='只驗證設定，不執行流程')
    parser.add_argument('--pipeline-mode', choices=['optimized', 'fast', 'safe'],
                        default='optimized',
                        help='流水線處理模式 (預設: optimized)')
    parser.add_argument('--update-stats-only', action='store_true',
                        help='只更新統計數據，不執行其他流程')
    parser.add_argument('--force-stats-recalc', action='store_true',
                        help='強制重新計算所有統計數據')
    parser.add_argument('--use-integrated-manager', action='store_true',
                        help='使用整合爬蟲管理器（推薦）')
    parser.add_argument('--use-optimized-crawler', action='store_true',
                        help='使用優化爬蟲（快速URL收集+逐平台多線程爬取）')
    parser.add_argument('--use-parallel-processing', action='store_true',
                        help='使用高級並行處理器（爬取和處理同時進行）')
    parser.add_argument('--max-crawl-workers', type=int, default=2,
                        help='同時爬取的立委數 (預設: 2)')
    parser.add_argument('--max-process-workers', type=int, default=2,
                        help='同時處理的立委數 (預設: 2)')

    return parser.parse_args()

def get_date_range(args):
    """獲取日期範圍"""
    if args.date_range:
        try:
            start_date = datetime.strptime(args.date_range[0], '%Y-%m-%d').date()
            end_date = datetime.strptime(args.date_range[1], '%Y-%m-%d').date()
            return start_date, end_date
        except ValueError as e:
            raise ValueError(f"日期格式錯誤: {e}")
    else:
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=args.days)
        return start_date, end_date

def main():
    """主程式"""
    # 設置日誌
    logger = setup_logging()
    logger.info("=== 社交媒體資料分析主程式啟動 ===")
    
    try:
        # 解析參數
        args = parse_arguments()
        
        # 設定安靜模式
        if args.quiet:
            logging.getLogger().setLevel(logging.WARNING)
        
        # 驗證設定
        logger.info("驗證系統設定...")
        if not validate_setup():
            logger.error("系統設定驗證失敗")
            if args.validate_only:
                return 1
            logger.warning("繼續執行，但可能遇到問題...")
        
        if args.validate_only:
            logger.info("✅ 驗證模式：系統設定正常")
            return 0
        
        # 獲取參數
        start_date, end_date = get_date_range(args)
        legislators = args.legislators if args.legislators else ALL_LEGISLATORS
        platforms = args.platforms.split(',') if isinstance(args.platforms, str) else args.platforms
        
        logger.info(f"處理日期範圍: {start_date} 到 {end_date}")
        logger.info(f"處理平台: {', '.join(platforms)}")
        logger.info(f"處理立委: {len(legislators)} 位")
        
        # 只更新統計數據
        if args.update_stats_only:
            logger.info("🔄 開始更新統計數據...")
            try:
                from crawler.legislator_stats import update_legislator_stats
                update_legislator_stats(force_recalc=args.force_stats_recalc)
                logger.info("✅ 統計數據更新完成")
                return 0
            except Exception as e:
                logger.error(f"統計數據更新失敗: {e}")
                return 1
        
        # 執行主流程
        success_count = 0
        total_count = len(legislators)

        # 檢查使用哪種處理模式
        if args.use_parallel_processing:
            logger.info("🚀 使用並行處理模式...")

            # 簡化的並行處理：同時處理多個立委
            cutoff_date_str = start_date.strftime('%Y-%m-%d')

            def process_single_legislator(legislator):
                """處理單個立委的完整流程"""
                try:
                    logger.info(f"📊 開始處理: {legislator}")

                    # 1. 爬取數據
                    if not args.skip_crawler:
                        from crawler.optimized_crawler_manager import OptimizedCrawlerManager
                        optimized_manager = OptimizedCrawlerManager()

                        crawl_result = optimized_manager.crawl_politician_optimized(
                            politician_name=legislator,
                            cutoff_date_str=cutoff_date_str,
                            platforms=platforms,
                            max_workers_per_platform=4
                        )

                        if crawl_result['summary']['successful_platforms'] == 0:
                            logger.warning(f"⚠️ {legislator} 爬取失敗")

                    # 2. 數據處理
                    if not args.skip_processing:
                        from crawler.complete_data_processor import (
                            step1_merge_platform_data, step2_create_user_format,
                            step3_gemini_analysis, step4_mongodb_storage
                        )

                        # 執行處理步驟
                        step1_merge_platform_data([legislator])
                        step2_create_user_format([legislator])
                        if not args.skip_analysis:
                            step3_gemini_analysis([legislator])
                        step4_mongodb_storage([legislator])

                    logger.info(f"✅ {legislator} 處理完成")
                    return True

                except Exception as e:
                    logger.error(f"❌ {legislator} 處理失敗: {e}")
                    return False

            # 使用線程池並行處理
            max_workers = min(args.max_crawl_workers, len(legislators))
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = {executor.submit(process_single_legislator, leg): leg for leg in legislators}

                success_count = 0
                for future in as_completed(futures):
                    legislator = futures[future]
                    try:
                        if future.result():
                            success_count += 1
                    except Exception as e:
                        logger.error(f"❌ {legislator} 執行異常: {e}")

            logger.info(f"🎉 並行處理完成: {success_count}/{len(legislators)} 位立委成功")
            return 0 if success_count > 0 else 1

        elif args.use_optimized_crawler:
            logger.info("🚀 使用優化爬蟲管理器...")
            from crawler.optimized_crawler_manager import OptimizedCrawlerManager
            optimized_manager = OptimizedCrawlerManager()

            # 逐一處理立委，使用優化流程
            for i, legislator in enumerate(legislators, 1):
                logger.info(f"📊 處理立委 {i}/{total_count}: {legislator}")
                try:
                    # 使用優化爬蟲 - 修復：使用start_date作為截止日期
                    cutoff_date_str = start_date.strftime('%Y-%m-%d')
                    crawl_result = optimized_manager.crawl_politician_optimized(
                        politician_name=legislator,
                        cutoff_date_str=cutoff_date_str,
                        platforms=platforms,
                        max_workers_per_platform=4
                    )

                    # 檢查爬取結果
                    if crawl_result['summary']['successful_platforms'] > 0:
                        logger.info(f"✅ {legislator} 爬取成功: {crawl_result['summary']['successful_platforms']}/{crawl_result['summary']['total_platforms']} 平台")

                        # 繼續後續處理流程
                        # 安全檢查：確保skip_processing屬性存在
                        skip_processing = getattr(args, 'skip_processing', False)
                        if not skip_processing:
                            # 資料處理
                            logger.info(f"📊 處理 {legislator} 的統計資料...")
                            # 使用新的完整數據處理流程
                            logger.info(f"📊 執行完整數據處理流程: {legislator}")
                            from crawler.complete_data_processor import main as process_main
                            import sys

                            # 保存原始參數
                            original_argv = sys.argv

                            # 設置處理參數
                            sys.argv = [
                                'complete_data_processor.py',
                                '--legislators', legislator,
                                '--days', str(args.days)
                            ]

                            # 添加可選參數
                            force_reprocess = getattr(args, 'force_reprocess', False)
                            quiet = getattr(args, 'quiet', False)

                            if force_reprocess:
                                sys.argv.append('--force-reprocess')
                            if quiet:
                                sys.argv.append('--quiet')

                            try:
                                # 執行完整處理流程
                                process_result = process_main()
                                if process_result == 0:
                                    logger.info(f"✅ {legislator} 完整數據處理成功")
                                else:
                                    logger.warning(f"⚠️ {legislator} 完整數據處理部分失敗")
                            except Exception as e:
                                logger.error(f"❌ {legislator} 完整數據處理異常: {e}")
                            finally:
                                # 恢復原始參數
                                sys.argv = original_argv

                            # 情感分析和MongoDB存儲已包含在完整數據處理流程中

                        success_count += 1
                        logger.info(f"✅ {legislator} 處理完成 ({success_count}/{total_count})")
                    else:
                        logger.warning(f"⚠️ {legislator} 爬取失敗: 所有平台都失敗")

                except Exception as e:
                    logger.error(f"❌ {legislator} 處理失敗: {e}")
                    if not args.quiet:
                        import traceback
                        logger.error(traceback.format_exc())
                    continue

        elif args.use_integrated_manager:
            logger.info("🚀 使用整合爬蟲管理器...")
            integrated_manager = IntegratedCrawlerManager()

            # 根據天數決定使用全量還是增量模式
            if args.days <= 2:
                # 增量模式
                logger.info("📊 使用增量爬取模式")
                result = integrated_manager.crawl_all_legislators_incremental(
                    legislators=legislators,
                    platforms=platforms
                )
            else:
                # 全量模式
                logger.info("📊 使用全量爬取模式")
                result = integrated_manager.crawl_all_legislators_full(
                    legislators=legislators,
                    days=args.days,
                    platforms=platforms
                )

            # 處理結果
            success_count = result.get('legislators_processed', 0)
            failed_count = result.get('legislators_failed', 0)

            logger.info(f"✅ 整合管理器處理完成: 成功 {success_count}, 失敗 {failed_count}")

        else:
            # 使用原有的逐一處理方式
            # 現在使用兩階段爬取：URL收集用3個執行緒，內容爬取每平台用6個執行緒
            max_workers = 6  # 每個平台用6個執行緒爬取內容
            if not args.skip_crawler:
                logger.info(f"🚀 初始化多平台爬蟲... (兩階段爬取，每平台 {max_workers} 個執行緒)")
                crawler_manager = MultiPlatformCrawler()
        
            # 處理每位立委
            for i, legislator in enumerate(legislators, 1):
                logger.info(f"📊 處理立委 {i}/{total_count}: {legislator}")
                try:
                    # 1. 爬蟲階段
                    if not args.skip_crawler:
                        logger.info(f"🔍 爬取 {legislator} 的資料... (兩階段爬取，每平台 {max_workers} 個執行緒)")
                        # 修復：使用start_date作為截止日期
                        cutoff_date_str = start_date.strftime('%Y-%m-%d')
                        # 自動重試1次
                        for attempt in range(2):
                            try:
                                crawl_result = crawler_manager.crawl_politician_all_platforms(
                                    politician_name=legislator,
                                    cutoff_date_str=cutoff_date_str,
                                    platforms=platforms,
                                    max_workers=max_workers,
                                    headless=True
                                )
                                logger.info(f"爬蟲結果: {json.dumps(crawl_result['summary'], ensure_ascii=False)}")
                                break
                            except Exception as e:
                                logger.error(f"爬蟲失敗 (第{attempt+1}次): {e}")
                                if attempt == 1:
                                    raise
                                logger.info("自動重試中...")

                    # 2. 資料處理階段
                    logger.info(f"📊 處理 {legislator} 的統計資料...")
                    process_legislators_data(
                        specific_legislators=[legislator],
                        force_reprocess=args.force_reprocess,
                        quiet=args.quiet
                    )

                    # 3. 情感分析階段
                    if not args.skip_analysis:
                        logger.info(f"🧠 分析 {legislator} 的情感資料...")

                        # 選擇增量或完整分析
                        if args.force_reprocess:
                            analyze_legislators_emotions(
                                legislators=[legislator],
                                batch_size=args.batch_size,
                                quiet=args.quiet
                            )
                        else:
                            analyze_legislators_emotions_incremental(
                                legislators=[legislator],
                                batch_size=args.batch_size,
                                quiet=args.quiet
                            )

                    # 4. MongoDB 增量存儲階段
                    logger.info(f"💾 增量存儲 {legislator} 的資料到 MongoDB...")
                    mongo_handler = DataToMongo()
                    mongo_handler.store_crawler_data(
                        politician_name=legislator,
                        is_incremental=True  # 總是使用增量更新
                    )

                    success_count += 1
                    logger.info(f"✅ {legislator} 處理完成 ({success_count}/{total_count})")

                except Exception as e:
                    logger.error(f"❌ {legislator} 處理失敗: {e}")
                    if not args.quiet:
                        import traceback
                        logger.error(traceback.format_exc())
                    continue
        
        # 5. 增量更新統計資料
        logger.info("📈 增量更新立委統計資料...")
        try:
            from crawler.legislator_stats import LegislatorStatsUpdater
            from pymongo import MongoClient

            # 連接MongoDB
            client = MongoClient('mongodb://localhost:27017/')
            db = client['legislator_recall']

            # 創建統計更新器並進行增量更新
            stats_updater = LegislatorStatsUpdater(db)
            result = stats_updater.update_all_legislators(force_recalculate=False)

            logger.info(f"✅ 增量統計更新完成: {len(result)} 位立委")
        except Exception as e:
            logger.error(f"增量統計更新失敗: {e}")
        
        # 6. 生成報告
        if args.save_report:
            logger.info("📋 生成執行報告...")
            try:
                report_data = {
                    'execution_time': datetime.now().isoformat(),
                    'processed_legislators': success_count,
                    'total_legislators': total_count,
                    'date_range': f"{start_date} to {end_date}",
                    'platforms': platforms,
                    'settings': {
                        'skip_crawler': args.skip_crawler,
                        'skip_analysis': args.skip_analysis,
                        'force_reprocess': args.force_reprocess,
                        'batch_size': args.batch_size,
                        'pipeline_mode': args.pipeline_mode
                    }
                }
                
                report_dir = os.path.join(current_dir, 'crawler', 'logs')
                os.makedirs(report_dir, exist_ok=True)
                report_file = os.path.join(report_dir, f"execution_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
                
                with open(report_file, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"📋 執行報告已儲存: {report_file}")
            except Exception as e:
                logger.error(f"報告生成失敗: {e}")
        
        # 完成
        logger.info("=== 處理完成 ===")
        logger.info(f"成功處理: {success_count}/{total_count} 位立委")
        
        if success_count == total_count:
            logger.info("🎉 所有立委處理完成!")
            return 0
        else:
            logger.warning(f"⚠️  部分立委處理失敗: {total_count - success_count} 位")
            return 1
            
    except KeyboardInterrupt:
        logger.warning("程式被使用者中斷")
        return 130
    except Exception as e:
        logger.error(f"程式執行錯誤: {e}")
        if not args.quiet:
            import traceback
            logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
