#!/usr/bin/env python3
"""
立委罷免數據爬蟲系統 - 簡化版
只保留核心功能：指定立委、天數、平台進行爬蟲和數據處理
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timedelta
from typing import List, Optional

# 添加項目根目錄到 Python 路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 導入必要模組
from crawler.complete_data_pipeline import CompleteDataPipeline

# 立委列表
ALL_LEGISLATORS = [
    "高虹安", "牛煦庭", "葉元之", "王鴻薇", "游顥", "徐巧芯", "吳宗憲", "陳菁徽",
    "張智倫", "鍾佳濱", "林楚茵", "吳琪銘", "蘇巧慧", "何志偉", "林昶佐", "范雲",
    "洪申翰", "林宜瑾", "莊競程", "邱議瑩", "林淑芬", "賴瑞隆", "邱志偉", "鄭運鵬",
    "湯蕙禎", "黃捷", "陳椒華"
]

def setup_logging():
    """設置日誌系統"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('crawler.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)

def validate_setup():
    """驗證系統設定"""
    try:
        # 檢查必要的環境變數
        required_env = ['GEMINI_API_KEY']
        missing_env = [env for env in required_env if not os.getenv(env)]
        
        if missing_env:
            print(f"❌ 缺少環境變數: {', '.join(missing_env)}")
            return False
            
        # 檢查必要目錄
        required_dirs = ['crawler/processed', 'crawler/processed/all_data', 
                        'crawler/processed/user_data', 'crawler/processed/final_data']
        
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                print(f"📁 創建目錄: {dir_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 驗證失敗: {e}")
        return False

def parse_arguments():
    """解析命令列參數 - 簡化版"""
    parser = argparse.ArgumentParser(
        description='立委罷免數據爬蟲系統',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例用法：
  python main_simple.py --days 30                           # 抓取最近30天資料
  python main_simple.py --platforms youtube,ptt             # 指定爬取平台
  python main_simple.py --legislators 高虹安 牛煦庭          # 指定立委
  python main_simple.py --days 400 --platforms youtube,ptt  # 400天YouTube+PTT資料
        """
    )
    
    # 核心參數 - 只保留必要的
    parser.add_argument('--days', type=int, default=30,
                        help='抓取天數 (預設: 30)')
    
    parser.add_argument('--platforms', type=str,
                        default='youtube,ptt',
                        help='爬蟲平台，用逗號分隔 (預設: youtube,ptt)')
    
    parser.add_argument('--legislators', nargs='+',
                        help='指定立委姓名 (預設: 所有立委)')
    
    return parser.parse_args()

def get_date_range(args):
    """計算日期範圍"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=args.days)
    return start_date, end_date

def main():
    """簡化主程式 - 只執行爬蟲和數據處理"""
    # 設置日誌
    logger = setup_logging()
    logger.info("=== 立委罷免數據爬蟲系統啟動 ===")
    
    try:
        # 解析參數
        args = parse_arguments()
        
        # 驗證設定
        logger.info("驗證系統設定...")
        if not validate_setup():
            logger.error("系統設定驗證失敗，請檢查環境配置")
            return 1
        
        # 獲取參數
        start_date, end_date = get_date_range(args)
        legislators = args.legislators if args.legislators else ALL_LEGISLATORS
        platforms = args.platforms.split(',') if isinstance(args.platforms, str) else args.platforms
        
        logger.info(f"📅 處理日期範圍: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} ({args.days}天)")
        logger.info(f"🌐 處理平台: {', '.join(platforms)}")
        logger.info(f"👥 處理立委: {len(legislators)} 位 - {legislators if len(legislators) <= 5 else legislators[:5] + ['...']}")
        
        # 使用完整數據管道處理
        logger.info("🚀 啟動完整數據管道...")
        pipeline = CompleteDataPipeline()
        success_count = 0
        
        for legislator in legislators:
            try:
                logger.info(f"📊 處理立委: {legislator}")
                result = pipeline.process_legislator_complete_pipeline(
                    legislator_name=legislator,
                    start_date=start_date,
                    end_date=end_date,
                    platforms=platforms
                )
                
                if result:
                    success_count += 1
                    logger.info(f"✅ {legislator} 處理完成")
                else:
                    logger.warning(f"⚠️ {legislator} 處理失敗")
                    
            except Exception as e:
                logger.error(f"❌ {legislator} 處理出錯: {e}")
        
        # 輸出結果
        logger.info(f"🎯 處理完成: {success_count}/{len(legislators)} 位立委成功")
        return 0 if success_count > 0 else 1
        
    except KeyboardInterrupt:
        logger.warning("程式被使用者中斷")
        return 1
    except Exception as e:
        logger.error(f"程式執行失敗: {e}")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
