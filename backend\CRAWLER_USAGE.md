# 爬蟲系統使用說明

## 🚀 修復完成的問題

### ✅ 已修復的錯誤：
1. **PTT 內容爬取異常**: `'tuple' object has no attribute 'get'` - 已修復返回格式
2. **YouTube URL收集無結果** - 已放寬日期篩選，提高收集成功率
3. **Threads爬取不到資料** - 已修復檔案路徑和返回格式問題
4. **統計資料更新失敗** - 已添加缺少的 `update_all_legislators` 方法

## 🔧 新增的優化功能

### 1. 優化爬蟲管理器 (推薦使用)
實現了你要求的兩階段流程：

**階段1：快速並行收集所有平台URL**
- 同時收集 YouTube、PTT、Threads 的 URL
- 快速模式，不深度爬取內容
- 並行處理，節省時間

**階段2：逐平台多線程爬取內容**
- YouTube: 4個線程並行爬取評論
- PTT: 4個線程並行爬取留言  
- Threads: 4個線程並行爬取貼文
- 逐平台處理，避免資源衝突

## 📋 使用方式

### 方式1：使用優化爬蟲 (推薦)
```bash
# 增量爬取（前一天資料）
python main.py --legislators 高虹安 --days 1 --use-optimized-crawler

# 全量爬取（400天資料）
python main.py --legislators 高虹安 --days 400 --use-optimized-crawler

# 多位立委
python main.py --legislators 高虹安,柯文哲,蔣萬安 --days 1 --use-optimized-crawler
```

### 方式2：使用整合管理器
```bash
# 自動選擇模式（≤2天用增量，>2天用全量）
python main.py --legislators 高虹安 --days 1 --use-integrated-manager
python main.py --legislators 高虹安 --days 400 --use-integrated-manager
```

### 方式3：使用原有流程
```bash
# 傳統逐一處理方式
python main.py --legislators 高虹安 --days 1
```

## ⚙️ 參數說明

### 基本參數
- `--legislators`: 立委名單（逗號分隔）
- `--days`: 爬取天數（1=增量，400=全量）
- `--platforms`: 平台選擇（預設：youtube,ptt,threads）

### 爬蟲模式
- `--use-optimized-crawler`: 使用優化爬蟲（推薦）
- `--use-integrated-manager`: 使用整合管理器
- 不指定：使用原有流程

### 處理選項
- `--skip-crawler`: 跳過爬蟲階段
- `--skip-analysis`: 跳過情感分析
- `--skip-processing`: 跳過資料處理
- `--force-reprocess`: 強制重新處理

## 🔄 完整流程說明

### 優化爬蟲流程：
1. **URL收集階段**：並行收集所有平台URL（快速）
2. **內容爬取階段**：逐平台多線程爬取內容
   - YouTube → PTT → Threads（依序進行）
   - 每平台使用4個線程
   - 平台間有資源釋放等待
3. **資料合併階段**：合併到 `process/alldata`
4. **用戶統計階段**：統計到 `user_data`
5. **情感分析階段**：使用 Gemini 分析到 `final_data`
6. **資料存儲階段**：存儲到 MongoDB `crawler_data`
7. **統計更新階段**：更新 `legislators` 統計

### 兩種模式：
- **增量模式**（days ≤ 2）：爬取前一天資料，用戶唯一性更新
- **全量模式**（days > 2）：爬取指定天數資料，完整重新處理

## 📊 效能優化

### 優化點：
1. **並行URL收集**：3個平台同時收集URL
2. **逐平台處理**：避免資源競爭
3. **多線程爬取**：每平台4線程並行
4. **資源管理**：WebDriver池管理，自動清理
5. **錯誤處理**：統一錯誤格式，詳細日誌

### 預期效果：
- URL收集時間：從 5-10分鐘 → 1-2分鐘
- 內容爬取穩定性：大幅提升
- 記憶體使用：更穩定
- 錯誤率：顯著降低

## 🐛 故障排除

### 常見問題：
1. **WebDriver 錯誤**：檢查 Chrome 版本和 chromedriver
2. **MongoDB 連接失敗**：確認 MongoDB 服務運行
3. **Gemini API 錯誤**：檢查 API 金鑰設定
4. **記憶體不足**：減少線程數或分批處理

### 日誌查看：
```bash
# 查看詳細日誌
python main.py --legislators 高虹安 --days 1 --use-optimized-crawler --verbose

# 安靜模式
python main.py --legislators 高虹安 --days 1 --use-optimized-crawler --quiet
```

## 📁 檔案結構

```
backend/crawler/
├── optimized_crawler_manager.py    # 優化爬蟲管理器
├── integrated_crawler_manager.py   # 整合管理器
├── multi_platform_crawler.py       # 原有多平台爬蟲
├── yt_crawler.py                   # YouTube爬蟲
├── ptt_crawler.py                  # PTT爬蟲
├── thread_crawler.py               # Threads爬蟲
├── user_data_processor.py          # 用戶資料處理
├── gemini_emo_user.py              # 情感分析
├── data_to_mongo_v2.py             # MongoDB存儲
└── legislator_stats.py             # 統計更新
```

## 🎯 建議使用方式

### 日常增量爬取：
```bash
python main.py --legislators 高虹安,柯文哲,蔣萬安 --days 1 --use-optimized-crawler
```

### 初次全量爬取：
```bash
python main.py --legislators 高虹安 --days 400 --use-optimized-crawler --force-reprocess
```

### 測試單一立委：
```bash
python main.py --legislators 高虹安 --days 1 --use-optimized-crawler --verbose
```
