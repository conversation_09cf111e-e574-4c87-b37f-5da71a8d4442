#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試YouTube時間修復腳本
驗證精確時間比較邏輯
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'crawler'))

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def test_time_conversion():
    """測試時間轉換函數"""
    logger.info("🧪 測試時間轉換函數...")
    
    try:
        from crawler.yt_crawler import convert_relative_time_to_datetime
        
        # 測試各種時間格式
        test_cases = [
            "13 小時前",
            "20 小時前", 
            "1 天前",
            "2 天前",
            "3 分鐘前",
            "直播時間：13 小時前",
            "直播時間：1 天前",
            "10 小時前",
            "14 小時前",
            "21 小時前"
        ]
        
        now = datetime.now()
        cutoff_date = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        
        logger.info(f"當前時間: {now.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"截止時間: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("")
        
        collected = []
        skipped = []
        
        for time_text in test_cases:
            video_datetime = convert_relative_time_to_datetime(time_text)
            
            if video_datetime >= cutoff_date:
                collected.append((time_text, video_datetime))
                status = "✅ 收集"
            else:
                skipped.append((time_text, video_datetime))
                status = "⏳ 跳過"
            
            logger.info(f"   {status}: '{time_text}' -> {video_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        
        logger.info("")
        logger.info(f"收集結果: {len(collected)} 個影片被收集, {len(skipped)} 個影片被跳過")
        
        # 驗證邏輯
        expected_collected = [
            "13 小時前", "20 小時前", "1 天前", "3 分鐘前", 
            "直播時間：13 小時前", "直播時間：1 天前", 
            "10 小時前", "14 小時前", "21 小時前"
        ]
        
        collected_texts = [item[0] for item in collected]
        
        correct_count = 0
        for expected in expected_collected:
            if expected in collected_texts:
                correct_count += 1
            else:
                logger.warning(f"   ⚠️ 應該收集但被跳過: {expected}")
        
        if correct_count >= len(expected_collected) - 1:  # 允許1個誤差
            logger.info("✅ 時間轉換測試通過")
            return True
        else:
            logger.error(f"❌ 時間轉換測試失敗: {correct_count}/{len(expected_collected)} 正確")
            return False
            
    except Exception as e:
        logger.error(f"❌ 時間轉換測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_date_filtering_logic():
    """測試日期篩選邏輯"""
    logger.info("🧪 測試日期篩選邏輯...")
    
    try:
        from crawler.yt_crawler import convert_relative_time_to_datetime
        from datetime import datetime, timedelta
        
        # 模擬爬取前1天的資料
        now = datetime.now()
        cutoff_date = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        
        # 模擬YouTube找到的時間
        found_times = [
            "13 小時前",    # 今天 - 應該收集
            "20 小時前",    # 今天 - 應該收集  
            "1 天前",       # 昨天 - 應該收集
            "2 天前",       # 前天 - 應該跳過
            "10 小時前",    # 今天 - 應該收集
            "14 小時前",    # 今天 - 應該收集
            "21 小時前",    # 今天 - 應該收集
            "3 天前",       # 3天前 - 應該跳過
        ]
        
        collected_count = 0
        skipped_count = 0
        
        for time_text in found_times:
            video_datetime = convert_relative_time_to_datetime(time_text)
            
            if video_datetime >= cutoff_date:
                collected_count += 1
                logger.info(f"   ✅ 收集: {time_text} ({video_datetime.strftime('%Y-%m-%d %H:%M')})")
            else:
                skipped_count += 1
                logger.info(f"   ⏳ 跳過: {time_text} ({video_datetime.strftime('%Y-%m-%d %H:%M')})")
        
        logger.info(f"篩選結果: 收集 {collected_count} 個, 跳過 {skipped_count} 個")
        
        # 驗證：應該收集6個（所有小時前的 + 1天前），跳過2個（2天前、3天前）
        if collected_count >= 5 and skipped_count >= 2:
            logger.info("✅ 日期篩選邏輯測試通過")
            return True
        else:
            logger.error(f"❌ 日期篩選邏輯測試失敗: 收集{collected_count}個，跳過{skipped_count}個")
            return False
            
    except Exception as e:
        logger.error(f"❌ 日期篩選邏輯測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    logger.info("🚀 開始測試YouTube時間修復...")
    
    tests = [
        ("時間轉換函數", test_time_conversion),
        ("日期篩選邏輯", test_date_filtering_logic)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"測試: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"測試 {test_name} 時發生異常: {e}")
            results[test_name] = False
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("YouTube時間修復測試總結")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個測試通過")
    
    if passed == total:
        logger.info("🎉 YouTube時間修復測試全部通過！")
        logger.info("現在應該能收集到更多符合條件的影片了！")
        return 0
    else:
        logger.warning(f"⚠️ 有 {total - passed} 個測試失敗")
        return 1

if __name__ == "__main__":
    sys.exit(main())
