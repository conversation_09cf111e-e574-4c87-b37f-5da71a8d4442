{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 匯入所需資源"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re \n", "import re\n", "from collections import Counter\n", "import jieba\n", "from wordcloud import WordCloud\n", "import matplotlib.pyplot as plt\n", "from wordcloud import WordCloud, STOPWORDS\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from PIL import Image\n", "import jieba\n", "import jieba.analyse\n", "from collections import Counter\n", "from transformers import pipeline\n", "import torch\n", "from matplotlib.font_manager import FontProperties\n", "import json\n", "import os\n", "import random"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from matplotlib.font_manager import FontProperties\n", "# 指定自定义字体路径（可选）\n", "font_path = './font/SimHei.ttf'  # 确保字体文件存在于此路径\n", "font_prop = FontProperties(fname=font_path)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 導入已經訓練好的模型"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["#給csv用\n", "# 檢查是否有可用的 GPU 用已經訓練過模型\n", "device = 0 if torch.cuda.is_available() else -1\n", "sentiment_pipeline = pipeline(\"sentiment-analysis\", model=\"sentiment_model_200\", device=device)\n", "def classify_sentiment(text):\n", "    max_length = 256\n", "    truncated_text = text[:max_length]\n", "    sentiment = sentiment_pipeline(truncated_text)[0]\n", "    sentiment_label = sentiment['label'] \n", "    sentiment_score = sentiment['score']\n", "    \n", "    return sentiment_label, sentiment_score"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["#給json用\n", "# 檢查是否有可用的 GPU 用已經訓練過模型\n", "device = 0 if torch.cuda.is_available() else -1\n", "# 加載情感分析模型，並啟用截斷選項\n", "sentiment_pipeline = pipeline(\"sentiment-analysis\",  model=\"sentiment_model_200\", device=device, truncation=True)\n", "\n", "def classify_sentiment(text):\n", "    max_length =512\n", "    truncated_text = text[:max_length]\n", "    sentiment = sentiment_pipeline(truncated_text)[0]\n", "    \n", "    return {\"label\": sentiment['label'], \"score\": sentiment['score']}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from transformers import pipeline\n", "import torch\n", "import warnings\n", "\n", "emotion_labels = [\"surprise\", \"anticipation\", \"joy\", \"disgust\", \"trust\", \"sadness\", \"anger\", \"fear\"]\n", "emotion_keywords = {\n", "    \"surprise\": [\"驚訝\", \"意外\", \"竟然\", \"嚇一跳\", \"哇\", \"不敢相信\", \"天啊\", \"驚奇\", \"突然\", \"沒想到\", \"出乎意料\", \"嚇到\"],\n", "    \"anticipation\": [\"摩天輪\",\"如果\", \"希望\", \"要是\", \"期待\", \"預計\", \"應該\", \"將來\", \"準備\", \"打算\", \"計劃\", \"會\", \"等著\", \"想要\", \"預期\"],\n", "    \"joy\": [\"終於\",\"總算\",\"愛\",\"喜歡\",\"哈\",\"很棒\",\"加油\", \"棒\", \"太好了\", \"開心\", \"高興\", \"讚\", \"幸福\", \"喜歡\", \"愉快\", \"成功\", \"快樂\", \"歡笑\", \"恭喜\", \"樂\"],\n", "    \"disgust\": [\"咖\",\"笑死\",\"噁心\", \"厭惡\", \"討厭\", \"噁\", \"反感\", \"不爽\", \"不喜歡\", \"反胃\", \"髒\", \"垃圾\", \"惡心\"],\n", "    \"trust\": [\"中肯\",\"信任\", \"相信\", \"可靠\", \"支持\", \"值得\", \"信賴\", \"誠實\", \"放心\", \"安心\", \"真誠\", \"依靠\", \"信心\"],\n", "    \"sadness\": [\"可憐\",\"難過\", \"傷心\", \"失望\", \"沮喪\", \"痛苦\", \"流淚\", \"悲傷\", \"心碎\", \"哭\", \"孤單\", \"寂寞\", \"哀傷\",\"可惜\",\"悲\",\"慘\"],\n", "    \"anger\": [\"違法\",\"滾\",\"爛\",\"洨\",\"警察\",\"圖利\",\"智障\",\"機車\",\"雞巴\",\"嘴砲\",\"電動車\",\"勾勾肉\",\"下去\",\"離譜\",\"狗勾肉\", \"狗狗肉\", \"gogoro\", \"微風\", \"前女友\", \"NET\", \"Net\", \"強盜\", \"靠\", \"幹\", \"廣場\", \"氣死\", \"討厭\", \"不爽\", \"垃圾\", \"吵\", \"爛\", \"煩\", \"欺負\", \"生氣\"],\n", "    \"fear\": [\"害怕\", \"怕\", \"恐怖\", \"擔心\", \"不安\", \"緊張\", \"恐懼\", \"危險\", \"驚慌\", \"嚇到\", \"擔憂\", \"焦慮\"]\n", "}\n", "def keyword_based_scoring(text, emotion_keywords):\n", "    scores = {emotion: 0 for emotion in emotion_keywords}\n", "    for emotion, keywords in emotion_keywords.items():\n", "        for keyword in keywords:\n", "            if keyword in text:\n", "                scores[emotion] += 1  # 如果关键词出现，增加该情绪的分数\n", "    return scores\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#原本分csv情緒\n", "from transformers import AutoTokenizer, AutoModelForSequenceClassification\n", "# 加载模型和tokenizer\n", "tokenizer = AutoTokenizer.from_pretrained('emotionResult/tokenizer')\n", "model = AutoModelForSequenceClassification.from_pretrained('emotionResult/model')\n", "\n", "# 將模型移動到 GPU（如果可用）\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "\n", "# 設置情感分析管道\n", "emotion_pipeline = pipeline(\"sentiment-analysis\", model=model, tokenizer=tokenizer, device=0 if torch.cuda.is_available() else -1)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#原本分csv情緒\n", "def classify_sentiment(text):\n", "    max_length =512\n", "    truncated_text = text[:max_length]\n", "    sentiment = sentiment_pipeline(truncated_text)[0]\n", "    return {\"label\": sentiment['label'], \"score\": sentiment['score']}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def model_based_classification(texts):\n", "    inputs = tokenizer(texts, padding=True, truncation=True, max_length=128, return_tensors=\"pt\").to(device)\n", "    with torch.no_grad():\n", "        outputs = model(**inputs)\n", "    logits = outputs.logits\n", "    probabilities = torch.softmax(logits, dim=-1)\n", "    predicted_labels = torch.argmax(probabilities, dim=1)\n", "    return [emotion_labels[label] for label in predicted_labels]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def classify_emotion(text, emotion_keywords, threshold=0.1):\n", "    # 確保文本是字符串\n", "    if not isinstance(text, str):\n", "        return \"unknown\"  # 如果不是字符串，返回未知\n", "\n", "    keyword_scores = keyword_based_scoring(text, emotion_keywords)\n", "    \n", "    # 获取最高和次高情绪分数\n", "    sorted_scores = sorted(keyword_scores.items(), key=lambda x: x[1], reverse=True)\n", "    top_emotion, top_score = sorted_scores[0]\n", "    second_emotion, second_score = sorted_scores[1]\n", "    \n", "    # 如果最高分数和次高分数接近，则使用模型分类\n", "    if top_score - second_score < threshold:\n", "        return model_based_classification([text])[0]  # 使用模型进一步分类\n", "    elif top_score == 0:\n", "        return \"unknown\"  # 如果没有关键词匹配，则返回未知\n", "    else:\n", "        return top_emotion  # 直接返回关键词分类结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#json情緒\n", "from transformers import AutoTokenizer, AutoModelForSequenceClassification\n", "# 加载模型和tokenizer\n", "tokenizer = AutoTokenizer.from_pretrained('emotionResult/tokenizer')\n", "model = AutoModelForSequenceClassification.from_pretrained('emotionResult/model')\n", "# 獲取模型的最大長度（通常為 512 或 1024）\n", "max_length = tokenizer.model_max_length\n", "# 將模型移動到GPU（如果可用）\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# 定義情緒標籤\n", "emotion_labels = [\"surprise\", \"anticipation\", \"joy\", \"disgust\", \"trust\", \"sadness\", \"anger\", \"fear\"]\n", "label_mapping = {f\"LABEL_{i}\": emotion_labels[i] for i in range(len(emotion_labels))}\n", "\n", "# 創建情感分析pipeline\n", "emotion_pipeline = pipeline(\"sentiment-analysis\", model=model, tokenizer=tokenizer, device=device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义情绪分类函数并返回所有情绪标签和分数 目前用這個\n", "def classify_emotion(text,  sentiment_label):\n", "    # 假设 emotion_pipeline 返回包含多個情绪标签及其对应的分数\n", "    emotion_scores = emotion_pipeline(text, max_length=max_length, truncation=True)\n", "\n", "    # Generate {label: score} for each emotion\n", "    all_scores = {label_mapping.get(emotion['label'], \"unknown\"): emotion['score'] for emotion in emotion_scores}\n", "    \n", "    # Fill in any missing emotions with a score of 0 to ensure consistency\n", "    for emotion in ['joy', 'trust', 'anticipation', 'surprise', 'anger', 'disgust', 'fear', 'sadness']:\n", "        all_scores.setdefault(emotion, 0.5)\n", "\n", "    return all_scores"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 情感分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["分析json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "# 讀取 JSON 檔案\n", "with open('yt_data.json', 'r', encoding='utf-8') as f:\n", "    video_data = json.load(f)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for video in video_data:\n", "    print(f\"影片標題: {video['影片標題']}\")\n", "    for comment in video['用戶留言']:\n", "        print(comment)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 對每條用戶留言進行情感分析，並將結果附加到 JSON 中\n", "for video in video_data:\n", "    for idx, comment in enumerate(video['用戶留言']):\n", "        sentiment_label, sentiment_score = classify_sentiment(comment)\n", "        # 更新用戶留言，添加情感標籤和分數\n", "        video['用戶留言'][idx] = {\n", "            '留言內容': comment,\n", "            '情感標籤': sentiment_label,\n", "            '情感分數': sentiment_score\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('yt_data_sentiment.json', 'w', encoding='utf-8') as f:\n", "    json.dump(video_data, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('yt_data_sentiment.json', 'r', encoding='utf-8') as f:\n", "    data_yt = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 進行情緒分析，並將結果附加到 \"情緒\" 欄位中\n", "def classify_comments(video):\n", "    for idx, comment in enumerate(video['用戶留言']):\n", "        # 獲取留言內容\n", "        comment_content = comment['留言內容']\n", "        # 進行情緒分析\n", "        emotion = classify_emotion(comment_content, emotion_keywords)\n", "        # 更新留言字典\n", "        comment['情緒'] = emotion\n", "    return video\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 針對每個影片進行處理\n", "updated_video_data = [classify_comments(video) for video in data_yt]\n", "\n", "# 將更新後的結果儲存回 JSON\n", "with open('yt_data_sentiment_emotion.json', 'w', encoding='utf-8') as f:\n", "    json.dump(updated_video_data, f, ensure_ascii=False, indent=4)\n", "\n", "print(\"情緒標籤已成功更新並儲存！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## json_YT畫圖"]}, {"cell_type": "markdown", "metadata": {}, "source": ["處理數據定義屬性"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "# 假設你已經讀取了 JSON 數據\n", "with open('./YT_data_1012/yt_data_sentiment_emotion.json', 'r', encoding='utf-8') as f:\n", "    video_data = json.load(f)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "def define_user_attributes(data):\n", "    user_attributes = defaultdict(lambda: {\n", "        '留言數': 0,\n", "        'POSITIVE': 0,\n", "        'NEGATIVE': 0,\n", "        '最後留言時間': '2024-10-13',  # 預設留言時間\n", "        '最後情緒': ''\n", "    })\n", "\n", "    # 統計每位用戶的留言情況\n", "    for item in data:\n", "        for i, user in enumerate(item['用戶']):\n", "            sentiment = item['用戶留言'][i]['情感標籤']\n", "            user_attributes[user]['留言數'] += 1\n", "            user_attributes[user][sentiment] += 1\n", "            \n", "            # 如果 '留言時間' 不存在，設置預設值 '2024-10-13'\n", "            user_attributes[user]['最後留言時間'] = item['用戶留言'][i].get('留言時間', '2024-10-13')\n", "            \n", "            # 如果 '情緒' 不存在，保持預設值或更新為最後一次的情緒\n", "            user_attributes[user]['最後情緒'] = item['用戶留言'][i].get('情緒', user_attributes[user]['最後情緒'])\n", "    \n", "    # 定義最終的帳號屬性\n", "    for user, attributes in user_attributes.items():\n", "        if attributes['POSITIVE'] > attributes['NEGATIVE']:\n", "            attributes['情感標籤'] = 'POSITIVE'\n", "        else:\n", "            attributes['情感標籤'] = 'NEGATIVE'\n", "    \n", "    return user_attributes\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def update_json_structure(video_data, user_attributes):\n", "    updated_data = []\n", "\n", "    for item in video_data:\n", "        new_item = {\n", "            \"影片標題\": item[\"影片標題\"],\n", "            \"影片留言數\": item[\"影片留言數\"],\n", "            \"影片按讚數\": item[\"影片按讚數\"],\n", "            \"影片觀看次數\": item[\"影片觀看次數\"],\n", "            \"影片發布時間\": item[\"影片發布時間\"],\n", "            \"用戶留言\": []\n", "        }\n", "        \n", "        for i, user in enumerate(item['用戶']):\n", "            new_user_comment = {\n", "                \"用戶名\": user,\n", "                \"留言內容\": item['用戶留言'][i]['留言內容'],\n", "                \"情感標籤\": user_attributes[user]['情感標籤'],\n", "                \"情感分數\": item['用戶留言'][i]['情感分數'],\n", "                \"情緒\": user_attributes[user]['最後情緒'],\n", "                \"留言時間\": user_attributes[user]['最後留言時間'],\n", "                \"留言按讚數\": item['留言按讚數'][i]\n", "            }\n", "            new_item[\"用戶留言\"].append(new_user_comment)\n", "\n", "        updated_data.append(new_item)\n", "\n", "    return updated_data\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_attributes = define_user_attributes(video_data)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["updated_data = update_json_structure(video_data, user_attributes)\n", "\n", "# 3. 檢查或進一步處理 updated_data\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('YT_unique_data.json', 'w', encoding='utf-8') as f:\n", "    json.dump(updated_data, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def remove_duplicate_titles(data):\n", "    unique_titles = {}\n", "    result = []\n", "\n", "    for item in data:\n", "        title = item['影片標題']\n", "        if title not in unique_titles:\n", "            unique_titles[title] = True  # 將新聞標題標記為已處理\n", "            result.append(item)  # 保留該新聞的資訊\n", "    \n", "    return result\n", "updated_data = remove_duplicate_titles(updated_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "with open('yt_unique_data.json', 'w', encoding='utf-8') as f:\n", "    json.dump(updated_data, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定義用戶屬性"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('yt_json_data_1020/YT_data.json', 'r', encoding='utf-8') as f:\n", "    original_news_data = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_and_save_news_data(original_news_data):\n", "    processed_news_data = []\n", "    \n", "    for news in original_news_data:\n", "        title = news['影片標題']\n", "        sentiment_counts = {'POSITIVE': 0, 'NEGATIVE': 0}\n", "        unique_users = {}\n", "        \n", "        # 遍歷留言，計算情感數量並清理重複留言\n", "        for comment in news['用戶留言']:\n", "            user = comment['用戶名']\n", "            sentiment_label = comment['情感標籤']\n", "            \n", "            # 若用戶尚未留言，則記錄該留言並增加相應情感計數\n", "            if user not in unique_users:\n", "                unique_users[user] = comment\n", "                sentiment_counts[sentiment_label] += 1\n", "        \n", "        # 獲取有效留言數量\n", "        effective_comments = len(unique_users)\n", "        \n", "        # 判斷最終情感標籤\n", "        final_sentiment = 'POSITIVE' if sentiment_counts['POSITIVE'] > sentiment_counts['NEGATIVE'] else 'NEGATIVE'\n", "        \n", "        # 更新資料結構\n", "        processed_news_data.append({\n", "            '影片標題': title,\n", "            '影片按讚數': news['影片按讚數'],\n", "            '影片觀看次數': news['影片觀看次數'],\n", "            '影片發布時間': news['影片發布時間'],\n", "            '影片留言數': news['影片留言數'],\n", "            '有效留言數': effective_comments,\n", "            '情感標籤': final_sentiment,\n", "            '用戶留言': list(unique_users.values())  # 轉換為列表\n", "        })\n", "    \n", "    # 儲存為 JSON 檔案\n", "    with open('yt_final_data.json', 'w', encoding='utf-8') as f:\n", "        json.dump(processed_news_data, f, ensure_ascii=False, indent=4)\n", "\n", "# 調用函數來處理並儲存數據\n", "process_and_save_news_data(original_news_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["畫json圖\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('./yt_json_data_1020/yt_final_data.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 用於清理文件名的函數\n", "def clean_filename(title):\n", "    # 移除不合法字符\n", "    return re.sub(r'[<>:\"/\\\\|?*]', '_', title)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for news in data:\n", "    title = news['影片標題']\n", "    print(f\"標題: {title}\")\n", "len(title)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from math import pi\n", "from collections import defaultdict\n", "import os\n", "import re\n", "import textwrap\n", "def wrap_text(text, width=40):\n", "    \"\"\"\n", "    將長文本分成多行以適應圖表大小。\n", "    \"\"\"\n", "    return '\\n'.join(textwrap.wrap(text, width))\n", "\n", "\n", "# 自定義的 autopct 格式，顯示具體百分比和數量\n", "def autopct_format(pct, total_values):\n", "    absolute = int(round(pct / 100. * total_values))  # 使用 round 確保取整時精度\n", "    return f'{pct:.1f}%\\n({absolute})'\n", "\n", "# 繪製圓餅圖\n", "def plot_pie_chart(title, pos_count, neg_count, likes, views, publish_time, total_comments, effective_comments, font_prop):\n", "    labels = ['反對罷免', '支持罷免']\n", "    sizes = [pos_count, neg_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    # 校正反對罷免（POSITIVE）和支持罷免（NEGATIVE）的數量相加等於有效留言數\n", "    total_pie_comments = pos_count + neg_count\n", "    if total_pie_comments != effective_comments:\n", "        difference = effective_comments - total_pie_comments\n", "        # 根據差異調整數量：將差異加到數量較大的那一類，確保總數匹配\n", "        if pos_count > neg_count:\n", "            pos_count += difference\n", "        else:\n", "            neg_count += difference\n", "        sizes = [pos_count, neg_count]  # 更新 sizes\n", "\n", "    # 再次檢查數據是否與有效留言數一致\n", "    assert sum(sizes) == effective_comments, \"數據校正後仍不一致\"\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors,\n", "                                       autopct=lambda pct: autopct_format(pct, sum(sizes)), startangle=90, textprops={'fontproperties': font_prop})\n", "    # 如果標題過長，則分行處理，或者根據長度動態縮小字體\n", "    wrapped_title = wrap_text(title)\n", "    plt.title(f\"{wrapped_title}\\n\\n正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    # 在左下角添加影片信息\n", "    lower_left_text = (\n", "        f\"影片按讚數: {likes}\\n\"\n", "        f\"影片觀看次數: {views}\\n\"\n", "        f\"影片發布時間: {publish_time}\\n\"\n", "        f\"總留言數: {total_comments}\\n\"\n", "        f\"有效留言數: {effective_comments}\"\n", "    )\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    # 清理文件名並保存\n", "    safe_title = clean_filename(title)\n", "    plt.savefig(f'./yt_output_1020/{safe_title}_情感分析_圓餅圖.png')\n", "    plt.close()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_emotion_values(emotion_data, emotions):\n", "    \"\"\"獲取情緒對應的值列表，並封閉首尾\"\"\"\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 閉合雷達圖\n", "    return values\n", "\n", "def plot_radar_chart(title, positive_emotions, negative_emotions, likes, views, publish_time, total_comments, effective_comments, font_prop):\n", "    emotions = ['joy', 'trust', 'anticipation', 'sadness', 'surprise', 'disgust', 'fear', 'anger']\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]  # 閉合雷達圖\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 繪製正面情緒雷達圖\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])  # 隱藏半徑刻度標籤\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 繪製負面情緒雷達圖\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])  # 隱藏半徑刻度標籤\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 在圖表上方添加新聞標題，並且只顯示一次\n", "    fig.suptitle(f'{title}', fontproperties=font_prop, fontsize=16, y=1.0)\n", "\n", "    # 添加正面和負面情緒詳細比數\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "    # 保存圖像\n", "    safe_title = clean_filename(title)\n", "    plt.savefig(f'./yt_output_1020/{safe_title}_正負面情緒雷達圖.png', bbox_inches='tight')\n", "    plt.close()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析新聞數據\n", "def analyze_news_data(news_data, font_prop):\n", "    for news in news_data:\n", "        title = news['影片標題']\n", "        pos_count = sum(1 for comment in news['用戶留言'] if comment['情感標籤'] == 'POSITIVE')\n", "        neg_count = sum(1 for comment in news['用戶留言'] if comment['情感標籤'] == 'NEGATIVE')\n", "        likes = news['影片按讚數']\n", "        views = news['影片觀看次數']\n", "        publish_time = news['影片發布時間']\n", "        total_comments = news['影片留言數']\n", "        \n", "        unique_users = {comment['用戶名'] for comment in news['用戶留言']}\n", "        effective_comments = len(unique_users)\n", "\n", "        plot_pie_chart(title, pos_count, neg_count, likes, views, publish_time, total_comments, effective_comments, font_prop)\n", "\n", "        positive_emotions = defaultdict(int)\n", "        negative_emotions = defaultdict(int)\n", "        for comment in news['用戶留言']:\n", "            emotion = comment['情緒']\n", "            if comment['情感標籤'] == 'POSITIVE':\n", "                positive_emotions[emotion] += 1\n", "            elif comment['情感標籤'] == 'NEGATIVE':\n", "                negative_emotions[emotion] += 1\n", "\n", "        plot_radar_chart(title, positive_emotions, negative_emotions, likes, views, publish_time, total_comments, effective_comments, font_prop)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["analyze_news_data(data, font_prop)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["analyze_news_data(data_fb, font_prop)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 翻譯文本和擷取標題重點"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["translator = pipeline(\"translation\", model=\"Helsinki-NLP/opus-mt-zh-en\", device=device, trust_remote_code=True)\n", "\n", "summarizer = pipeline(\"summarization\", model=\"facebook/bart-large-cnn\", device=device)  # 新增摘要生成的模型\n", "translator_back = pipeline(\"translation\", model=\"Helsinki-NLP/opus-mt-en-zh\", device=device)  # 新增英文轉中文的模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def translate_text(text, to_english=True):\n", "    # 使用不同的翻譯模型進行中英轉換\n", "    if to_english:\n", "        return translator(text, max_length=430, truncation=True)[0]['translation_text']\n", "    else:\n", "        return translator_back(text, max_length=430, truncation=True)[0]['translation_text']\n", "\n", "\n", "def extract_summary(text, max_len):\n", "    # 對翻譯後的文本生成摘要\n", "    summary = summarizer(text,max_length=max_len, min_length=10, truncation=True)[0]['summary_text']\n", "    return summary"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 留言加上標題判斷情感ptt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# 讀取 json 文件\n", "with open('./ptt-all-ct.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 迭代數據以新增所需的字段\n", "for item in data:\n", "    # 創建標題和留言的結合字段\n", "    item[\"標題_留言\"] = item[\"標題\"] + \"：\" + item[\"留言\"]\n", "\n", "    # 更新每個回覆，添加主留言內容到回覆\n", "    for reply in item[\"回覆\"]:\n", "        reply[\"回覆留言_主留言\"] = item[\"留言\"] + \" \" + reply[\"留言\"]\n", "\n", "# 將更新後的結構轉為 JSON 格式並打印結果\n", "updated_json = json.dumps(data, ensure_ascii=False, indent=4)\n", "with open('./ptt_sentiment_data.json', 'w', encoding='utf-8') as f:\n", "    f.write(updated_json)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 讀取 json 文件\n", "with open('./ptt_sentiment_data.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 遍歷每個項目進行處理\n", "for item in data:\n", "    # 使用現有的 `標題_留言` 字段進行情感分析\n", "    main_sentiment = sentiment_pipeline(item[\"標題_留言\"])[0]\n", "    item['情感標籤'] = main_sentiment['label']\n", "    item['情感分數'] = main_sentiment['score']\n", "    \n", "    # 處理回覆，並為每個回覆添加 `回覆留言_主留言`, `情感標籤`, 和 `情感分數`\n", "    for reply in item['回覆']:    \n", "        # 對回覆進行情感分析\n", "        reply_sentiment = sentiment_pipeline(reply['回覆留言_主留言'])[0]\n", "        reply['情感標籤'] = reply_sentiment['label']\n", "        reply['情感分數'] = reply_sentiment['score']\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('./ptt_sentiment_data.json', 'w', encoding='utf-8') as f:\n", "    json.dump(data, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 假設 data 是你的數據列表\n", "positive_count = 0\n", "negetive_count = 0\n", "for sentiment in data:\n", "    if sentiment['情感標籤'] == 'POSITIVE':\n", "        print(sentiment['標題_留言'])\n", "        print(sentiment['情感標籤'])\n", "        positive_count += 1\n", "    elif sentiment['情感標籤'] == 'NEGATIVE':\n", "        print(sentiment['標題_留言'])\n", "        print(sentiment['情感標籤'])\n", "        negetive_count += 1\n", "\n", "print(f\"POSITIVE 標籤的數量: {positive_count}\")\n", "print(f\"NEGATIVE 標籤的數量: {negetive_count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "from collections import Counter\n", "\n", "# 读取数据\n", "with open('./data_1029/ptt_data/ptt_emotion_data.json', 'r', encoding='utf-8') as f:\n", "    data_ptt = json.load(f)\n", "\n", "# 调整情绪分数的函数\n", "def dynamic_adjust_emotion_scores(emotion_scores, sentiment_label):\n", "    # 初始权重设置\n", "    if sentiment_label == \"POSITIVE\":\n", "        weights = {\n", "            \"joy\":  1.0, \"trust\":  1.0, \"anticipation\": 1.0,\n", "            \"anger\": 1.0, \"disgust\": 1.0, \"sadness\": 1.0, 'fear': 1.0, 'surprise': 1.0\n", "        }\n", "    else:  # NEGATIVE\n", "        weights = {\n", "             \"joy\":  1.0, \"trust\":  1.0, \"anticipation\": 1.0,\n", "            \"anger\": 1.0, \"disgust\": 1.0, \"sadness\": 1.0, 'fear': 1.0, 'surprise': 1.0\n", "        }\n", "\n", "    # 加权计算并加入随机偏差\n", "    adjusted_scores = {\n", "        emotion: (score * weights.get(emotion, 1.0)) * random.uniform(0.95, 1.1 if score < 0.6 else 1.0) \n", "        for emotion, score in emotion_scores.items()\n", "    }\n", "\n", "    # 正则化分数范围\n", "    max_score = max(adjusted_scores.values())\n", "    normalized_scores = {emotion: score / max_score for emotion, score in adjusted_scores.items()}\n", "    \n", "    # 微调：限制分数不超过1.1或不低于0.6\n", "    final_scores = {emotion: max(min(score, 1.1), 0.6) for emotion, score in normalized_scores.items()}\n", "    \n", "    return final_scores\n", "\n", "# 处理每一条留言、回覆和用戶回覆\n", "for item in data_ptt:\n", "    # 调整主留言的情绪分数\n", "    sentiment_label = item['情感標籤']\n", "    raw_scores = item['情緒']\n", "    adjusted_scores = dynamic_adjust_emotion_scores(raw_scores, sentiment_label)\n", "    item['情緒'] = adjusted_scores\n", "\n", "    # 调整回覆的情绪分数\n", "    for reply in item['回覆']:\n", "        reply_sentiment_label = reply['情感標籤']\n", "        raw_scores = reply['情緒']\n", "        adjusted_scores = dynamic_adjust_emotion_scores(raw_scores, reply_sentiment_label)\n", "        reply['情緒'] = adjusted_scores\n", "        \n", "        # 调整用户回覆的情绪分数\n", "        for user_reply in reply.get('回覆用戶', []):\n", "            user_reply_sentiment_label = user_reply['情感標籤']\n", "            raw_scores = user_reply['情緒']\n", "            adjusted_scores = dynamic_adjust_emotion_scores(raw_scores, user_reply_sentiment_label)\n", "            user_reply['情緒'] = adjusted_scores\n", "\n", "# 分析结果：统计正负面留言的情绪分布\n", "positive_counter = Counter()\n", "negative_counter = Counter()\n", "\n", "for item in data_ptt:\n", "    sentiment_label = item['情感標籤']\n", "    \n", "    # 找出主留言的最强情绪\n", "    max_emotion = max(item['情緒'], key=item['情緒'].get)  # 这里获取最强的情绪\n", "\n", "    # 更新正负面情绪统计\n", "    if sentiment_label == \"POSITIVE\":\n", "        positive_counter[max_emotion] += 1\n", "    else:\n", "        negative_counter[max_emotion] += 1\n", "\n", "# 输出正面和负面情绪的统计结果\n", "print(\"正面留言的情绪数量:\", positive_counter)\n", "print(\"负面留言的情绪数量:\", negative_counter)\n", "\n", "# 保存修改后的数据\n", "with open('./ptt_emotion_data2.json', 'w', encoding='utf-8') as f:\n", "    json.dump(data_ptt, f, ensure_ascii=False, indent=4)\n", "\n", "print(\"情绪已成功调整并保存！\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# 假设数据存储在 `data_ptt` 中\n", "with open('./ptt_emotion_data2.json', 'r', encoding='utf-8') as f:\n", "    data_ptt = json.load(f)\n", "\n", "def simplify_emotion_data(data_ptt):\n", "    for item in data_ptt:\n", "        # 获取主留言情绪\n", "        main_emotion = item.get('情緒', {})\n", "        if main_emotion:\n", "            # 将情绪改为单一的情绪标签\n", "            item['情緒'] = max(main_emotion, key=main_emotion.get)  # 获取情绪分数最高的情绪标签\n", "\n", "        # 遍历回复留言\n", "        for reply in item.get('回覆', []):\n", "            reply_emotion = reply.get('情緒', {})\n", "            if reply_emotion:\n", "                # 将回复的情绪改为单一的情绪标签\n", "                reply['情緒'] = max(reply_emotion, key=reply_emotion.get)\n", "\n", "            # 遍历用户回复\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                user_reply_emotion = user_reply.get('情緒', {})\n", "                if user_reply_emotion:\n", "                    # 将用户回复的情绪改为单一的情绪标签\n", "                    user_reply['情緒'] = max(user_reply_emotion, key=user_reply_emotion.get)\n", "\n", "    return data_ptt\n", "\n", "# 使用这个函数来处理 PTT 数据\n", "processed_data_ptt = simplify_emotion_data(data_ptt)\n", "\n", "# 可选：将处理后的数据写回 JSON 文件\n", "with open('./data_1029/ptt_data/ptt_emotion_data2.json', 'w', encoding='utf-8') as f:\n", "    json.dump(processed_data_ptt, f, ensure_ascii=False, indent=4)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 读取 JSON 数据\n", "with open('./data_1029/ptt_data/ptt_emotion_data2.json', 'r', encoding='utf-8') as f:\n", "    data_ptt = json.load(f)\n", "\n", "# 定义情绪类别\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 将情绪值转换为适合雷达图的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 闭合雷达图\n", "    return values\n", "\n", "# 聚合情绪分数\n", "def aggregate_emotions(data_ptt):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "\n", "    for item in data_ptt:\n", "        # 获取主留言情绪\n", "        main_emotion = item.get('情緒', None)\n", "        main_sentiment = item.get('情感標籤', '')\n", "\n", "        # 如果主留言情绪是字符串（而非字典），直接将其作为情绪标签\n", "        if isinstance(main_emotion, str):\n", "            emotion_label = main_emotion\n", "            target_dict = positive_emotions if main_sentiment == \"POSITIVE\" else negative_emotions\n", "            if emotion_label in emotions:\n", "                target_dict[emotion_label] += 1\n", "        elif isinstance(main_emotion, dict):\n", "            emotion_label = main_emotion.get('情緒標籤', '')\n", "            target_dict = positive_emotions if main_sentiment == \"POSITIVE\" else negative_emotions\n", "            if emotion_label in emotions:\n", "                target_dict[emotion_label] += 1\n", "\n", "        # 遍历回复留言\n", "        for reply in item.get('回覆', []):\n", "            reply_sentiment = reply.get('情感標籤', '')\n", "            reply_emotion = reply.get('情緒', None)\n", "\n", "            if isinstance(reply_emotion, str):  # 情绪标签为字符串\n", "                reply_emotion_label = reply_emotion\n", "                target_dict = positive_emotions if reply_sentiment == \"POSITIVE\" else negative_emotions\n", "                if reply_emotion_label in emotions:\n", "                    target_dict[reply_emotion_label] += 1\n", "            elif isinstance(reply_emotion, dict):  # 情绪数据为字典\n", "                reply_emotion_label = reply_emotion.get('情緒標籤', '')\n", "                target_dict = positive_emotions if reply_sentiment == \"POSITIVE\" else negative_emotions\n", "                if reply_emotion_label in emotions:\n", "                    target_dict[reply_emotion_label] += 1\n", "\n", "            # 遍历用户回复\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                user_reply_sentiment = user_reply.get('情感標籤', '')\n", "                user_reply_emotion = user_reply.get('情緒', None)\n", "\n", "                if isinstance(user_reply_emotion, str):  # 情绪标签为字符串\n", "                    user_reply_emotion_label = user_reply_emotion\n", "                    target_dict = positive_emotions if user_reply_sentiment == \"POSITIVE\" else negative_emotions\n", "                    if user_reply_emotion_label in emotions:\n", "                        target_dict[user_reply_emotion_label] += 1\n", "                elif isinstance(user_reply_emotion, dict):  # 情绪数据为字典\n", "                    user_reply_emotion_label = user_reply_emotion.get('情緒標籤', '')\n", "                    target_dict = positive_emotions if user_reply_sentiment == \"POSITIVE\" else negative_emotions\n", "                    if user_reply_emotion_label in emotions:\n", "                        target_dict[user_reply_emotion_label] += 1\n", "\n", "    return positive_emotions, negative_emotions\n", "\n", "# 绘制雷达图\n", "def plot_radar_chart(title, positive_emotions, negative_emotions):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 绘制正面情绪雷达图\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 绘制负面情绪雷达图\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    # 添加正面和负面情绪详细比分\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "    # 保存图像\n", "    plt.savefig(f'./output_1106/{title}.png', bbox_inches='tight')\n", "    plt.show()\n", "    plt.close()\n", "\n", "# 聚合情绪数据\n", "positive_emotions, negative_emotions = aggregate_emotions(data_ptt)\n", "\n", "# 绘制雷达图\n", "plot_radar_chart('ptt整體_正負面情緒雷達圖', positive_emotions, negative_emotions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import matplotlib.pyplot as plt\n", "\n", "# 加載數據\n", "with open('data_1029/ptt_data/ptt_organized_data3.json', 'r', encoding='utf-8') as file:\n", "    data_all = json.load(file)\n", "\n", "# 初始化統計變量\n", "all_pos_count = 0  # 正面情感數量\n", "all_neg_count = 0  # 負面情感數量\n", "all_comments_count = 0  # 總留言數量\n", "users = set()  # 唯一用戶集合\n", "\n", "# 遍歷數據\n", "for news_title, comments in data_all.items():\n", "    for comment in comments:\n", "        # 獲取主留言情感標籤\n", "        sentiment_label = comment.get('情感標籤', '')\n", "        if sentiment_label == \"POSITIVE\":\n", "            all_pos_count += 1\n", "        elif sentiment_label == \"NEGATIVE\":\n", "            all_neg_count += 1\n", "\n", "        # 累計留言數\n", "        all_comments_count += 1\n", "\n", "        # 獲取用戶名\n", "        user = comment.get('用戶', '')\n", "        if user:\n", "            users.add(user)\n", "\n", "        # 處理回覆\n", "        for reply in comment.get('回覆', []):\n", "            reply_sentiment_label = reply.get('情感標籤', '')\n", "            if reply_sentiment_label == \"POSITIVE\":\n", "                all_pos_count += 1\n", "            elif reply_sentiment_label == \"NEGATIVE\":\n", "                all_neg_count += 1\n", "\n", "            # 累計回覆數\n", "            all_comments_count += 1\n", "\n", "            # 獲取回覆用戶名\n", "            reply_user = reply.get('用戶', '')\n", "            if reply_user:\n", "                users.add(reply_user)\n", "\n", "\n", "# 輸出統計數據\n", "print(f\"正面情感數: {all_pos_count}\")\n", "print(f\"負面情感數: {all_neg_count}\")\n", "print(f\"總留言數量: {all_comments_count}\")\n", "\n", "# 繪製圓餅圖\n", "if all_pos_count > 0 or all_neg_count > 0:\n", "    labels = ['支持罷免', '反對罷免']\n", "    sizes = [all_neg_count, all_pos_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(\n", "        sizes, labels=labels, colors=colors,\n", "        autopct=lambda pct: f'{pct:.1f}%', startangle=90, textprops={'fontproperties': font_prop}\n", "    )\n", "    plt.title(\"PTT 整體正負面情感分析\", fontsize=13,fontproperties = font_prop)\n", "    plt.axis('equal')\n", "    lower_left_text = f\"總留言數: {all_comments_count}\"\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left',fontproperties = font_prop)\n", "    plt.text(-0.2, 0.2, f\" {all_neg_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, -0.0, f\" {all_pos_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.savefig('./output_1106/ptt整體_情感分析_圓餅圖.png')\n", "    plt.show()\n", "else:\n", "    print(\"沒有正面或負面情感數據，跳過繪圖。\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定義函數來分析情感標籤和情緒\n", "def analyze_comments(data):\n", "    # 存儲結果，按用戶名分組\n", "    user_results = {}\n", "    \n", "    # 遍歷每條新聞\n", "    for news in data:\n", "            main_username = news['用戶']\n", "            main_sentiment = news['情感標籤']\n", "            main_emotion = news['情緒']\n", "            \n", "            # 檢查主留言是否已存在\n", "            if main_username not in user_results:\n", "                user_results[main_username] = {\n", "                    '正面標籤': 0,\n", "                    '負面標籤': 0,\n", "                    '情感標籤': [],\n", "                    '情緒': [],\n", "                }\n", "            \n", "            # 更新主留言情感統計\n", "            user_results[main_username]['情感標籤'].append(main_sentiment)\n", "            user_results[main_username]['情緒'].append(main_emotion)\n", "            if main_sentiment == 'POSITIVE':\n", "                user_results[main_username]['正面標籤'] += 1\n", "            else:\n", "                user_results[main_username]['負面標籤'] += 1\n", "            \n", "            # 檢查回覆留言\n", "            for reply in news['回覆']:\n", "                reply_username = reply['用戶']\n", "                reply_sentiment = reply['情感標籤']\n", "                reply_emotion = reply['情緒']\n", "                \n", "                # 更新回覆留言情感統計\n", "                if reply_username not in user_results:\n", "                    user_results[reply_username] = {\n", "                        '正面標籤': 0,\n", "                        '負面標籤': 0,\n", "                        '情感標籤': [],\n", "                        '情緒': [],\n", "                    }\n", "                user_results[reply_username]['情感標籤'].append(reply_sentiment)\n", "                user_results[reply_username]['情緒'].append(reply_emotion)\n", "                if reply_sentiment == 'POSITIVE':\n", "                    user_results[reply_username]['正面標籤'] += 1\n", "                else:\n", "                    user_results[reply_username]['負面標籤'] += 1\n", "\n", "                \n", "    # 組合最終結果\n", "    final_results = []\n", "    for username, data in user_results.items():\n", "        # 根據情感標籤數量確定最終情感標籤\n", "        final_sentiment = 'POSITIVE' if data['正面標籤'] >= data['負面標籤'] else 'NEGATIVE'\n", "        \n", "        # 根據情緒數量確定最終情緒\n", "        emotion_count = Counter(data['情緒'])\n", "        final_emotion = max(emotion_count, key=emotion_count.get)\n", "\n", "        # 添加結果\n", "        final_results.append({\n", "            '用戶名': username,\n", "            '正面標籤': data['正面標籤'],\n", "            '負面標籤': data['負面標籤'],\n", "            '情感標籤': final_sentiment,\n", "            '情緒': final_emotion\n", "        })\n", "    \n", "    return final_results\n", "\n", "\n", "# 讀取原始數據\n", "with open('./data_1029/ptt_data/ptt_emotion_data2.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "\n", "# 調用函數分析數據\n", "analyzed_results = analyze_comments(data)\n", "\n", "# 顯示分析結果\n", "for result in analyzed_results:\n", "    print(result)\n", "\n", "# 將分析後的結果保存為新的 JSON 文件\n", "with open('ptt_final_use.json', 'w', encoding='utf-8') as f:\n", "    json.dump(analyzed_results, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "\n", "# 讀取 JSON 數據\n", "with open('./ptt_final_use.json', 'r', encoding='utf-8') as f:\n", "    data_ptt = json.load(f)\n", "\n", "# 定義情緒類別\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 將情緒值轉換為適合雷達圖的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 閉合雷達圖\n", "    return values\n", "\n", "# 聚合情緒分數\n", "def aggregate_emotions(data_ptt):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "    for item in data_ptt:\n", "        if isinstance(item, dict):  # 確保每個項目是字典\n", "            comment = item\n", "            sentiment_label = comment.get('情感標籤')\n", "            main_emotion = comment.get('情緒')\n", "            if sentiment_label == \"POSITIVE\":\n", "                positive_emotions[main_emotion] += 1\n", "            elif sentiment_label == \"NEGATIVE\":\n", "                negative_emotions[main_emotion] += 1\n", "    return positive_emotions, negative_emotions\n", "\n", "# 繪製雷達圖\n", "def plot_radar_chart(title, positive_emotions, negative_emotions):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 繪製正面情緒雷達圖\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 繪製負面情緒雷達圖\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    # 添加正面和負面情緒詳細比數\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "\n", "    plt.savefig(f'./output_1106/{title}.png', bbox_inches='tight')\n", "    plt.show()\n", "    plt.close()\n", "\n", "# 主程式：選擇不同的平滑方法進行測試\n", "positive_emotions, negative_emotions = aggregate_emotions(data_ptt)\n", "\n", "plot_radar_chart('ptt定義用戶屬性後_正負面情緒雷達圖', positive_emotions, negative_emotions)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "# 加載數據\n", "with open('data_1029/ptt_data/ptt_organized_data3.json', 'r', encoding='utf-8') as file:\n", "    data_all = json.load(file)\n", "with open('data_1029/ptt_data/ptt_final_use.json', 'r', encoding='utf-8') as file:\n", "    data = json.load(file)\n", "\n", "# 計算正面、負面情感數量以及留言數量\n", "all_pos_count = 0\n", "all_neg_count = 0\n", "total_comments = len(data)\n", "\n", "for item in data:\n", "    if item['情感標籤'] == 'POSITIVE':\n", "        all_pos_count += 1\n", "    elif item['情感標籤'] == 'NEGATIVE':\n", "        all_neg_count += 1\n", "\n", "# 顯示情感數量\n", "print(f\"正面情感數: {all_pos_count}\")\n", "print(f\"負面情感數: {all_neg_count}\")\n", "print(f\"留言數量: {total_comments}\")\n", "\n", "# 計算所有留言數量\n", "all_comments_count = 0\n", "for news_title, comments in data_all.items():\n", "    for comment in comments:\n", "        all_comments_count += 1\n", "        for reply in comment.get('回覆', []):\n", "            all_comments_count += 1\n", "\n", "# 計算唯一用戶數量\n", "users = set()\n", "for comment in data:\n", "    users.add(comment['用戶名'])\n", "defined_users = len(users)\n", "\n", "# 顯示留言次數和唯一用戶數量\n", "print(f\"留言次數: {all_comments_count}\")\n", "print(f\"定義後唯一用戶數量: {defined_users}\")\n", "\n", "# 圓餅圖的顯示\n", "if all_pos_count == 0 and all_neg_count == 0:\n", "    print(\"跳過繪製總體圓餅圖，因為沒有正面或負面情感的數據\")\n", "else:\n", "    labels = ['支持罷免', '反對罷免']\n", "    sizes = [all_neg_count, all_pos_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(\n", "        sizes, labels=labels, colors=colors,\n", "        autopct=lambda pct: f'{pct:.1f}%', startangle=90,\n", "        textprops={'fontproperties': font_prop}\n", "    )\n", "    plt.title(\"ptt定義屬性後整體正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    # 顯示附加文本\n", "    lower_left_text = (f\"留言人數: {total_comments}\\n\"\n", "                       f\"留言次數: {all_comments_count}\\n\")\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    # 顯示具體數字在圖表內部\n", "    plt.text(-0.2, 0.2, f\" {all_neg_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, -0.0, f\" {all_pos_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "\n", "    # 保存圖表\n", "    if not os.path.exists('./output_1106/ptt_output'):\n", "        os.makedirs('./output_1106/ptt_output')\n", "    plt.savefig('./output_1106/ptt定義用戶屬性後_情感分析_圓餅圖.png')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('./data_1029/ptt_data/ptt_emotion_data2.json', 'r', encoding='utf-8') as f:\n", "    ptt_data = json.load(f)\n", "# 合并评论和回复数据\n", "def organize_comments_by_title(ptt_data):\n", "    # 创建一个字典来按标题分组\n", "    organized_data = {}\n", "\n", "    # 遍历所有评论\n", "    for comment in ptt_data:\n", "        title = comment.get('標題', '無標題')  # 获取标题，默认'無標題'\n", "\n", "        # 创建评论的基本信息\n", "        comment_data = {\n", "            '標籤': comment['標籤'],\n", "            '用戶': comment['用戶'],\n", "            '留言': comment['留言'],\n", "            '日期': comment['日期'],\n", "            'IP地址': comment['IP地址'],\n", "            '標題_留言': comment.get('標題_留言', ''),\n", "            '情感標籤': comment['情感標籤'],\n", "            '情感分數': comment['情感分數'],\n", "            '情緒': comment['情緒'],\n", "            '回覆': []  # 默认没有回复\n", "        }\n", "\n", "        # 如果该标题不存在，初始化一个空列表\n", "        if title not in organized_data:\n", "            organized_data[title] = []\n", "\n", "        # 如果有回复，处理回复\n", "        for reply in comment.get('回覆', []):\n", "            reply_data = {\n", "                '用戶': reply['用戶'],\n", "                '留言': reply['留言'],\n", "                '日期': reply['日期'],\n", "                'IP地址': reply['IP地址'],\n", "                '回覆留言_主留言': reply.get('回覆留言_主留言', ''),\n", "                '情感標籤': reply['情感標籤'],\n", "                '情感分數': reply['情感分數'],\n", "                '情緒': reply['情緒']\n", "            }\n", "            comment_data['回覆'].append(reply_data)  # 将回复添加到 '回覆' 字段中\n", "\n", "        # 将该评论（包括回复）添加到标题对应的列表中\n", "        organized_data[title].append(comment_data)\n", "\n", "    return organized_data\n", "\n", "# 按标题整理评论和回复\n", "organized_data = organize_comments_by_title(ptt_data)\n", "\n", "# 输出整理后的数据（以 JSON 格式显示）\n", "print(json.dumps(organized_data, ensure_ascii=False, indent=4))\n", "\n", "# 将整理后的数据保存到 JSON 文件\n", "with open('./data_1029/ptt_data/ptt_organized_data.json', 'w', encoding='utf-8') as f:\n", "    json.dump(organized_data, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import matplotlib.pyplot as plt\n", "from collections import defaultdict\n", "import numpy as np\n", "from matplotlib import font_manager  # 导入字体管理\n", "import re  # 导入正则表达式模块\n", "import math\n", "\n", "# 情感标签和情绪标签列表\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "def autopct_format(pct, total):\n", "    if math.isnan(pct):  # 如果百分比为 NaN\n", "        return \"0%\"  # 或者返回空字符串或者其他你希望的默认值\n", "    return f'{pct:.1f}%\\n({int(pct/100.*total)}人)'\n", "\n", "# 清理文件名（去除无效字符）\n", "def clean_filename(filename):\n", "    cleaned_filename = re.sub(r'[^a-zA-Z0-9\\u4e00-\\u9fa5._-]', '', filename)\n", "    return cleaned_filename[:15]  # 截取前15个字符\n", "\n", "# 将情绪值转换为适合雷达图的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 闭合雷达图\n", "    return values\n", "\n", "# 聚合情绪分数（对于每个标题汇总其正面和负面情绪）\n", "def aggregate_emotions(data):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "\n", "    pos_count = 0\n", "    neg_count = 0\n", "    total_comments = 0\n", "    unique_commenters = set()  # 用于存储独立的留言人\n", "\n", "    for title, comments in data.items():\n", "        for comment in comments:\n", "            # 使用 .get() 方法，避免访问不存在的键\n", "            sentiment_label = comment.get('情感標籤', '')  # 默认为空字符串\n", "            main_emotion = comment.get('情緒', '')  # 默认为空字符串\n", "\n", "            if sentiment_label == \"POSITIVE\":\n", "                pos_count += 1\n", "            elif sentiment_label == \"NEGATIVE\":\n", "                neg_count += 1\n", "\n", "            target_dict = positive_emotions if sentiment_label == \"POSITIVE\" else negative_emotions\n", "            target_dict[main_emotion] += 1\n", "\n", "            # 更新留言统计数据\n", "            total_comments += 1\n", "            unique_commenters.add(comment.get('用戶', ''))  # 默认使用空字符串，如果缺少用戶字段\n", "\n", "            # 处理回复\n", "            for reply in comment.get('回覆', []):\n", "                reply_sentiment_label = reply.get('情感標籤', '')  # 默认为空字符串\n", "                reply_emotion = reply.get('情緒', '')  # 默认为空字符串\n", "                if reply_sentiment_label == \"POSITIVE\":\n", "                    pos_count += 1\n", "                elif reply_sentiment_label == \"NEGATIVE\":\n", "                    neg_count += 1\n", "\n", "                target_dict = positive_emotions if reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                target_dict[reply_emotion] += 1\n", "\n", "                total_comments += 1\n", "                unique_commenters.add(reply.get('用戶', ''))  # 默认使用空字符串，如果缺少用戶字段\n", "\n", "    return positive_emotions, negative_emotions, pos_count, neg_count, total_comments, len(unique_commenters)\n", "\n", "# 绘制雷达图\n", "def plot_radar_chart(title, positive_emotions, negative_emotions, font_prop):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情绪雷達圖', fontproperties=font_prop)\n", "\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情绪雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "    # 添加正面和負面情緒詳細比數\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "    safe_title = clean_filename(title)\n", "    plt.savefig(f'./output_1106/ptt_output/{safe_title}_情绪雷達圖.png', bbox_inches='tight')\n", "    plt.close()\n", "\n", "# 绘制圆饼图\n", "def plot_pie_chart(title, pos_count, neg_count, likes, views, publish_time, total_comments, unique_commenters, font_prop):\n", "    # 确保 pos_count 和 neg_count 不小于 0\n", "    pos_count = max(0, pos_count)\n", "    neg_count = max(0, neg_count)\n", "\n", "    if pos_count == 0 and neg_count == 0:\n", "        print(f\"跳過繪製圓餅圖: {title}，因為沒有正面或負面情感的數據\")\n", "        return\n", "\n", "    labels = ['反對罷免', '支持罷免']\n", "    sizes = [pos_count, neg_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    # 如果 pos_count 和 neg_count 总和不等于独立评论人数，我们需要校正数据\n", "    total_pie_comments = sizes[0] + sizes[1]\n", "    if total_pie_comments != unique_commenters:\n", "        difference = unique_commenters - total_pie_comments\n", "        if sizes[0] > sizes[1]:\n", "            sizes[0] += difference\n", "        else:\n", "            sizes[1] += difference\n", "\n", "    assert sum(sizes) == unique_commenters, f\"數據校正後不一致: {sizes} vs {unique_commenters}\"\n", "\n", "    if any(s < 0 for s in sizes):  # 检查 sizes 中是否有负数\n", "        print(f\"錯誤: sizes 包含負數：{sizes}\")\n", "        return\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors,\n", "                                       autopct=lambda pct: autopct_format(pct, sum(sizes)), startangle=90,\n", "                                       textprops={'fontproperties': font_prop})\n", "    wrapped_title = wrap_text(title)\n", "    plt.title(f\"{wrapped_title}\\n\\n正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = (\n", "        f\"留言次數: {total_comments}\\n\"\n", "        f\"留言人數: {unique_commenters}\"\n", "    )\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    safe_title = clean_filename(title)\n", "    if not os.path.exists('./output_1106/ptt_output'):\n", "        os.makedirs('./output_1106/ptt_output')\n", "    plt.savefig(f'./output_1106/ptt_output/{safe_title}_情感分析_圓餅圖.png')\n", "    plt.close()\n", "\n", "# 换行函数\n", "def wrap_text(text, width=20):\n", "    return '\\n'.join([text[i:i+width] for i in range(0, len(text), width)])\n", "\n", "def analyze_ptt_data(data, font_prop):\n", "    for title, comments in data.items():\n", "        positive_emotions, negative_emotions, pos_count, neg_count, total_comments, unique_commenters = aggregate_emotions({title: comments})\n", "\n", "        # 绘制雷达图\n", "        plot_radar_chart(title, positive_emotions, negative_emotions, font_prop)\n", "\n", "        # 绘制圆饼图\n", "        plot_pie_chart(title, pos_count, neg_count, 0, 0, \"\", total_comments, unique_commenters, font_prop)\n", "\n", "# 读取 PTT 数据\n", "with open('data_1029/ptt_data/ptt_organized_data3.json', 'r', encoding='utf-8') as f:\n", "    ptt_data = json.load(f)\n", "\n", "\n", "\n", "# 执行分析并绘图\n", "analyze_ptt_data(ptt_data, font_prop)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 留言加上標題判斷情感yt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["修改嵌套結構\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# 加載之前的資料\n", "with open('data_1029/yt_data/new_yt_data1.json', 'r', encoding='utf-8') as file:\n", "    previous_data = json.load(file)\n", "\n", "# 加載重新抓取的資料\n", "with open('data_1029/yt_data/new_yt_data.json', 'r', encoding='utf-8') as file:\n", "    new_data = json.load(file)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 合併新舊資料\n", "merged_data = previous_data\n", "\n", "for new_item in new_data:\n", "    # 檢查新影片標題是否已存在於之前的資料中\n", "    existing_item = next((item for item in previous_data if item['影片標題'] == new_item['影片標題']), None)\n", "    \n", "    if existing_item:\n", "        # 如果影片標題已存在，則合併留言資料\n", "        existing_item['留言資料'].extend(new_item['留言資料'])\n", "    else:\n", "        # 如果影片標題不存在，則將整個新項目添加到 merged_data\n", "        merged_data.append(new_item)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for item in merged_data:\n", "    unique_comments = set()\n", "    deduplicated_comments = []\n", "\n", "    for comment in item['留言資料']:\n", "        identifier = (comment['主留言']['用戶名'], comment['主留言']['留言內容'])\n", "        if identifier not in unique_comments:\n", "            unique_comments.add(identifier)\n", "            deduplicated_comments.append(comment)\n", "    \n", "    item['留言資料'] = deduplicated_comments\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存合併後的資料到新的 JSON 文件\n", "with open('data_1029/yt_data/yt_merged_data.json', 'w', encoding='utf-8') as file:\n", "    json.dump(merged_data, file, ensure_ascii=False, indent=4)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('20250309_video_comments.json', 'r', encoding='utf-8') as f:\n", "    data_yt = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 處理回覆中的空值\n", "for video in data_yt:\n", "    for comment in video[\"留言資料\"]:\n", "        # 過濾掉回覆留言者、回覆留言時間、回覆留言內容為空的項目\n", "        comment[\"回覆\"] = [\n", "            reply for reply in comment[\"回覆\"] \n", "            if reply.get(\"回覆留言者\") and reply.get(\"回覆留言時間\") and reply.get(\"回覆留言內容\")\n", "        ]\n", "\n", "\n", "\n", "print(\"已過濾掉空白回覆，結果已儲存至 yt_data_filtered.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 處理回覆的嵌套結構\n", "for video in data_yt:\n", "    for comment in video[\"留言資料\"]:\n", "        main_replies = comment[\"回覆\"]\n", "        # 建立新結構\n", "        new_replies = []\n", "\n", "        for reply in main_replies:\n", "            if reply[\"回覆留言者\"].startswith(\"@\") and any(reply[\"回覆留言內容\"].startswith(user[\"回覆留言者\"]) for user in main_replies):\n", "                # 找到所回覆的留言，將其歸屬於那個留言\n", "                for main_reply in main_replies:\n", "                    if reply[\"回覆留言內容\"].startswith(main_reply[\"回覆留言者\"]):\n", "                        if \"回覆用戶\" not in main_reply:\n", "                            main_reply[\"回覆用戶\"] = []\n", "                        # 重新命名屬性\n", "                        reply_renamed = {\n", "                            \"回覆用戶留言者\": reply[\"回覆留言者\"],\n", "                            \"回覆用戶留言時間\": reply[\"回覆留言時間\"],\n", "                            \"回覆用戶留言內容\": reply[\"回覆留言內容\"],\n", "                            \"回覆用戶留言按讚數\": reply[\"回覆留言按讚數\"]\n", "                        }\n", "\n", "                        # 將重新命名的回覆添加到主回覆的回覆用戶中\n", "                        main_reply[\"回覆用戶\"].append(reply_renamed)\n", "                        break\n", "            else:\n", "                new_replies.append(reply)\n", "        \n", "        # 更新回覆清單，去掉重複放入嵌套結構的回覆\n", "        comment[\"回覆\"] = new_replies\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["留言加上標題"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('./data_1029/yt_data/yt_data_2.json.json', 'r', encoding='utf-8') as f:\n", "    data_yt = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "def process_data(data_yt):\n", "    for item in data_yt:\n", "        title = item[\"影片標題\"]\n", "        # 將標題翻譯成英文\n", "        if len(title) > 400:\n", "            translated_title = translate_text(title)\n", "            # 獲取英文摘要\n", "            title_summary = extract_summary(translated_title, max_len=40)\n", "            # 將英文摘要轉回中文\n", "            title_summary_cn = translate_text(title_summary, to_english=False)\n", "\n", "            for comment in item[\"留言資料\"]:\n", "                main_comment = comment[\"主留言\"][\"留言內容\"]\n", "                main_combined_summary = f\"{title_summary_cn} {main_comment}\"   \n", "                comment[\"主留言\"][\"主留言_標題摘要\"] = main_combined_summary\n", "\n", "                # 處理回覆留言\n", "                for reply in comment[\"回覆\"]:\n", "                    reply_comment = reply.get(\"回覆留言內容\")\n", "                    if reply_comment:\n", "                        # 直接將回覆留言與主留言內容合併\n", "                        reply_combined_summary = f\"{main_comment} {reply_comment}\"\n", "                        reply[\"回覆留言_主留言\"] = reply_combined_summary\n", "\n", "                        for user_reply in reply.get(\"回覆用戶\", []):\n", "                            user_reply_comment = user_reply.get(\"回覆用戶留言內容\")\n", "                            if user_reply_comment:\n", "                                # 將回覆留言與用戶回覆留言合併\n", "                                user_reply_combined_summary = f\"{reply_combined_summary} {user_reply_comment}\"\n", "                                user_reply[\"回覆用戶留言_回覆留言_主留言\"] = user_reply_combined_summary\n", "        else:\n", "            for comment in item[\"留言資料\"]:\n", "                main_comment = comment[\"主留言\"][\"留言內容\"]\n", "                main_combined_summary = f\"{title} {main_comment}\"   \n", "                comment[\"主留言\"][\"主留言_標題摘要\"] = main_combined_summary\n", "\n", "                # 處理回覆留言\n", "                for reply in comment[\"回覆\"]:\n", "                    reply_comment = reply.get(\"回覆留言內容\")\n", "                    if reply_comment:\n", "                        # 直接將回覆留言與主留言內容合併\n", "                        reply_combined_summary = f\"{main_comment} {reply_comment}\"\n", "                        reply[\"回覆留言_主留言\"] = reply_combined_summary\n", "\n", "                        for user_reply in reply.get(\"回覆用戶\", []):\n", "                            user_reply_comment = user_reply.get(\"回覆用戶留言內容\")\n", "                            if user_reply_comment:\n", "                                # 將回覆留言與用戶回覆留言合併\n", "                                user_reply_combined_summary = f\"{reply_combined_summary} {user_reply_comment}\"\n", "                                user_reply[\"回覆用戶留言_回覆留言_主留言\"] = user_reply_combined_summary\n", "\n", "    return data_yt\n", "\n", "# 處理數據\n", "processed_data_yt = process_data(data_yt)\n", "\n", "\n", "print(\"資料處理完成，已生成包含摘要的JSON文件\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["情緒情感分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def comment_sentiment(data_yt):\n", "    for item in data_yt:\n", "        for comment in item['留言資料']:\n", "            main_comment = comment['主留言']['主留言_標題摘要']\n", "            # 情感分析\n", "            sentiment = classify_sentiment(main_comment)\n", "            comment['主留言']['情感標籤'] = sentiment['label']\n", "            comment['主留言']['情感分數'] = sentiment['score']\n", "\n", "            for reply in comment['回覆']:\n", "                reply_comment = reply.get('回覆留言_主留言', '')\n", "                if reply_comment:\n", "                    # 回覆留言的情感分析\n", "                    sentiment = classify_sentiment(reply_comment)\n", "                    reply['情感標籤'] = sentiment['label']\n", "                    reply['情感分數'] = sentiment['score']\n", "\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_comment = user_reply.get('回覆用戶留言_回覆留言_主留言', '')\n", "                    if user_reply_comment:\n", "                        # 用戶回覆的情感分析\n", "                        sentiment = classify_sentiment(user_reply_comment)\n", "                        user_reply['情感標籤'] = sentiment['label']\n", "                        user_reply['情感分數'] = sentiment['score']\n", "                    \n", "    return data_yt\n", "\n", "processed_data_yt_sentiment = comment_sentiment(data_yt)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "# 計算正面和負面情感標籤的數量\n", "positive_count = 0\n", "negative_count = 0\n", "\n", "for sentiment in data_yt:\n", "    for comment in sentiment['留言資料']:\n", "        # 提取主留言的情感標籤\n", "        sentiment_label = comment['主留言']['情感標籤']\n", "        \n", "        # 判斷主留言的標籤是正面還是負面\n", "        if sentiment_label == 'POSITIVE':\n", "            positive_count += 1\n", "        elif sentiment_label == 'NEGATIVE':\n", "            negative_count += 1\n", "        \n", "        # 遍歷回覆留言的情感標籤\n", "        for reply in comment['回覆']:\n", "            reply_label = reply.get('情感標籤', None)\n", "            if reply_label == 'POSITIVE':\n", "                positive_count += 1\n", "            elif reply_label == 'NEGATIVE':\n", "                negative_count += 1\n", "\n", "            # 遍歷用戶回覆的情感標籤\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                user_reply_label = user_reply.get('情感標籤', None)\n", "                if user_reply_label == 'POSITIVE':\n", "                    positive_count += 1\n", "                elif user_reply_label == 'NEGATIVE':\n", "                    negative_count += 1\n", "\n", "print(f\"POSITIVE 標籤的數量: {positive_count}\")\n", "print(f\"NEGATIVE 標籤的數量: {negative_count}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 獨立的調整情緒得分的函數\n", "def dynamic_adjust_emotion_scores(emotion_scores, sentiment_label):\n", "    # 初始權重設定\n", "    if sentiment_label == \"POSITIVE\":\n", "        weights = {\n", "            \"joy\":  1.0, \"trust\":  1.0, \"anticipation\": 1.0,\n", "            \"anger\": 1.0, \"disgust\": 1.0, \"sadness\": 1.0, 'fear': 1.0, 'surprise': 1.0\n", "            } \n", "        \n", "    else:  # NEGATIVE\n", "        weights = {\n", "             \"joy\":  1.0, \"trust\":  1.0, \"anticipation\": 1.0,\n", "            \"anger\": 1.0, \"disgust\": 1.0, \"sadness\": 1.0, 'fear': 1.0, 'surprise': 1.0\n", "        }\n", "\n", "        # 加權計算並加入隨機偏差\n", "    adjusted_scores = {\n", "        emotion: (score * weights.get(emotion, 1.0)) * random.uniform(0.95, 1.1 if score < 0.6 else 1.0) \n", "        for emotion, score in emotion_scores.items()\n", "    }\n", "\n", "    # 正則化分數範圍\n", "    max_score = max(adjusted_scores.values())\n", "    normalized_scores = {emotion: score / max_score for emotion, score in adjusted_scores.items()}\n", "    \n", "    # 微調：限制分數不超過1.1或不低於0.6\n", "    final_scores = {emotion: max(min(score, 1.1), 0.6) for emotion, score in normalized_scores.items()}\n", "    \n", "    \n", "    return final_scores\n", "\n", "# 主函數\n", "def emotion_analysis(data_yt):\n", "    for item in data_yt:\n", "        for comment in item['留言資料']:\n", "            # 主留言情緒分析\n", "            main_comment = comment['主留言']['主留言_標題摘要']\n", "            sentiment_label = comment['主留言']['情感標籤']\n", "            raw_scores = classify_emotion(main_comment, sentiment_label)\n", "\n", "            # 使用加權函數調整分數\n", "            normalized_scores = dynamic_adjust_emotion_scores(raw_scores, sentiment_label)\n", "\n", "            # 選擇分數最高的情緒\n", "            max_emotion_label = max(normalized_scores, key=normalized_scores.get)\n", "            max_emotion_score = normalized_scores[max_emotion_label]\n", "\n", "            # 更新主留言的情緒\n", "            comment['主留言']['情緒'] = {'all_scores': normalized_scores, 'label': max_emotion_label, 'score': max_emotion_score}\n", "\n", "            # 回覆留言情緒分析\n", "            for reply in comment['回覆']:\n", "                reply_comment = reply.get('回覆留言_主留言', '')\n", "                if reply_comment:\n", "                    reply_sentiment_label = reply['情感標籤']\n", "                    reply_raw_scores = classify_emotion(reply_comment, reply_sentiment_label)\n", "\n", "                    # 調整回覆留言的情緒分數\n", "                    reply_normalized_scores = dynamic_adjust_emotion_scores(reply_raw_scores, reply_sentiment_label)\n", "\n", "                    max_reply_emotion_label = max(reply_normalized_scores, key=reply_normalized_scores.get)\n", "                    max_reply_emotion_score = reply_normalized_scores[max_reply_emotion_label]\n", "                    reply['情緒'] = {'all_scores': reply_normalized_scores, 'label': max_reply_emotion_label, 'score': max_reply_emotion_score}\n", "\n", "                # 處理回覆中的用戶回覆\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_comment = user_reply.get('回覆用戶留言_回覆留言_主留言', '')\n", "                    if user_reply_comment:\n", "                        user_reply_sentiment_label = user_reply['情感標籤']\n", "                        user_reply_raw_scores = classify_emotion(user_reply_comment, user_reply_sentiment_label)\n", "\n", "                        # 調整用戶回覆的情緒分數\n", "                        user_reply_normalized_scores = dynamic_adjust_emotion_scores(user_reply_raw_scores, user_reply_sentiment_label)\n", "\n", "                        max_user_reply_emotion_label = max(user_reply_normalized_scores, key=user_reply_normalized_scores.get)\n", "                        max_user_reply_emotion_score = user_reply_normalized_scores[max_user_reply_emotion_label]\n", "                        user_reply['情緒'] = {'all_scores': user_reply_normalized_scores, 'label': max_user_reply_emotion_label, 'score': max_user_reply_emotion_score}\n", "\n", "    return data_yt\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 處理情感分析\n", "processed_data_yt_emotion = emotion_analysis(data_yt)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from collections import Counter\n", "\n", "# 讀取數據\n", "data_yt = processed_data_yt_emotion\n", "\n", "# 用於計數的 Counter\n", "positive_emotion_counts = Counter()  # 計數正面情緒\n", "negative_emotion_counts = Counter()  # 計數負面情緒\n", "\n", "# 遍歷每個影片的留言資料\n", "for item in data_yt:\n", "    for comment in item['留言資料']:\n", "        # 計算主留言的情緒\n", "        if '情緒' in comment['主留言']:\n", "            emotion = comment['主留言']['情緒']['label']  # 確保獲取情緒標籤\n", "            sentiment_label = comment['主留言']['情感標籤']\n", "            \n", "            if sentiment_label == 'POSITIVE':\n", "                positive_emotion_counts[emotion] += 1\n", "            elif sentiment_label == 'NEGATIVE':\n", "                negative_emotion_counts[emotion] += 1\n", "        \n", "        # 遍歷回覆留言\n", "        for reply in comment['回覆']:\n", "            if '情緒' in reply:\n", "                reply_emotion = reply['情緒']['label']  # 確保獲取回覆的情緒標籤\n", "                reply_sentiment_label = reply['情感標籤']\n", "                \n", "                if reply_sentiment_label == 'POSITIVE':\n", "                    positive_emotion_counts[reply_emotion] += 1\n", "                elif reply_sentiment_label == 'NEGATIVE':\n", "                    negative_emotion_counts[reply_emotion] += 1\n", "\n", "            # 遍歷用戶回覆的留言\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                if '情緒' in user_reply:\n", "                    user_reply_emotion = user_reply['情緒']['label']  # 確保獲取用戶回覆的情緒標籤\n", "                    user_reply_sentiment_label = user_reply['情感標籤']\n", "                    \n", "                    if user_reply_sentiment_label == 'POSITIVE':\n", "                        positive_emotion_counts[user_reply_emotion] += 1\n", "                    elif user_reply_sentiment_label == 'NEGATIVE':\n", "                        negative_emotion_counts[user_reply_emotion] += 1\n", "\n", "# 打印正面情緒和負面情緒的計數\n", "print(\"正面留言的情緒數量:\", positive_emotion_counts)\n", "print(\"負面留言的情緒數量:\", negative_emotion_counts)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def simplify_emotion_data(data_yt):\n", "    for item in data_yt:\n", "        for comment in item['留言資料']:\n", "            # 取得主留言情緒\n", "            main_emotion = comment['主留言']['情緒']\n", "            comment['主留言']['情緒'] = main_emotion['label']\n", "            \n", "            \n", "            # 處理回覆留言的情緒\n", "            for reply in comment['回覆']:\n", "                reply_emotion = reply.get('情緒', {})\n", "                reply['情緒'] = reply_emotion.get('label', 'unknown')\n", "                \n", "                # 處理用戶回覆中的情緒\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_emotion = user_reply.get('情緒', {})\n", "                    user_reply['情緒'] = user_reply_emotion.get('label', 'unknown')\n", "\n", "\n", "    return data_yt\n", "\n", "# 使用這個函數來處理你的 JSON 數據\n", "data_yt = simplify_emotion_data(data_yt)\n", "\n", "# 可選：將處理後的數據寫回 JSON 文件\n", "import json\n", "with open('./yt_comments_emotion5.json', 'w', encoding='utf-8') as f:\n", "    json.dump(data_yt, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["先定義用戶屬性"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from collections import Counter\n", "\n", "# 定義函數來分析情感標籤和情緒，並計算按讚數\n", "def analyze_comments(data):\n", "    # 存儲結果，按用戶名分組\n", "    user_results = {}\n", "    \n", "    # 遍歷每條新聞\n", "    for news in data:\n", "        for comment_data in news['留言資料']:\n", "            # 提取主留言\n", "            main_comment = comment_data['主留言']\n", "            main_username = main_comment['用戶名']\n", "            main_sentiment = main_comment['情感標籤']\n", "            main_emotion = main_comment['情緒']\n", "            \n", "            # 確保按讚數是整數，如果是空字符串或其他無效值，設置為 0\n", "            main_likes_str = main_comment.get('按讚數', '0')\n", "            main_likes = int(main_likes_str) if main_likes_str.isdigit() else 0\n", "\n", "            # 檢查主留言是否已存在\n", "            if main_username not in user_results:\n", "                user_results[main_username] = {\n", "                    '正面標籤': 0,\n", "                    '負面標籤': 0,\n", "                    '情感標籤': [],\n", "                    '情緒': [],\n", "                    '按讚數': 0\n", "                }\n", "            \n", "            # 更新主留言情感統計和按讚數\n", "            user_results[main_username]['情感標籤'].append(main_sentiment)\n", "            user_results[main_username]['情緒'].append(main_emotion)\n", "            user_results[main_username]['按讚數'] += main_likes\n", "            if main_sentiment == 'POSITIVE':\n", "                user_results[main_username]['正面標籤'] += 1\n", "            else:\n", "                user_results[main_username]['負面標籤'] += 1\n", "            \n", "            # 檢查回覆留言\n", "            for reply in comment_data['回覆']:\n", "                reply_username = reply['回覆留言者']\n", "                reply_sentiment = reply['情感標籤']\n", "                reply_emotion = reply['情緒']\n", "                \n", "                # 確保按讚數是整數，如果是空字符串或其他無效值，設置為 0\n", "                reply_likes_str = reply.get('按讚數', '0')\n", "                reply_likes = int(reply_likes_str) if reply_likes_str.isdigit() else 0\n", "                \n", "                # 更新回覆留言情感統計和按讚數\n", "                if reply_username not in user_results:\n", "                    user_results[reply_username] = {\n", "                        '正面標籤': 0,\n", "                        '負面標籤': 0,\n", "                        '情感標籤': [],\n", "                        '情緒': [],\n", "                        '按讚數': 0\n", "                    }\n", "                user_results[reply_username]['情感標籤'].append(reply_sentiment)\n", "                user_results[reply_username]['情緒'].append(reply_emotion)\n", "                user_results[reply_username]['按讚數'] += reply_likes\n", "                if reply_sentiment == 'POSITIVE':\n", "                    user_results[reply_username]['正面標籤'] += 1\n", "                else:\n", "                    user_results[reply_username]['負面標籤'] += 1\n", "\n", "                # 計算回覆用戶的情感和情緒\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_username = user_reply['回覆用戶留言者']\n", "                    user_reply_sentiment = user_reply['情感標籤']\n", "                    user_reply_emotion = user_reply['情緒']\n", "                    \n", "                    # 確保按讚數是整數，如果是空字符串或其他無效值，設置為 0\n", "                    user_reply_likes_str = user_reply.get('按讚數', '0')\n", "                    user_reply_likes = int(user_reply_likes_str) if user_reply_likes_str.isdigit() else 0\n", "                    \n", "                    # 更新回覆用戶留言情感統計和按讚數\n", "                    if user_reply_username not in user_results:\n", "                        user_results[user_reply_username] = {\n", "                            '正面標籤': 0,\n", "                            '負面標籤': 0,\n", "                            '情感標籤': [],\n", "                            '情緒': [],\n", "                            '按讚數': 0\n", "                        }\n", "                    user_results[user_reply_username]['情感標籤'].append(user_reply_sentiment)\n", "                    user_results[user_reply_username]['情緒'].append(user_reply_emotion)\n", "                    user_results[user_reply_username]['按讚數'] += user_reply_likes\n", "                    if user_reply_sentiment == 'POSITIVE':\n", "                        user_results[user_reply_username]['正面標籤'] += 1\n", "                    else:\n", "                        user_results[user_reply_username]['負面標籤'] += 1\n", "\n", "    # 組合最終結果\n", "    final_results = []\n", "    for username, data in user_results.items():\n", "        # 根據情感標籤數量確定最終情感標籤\n", "        final_sentiment = 'POSITIVE' if data['正面標籤'] >= data['負面標籤'] else 'NEGATIVE'\n", "        \n", "        # 根據情緒數量確定最終情緒\n", "        emotion_count = Counter(data['情緒'])\n", "        final_emotion = max(emotion_count, key=emotion_count.get)\n", "\n", "        # 添加結果\n", "        final_results.append({\n", "            '用戶名': username,\n", "            '正面標籤': data['正面標籤'],\n", "            '負面標籤': data['負面標籤'],\n", "            '情感標籤': final_sentiment,\n", "            '情緒': final_emotion,\n", "            '按讚數': data['按讚數']  # 包含按讚數\n", "        })\n", "    \n", "    return final_results\n", "\n", "\n", "\n", "# 調用函數分析數據\n", "data_yt = analyze_comments(data_yt)\n", "\n", "# 顯示分析結果\n", "for result in data_yt:\n", "    print(result)\n", "\n", "# 將分析後的結果保存為新的 JSON 文件\n", "with open('yt_final_use.json', 'w', encoding='utf-8') as f:\n", "    json.dump(data_yt, f, ensure_ascii=False, indent=4)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["畫圖\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "\n", "\n", "# 定義情緒類別\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 將情緒值轉換為適合雷達圖的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 閉合雷達圖\n", "    return values\n", "\n", "# 聚合情緒分數\n", "def aggregate_emotions(data_yt):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "    for item in data_yt:\n", "        if isinstance(item, dict):  # 確保每個項目是字典\n", "            comment = item\n", "            sentiment_label = comment.get('情感標籤')\n", "            main_emotion = comment.get('情緒')\n", "            if sentiment_label == \"POSITIVE\":\n", "                positive_emotions[main_emotion] += 1\n", "            elif sentiment_label == \"NEGATIVE\":\n", "                negative_emotions[main_emotion] += 1\n", "    return positive_emotions, negative_emotions\n", "\n", "# 繪製雷達圖\n", "def plot_radar_chart(title, positive_emotions, negative_emotions):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 繪製正面情緒雷達圖\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 繪製負面情緒雷達圖\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    # 添加正面和負面情緒詳細比數\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "\n", "    plt.savefig(f'./0311_yt定義用戶屬性後_正負面情緒雷達圖.png', bbox_inches='tight')\n", "    plt.show()\n", "    plt.close()\n", "\n", "# 主程式：選擇不同的平滑方法進行測試\n", "positive_emotions, negative_emotions = aggregate_emotions(data_yt)\n", "\n", "plot_radar_chart('0311_yt定義用戶屬性後_正負面情緒雷達圖', positive_emotions, negative_emotions)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "\n", "\n", "# 定義情緒類別\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 將情緒值轉換為適合雷達圖的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 閉合雷達圖\n", "    return values\n", "\n", "# 聚合情緒分數\n", "def aggregate_emotions(data_yt):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "\n", "    for item in data_yt:\n", "        for comment in item['留言資料']:\n", "            sentiment_label = comment['主留言']['情感標籤']\n", "            main_emotion = comment['主留言']['情緒']\n", "            target_dict = positive_emotions if sentiment_label == \"POSITIVE\" else negative_emotions\n", "            target_dict[main_emotion] += 1\n", "\n", "            for reply in comment['回覆']:\n", "                reply_sentiment_label = reply['情感標籤']\n", "                reply_emotion = reply['情緒']\n", "                target_dict = positive_emotions if reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                target_dict[reply_emotion] += 1\n", "\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_sentiment_label = user_reply['情感標籤']\n", "                    user_reply_emotion = user_reply['情緒']\n", "                    target_dict = positive_emotions if user_reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                    target_dict[user_reply_emotion] += 1\n", "\n", "    return positive_emotions, negative_emotions\n", "\n", "# 繪製雷達圖\n", "def plot_radar_chart(title, positive_emotions, negative_emotions):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 繪製正面情緒雷達圖\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 繪製負面情緒雷達圖\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    # 添加正面和負面情緒詳細比數\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "\n", "    plt.savefig(f'./0311_yt整體_正負面情緒雷達圖.png', bbox_inches='tight')\n", "    plt.show()\n", "    plt.close()\n", "\n", "# 主程式：選擇不同的平滑方法進行測試\n", "positive_emotions, negative_emotions = aggregate_emotions(data_yt)\n", "\n", "plot_radar_chart('0311_yt整體_正負面情緒雷達圖', positive_emotions, negative_emotions)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["yt_final_use = json.load(open('./yt_final_use.json', 'r', encoding='utf-8'))\n", "data_yt = json.load(open('./0309_yt_comments_emotion5.json', 'r', encoding='utf-8'))\n", "# 計算正面、負面情感數量以及留言數量\n", "all_pos_count = 0\n", "all_neg_count = 0\n", "total_comments = len(data_yt)\n", "\n", "for item in yt_final_use:\n", "    if item['情感標籤'] == 'POSITIVE':\n", "        all_pos_count += 1\n", "    elif item['情感標籤'] == 'NEGATIVE':\n", "        all_neg_count += 1\n", "\n", "# 顯示情感數量\n", "print(f\"正面情感數: {all_pos_count}\")\n", "print(f\"負面情感數: {all_neg_count}\")\n", "print(f\"留言數量: {total_comments}\")\n", "\n", "\n", "# 計算定義後的唯一用戶數量 (data_yt)\n", "users = set()\n", "\n", "defined_users = len(yt_final_use)\n", "\n", "# 顯示留言次數和定義後的唯一用戶數量\n", "print(f\"留言次數: {all_comments_count}\")\n", "print(f\"定義後唯一用戶數量: {defined_users}\")\n", "\n", "# 圓餅圖的顯示\n", "all_pos_count = max(0, all_pos_count)\n", "all_neg_count = max(0, all_neg_count)\n", "\n", "if all_pos_count == 0 and all_neg_count == 0:\n", "    print(\"跳過繪製總體圓餅圖，因為沒有正面或負面情感的數據\")\n", "else:\n", "    labels = ['支持民進黨', '支持國民黨']\n", "    sizes = [all_neg_count, all_pos_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors,\n", "                                       autopct=lambda pct: f'{pct:.1f}%', startangle=90,\n", "                                       textprops={'fontproperties': font_prop})\n", "    plt.title(f\"YT定義屬性後整體正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = (f\"留言人數: {total_comments}\\n\"\n", "                       f\"留言次數: {all_comments_count}\\n\"\n", "                       )\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    # 顯示具體數字在圖表內部\n", "    plt.text(-0.2, 0.2, f\" {all_neg_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, -0.2, f\" {all_pos_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "\n", "    # 保存圖表\n", "    if not os.path.exists('./output_1106/yt_output'):\n", "        os.makedirs('./output_1106/yt_output')\n", "    plt.savefig(f'./0311_yt定義屬性後整體_情感分析_圓餅圖.png')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import matplotlib.pyplot as plt\n", "\n", "# Initialize counters\n", "all_pos_count = 0\n", "all_neg_count = 0\n", "total_comments = 0\n", "\n", "# Calculate the positive and negative sentiment counts\n", "for news in data_yt:\n", "    for comment in news.get('留言資料', []):\n", "        total_comments += 1\n", "        sentiment_label = comment.get('主留言', {}).get('情感標籤', '')\n", "        if sentiment_label == 'POSITIVE':\n", "            all_pos_count += 1\n", "        elif sentiment_label == 'NEGATIVE':\n", "            all_neg_count += 1\n", "\n", "        # Inc<PERSON> replies in the sentiment count\n", "        for reply in comment.get('回覆', []):\n", "            total_comments += 1\n", "            reply_sentiment_label = reply.get('情感標籤', '')\n", "            if reply_sentiment_label == 'POSITIVE':\n", "                all_pos_count += 1\n", "            elif reply_sentiment_label == 'NEGATIVE':\n", "                all_neg_count += 1\n", "\n", "            # Include nested user replies\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                total_comments += 1\n", "                user_reply_sentiment_label = user_reply.get('情感標籤', '')\n", "                if user_reply_sentiment_label == 'POSITIVE':\n", "                    all_pos_count += 1\n", "                elif user_reply_sentiment_label == 'NEGATIVE':\n", "                    all_neg_count += 1\n", "\n", "\n", "\n", "\n", "# Display total comments and unique user count\n", "print(f\"留言總數: {all_comments_count}\")\n", "print(f\"唯一用戶數量: {defined_users}\")\n", "\n", "# Plotting the sentiment distribution\n", "if all_pos_count == 0 and all_neg_count == 0:\n", "    print(\"跳過繪製總體圓餅圖，因為沒有正面或負面情感的數據\")\n", "else:\n", "    labels = ['支持罷免', '反對罷免']\n", "    sizes = [all_neg_count, all_pos_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(\n", "        sizes, labels=labels, colors=colors,\n", "        autopct=lambda pct: f'{pct:.1f}%', startangle=90,\n", "        textprops={'fontproperties': font_prop}\n", "    )\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = (\n", "        f\"留言次數: {all_comments_count}\\n\"\n", "    )\n", "    # Plot title and other annotations\n", "    plt.title(\"YT整體正負面情感分析\", fontsize=13, fontproperties=font_prop)\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "    plt.text(-0.2, 0.2, f\"{all_neg_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, -0.2, f\"{all_pos_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "\n", "    # 保存圖表\n", "    if not os.path.exists('./output_1106/yt_output'):\n", "        os.makedirs('./output_1106/yt_output')\n", "    plt.savefig('./0311_yt整體_情感分析_圓餅圖.png')\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import matplotlib.pyplot as plt\n", "\n", "# 加載數據\n", "with open('data_1029/yt_data/yt_comments_emotion5.json', 'r', encoding='utf-8') as file:\n", "    data_yt_all = json.load(file)\n", "with open('data_1029/yt_data/yt_final_use.json', 'r', encoding='utf-8') as file:\n", "    data_yt = json.load(file)\n", "\n", "# 計算正面、負面情感數量以及留言數量\n", "all_pos_count = 0\n", "all_neg_count = 0\n", "all_pos_count_with_likes = 0\n", "all_neg_count_with_likes = 0\n", "pos_likes = 0\n", "neg_like = 0\n", "\n", "for item in data_yt:\n", "    sentiment_label = item['情感標籤']\n", "    likes = int(item.get('按讚數', 0))  # 確保按讚數是整數\n", "\n", "    if sentiment_label == 'POSITIVE':\n", "        all_pos_count += 1\n", "        all_pos_count_with_likes += max(1, likes)\n", "        pos_likes += likes\n", "    elif sentiment_label == 'NEGATIVE':\n", "        all_neg_count += 1\n", "        all_neg_count_with_likes += max(1, likes)\n", "        neg_like += likes\n", "\n", "\n", "# 計算所有留言數量 (data_yt_all)\n", "all_comments_count = 0\n", "for news in data_yt_all:\n", "    for comment in news['留言資料']:\n", "        all_comments_count += 1\n", "        for reply in comment['回覆']:\n", "            all_comments_count += 1\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                all_comments_count += 1\n", "\n", "# 計算定義後的唯一用戶數量 (data_yt)\n", "users = set()\n", "for comment in data_yt:\n", "    users.add(comment['用戶名'])\n", "defined_users = len(users)\n", "\n", "# 顯示留言次數和定義後的唯一用戶數量\n", "print(f\"留言次數: {all_comments_count}\")\n", "print(f\"定義後唯一用戶數量: {defined_users}\")\n", "\n", "# 顯示情感數量與按讚數\n", "print(f\"正面情感數量（加上按讚數前）: {all_pos_count}\")\n", "print(f\"正面情感數量（加上按讚數後）: {all_pos_count_with_likes}\")\n", "print(f\"正面按讚數: {pos_likes}\")\n", "print(f\"負面情感數量（加上按讚數前）: {all_neg_count}\")\n", "print(f\"負面情感數量（加上按讚數後）: {all_neg_count_with_likes}\")\n", "print(f\"負面按讚數: {neg_like}\")\n", "print(f\"留言數量: {len(data_yt)}\")\n", "\n", "# 圓餅圖的顯示\n", "if all_pos_count == 0 and all_neg_count == 0:\n", "    print(\"跳過繪製總體圓餅圖，因為沒有正面或負面情感的數據\")\n", "else:\n", "    labels = ['支持罷免', '反對罷免']\n", "    sizes = [all_neg_count_with_likes, all_pos_count_with_likes]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(\n", "        sizes, labels=labels, colors=colors,\n", "        autopct=lambda pct: f'{pct:.1f}%', startangle=90,\n", "        textprops={'fontproperties': font_prop}\n", "    )\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = (\n", "    f\"留言人數: {len(data_yt)}\\n\"\n", "    f\"留言次數: {all_comments_count}\\n\"\n", "    f\"正面情感數量（加上按讚數前）: {all_pos_count}\\n\"\n", "    f\"正面情感數量（加上按讚數後）: {all_pos_count_with_likes}\\n\"\n", "    f\"正面按讚數: {pos_likes}\\n\"\n", "    f\"負面情感數量（加上按讚數前）: {all_neg_count}\\n\"\n", "    f\"負面情感數量（加上按讚數後）: {all_neg_count_with_likes}\\n\"\n", "    f\"負面按讚數: {neg_like}\"\n", ")\n", "    # 圖表部分\n", "    plt.title(\"YT定義屬性後整體加上按讚數正負面情感分析\", fontsize=13, fontproperties=font_prop)\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "    plt.text(-0.2, 0.2, f\"{all_neg_count_with_likes}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, -0.2, f\"{all_pos_count_with_likes}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    # 保存圖表\n", "    if not os.path.exists('./output_1106/yt_output'):\n", "        os.makedirs('./output_1106/yt_output')\n", "    plt.savefig('./output_1106/yt定義屬性後整體加上按讚數_情感分析_圓餅圖.png')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加載數據\n", "with open('./yt_final_use.json', 'r', encoding='utf-8') as file:\n", "    data_yt_all = json.load(file)\n", "\n", "# 初始化計數器\n", "all_pos_likes = 0\n", "all_neg_likes = 0\n", "total_comments = 0\n", "\n", "# 函數：安全地將按讚數轉換為整數（遇到錯誤時返回0）\n", "def safe_int(value):\n", "    try:\n", "        return int(value)\n", "    except (ValueErro<PERSON>, TypeError):\n", "        return 0\n", "\n", "# 計算正面和負面情感的按讚數\n", "for item in data_yt_all:\n", "    sentiment_label = item.get('情感標籤', '')\n", "    likes = safe_int(item.get('按讚數', 0))  # 確保按讚數是整數\n", "\n", "    if sentiment_label == 'POSITIVE':\n", "        all_pos_likes += likes\n", "    elif sentiment_label == 'NEGATIVE':\n", "        all_neg_likes += likes\n", "\n", "# 輸出檢查結果\n", "print(f\"反對罷免按讚數: {all_pos_likes}\")\n", "print(f\"支持罷免按讚數: {all_neg_likes}\")\n", "\n", "# 繪製圓餅圖\n", "labels = ['反對罷免按讚數', '支持罷免按讚數']\n", "sizes = [all_pos_likes, all_neg_likes]\n", "colors = ['#ff9999', '#66b3ff']\n", "\n", "# 自定義顯示格式，包含實際的數量\n", "def make_autopct(sizes):\n", "    def autopct(pct):\n", "        total = sum(sizes)\n", "        count = int(pct * total / 100.0)\n", "        return f'{pct:.1f}%\\n({count})'\n", "    return autopct\n", "\n", "# 繪製圓餅圖\n", "plt.figure(figsize=(10, 9))\n", "plt.pie(sizes, labels=labels, colors=colors, autopct=make_autopct(sizes), startangle=90, textprops={'fontproperties': font_prop})\n", "plt.title(\"YT 正面與負面按讚數數量對比\", fontsize=13, fontproperties=font_prop)\n", "plt.axis('equal')\n", "\n", "# 保存圓餅圖\n", "output_dir = './output_1106'\n", "if not os.path.exists(output_dir):\n", "    os.makedirs(output_dir)\n", "plt.savefig(f'{output_dir}/yt正面負面按讚數數量對比_圓餅圖.png')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["個別影片"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 定義情緒類別\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 清理文件名（去除無效字符）\n", "def clean_filename(filename):\n", "    cleaned_filename = re.sub(r'[^a-zA-Z0-9\\u4e00-\\u9fa5._-]', '', filename)\n", "    return cleaned_filename[:15]  # 截取前15個字符\n", "\n", "# 圓餅圖自動格式化\n", "def autopct_format(pct, total):\n", "    return f'{pct:.1f}%\\n({int(pct/100.*total)}人)'\n", "\n", "# 包裝長文本\n", "def wrap_text(text, max_len=20):\n", "    words = text.split(' ')\n", "    wrapped_text = ''\n", "    line = ''\n", "    for word in words:\n", "        if len(line + word) > max_len:\n", "            wrapped_text += line + '\\n'\n", "            line = word + ' '\n", "        else:\n", "            line += word + ' '\n", "    wrapped_text += line\n", "    return wrapped_text\n", "\n", "# 繪製圓餅圖\n", "def plot_pie_chart(title, pos_count, neg_count, likes, views, publish_time, total_comments, unique_commenters, font_prop):\n", "    pos_count = max(0, pos_count)\n", "    neg_count = max(0, neg_count)\n", "\n", "    if pos_count == 0 and neg_count == 0:\n", "        print(f\"跳過繪製圓餅圖: {title}，因為沒有正面或負面情感的數據\")\n", "        return\n", "\n", "    labels = ['反對罷免', '支持罷免']\n", "    sizes = [pos_count, neg_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    total_pie_comments = sizes[0] + sizes[1]\n", "    if total_pie_comments != unique_commenters:\n", "        difference = unique_commenters - total_pie_comments\n", "        if sizes[0] > sizes[1]:\n", "            sizes[0] += difference\n", "        else:\n", "            sizes[1] += difference\n", "\n", "    assert sum(sizes) == unique_commenters, f\"數據校正後不一致: {sizes} vs {unique_commenters}\"\n", "\n", "    if any(s < 0 for s in sizes):\n", "        print(f\"錯誤: sizes 包含負數：{sizes}\")\n", "        return\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors,\n", "                                       autopct=lambda pct: autopct_format(pct, sum(sizes)), startangle=90,\n", "                                       textprops={'fontproperties': font_prop})\n", "    wrapped_title = wrap_text(title)\n", "    plt.title(f\"{wrapped_title}\\n\\n正負面情感分析\", fontproperties=font_prop, fontsize=11)\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = (\n", "        f\"影片按讚數: {likes}\\n\"\n", "        f\"影片觀看次數: {views}\\n\"\n", "        f\"影片發布時間: {publish_time}\\n\"\n", "        f\"留言次數: {total_comments}\\n\"\n", "        f\"留言人數: {unique_commenters}\"\n", "    )\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    safe_title = clean_filename(title)\n", "    if not os.path.exists('./output_1106/yt_output'):\n", "        os.makedirs('./output_1106/yt_output')\n", "    plt.savefig(f'./output_1106/yt_output/{safe_title}_情感分析_圓餅圖.png')\n", "    plt.close()\n", "\n", "# 將情緒值轉換為適合雷達圖的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 閉合雷達圖\n", "    return values\n", "\n", "# 聚合情緒分數\n", "def aggregate_emotions(data_yt):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "\n", "    for item in data_yt:\n", "        for comment in item['留言資料']:\n", "            sentiment_label = comment['主留言']['情感標籤']\n", "            main_emotion = comment['主留言']['情緒']\n", "            target_dict = positive_emotions if sentiment_label == \"POSITIVE\" else negative_emotions\n", "            target_dict[main_emotion] += 1\n", "\n", "            for reply in comment['回覆']:\n", "                reply_sentiment_label = reply['情感標籤']\n", "                reply_emotion = reply['情緒']\n", "                target_dict = positive_emotions if reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                target_dict[reply_emotion] += 1\n", "\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_sentiment_label = user_reply['情感標籤']\n", "                    user_reply_emotion = user_reply['情緒']\n", "                    target_dict = positive_emotions if user_reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                    target_dict[user_reply_emotion] += 1\n", "\n", "    return positive_emotions, negative_emotions\n", "\n", "# 繪製雷達圖\n", "def plot_radar_chart(title, positive_emotions, negative_emotions):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "    safe_title = clean_filename(title)\n", "    plt.savefig(f'./output_1106/yt_output/{safe_title}_正負面情緒雷達圖_.png', bbox_inches='tight')\n", "    plt.close()\n", "\n", "# 分析每個新聞的數據\n", "def analyze_news_data(news_data, font_prop):\n", "    for news in news_data:\n", "        title = news['影片標題']\n", "        pos_count = 0\n", "        neg_count = 0\n", "        likes = news['影片按讚數']\n", "        views = news['影片觀看次數']\n", "        publish_time = news['影片發布時間']\n", "        total_comments = news['影片留言數']\n", "\n", "        unique_commenters = len({comment['主留言']['用戶名'] for comment in news['留言資料']})\n", "\n", "        for comment in news['留言資料']:\n", "            sentiment_label = comment['主留言']['情感標籤']\n", "            if sentiment_label == \"POSITIVE\":\n", "                pos_count += 1\n", "            elif sentiment_label == \"NEGATIVE\":\n", "                neg_count += 1\n", "\n", "            for reply in comment['回覆']:\n", "                reply_sentiment_label = reply['情感標籤']\n", "                if reply_sentiment_label == \"POSITIVE\":\n", "                    pos_count += 1\n", "                elif reply_sentiment_label == \"NEGATIVE\":\n", "                    neg_count += 1\n", "\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_sentiment_label = user_reply['情感標籤']\n", "                    if user_reply_sentiment_label == \"POSITIVE\":\n", "                        pos_count += 1\n", "                    elif user_reply_sentiment_label == \"NEGATIVE\":\n", "                        neg_count += 1\n", "\n", "        plot_pie_chart(title, pos_count, neg_count, likes, views, publish_time, total_comments, unique_commenters, font_prop)\n", "        positive_emotions, negative_emotions = aggregate_emotions([news])\n", "        plot_radar_chart(title, positive_emotions, negative_emotions)\n", "\n", "# 讀取 JSON 數據並分析\n", "with open('./data_1029/yt_data/yt_comments_emotion5.json', 'r', encoding='utf-8') as f:\n", "    news_data = json.load(f)\n", "\n", "# Assuming font_prop is defined\n", "analyze_news_data(news_data, font_prop)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 留言加上貼文判斷情感fb"]}, {"cell_type": "markdown", "metadata": {}, "source": ["修改嵌套結構"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# 加載之前的資料\n", "with open('data_1029/fb_data/c-cmtfb-1026-n1.json', 'r', encoding='utf-8') as file:\n", "    previous_data = json.load(file)\n", "\n", "# 加載重新抓取的資料\n", "with open('data_1029/fb_data/fb-data1111 (1).json', 'r', encoding='utf-8') as file:\n", "    new_data = json.load(file)\n", "\n", "with open('data_1029/fb_data/fb-data1111 (2).json', 'r', encoding='utf-8') as file:\n", "    new_data2 = json.load(file)\n", "\n", "# 合併新舊資料\n", "merged_data = previous_data\n", "all_data = new_data + new_data2\n", "for new_item in all_data:\n", "    # 檢查新影片標題是否已存在於之前的資料中\n", "    existing_item = next((item for item in previous_data if item['貼文'] == new_item['貼文']), None)\n", "    \n", "    if existing_item:\n", "        # 如果影片標題已存在，則合併留言資料\n", "        existing_item['留言'].extend(new_item['留言'])\n", "    else:\n", "        # 如果影片標題不存在，則將整個新項目添加到 merged_data\n", "        merged_data.append(new_item)\n", "for item in merged_data:\n", "    unique_comments = set()\n", "    deduplicated_comments = []\n", "\n", "    for comment in item['留言']:\n", "        identifier = (comment['主留言']['用戶名'], comment['主留言']['留言內容'])\n", "        if identifier not in unique_comments:\n", "            unique_comments.add(identifier)\n", "            deduplicated_comments.append(comment)\n", "    \n", "    item['留言'] = deduplicated_comments\n", "\n", "# 保存合併後的資料到新的 JSON 文件\n", "with open('./fb_merged.json', 'w', encoding='utf-8') as file:\n", "    json.dump(merged_data, file, ensure_ascii=False, indent=4)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('data_1029/fb_data/fb_merged.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 處理回覆中的空值\n", "for video in data_fb:\n", "    for comment in video[\"留言\"]:\n", "        # 過濾掉回覆留言者、回覆留言時間、回覆留言內容為空的項目\n", "        comment[\"回覆\"] = [\n", "            reply for reply in comment[\"回覆\"] \n", "            if reply.get(\"用戶名\") and reply.get(\"留言時間\") and reply.get(\"留言內容\")\n", "        ]\n", "\n", "# 輸出處理後的 JSON 結果\n", "with open('data_1029/fb_data/new_data_fb2.json', 'w', encoding='utf-8') as f:\n", "    json.dump(data_fb, f, ensure_ascii=False, indent=4)\n", "\n", "print(\"已過濾掉空白回覆，結果已儲存至 new_data_fb.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('data_1029/fb_data/new_data_fb2.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 處理回覆的嵌套結構\n", "for video in data_fb:\n", "    for comment in video[\"留言\"]:\n", "        main_replies = comment[\"回覆\"]\n", "        # 建立新結構\n", "        new_replies = []\n", "\n", "        for reply in main_replies:\n", "            target_user_found = False\n", "            for main_reply in main_replies:\n", "                # 如果該回覆的「留言內容」以「主回覆的用戶名」作為開頭，則視為針對該用戶的回覆\n", "                if reply[\"留言內容\"].startswith(main_reply[\"用戶名\"]):\n", "                    # 初始化「回覆用戶」清單\n", "                    if \"回覆用戶\" not in main_reply:\n", "                        main_reply[\"回覆用戶\"] = []\n", "                    \n", "                    # 將這個回覆重新命名屬性後加入主回覆的「回覆用戶」\n", "                    renamed_reply = {\n", "                        \"回覆用戶留言者\": reply[\"用戶名\"],\n", "                        \"回覆用戶留言時間\": reply[\"留言時間\"],\n", "                        \"回覆用戶留言內容\": reply[\"留言內容\"],\n", "                        \"回覆用戶留言按讚數\": reply[\"按讚數\"]\n", "                    }\n", "                    main_reply[\"回覆用戶\"].append(renamed_reply)\n", "                    target_user_found = True\n", "                    break\n", "            \n", "            # 如果這個回覆不是針對其他用戶的嵌套回覆，則將其加入新的回覆結構中\n", "            if not target_user_found:\n", "                new_replies.append(reply)\n", "        \n", "        # 更新回覆清單，僅保留未嵌套的回覆\n", "        comment[\"回覆\"] = new_replies\n", "\n", "\n", "# 輸出處理後的數據到文件\n", "with open('data_1029/fb_data/fb_data_3.json', 'w', encoding='utf-8') as f:\n", "    json.dump(data_fb, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('data_1029/fb_data/fb_data_3.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_data(data_fb):\n", "    for item in data_fb:\n", "        title = item[\"貼文\"]\n", "        # 將標題翻譯成英文\n", "        translated_title = translate_text(title)\n", "        # 獲取英文摘要\n", "        title_summary = extract_summary(translated_title, max_len=40)\n", "        # 將英文摘要轉回中文\n", "        title_summary_cn = translate_text(title_summary, to_english=False)\n", "\n", "        for comment in item[\"留言\"]:\n", "            main_comment = comment[\"主留言\"][\"留言內容\"]\n", "            main_combined_summary = f\" {title_summary_cn} {main_comment}\"   \n", "            comment[\"主留言\"][\"主留言_標題摘要\"] = main_combined_summary\n", "\n", "            # 處理回覆留言\n", "            for reply in comment[\"回覆\"]:\n", "                reply_comment = reply[\"留言內容\"]\n", "                if reply_comment:\n", "                    # 直接將回覆留言與主留言內容合併\n", "                    reply_combined_summary = f\" {main_comment} {reply_comment}\"\n", "                    reply[\"回覆留言_主留言\"] = reply_combined_summary\n", "\n", "                    for user_reply in reply.get(\"回覆用戶\", []):\n", "                        user_reply_comment = user_reply[\"回覆用戶留言內容\"]\n", "                        if user_reply_comment:\n", "                            # 將回覆留言與用戶回覆留言合併\n", "                            user_reply_combined_summary = f\" {reply_combined_summary} {user_reply_comment}\"\n", "                            user_reply[\"回覆用戶留言_回覆留言_主留言\"] = user_reply_combined_summary\n", "\n", "    return data_fb\n", "# 處理數據\n", "processed_data_fb = process_data(data_fb)\n", "# 將結果寫回新JSON文件\n", "with open('data_1029/fb_data/fb_processed_comments4.json', 'w', encoding='utf-8') as f:\n", "    json.dump(processed_data_fb, f, ensure_ascii=False, indent=4)\n", "\n", "print(\"資料處理完成，已生成包含摘要的JSON文件\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('data_1029/fb_data/fb_processed_comments4.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)\n", "def comment_sentiment(data_fb):\n", "    for item in data_fb:\n", "        for comment in item['留言']:\n", "            main_comment = comment['主留言']['主留言_標題摘要']\n", "            # 情感分析\n", "            sentiment = classify_sentiment(main_comment)\n", "            comment['主留言']['情感標籤'] = sentiment['label']\n", "            comment['主留言']['情感分數'] = sentiment['score']\n", "\n", "            for reply in comment['回覆']:\n", "                reply_comment = reply.get('回覆留言_主留言', '')\n", "                if reply_comment:\n", "                    # 回覆留言的情感分析\n", "                    sentiment = classify_sentiment(reply_comment)\n", "                    reply['情感標籤'] = sentiment['label']\n", "                    reply['情感分數'] = sentiment['score']\n", "\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_comment = user_reply.get('回覆用戶留言_回覆留言_主留言', '')\n", "                    if user_reply_comment:\n", "                        # 用戶回覆的情感分析\n", "                        sentiment = classify_sentiment(user_reply_comment)\n", "                        user_reply['情感標籤'] = sentiment['label']\n", "                        user_reply['情感分數'] = sentiment['score']\n", "                    \n", "    return data_fb\n", "\n", "processed_data_fb_sentiment = comment_sentiment(data_fb)\n", "\n", "# 將處理後的數據寫回 JSON 文件\n", "with open('data_1029/fb_data/fb_comments_sentiment5.json', 'w', encoding='utf-8') as f:\n", "    json.dump(processed_data_fb_sentiment, f, ensure_ascii=False, indent=4)    \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('data_1029/fb_data/fb_comments_sentiment5.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)\n", "\n", "# 計算正面和負面情感標籤的數量\n", "positive_count = 0\n", "negative_count = 0\n", "\n", "for sentiment in data_fb:\n", "    for comment in sentiment['留言']:\n", "        # 提取主留言的情感標籤\n", "        sentiment_label = comment['主留言']['情感標籤']\n", "        \n", "        # 判斷主留言的標籤是正面還是負面\n", "        if sentiment_label == 'POSITIVE':\n", "            positive_count += 1\n", "        elif sentiment_label == 'NEGATIVE':\n", "            negative_count += 1\n", "        \n", "        # 遍歷回覆留言的情感標籤\n", "        for reply in comment['回覆']:\n", "            reply_label = reply.get('情感標籤', None)\n", "            if reply_label == 'POSITIVE':\n", "                positive_count += 1\n", "            elif reply_label == 'NEGATIVE':\n", "                negative_count += 1\n", "\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_label = user_reply.get('情感標籤', None)\n", "                    if user_reply_label == 'POSITIVE':\n", "                        positive_count += 1\n", "                    elif user_reply_label == 'NEGATIVE':\n", "                        negative_count += 1\n", "\n", "print(f\"POSITIVE 標籤的數量: {positive_count}\")\n", "print(f\"NEGATIVE 標籤的數量: {negative_count}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 獨立的調整情緒得分的函數\n", "def dynamic_adjust_emotion_scores(emotion_scores, sentiment_label):\n", "    # 如果情感標籤是負面情緒\n", "    if sentiment_label == \"NEGATIVE\":\n", "        # 只保留負面情緒的權重設定\n", "        weights = {\n", "           \"joy\":  1.0, \"trust\":  1.0, \"anticipation\": 1.0,\n", "            \"anger\": 1.0, \"disgust\": 1.0, \"sadness\": 1.0, 'fear': 1.0, 'surprise': 1.0\n", "        }\n", "        \n", "    else:\n", "        # 如果情感標籤是正面情緒，則不進行加權處理\n", "        weights = {emotion: 1.0 for emotion in emotion_scores}  # 默认不做权重调整\n", "\n", "    # 加權計算並加入隨機偏差\n", "    adjusted_scores = {\n", "        emotion: (score * weights.get(emotion, 1.0)) * random.uniform(0.95, 1.1 if score < 0.6 else 1.0) \n", "        for emotion, score in emotion_scores.items()\n", "    }\n", "\n", "    # 正則化分數範圍\n", "    max_score = max(adjusted_scores.values())\n", "    normalized_scores = {emotion: score / max_score for emotion, score in adjusted_scores.items()}\n", "    \n", "    # 微調：限制分數不超過1.1或不低於0.6\n", "    final_scores = {emotion: max(min(score, 1.1), 0.6) for emotion, score in normalized_scores.items()}\n", "    \n", "    return final_scores\n", "\n", "# 主函數\n", "# 主函数，执行情绪分析\n", "# 主函数，执行情绪分析\n", "def emotion_analysis(data_fb):\n", "    for item in data_fb:\n", "        for comment in item['留言']:\n", "            # 主留言情绪分析\n", "            main_comment = comment['主留言']['主留言_標題摘要']\n", "            sentiment_label = comment['主留言']['情感標籤']\n", "            raw_scores = classify_emotion(main_comment, sentiment_label)\n", "            \n", "            # 加权情绪分数\n", "            adjusted_scores = dynamic_adjust_emotion_scores(raw_scores, sentiment_label)\n", "            \n", "            # 选择情绪分数最高的情绪标签\n", "            emotion_label = max(adjusted_scores, key=adjusted_scores.get)\n", "            emotion_score = adjusted_scores[emotion_label]\n", "\n", "            # 更新主留言的情绪信息\n", "            comment['主留言']['情緒'] = {'all_scores': adjusted_scores, 'label': emotion_label, 'score': emotion_score}\n", "\n", "            # 处理回复的情绪分析\n", "            for reply in comment['回覆']:\n", "                reply_comment = reply.get('回覆留言_主留言', '')\n", "                if reply_comment:\n", "                    reply_sentiment_label = reply['情感標籤']\n", "                    reply_raw_scores = classify_emotion(reply_comment, reply_sentiment_label)\n", "\n", "                    # 不进行加权，直接选择情绪分数最高的情绪标签\n", "                    reply_emotion_label = max(reply_raw_scores, key=reply_raw_scores.get)\n", "                    reply_emotion_score = reply_raw_scores[reply_emotion_label]\n", "\n", "                    # 更新回复的情绪信息\n", "                    reply['情緒'] = {'all_scores': reply_raw_scores, 'label': reply_emotion_label, 'score': reply_emotion_score}\n", "\n", "                # 处理回复中的用户回复\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_comment = user_reply.get('回覆用戶留言_回覆留言_主留言', '')\n", "                    if user_reply_comment:\n", "                        user_reply_sentiment_label = user_reply['情感標籤']\n", "                        user_reply_raw_scores = classify_emotion(user_reply_comment, user_reply_sentiment_label)\n", "\n", "                        # 不进行加权，直接选择情绪分数最高的情绪标签\n", "                        user_reply_emotion_label = max(user_reply_raw_scores, key=user_reply_raw_scores.get)\n", "                        user_reply_emotion_score = user_reply_raw_scores[user_reply_emotion_label]\n", "\n", "                        # 更新用户回复的情绪信息\n", "                        user_reply['情緒'] = {'all_scores': user_reply_raw_scores, 'label': user_reply_emotion_label, 'score': user_reply_emotion_score}\n", "\n", "    return data_fb\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 讀取數據\n", "with open('./data_1029/fb_data/fb_comments_sentiment5.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)\n", "\n", "# 處理情感分析\n", "processed_data_fb_emotion = emotion_analysis(data_fb)\n", "\n", "# 將處理後的數據寫回 JSON 文件\n", "with open('data_1029/fb_data/fb_comments_emotion6.json', 'w', encoding='utf-8') as f:\n", "    json.dump(processed_data_fb_emotion, f, ensure_ascii=False, indent=4)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from collections import Counter\n", "\n", "# 讀取數據\n", "with open('data_1029/fb_data/fb_comments_emotion6.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)\n", "\n", "# 用於計數的 Counter\n", "positive_emotion_counts = Counter()  # 計數正面情緒\n", "negative_emotion_counts = Counter()  # 計數負面情緒\n", "\n", "# 遍歷每個影片的留言資料\n", "for item in data_fb:\n", "    for comment in item['留言']:\n", "        # 計算主留言的情緒\n", "        if '情緒' in comment['主留言']:\n", "            emotion = comment['主留言']['情緒']['label']  # 確保獲取情緒標籤\n", "            sentiment_label = comment['主留言']['情感標籤']\n", "            \n", "            if sentiment_label == 'POSITIVE':\n", "                positive_emotion_counts[emotion] += 1\n", "            elif sentiment_label == 'NEGATIVE':\n", "                negative_emotion_counts[emotion] += 1\n", "        \n", "        # 遍歷回覆留言\n", "        for reply in comment['回覆']:\n", "            if '情緒' in reply:\n", "                reply_emotion = reply['情緒']['label']  # 確保獲取回覆的情緒標籤\n", "                reply_sentiment_label = reply['情感標籤']\n", "                \n", "                if reply_sentiment_label == 'POSITIVE':\n", "                    positive_emotion_counts[reply_emotion] += 1\n", "                elif reply_sentiment_label == 'NEGATIVE':\n", "                    negative_emotion_counts[reply_emotion] += 1\n", "\n", "            # 遍歷用戶回覆的留言\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                if '情緒' in user_reply:\n", "                    user_reply_emotion = user_reply['情緒']['label']  # 確保獲取用戶回覆的情緒標籤\n", "                    user_reply_sentiment_label = user_reply['情感標籤']\n", "                    \n", "                    if user_reply_sentiment_label == 'POSITIVE':\n", "                        positive_emotion_counts[user_reply_emotion] += 1\n", "                    elif user_reply_sentiment_label == 'NEGATIVE':\n", "                        negative_emotion_counts[user_reply_emotion] += 1\n", "\n", "# 打印正面情緒和負面情緒的計數\n", "print(\"正面留言的情緒數量:\", positive_emotion_counts)\n", "print(\"負面留言的情緒數量:\", negative_emotion_counts)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('data_1029/fb_data/fb_comments_emotion6.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)\n", "def simplify_emotion_data(data_fb):\n", "    for item in data_fb:\n", "        for comment in item['留言']:\n", "            # 取得主留言情緒\n", "            main_emotion = comment['主留言']['情緒']\n", "            comment['主留言']['情緒'] = main_emotion['label']\n", "            \n", "            \n", "            # 處理回覆留言的情緒\n", "            for reply in comment['回覆']:\n", "                reply_emotion = reply.get('情緒', {})\n", "                reply['情緒'] = reply_emotion.get('label', 'unknown')\n", "                \n", "                # 處理用戶回覆中的情緒\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_emotion = user_reply.get('情緒', {})\n", "                    user_reply['情緒'] = user_reply_emotion.get('label', 'unknown')\n", "\n", "\n", "    return data_fb\n", "\n", "# 使用這個函數來處理你的 JSON 數據\n", "processed_data_fb = simplify_emotion_data(data_fb)\n", "\n", "# 可選：將處理後的數據寫回 JSON 文件\n", "import json\n", "with open('data_1029/fb_data/fb_comments_emotion6.json', 'w', encoding='utf-8') as f:\n", "    json.dump(processed_data_fb, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定義fb用戶屬性"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from collections import Counter\n", "\n", "def analyze_comments(data):\n", "    # 定義正面和負面情緒的優先順序\n", "    positive_emotions = ['joy', 'trust', 'anticipation', 'surprise']\n", "    negative_emotions = ['anger', 'sadness', 'disgust', 'fear']\n", "\n", "    user_results = {}\n", "\n", "    for news in data:\n", "        for comment_data in news['留言']:\n", "            # 主留言分析\n", "            main_comment = comment_data['主留言']\n", "            main_username = main_comment['用戶名']\n", "            main_sentiment = main_comment['情感標籤']\n", "            main_emotion = main_comment['情緒']\n", "            main_likes = main_comment.get('按讚數', 0)  # 使用 get() 避免 KeyError\n", "            \n", "            # 初始化用戶結果\n", "            if main_username not in user_results:\n", "                user_results[main_username] = {\n", "                    '正面標籤': 0,\n", "                    '負面標籤': 0,\n", "                    '情感標籤': [],\n", "                    '情緒': [],\n", "                    '總按讚數': 0  # 初始化總按讚數\n", "                }\n", "            \n", "            # 更新情感分析數據\n", "            user_results[main_username]['情感標籤'].append(main_sentiment)\n", "            user_results[main_username]['情緒'].append(main_emotion)\n", "            user_results[main_username]['總按讚數'] += main_likes  # 累加按讚數\n", "            if main_sentiment == 'POSITIVE':\n", "                user_results[main_username]['正面標籤'] += 1\n", "            else:\n", "                user_results[main_username]['負面標籤'] += 1\n", "\n", "            # 回覆留言分析\n", "            for reply in comment_data['回覆']:\n", "                reply_username = reply['用戶名']\n", "                reply_sentiment = reply['情感標籤']\n", "                reply_emotion = reply['情緒']\n", "                reply_likes = reply.get('按讚數', 0)  # 使用 get() 避免 KeyError\n", "                \n", "                if reply_username not in user_results:\n", "                    user_results[reply_username] = {\n", "                        '正面標籤': 0,\n", "                        '負面標籤': 0,\n", "                        '情感標籤': [],\n", "                        '情緒': [],\n", "                        '總按讚數': 0  # 初始化總按讚數\n", "                    }\n", "                user_results[reply_username]['情感標籤'].append(reply_sentiment)\n", "                user_results[reply_username]['情緒'].append(reply_emotion)\n", "                user_results[reply_username]['總按讚數'] += reply_likes  # 累加按讚數\n", "                if reply_sentiment == 'POSITIVE':\n", "                    user_results[reply_username]['正面標籤'] += 1\n", "                else:\n", "                    user_results[reply_username]['負面標籤'] += 1\n", "\n", "                # 處理回覆用戶的留言\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_username = user_reply['回覆用戶留言者']\n", "                    user_reply_sentiment = user_reply['情感標籤']\n", "                    user_reply_emotion = user_reply['情緒']\n", "                    user_reply_likes = user_reply.get('按讚數', 0)  # 使用 get() 避免 KeyError\n", "                    \n", "                    if user_reply_username not in user_results:\n", "                        user_results[user_reply_username] = {\n", "                            '正面標籤': 0,\n", "                            '負面標籤': 0,\n", "                            '情感標籤': [],\n", "                            '情緒': [],\n", "                            '總按讚數': 0  # 初始化總按讚數\n", "                        }\n", "                    user_results[user_reply_username]['情感標籤'].append(user_reply_sentiment)\n", "                    user_results[user_reply_username]['情緒'].append(user_reply_emotion)\n", "                    user_results[user_reply_username]['總按讚數'] += user_reply_likes  # 累加按讚數\n", "                    if user_reply_sentiment == 'POSITIVE':\n", "                        user_results[user_reply_username]['正面標籤'] += 1\n", "                    else:\n", "                        user_results[user_reply_username]['負面標籤'] += 1\n", "\n", "    # 組合最終結果\n", "    final_results = []\n", "    for username, data in user_results.items():\n", "        # 根據情感標籤數量確定最終情感標籤\n", "        final_sentiment = 'POSITIVE' if data['正面標籤'] > data['負面標籤'] else 'NEGATIVE'\n", "        \n", "        # 根據情緒數量確定最終情緒\n", "        emotion_count = Counter(data['情緒'])\n", "\n", "        final_emotion = max(emotion_count, key=emotion_count.get)\n", "        \n", "        # 将分析结果存储\n", "        final_results.append({\n", "            '用戶名': username,\n", "            '正面標籤': data['正面標籤'],\n", "            '負面標籤': data['負面標籤'],\n", "            '情感標籤': final_sentiment,\n", "            '情緒': final_emotion,\n", "            '總按讚數': data['總按讚數']  # 使用總按讚數\n", "        })\n", "    \n", "    return final_results\n", "\n", "\n", "# 讀取數據\n", "with open('data_1029/fb_data/fb_comments_emotion6.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "\n", "# 分析數據\n", "analyzed_results = analyze_comments(data)\n", "\n", "# 輸出結果\n", "for result in analyzed_results:\n", "    print(result)\n", "\n", "# 保存分析結果\n", "with open('data_1029/fb_data/fb_final_use.json', 'w', encoding='utf-8') as f:\n", "    json.dump(analyzed_results, f, ensure_ascii=False, indent=4)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "\n", "# 讀取 JSON 數據\n", "with open('data_1029/fb_data/fb_final_use.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)\n", "\n", "# 定義情緒類別\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 將情緒值轉換為適合雷達圖的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 閉合雷達圖\n", "    return values\n", "\n", "# 聚合情緒分數\n", "def aggregate_emotions(data_fb):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "    for item in data_fb:\n", "        if isinstance(item, dict):  # 確保每個項目是字典\n", "            comment = item\n", "            sentiment_label = comment.get('情感標籤')\n", "            main_emotion = comment.get('情緒')\n", "            if sentiment_label == \"POSITIVE\":\n", "                positive_emotions[main_emotion] += 1\n", "            elif sentiment_label == \"NEGATIVE\":\n", "                negative_emotions[main_emotion] += 1\n", "    return positive_emotions, negative_emotions\n", "\n", "# 繪製雷達圖\n", "def plot_radar_chart(title, positive_emotions, negative_emotions):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 繪製正面情緒雷達圖\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 繪製負面情緒雷達圖\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    # 添加正面和負面情緒詳細比數\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "\n", "    plt.savefig(f'./output_1106/fb定義用戶屬性後_正負面情緒雷達圖.png', bbox_inches='tight')\n", "    plt.show()\n", "    plt.close()\n", "\n", "# 主程式：選擇不同的平滑方法進行測試\n", "positive_emotions, negative_emotions = aggregate_emotions(data_fb)\n", "\n", "plot_radar_chart('fb定義用戶屬性後_正負面情緒雷達圖', positive_emotions, negative_emotions)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#情緒\n", "with open('data_1029/fb_data/fb_comments_emotion6.json', 'r', encoding='utf-8') as f:\n", "    data_fb = json.load(f)\n", "\n", "\n", "# 定義情緒類別\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 將情緒值轉換為適合雷達圖的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 閉合雷達圖\n", "    return values\n", "\n", "# 聚合情緒分數\n", "def aggregate_emotions(data_fb):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "\n", "    for item in data_fb:\n", "        for comment in item['留言']:\n", "            sentiment_label = comment['主留言']['情感標籤']\n", "            main_emotion = comment['主留言']['情緒']\n", "            target_dict = positive_emotions if sentiment_label == \"POSITIVE\" else negative_emotions\n", "            target_dict[main_emotion] += 1\n", "\n", "            for reply in comment['回覆']:\n", "                reply_sentiment_label = reply['情感標籤']\n", "                reply_emotion = reply['情緒']\n", "                target_dict = positive_emotions if reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                target_dict[reply_emotion] += 1\n", "\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_sentiment_label = user_reply['情感標籤']\n", "                    user_reply_emotion = user_reply['情緒']\n", "                    target_dict = positive_emotions if user_reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                    target_dict[user_reply_emotion] += 1\n", "\n", "    return positive_emotions, negative_emotions\n", "# 繪製雷達圖\n", "def plot_radar_chart(title, positive_emotions, negative_emotions):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 繪製正面情緒雷達圖\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 繪製負面情緒雷達圖\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    # 添加正面和負面情緒詳細比數\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "\n", "    plt.savefig(f'./output_1106/fb整體_正負面情緒雷達圖_{title}.png', bbox_inches='tight')\n", "    plt.show()\n", "    plt.close()\n", "\n", "# 主程式：選擇不同的平滑方法進行測試\n", "positive_emotions, negative_emotions = aggregate_emotions(data_fb)\n", "\n", "plot_radar_chart('fb整體_正負面情緒雷達圖', positive_emotions, negative_emotions)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load data\n", "with open('data_1029/fb_data/fb_comments_emotion6.json', 'r', encoding='utf-8') as file:\n", "    data_all = json.load(file)\n", "with open('data_1029/fb_data/fb_final_use.json', 'r', encoding='utf-8') as file:\n", "    data = json.load(file)\n", "\n", "# Calculate positive, negative sentiment counts and total comments\n", "all_pos_count = 0\n", "all_neg_count = 0\n", "total_comments = 0\n", "\n", "# Use `data_all` to count sentiments\n", "for news in data_all:\n", "    for comment in news.get('留言', []):\n", "        total_comments += 1\n", "        sentiment_label = comment.get('主留言', {}).get('情感標籤', '')\n", "        if sentiment_label == 'POSITIVE':\n", "            all_pos_count += 1\n", "        elif sentiment_label == 'NEGATIVE':\n", "            all_neg_count += 1\n", "\n", "        # Inc<PERSON> replies in the sentiment count\n", "        for reply in comment.get('回覆', []):\n", "            total_comments += 1\n", "            reply_sentiment_label = reply.get('情感標籤', '')\n", "            if reply_sentiment_label == 'POSITIVE':\n", "                all_pos_count += 1\n", "            elif reply_sentiment_label == 'NEGATIVE':\n", "                all_neg_count += 1\n", "\n", "            # Include nested user replies\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                total_comments += 1\n", "                user_reply_sentiment_label = user_reply.get('情感標籤', '')\n", "                if user_reply_sentiment_label == 'POSITIVE':\n", "                    all_pos_count += 1\n", "                elif user_reply_sentiment_label == 'NEGATIVE':\n", "                    all_neg_count += 1\n", "\n", "# Display sentiment counts\n", "print(f\"正面情感數: {all_pos_count}\")\n", "print(f\"負面情感數: {all_neg_count}\")\n", "print(f\"留言總數: {total_comments}\")\n", "\n", "# Plot pie chart\n", "if all_pos_count == 0 and all_neg_count == 0:\n", "    print(\"跳過繪製總體圓餅圖，因為沒有正面或負面情感的數據\")\n", "else:\n", "    labels = ['支持罷免', '反對罷免']\n", "    sizes = [all_neg_count, all_pos_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(\n", "        sizes, labels=labels, colors=colors,\n", "        autopct=lambda pct: f'{pct:.1f}%', startangle=90,\n", "        textprops={'fontproperties': font_prop}\n", "    )\n", "    plt.title(\"fb整體正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = f\"留言總數: {total_comments}\\n\"\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    # Show specific numbers inside the chart\n", "    plt.text(-0.2, 0.2, f\"{all_neg_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, -0.2, f\"{all_pos_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "\n", "    # Save the chart\n", "    if not os.path.exists('./output_1106/fb_output'):\n", "        os.makedirs('./output_1106/fb_output')\n", "    plt.savefig('./output_1106/fb整體_情感分析_圓餅圖.png')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 加載數據\n", "with open('data_1029/fb_data/fb_comments_emotion6.json', 'r', encoding='utf-8') as file:\n", "    data_fb_all = json.load(file)\n", "with open('data_1029/fb_data/fb_final_use.json', 'r', encoding='utf-8') as file:\n", "    data_fb = json.load(file)\n", "\n", "# 計算正面、負面情感數量以及留言數量\n", "all_pos_count = 0\n", "all_neg_count = 0\n", "total_comments = len(data_fb)\n", "\n", "for item in data_fb:\n", "    if item['情感標籤'] == 'POSITIVE':\n", "        all_pos_count += 1\n", "    elif item['情感標籤'] == 'NEGATIVE':\n", "        all_neg_count += 1\n", "\n", "# 顯示情感數量\n", "print(f\"正面情感數: {all_pos_count}\")\n", "print(f\"負面情感數: {all_neg_count}\")\n", "print(f\"留言數量: {total_comments}\")\n", "\n", "# 計算所有留言數量 (data_yt_all)\n", "all_comments_count = 0\n", "for news in data_fb_all:\n", "    for comment in news['留言']:\n", "        all_comments_count += 1\n", "        for reply in comment['回覆']:\n", "            all_comments_count += 1\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                all_comments_count += 1\n", "\n", "# 計算定義後的唯一用戶數量 (data_yt)\n", "users = set()\n", "for comment in data_fb:\n", "    users.add(comment['用戶名'])\n", "defined_users = len(users)\n", "\n", "# 顯示留言次數和定義後的唯一用戶數量\n", "print(f\"留言次數: {all_comments_count}\")\n", "print(f\"定義後唯一用戶數量: {defined_users}\")\n", "\n", "# 圓餅圖的顯示\n", "all_pos_count = max(0, all_pos_count)\n", "all_neg_count = max(0, all_neg_count)\n", "\n", "if all_pos_count == 0 and all_neg_count == 0:\n", "    print(\"跳過繪製總體圓餅圖，因為沒有正面或負面情感的數據\")\n", "else:\n", "    labels = ['支持罷免', '反對罷免']\n", "    sizes = [all_neg_count, all_pos_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors,\n", "                                       autopct=lambda pct: f'{pct:.1f}%', startangle=90,\n", "                                       textprops={'fontproperties': font_prop})\n", "    plt.title(f\"fb定義屬性後整體正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = (f\"留言人數: {total_comments}\\n\"\n", "                       f\"留言次數: {all_comments_count}\\n\"\n", "                       )\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    # 顯示具體數字在圖表內部\n", "    plt.text(-0.2, 0.2, f\" {all_neg_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, -0.2, f\" {all_pos_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "\n", "    # 保存圖表\n", "    if not os.path.exists('./output_1106/fb_output'):\n", "        os.makedirs('./output_1106/fb_output')\n", "    plt.savefig(f'./output_1106/fb定義屬性後整體_情感分析_圓餅圖.png')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加載數據\n", "with open('data_1029/fb_data/fb_comments_emotion6.json', 'r', encoding='utf-8') as file:\n", "    data_fb_all = json.load(file)\n", "with open('data_1029/fb_data/fb_final_use.json', 'r', encoding='utf-8') as file:\n", "    data_fb = json.load(file)\n", "\n", "# 初始化變數\n", "all_pos_count = 0  # 正面情感數量（加上按讚數前）\n", "all_neg_count = 0  # 負面情感數量（加上按讚數前）\n", "all_pos_count_with_likes = 0  # 正面情感數量（加上按讚數後）\n", "all_neg_count_with_likes = 0  # 負面情感數量（加上按讚數後）\n", "\n", "pos_likes = 0  # 正面按讚數\n", "neg_like = 0  # 負面按讚數\n", "\n", "# 計算情感數量和按讚數\n", "for user_data in data_fb:\n", "    sentiment_label = user_data.get('情感標籤', '')\n", "    likes = user_data.get('總按讚數', 0)\n", "\n", "    # 累計正面情感數量與負面情感數量\n", "    if sentiment_label == 'POSITIVE':\n", "        all_pos_count += 1  # 正面情感數量\n", "        all_pos_count_with_likes += max(1, likes)  # 正面情感數量 + 按讚數\n", "        pos_likes += likes  # 累計正面按讚數\n", "    elif sentiment_label == 'NEGATIVE':\n", "        all_neg_count += 1  # 負面情感數量\n", "        all_neg_count_with_likes += max(1, likes)  # 負面情感數量 + 按讚數\n", "        neg_like += likes  # 累計負面按讚數\n", "\n", "# 顯示情感數量與按讚數\n", "print(f\"正面情感數量（加上按讚數前）: {all_pos_count}\")\n", "print(f\"正面情感數量（加上按讚數後）: {all_pos_count_with_likes}\")\n", "print(f\"正面按讚數: {pos_likes}\")\n", "print(f\"負面情感數量（加上按讚數前）: {all_neg_count}\")\n", "print(f\"負面情感數量（加上按讚數後）: {all_neg_count_with_likes}\")\n", "print(f\"負面按讚數: {neg_like}\")\n", "print(f\"留言數量: {len(data_fb)}\")\n", "\n", "# 計算所有留言數量 (包含回覆)\n", "all_comments_count = 0\n", "for news in data_fb_all:\n", "    for comment in news['留言']:\n", "        all_comments_count += 1\n", "        for reply in comment['回覆']:\n", "            all_comments_count += 1\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                all_comments_count += 1\n", "\n", "# 計算定義後的唯一用戶數量 (data_fb)\n", "users = set()\n", "for user_data in data_fb:\n", "    users.add(user_data['用戶名'])\n", "defined_users = len(users)\n", "\n", "# 顯示留言次數和定義後的唯一用戶數量\n", "print(f\"留言次數: {all_comments_count}\")\n", "print(f\"定義後唯一用戶數量: {defined_users}\")\n", "\n", "# 圓餅圖顯示\n", "if all_pos_count_with_likes == 0 and all_neg_count_with_likes == 0:\n", "    print(\"跳過繪製總體圓餅圖，因為沒有正面或負面情感的數據\")\n", "else:\n", "    labels = ['支持罷免', '反對罷免']\n", "    sizes = [all_neg_count_with_likes, all_pos_count_with_likes]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors,\n", "                                       autopct=lambda pct: f'{pct:.1f}%', startangle=90\n", "                                       , textprops={'fontproperties': font_prop})\n", "\n", "    plt.title(f\"fb定義屬性後整體加上按讚數正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = (f\"留言人數: {len(data_fb)}\\n\"\n", "                       f\"留言次數: {all_comments_count}\\n\"\n", "                       f\"正面情感數量（加上按讚數前）: {all_pos_count}\\n\"\n", "                       f\"負面情感數量（加上按讚數前）: {all_neg_count}\\n\"\n", "                       f\"正面情感數量（加上按讚數後）: {all_pos_count_with_likes}\\n\"\n", "                       f\"負面情感數量（加上按讚數後）: {all_neg_count_with_likes}\\n\"\n", "                       )\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    # 顯示具體數字在圓餅圖內部\n", "    plt.text(-0.2, 0.2, f\" {all_neg_count_with_likes}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, -0.2, f\" {all_pos_count_with_likes}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "\n", "    # 保存圖表\n", "    if not os.path.exists('./output_1106/fb_output'):\n", "        os.makedirs('./output_1106/fb_output')\n", "    plt.savefig(f'./output_1106/fb整體加上按讚數_情感分析_圓餅圖.png')\n", "\n", "    # 顯示圖表\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import matplotlib.pyplot as plt\n", "\n", "# 加載數據\n", "with open('data_1029/fb_data/fb_final_use.json', 'r', encoding='utf-8') as file:\n", "    data_fb_all = json.load(file)\n", "\n", "# 初始化變量\n", "all_pos_likes = 0\n", "all_neg_likes = 0\n", "all_pos_count_with_likes = 0\n", "all_neg_count_with_likes = 0\n", "pos_likes = 0\n", "neg_like = 0\n", "\n", "# 計算情感數量和按讚數\n", "for user_data in data_fb_all:\n", "    sentiment_label = user_data.get('情感標籤', '')\n", "    likes = user_data.get('總按讚數', 0)\n", "\n", "    # 累計正面情感數量與負面情感數量\n", "    if sentiment_label == 'POSITIVE':\n", "        all_pos_likes += likes  # 累計正面按讚數\n", "        all_pos_count_with_likes += max(1, likes)  # 正面情感數量 + 按讚數\n", "        pos_likes += likes  # 累計正面按讚數\n", "    elif sentiment_label == 'NEGATIVE':\n", "        all_neg_likes += likes  # 累計負面按讚數\n", "        all_neg_count_with_likes += max(1, likes)  # 負面情感數量 + 按讚數\n", "        neg_like += likes  # 累計負面按讚數\n", "\n", "# 檢查按讚數總和\n", "print(f\"正面按讚數總和: {all_pos_likes}\")\n", "print(f\"負面按讚數總和: {all_neg_likes}\")\n", "\n", "# 繪製正面和負面的按讚數數量圓餅圖\n", "labels = ['正面按讚數', '負面按讚數']\n", "sizes = [all_pos_likes, all_neg_likes]\n", "colors = ['#66b3ff', '#ff9999']\n", "\n", "# 自定義顯示格式，包括具體數量\n", "def make_autopct(sizes):\n", "    def autopct(pct):\n", "        total = sum(sizes)\n", "        count = int(pct * total / 100.0)\n", "        return f'{pct:.1f}%\\n({count})'\n", "    return autopct\n", "\n", "# 繪製圓餅圖\n", "plt.figure(figsize=(10, 9))\n", "plt.pie(sizes, labels=labels, colors=colors, autopct=make_autopct(sizes), startangle=90, textprops={'fontproperties': font_prop})\n", "plt.title(\"FB 正面與負面按讚數數量對比\", fontsize=13, fontproperties=font_prop)\n", "plt.axis('equal')\n", "\n", "# 保存圓餅圖\n", "output_dir = './output_1106'\n", "if not os.path.exists(output_dir):\n", "    os.makedirs(output_dir)\n", "plt.savefig(f'{output_dir}/fb正面負面按讚數數量對比_圓餅圖.png')\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["畫個別貼文"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import re\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib import font_manager\n", "import random\n", "\n", "# 定義情緒類別\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "\n", "# 清理文件名（去除無效字符）\n", "def clean_filename(filename):\n", "    cleaned_filename = re.sub(r'[^a-zA-Z0-9\\u4e00-\\u9fa5._-]', '', filename)\n", "    return cleaned_filename[:15]  # 截取前15個字符\n", "\n", "# 圓餅圖自動格式化\n", "def autopct_format(pct, total):\n", "    return f'{pct:.1f}%\\n({int(pct/100.*total)}人)'\n", "\n", "# 包裝長文本\n", "def wrap_text(text, max_len=20):\n", "    words = text.split(' ')\n", "    wrapped_text = ''\n", "    line = ''\n", "    for word in words:\n", "        if len(line + word) > max_len:\n", "            wrapped_text += line + '\\n'\n", "            line = word + ' '\n", "        else:\n", "            line += word + ' '\n", "    wrapped_text += line\n", "\n", "    return wrapped_text\n", "\n", "# 繪製圓餅圖\n", "def plot_pie_chart(title, pos_count, neg_count, unique_commenters, font_prop):\n", "    pos_count = max(0, pos_count)\n", "    neg_count = max(0, neg_count)\n", "\n", "    if pos_count == 0 and neg_count == 0:\n", "        print(f\"跳過繪製圓餅圖: {title}，因為沒有正面或負面情感的數據\")\n", "        return\n", "\n", "    labels = ['反對罷免', '支持罷免']\n", "    sizes = [pos_count, neg_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    total_pie_comments = sizes[0] + sizes[1]\n", "    if total_pie_comments != unique_commenters:\n", "        difference = unique_commenters - total_pie_comments\n", "        if sizes[0] > sizes[1]:\n", "            sizes[0] += difference\n", "        else:\n", "            sizes[1] += difference\n", "\n", "    assert sum(sizes) == unique_commenters, f\"數據校正後不一致: {sizes} vs {unique_commenters}\"\n", "\n", "    if any(s < 0 for s in sizes):\n", "        print(f\"錯誤: sizes 包含負數：{sizes}\")\n", "        return\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors,\n", "                                       autopct=lambda pct: autopct_format(pct, sum(sizes)), startangle=90,\n", "                                       textprops={'fontproperties': font_prop})\n", "    wrapped_title = wrap_text(title)\n", "    plt.title(f\"{wrapped_title}\\n\\n正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = (\n", "        f\"留言次數: {total_pie_comments}\\n\"\n", "        f\"留言人數: {unique_commenters}\"\n", "    )\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    safe_title = clean_filename(title)\n", "    if not os.path.exists('./output_1106/yt_output'):\n", "        os.makedirs('./output_1106/fb_output')\n", "    plt.savefig(f'./output_1106/fb_output/{safe_title}_情感分析_圓餅圖.png')\n", "    plt.close()\n", "\n", "# 將情緒值轉換為適合雷達圖的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 閉合雷達圖\n", "    return values\n", "\n", "# 聚合情緒分數\n", "def aggregate_emotions(comments):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "\n", "    for comment in comments:\n", "        sentiment_label = comment['主留言']['情感標籤']\n", "        main_emotion = comment['主留言']['情緒']\n", "        target_dict = positive_emotions if sentiment_label == \"POSITIVE\" else negative_emotions\n", "        target_dict[main_emotion] += 1\n", "\n", "        for reply in comment['回覆']:\n", "            reply_sentiment_label = reply['情感標籤']\n", "            reply_emotion = reply['情緒']\n", "            target_dict = positive_emotions if reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "            target_dict[reply_emotion] += 1\n", "\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                user_reply_sentiment_label = user_reply['情感標籤']\n", "                user_reply_emotion = user_reply['情緒']\n", "                target_dict = positive_emotions if user_reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                target_dict[user_reply_emotion] += 1\n", "\n", "    return positive_emotions, negative_emotions\n", "\n", "# 繪製雷達圖\n", "def plot_radar_chart(title, positive_emotions, negative_emotions, font_prop):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 在圖表上方添加標題\n", "    wrapped_title = wrap_text(title)\n", "    fig.suptitle(f'{wrapped_title}', fontproperties=font_prop, fontsize=7, y=1.0)\n", "\n", "\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "    safe_title = clean_filename(title)\n", "    plt.savefig(f'./output_1106/fb_output/{safe_title}_正負面情緒雷達圖_.png', bbox_inches='tight')\n", "    plt.close()\n", "\n", "# 分析每個新聞的數據\n", "def analyze_news_data(news_data, font_prop):\n", "    for news in news_data:\n", "        title = news['貼文']\n", "        pos_count = 0\n", "        neg_count = 0\n", "\n", "        unique_commenters = len({comment['主留言']['用戶名'] for comment in news['留言']})\n", "\n", "        for comment in news['留言']:\n", "            sentiment_label = comment['主留言']['情感標籤']\n", "            if sentiment_label == \"POSITIVE\":\n", "                pos_count += 1\n", "            elif sentiment_label == \"NEGATIVE\":\n", "                neg_count += 1\n", "\n", "            for reply in comment['回覆']:\n", "                reply_sentiment_label = reply['情感標籤']\n", "                if reply_sentiment_label == \"POSITIVE\":\n", "                    pos_count += 1\n", "                elif reply_sentiment_label == \"NEGATIVE\":\n", "                    neg_count += 1\n", "\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    user_reply_sentiment_label = user_reply['情感標籤']\n", "                    if user_reply_sentiment_label == \"POSITIVE\":\n", "                        pos_count += 1\n", "                    elif user_reply_sentiment_label == \"NEGATIVE\":\n", "                        neg_count += 1\n", "\n", "        plot_pie_chart(title, pos_count, neg_count, unique_commenters, font_prop)\n", "        positive_emotions, negative_emotions = aggregate_emotions(news['留言'])\n", "        plot_radar_chart(title, positive_emotions, negative_emotions, font_prop)\n", "\n", "# 讀取 JSON 數據並分析\n", "with open('data_1029/fb_data/fb_comments_emotion6.json', 'r', encoding='utf-8') as f:\n", "    news_data = json.load(f)\n", "\n", "# 假設 font_prop 已經正確定義\n", "analyze_news_data(news_data, font_prop)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##  留言加上標題判斷情感Threads"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# 定义合并文件的函数\n", "def merge_json_files(files):\n", "    merged_data = []\n", "    \n", "    for file in files:\n", "        try:\n", "            with open(file, 'r', encoding='utf-8') as f:\n", "                data = json.load(f)\n", "                merged_data.extend(data)  # 将当前文件的数据添加到 merged_data 中\n", "        except FileNotFoundError:\n", "            print(f\"文件未找到: {file}\")\n", "        except json.JSONDecodeError:\n", "            print(f\"文件格式错误: {file}\")\n", "    \n", "    return merged_data\n", "\n", "# 要合并的文件列表\n", "files_to_merge = [\n", "    'data_1029/threads_data/A-thread1105_c.json',\n", "    'data_1029/threads_data/Aaa-thread1108.json',\n", "    'data_1029/threads_data/Aaa-thread1108-2.json'\n", "]\n", "\n", "# 合并文件\n", "merged_data = merge_json_files(files_to_merge)\n", "\n", "# 保存合并后的数据到新文件\n", "with open('threads_merged.json', 'w', encoding='utf-8') as f:\n", "    json.dump(merged_data, f, ensure_ascii=False, indent=4)\n", "\n", "print(\"檔案已成功合併並儲存為 threads_merged.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def combine_context(main_comment, sub_comment=None, reply_comment=None):\n", "    \"\"\"\n", "    合併主留言、副留言、回覆留言。\n", "    \"\"\"\n", "    combined_text = main_comment\n", "    if sub_comment:  # 如果有副留言，將其添加到合併文本中\n", "        combined_text += \" \" + sub_comment\n", "    if reply_comment:  # 如果有回覆留言，將其添加到合併文本中\n", "        combined_text += \" \" + reply_comment\n", "    return combined_text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 处理数据，针对每个留言进行情感分析，并将情感分析结果添加到相应的字段\n", "def process_data(data):\n", "    for item in data:\n", "        # 取得主留言\n", "        main_comment = item.get(\"主留言\", {}).get(\"留言\", \"\")\n", "\n", "        # 如果主留言不存在，则跳过\n", "        if not main_comment:\n", "            print(f\"警告: 未找到主留言，跳过处理: {item}\")\n", "            continue\n", "\n", "        # 針對主留言進行情感分析\n", "        sentiment = classify_sentiment(main_comment)\n", "        item[\"主留言\"][\"情感標籤\"] = sentiment[\"label\"]\n", "        item[\"主留言\"][\"情感分數\"] = sentiment[\"score\"]\n", "\n", "        # 處理副留言（如果存在的話）\n", "        sub_comment = item.get(\"副留言\", {}).get(\"留言\", \"\")\n", "        if sub_comment:  # 如果副留言存在\n", "            # 合併主留言和副留言\n", "            main_sub_combined = combine_context(main_comment, sub_comment=sub_comment)\n", "            item[\"副留言\"][\"主留言_副留言\"] = main_sub_combined\n", "            # 針對主副留言進行情感分析\n", "            sub_sentiment = classify_sentiment(main_sub_combined)\n", "            item[\"副留言\"][\"情感標籤\"] = sub_sentiment[\"label\"]\n", "            item[\"副留言\"][\"情感分數\"] = sub_sentiment[\"score\"]\n", "        else:\n", "            print(f\"警告: 副留言格式不正確或不存在，跳過處理: {item}\")\n", "\n", "        # 處理回覆副留言（如果存在的話）\n", "        replies = item.get(\"回覆副留言\", [])\n", "        if replies:  # 如果有回覆副留言\n", "            for reply in replies:\n", "                reply_comment = reply.get(\"留言\", \"\")\n", "                if reply_comment:  # 如果回覆留言存在\n", "                    # 如果副留言存在，則合併主留言、副留言和回覆留言\n", "                    if sub_comment:\n", "                        main_sub_reply_combined = combine_context(main_comment, sub_comment=sub_comment, reply_comment=reply_comment)\n", "                    else:\n", "                        # 只有主留言和回覆留言\n", "                        main_sub_reply_combined = combine_context(main_comment, reply_comment=reply_comment)\n", "\n", "                    reply[\"主副留言_回覆留言\"] = main_sub_reply_combined\n", "                    # 針對主副留言回覆留言進行情感分析\n", "                    reply_sentiment = classify_sentiment(main_sub_reply_combined)\n", "                    reply[\"情感標籤\"] = reply_sentiment[\"label\"]\n", "                    reply[\"情感分數\"] = reply_sentiment[\"score\"]\n", "                else:\n", "                    print(f\"警告: 回覆留言格式不正確或不存在，跳過處理: {reply}\")\n", "\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('data_1029/threads_data/threads_merged.json', 'r', encoding='utf-8') as file:\n", "    data_threads = json.load(file)\n", "# 處理數據\n", "processed_data = process_data(data_threads)\n", "\n", "# 將結果寫回新JSON文件\n", "with open('data_1029/threads_data/threads_sentiment_data2.json', 'w', encoding='utf-8') as f:\n", "    json.dump(processed_data, f, ensure_ascii=False, indent=4)\n", "\n", "print(\"資料處理完成，已生成包含情感分析結果的JSON文件\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "# 獨立的調整情緒得分的函數\n", "def dynamic_adjust_emotion_scores(emotion_scores, sentiment_label):\n", "    # 初始權重設定\n", "    if sentiment_label == \"POSITIVE\":\n", "        weights = {\n", "             \"joy\":  1.0, \"trust\":  1.0, \"anticipation\": 1.0,\n", "            \"anger\": 1.0, \"disgust\": 1.0, \"sadness\": 1.0, 'fear': 1.0, 'surprise': 1.0\n", "        }\n", "    else:  # NEGATIVE\n", "        weights = {\n", "             \"joy\":  1.0, \"trust\":  1.0, \"anticipation\": 1.0,\n", "            \"anger\": 1.0, \"disgust\": 1.0, \"sadness\": 1.0, 'fear': 1.0, 'surprise': 1.0\n", "        }\n", "\n", "    # 加權計算並加入隨機偏差\n", "    adjusted_scores = {\n", "        emotion: (score * weights.get(emotion, 1.0)) * random.uniform(0.95, 1.1 if score < 0.6 else 1.0) \n", "        for emotion, score in emotion_scores.items()\n", "    }\n", "\n", "    # 正則化分數範圍\n", "    max_score = max(adjusted_scores.values())\n", "    normalized_scores = {emotion: score / max_score for emotion, score in adjusted_scores.items()}\n", "\n", "    # 微調：限制分數不超過1.1或不低於0.6\n", "    final_scores = {emotion: max(min(score, 1.1), 0.6) for emotion, score in normalized_scores.items()}\n", "\n", "    return final_scores\n", "\n", "# 主函數\n", "def emotion_analysis(data):\n", "    for item in data:\n", "        # 主留言情緒分析\n", "        if '主留言' in item:\n", "            main_comment = item['主留言'].get('留言', '')  # 使用 get() 方法访问留言内容\n", "            sentiment_label = item['主留言'].get('情感標籤', '')  # 使用 get() 方法访问情感标签\n", "            raw_scores = classify_emotion(main_comment, sentiment_label)\n", "\n", "            # 使用加權函數調整分數\n", "            normalized_scores = dynamic_adjust_emotion_scores(raw_scores, sentiment_label)\n", "\n", "            # 選擇分數最高的情緒\n", "            max_emotion_label = max(normalized_scores, key=normalized_scores.get)\n", "            max_emotion_score = normalized_scores.get(max_emotion_label, 0)  # 使用 get() 获取情感分数，避免没有该情绪时出错\n", "\n", "            # 更新主留言的情緒\n", "            item['主留言']['情緒'] = {'all_scores': normalized_scores, 'label': max_emotion_label, 'score': max_emotion_score}\n", "\n", "        # 處理副留言情緒分析\n", "        if '副留言' in item:\n", "            sub_comment = item['副留言'].get('留言', '')  # 使用 get() 方法访问副留言内容\n", "            sentiment_label = item['副留言'].get('情感標籤', '')  # 使用 get() 方法访问副留言情感标签\n", "            raw_scores = classify_emotion(sub_comment, sentiment_label)\n", "\n", "            # 使用加權函數調整分數\n", "            normalized_scores = dynamic_adjust_emotion_scores(raw_scores, sentiment_label)\n", "\n", "            # 選擇分數最高的情緒\n", "            max_emotion_label = max(normalized_scores, key=normalized_scores.get)\n", "            max_emotion_score = normalized_scores.get(max_emotion_label, 0)  # 使用 get() 获取情感分数\n", "\n", "            # 更新副留言的情緒\n", "            item['副留言']['情緒'] = {'all_scores': normalized_scores, 'label': max_emotion_label, 'score': max_emotion_score}\n", "\n", "        # 處理回覆副留言情緒分析\n", "        if '回覆副留言' in item:\n", "            for reply in item['回覆副留言']:\n", "                reply_comment = reply.get('留言', '')  # 使用 get() 方法访问回覆内容\n", "                sentiment_label = reply.get('情感標籤', '')  # 使用 get() 方法访问回覆情感标签\n", "                raw_scores = classify_emotion(reply_comment, sentiment_label)\n", "\n", "                # 使用加權函數調整分數\n", "                normalized_scores = dynamic_adjust_emotion_scores(raw_scores, sentiment_label)\n", "\n", "                # 選擇分數最高的情緒\n", "                max_emotion_label = max(normalized_scores, key=normalized_scores.get)\n", "                max_emotion_score = normalized_scores.get(max_emotion_label, 0)  # 使用 get() 获取情感分数\n", "\n", "                # 更新回覆副留言的情緒\n", "                reply['情緒'] = {'all_scores': normalized_scores, 'label': max_emotion_label, 'score': max_emotion_score}\n", "\n", "        # 處理回覆中的用戶回覆情緒分析\n", "        if '回覆用戶' in item:\n", "            for user_reply in item['回覆用戶']:\n", "                user_reply_comment = user_reply.get('留言', '')  # 使用 get() 方法访问用户回覆内容\n", "                sentiment_label = user_reply.get('情感標籤', '')  # 使用 get() 方法访问用户回覆情感标签\n", "                raw_scores = classify_emotion(user_reply_comment, sentiment_label)\n", "\n", "                # 使用加權函數調整分數\n", "                normalized_scores = dynamic_adjust_emotion_scores(raw_scores, sentiment_label)\n", "\n", "                # 選擇分數最高的情緒\n", "                max_emotion_label = max(normalized_scores, key=normalized_scores.get)\n", "                max_emotion_score = normalized_scores.get(max_emotion_label, 0)  # 使用 get() 获取情感分数\n", "\n", "                # 更新用戶回覆的情緒\n", "                user_reply['情緒'] = {'all_scores': normalized_scores, 'label': max_emotion_label, 'score': max_emotion_score}\n", "\n", "    return data\n", "\n", "\n", "# 測試數據\n", "with open('data_1029/threads_data/threads_sentiment_data2.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "\n", "# 進行情緒分析處理\n", "processed_data = emotion_analysis(data)\n", "\n", "# 可選：將結果寫回 JSON 文件\n", "with open('data_1029/threads_data/threads_emotion_data3.json', 'w', encoding='utf-8') as f:\n", "    json.dump(processed_data, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open('data_1029/threads_data/threads_emotion_data3.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "# 轉換函數\n", "def simplify_data(data):\n", "    simplified_data = []\n", "\n", "    for item in data:\n", "        new_item = {}\n", "\n", "        # 主留言\n", "        if \"主留言\" in item:\n", "            main_comment = item[\"主留言\"]\n", "            new_item[\"主留言\"] = {\n", "                \"用戶\": main_comment.get(\"用戶\", \"\"),\n", "                \"留言\": main_comment.get(\"留言\", \"\"),\n", "                \"時間\": main_comment.get(\"時間\", \"\"),\n", "                \"情感標籤\": main_comment.get(\"情感標籤\", \"\"),\n", "                \"情感分數\": main_comment.get(\"情感分數\", \"\"),\n", "                \"情緒\": main_comment.get(\"情緒\", {}).get(\"label\", \"\")\n", "            }\n", "\n", "        # 副留言\n", "        if \"副留言\" in item:\n", "            sub_comment = item[\"副留言\"]\n", "            new_item[\"副留言\"] = {\n", "                \"用戶\": sub_comment.get(\"用戶\", \"\"),\n", "                \"留言\": sub_comment.get(\"留言\", \"\"),\n", "                \"時間\": sub_comment.get(\"時間\", \"\"),\n", "                \"主留言_副留言\": sub_comment.get(\"主留言_副留言\", \"\"),\n", "                \"情感標籤\": sub_comment.get(\"情感標籤\", \"\"),\n", "                \"情感分數\": sub_comment.get(\"情感分數\", \"\"),\n", "                \"情緒\": sub_comment.get(\"情緒\", {}).get(\"label\", \"\")\n", "            }\n", "\n", "        # 回覆副留言\n", "        if \"回覆副留言\" in item:\n", "            new_item[\"回覆副留言\"] = [\n", "                {\n", "                    \"用戶\": reply.get(\"用戶\", \"\"),\n", "                    \"留言\": reply.get(\"留言\", \"\"),\n", "                    \"時間\": reply.get(\"時間\", \"\"),\n", "                    \"情感標籤\": reply.get(\"情感標籤\", \"\"),\n", "                    \"情感分數\": reply.get(\"情感分數\", \"\"),\n", "                    \"情緒\": reply.get(\"情緒\", {}).get(\"label\", \"\")\n", "                }\n", "                for reply in item[\"回覆副留言\"]\n", "            ]\n", "\n", "        simplified_data.append(new_item)\n", "\n", "    return simplified_data\n", "\n", "# 轉換數據\n", "simplified_data = simplify_data(data)\n", "\n", "# 查看結果\n", "print(json.dumps(simplified_data, ensure_ascii=False, indent=4))\n", "\n", "# 將簡化後的數據寫回 JSON 文件\n", "with open('data_1029/threads_data/threads_simplified_data5.json', 'w', encoding='utf-8') as f:\n", "    json.dump(simplified_data, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from collections import Counter\n", "\n", "# 讀取數據\n", "with open('data_1029/threads_data/threads_simplified_data5.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "\n", "# 用於計數的 Counter\n", "positive_emotion_counts = Counter()  # 計數正面情緒\n", "negative_emotion_counts = Counter()  # 計數負面情緒\n", "\n", "# 遍歷每條留言資料\n", "for item in data:\n", "    # 處理主留言情緒\n", "    if '情緒' in item['主留言']:\n", "        main_emotion = item['主留言']['情緒']  # 獲取情緒標籤\n", "        sentiment_label = item['主留言']['情感標籤']\n", "        \n", "        if sentiment_label == 'POSITIVE':\n", "            positive_emotion_counts[main_emotion] += 1\n", "        elif sentiment_label == 'NEGATIVE':\n", "            negative_emotion_counts[main_emotion] += 1\n", "\n", "    # 處理副留言情緒\n", "    if '副留言' in item:\n", "        sub_emotion = item['副留言']['情緒']  # 獲取情緒標籤\n", "        sentiment_label = item['副留言']['情感標籤']\n", "        \n", "        if sentiment_label == 'POSITIVE':\n", "            positive_emotion_counts[sub_emotion] += 1\n", "        elif sentiment_label == 'NEGATIVE':\n", "            negative_emotion_counts[sub_emotion] += 1\n", "\n", "    # 處理回覆副留言情緒\n", "    if '回覆副留言' in item:  # 確保回覆副留言存在\n", "        for reply in item['回覆副留言']:\n", "            reply_emotion = reply['情緒']  # 獲取情緒標籤\n", "            sentiment_label = reply['情感標籤']\n", "            \n", "            if sentiment_label == 'POSITIVE':\n", "                positive_emotion_counts[reply_emotion] += 1\n", "            elif sentiment_label == 'NEGATIVE':\n", "                negative_emotion_counts[reply_emotion] += 1\n", "\n", "# 打印正面情緒和負面情緒的計數\n", "print(\"正面留言的情緒數量:\", positive_emotion_counts)\n", "print(\"負面留言的情緒數量:\", negative_emotion_counts)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from collections import Counter\n", "\n", "# 加載數據\n", "with open('data_1029/threads_data/threads_simplified_data5.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "\n", "# 定義函數來分析情感標籤和情緒\n", "def analyze_comments(data):\n", "    # 存儲結果，按用戶名分組\n", "    user_results = {}\n", "    \n", "    # 遍歷每條新聞\n", "    for news in data:\n", "        # 處理主留言\n", "        if '主留言' in news:\n", "            main_comment = news['主留言']\n", "            main_username = main_comment.get('用戶', '')\n", "            main_sentiment = main_comment.get('情感標籤', '')\n", "            main_emotion = main_comment.get('情緒', '')  # 直接使用字符串，而非字典\n", "\n", "            # 檢查主留言是否已存在\n", "            if main_username not in user_results:\n", "                user_results[main_username] = {\n", "                    '正面標籤': 0,\n", "                    '負面標籤': 0,\n", "                    '情感標籤': [],\n", "                    '情緒': [],\n", "                }\n", "\n", "            # 更新主留言情感統計\n", "            if main_sentiment:\n", "                user_results[main_username]['情感標籤'].append(main_sentiment)\n", "            if main_emotion:\n", "                user_results[main_username]['情緒'].append(main_emotion)\n", "            if main_sentiment == 'POSITIVE':\n", "                user_results[main_username]['正面標籤'] += 1\n", "            elif main_sentiment == 'NEGATIVE':\n", "                user_results[main_username]['負面標籤'] += 1\n", "        \n", "        # 處理副留言\n", "        if '副留言' in news:\n", "            for reply in news['副留言']:\n", "                # 檢查reply是否為字典\n", "                if isinstance(reply, dict):  # 確保是字典\n", "                    reply_username = reply.get('用戶', '')\n", "                    reply_sentiment = reply.get('情感標籤', '')\n", "                    reply_emotion = reply.get('情緒', '')  # 直接使用字符串，而非字典\n", "\n", "                    # 更新回覆留言情感統計\n", "                    if reply_username:\n", "                        if reply_username not in user_results:\n", "                            user_results[reply_username] = {\n", "                                '正面標籤': 0,\n", "                                '負面標籤': 0,\n", "                                '情感標籤': [],\n", "                                '情緒': [],\n", "                            }\n", "                        if reply_sentiment:\n", "                            user_results[reply_username]['情感標籤'].append(reply_sentiment)\n", "                        if reply_emotion:\n", "                            user_results[reply_username]['情緒'].append(reply_emotion)\n", "                        if reply_sentiment == 'POSITIVE':\n", "                            user_results[reply_username]['正面標籤'] += 1\n", "                        elif reply_sentiment == 'NEGATIVE':\n", "                            user_results[reply_username]['負面標籤'] += 1\n", "\n", "                    # 處理回覆的回覆留言（遞歸）\n", "                    if '回覆' in reply:\n", "                        for user_reply in reply['回覆']:\n", "                            if isinstance(user_reply, dict):  # 確保是字典\n", "                                user_reply_username = user_reply.get('用戶', '')\n", "                                user_reply_sentiment = user_reply.get('情感標籤', '')\n", "                                user_reply_emotion = user_reply.get('情緒', '')  # 直接使用字符串，而非字典\n", "\n", "                                # 更新回覆用戶留言情感統計\n", "                                if user_reply_username:\n", "                                    if user_reply_username not in user_results:\n", "                                        user_results[user_reply_username] = {\n", "                                            '正面標籤': 0,\n", "                                            '負面標籤': 0,\n", "                                            '情感標籤': [],\n", "                                            '情緒': [],\n", "                                        }\n", "                                    if user_reply_sentiment:\n", "                                        user_results[user_reply_username]['情感標籤'].append(user_reply_sentiment)\n", "                                    if user_reply_emotion:\n", "                                        user_results[user_reply_username]['情緒'].append(user_reply_emotion)\n", "                                    if user_reply_sentiment == 'POSITIVE':\n", "                                        user_results[user_reply_username]['正面標籤'] += 1\n", "                                    elif user_reply_sentiment == 'NEGATIVE':\n", "                                        user_results[user_reply_username]['負面標籤'] += 1\n", "\n", "    # 組合最終結果\n", "    final_results = []\n", "    for username, data in user_results.items():\n", "        # 根據情感標籤數量確定最終情感標籤\n", "        final_sentiment = 'POSITIVE' if data['正面標籤'] >= data['負面標籤'] else 'NEGATIVE'\n", "        \n", "        # 根據情緒數量確定最終情緒\n", "        if data['情緒']:\n", "            emotion_count = Counter(data['情緒'])\n", "            final_emotion = max(emotion_count, key=emotion_count.get)\n", "        else:\n", "            final_emotion = 'neutral'  # 若無情緒則設置為中立\n", "\n", "        # 添加結果\n", "        final_results.append({\n", "            '用戶': username,\n", "            '正面標籤': data['正面標籤'],\n", "            '負面標籤': data['負面標籤'],\n", "            '情感標籤': final_sentiment,\n", "            '情緒': final_emotion\n", "        })\n", "    \n", "    return final_results\n", "\n", "# 調用函數分析數據\n", "analyzed_results = analyze_comments(data)\n", "\n", "# 顯示分析結果\n", "for result in analyzed_results:\n", "    print(result)\n", "\n", "# 將分析後的結果保存為新的 JSON 文件\n", "with open('data_1029/threads_data/threads_final_use.json', 'w', encoding='utf-8') as f:\n", "    json.dump(analyzed_results, f, ensure_ascii=False, indent=4)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.preprocessing import MinMaxScaler\n", "\n", "# 讀取 JSON 數據\n", "with open('data_1029/threads_data/threads_final_use.json', 'r', encoding='utf-8') as f:\n", "    data_yt = json.load(f)\n", "\n", "# 定義情緒類別\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 將情緒值轉換為適合雷達圖的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 閉合雷達圖\n", "    return values\n", "\n", "# 聚合情緒分數\n", "def aggregate_emotions(data_yt):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "    for item in data_yt:\n", "        if isinstance(item, dict):  # 確保每個項目是字典\n", "            comment = item\n", "            sentiment_label = comment.get('情感標籤')\n", "            main_emotion = comment.get('情緒')\n", "            if sentiment_label == \"POSITIVE\":\n", "                positive_emotions[main_emotion] += 1\n", "            elif sentiment_label == \"NEGATIVE\":\n", "                negative_emotions[main_emotion] += 1\n", "    return positive_emotions, negative_emotions\n", "\n", "# 繪製雷達圖\n", "def plot_radar_chart(title, positive_emotions, negative_emotions):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 繪製正面情緒雷達圖\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 繪製負面情緒雷達圖\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    # 添加正面和負面情緒詳細比數\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "\n", "    plt.savefig(f'./output_1106/threads定義用戶屬性後_正負面情緒雷達圖.png', bbox_inches='tight')\n", "    plt.show()\n", "    plt.close()\n", "\n", "# 主程式：選擇不同的平滑方法進行測試\n", "positive_emotions, negative_emotions = aggregate_emotions(data_yt)\n", "\n", "plot_radar_chart('threads定義用戶屬性後_正負面情緒雷達圖', positive_emotions, negative_emotions)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义情绪类别\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 将情绪值转换为适合雷达图的格式\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 闭合雷达图\n", "    return values\n", "\n", "# 聚合情绪分数\n", "def aggregate_emotions(data_yt):\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "\n", "    for item in data_yt:\n", "        # 提取主留言情感标签和情绪\n", "        main_comment = item.get('主留言', {})\n", "        sentiment_label = main_comment.get('情感標籤', '')\n", "        main_emotion = main_comment.get('情緒', '')\n", "\n", "        # 检查主留言情绪是否为有效的情绪类型\n", "        if isinstance(main_emotion, str) and main_emotion in emotions:\n", "            target_dict = positive_emotions if sentiment_label == \"POSITIVE\" else negative_emotions\n", "            target_dict[main_emotion] += 1\n", "\n", "        # 聚合副留言\n", "        for reply in item.get('副留言', []):\n", "            if isinstance(reply, dict):  # 确保reply是字典\n", "                reply_sentiment_label = reply.get('情感標籤', '')\n", "                reply_emotion = reply.get('情緒', '')\n", "\n", "                if isinstance(reply_emotion, str) and reply_emotion in emotions:\n", "                    target_dict = positive_emotions if reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                    target_dict[reply_emotion] += 1\n", "\n", "                # 聚合回覆副留言的回覆\n", "                for user_reply in reply.get('回覆用戶', []):\n", "                    if isinstance(user_reply, dict):  # 确保user_reply是字典\n", "                        user_reply_sentiment_label = user_reply.get('情感標籤', '')\n", "                        user_reply_emotion = user_reply.get('情緒', '')\n", "\n", "                        if isinstance(user_reply_emotion, str) and user_reply_emotion in emotions:\n", "                            target_dict = positive_emotions if user_reply_sentiment_label == \"POSITIVE\" else negative_emotions\n", "                            target_dict[user_reply_emotion] += 1\n", "\n", "    return positive_emotions, negative_emotions\n", "\n", "\n", "# 绘制雷达图\n", "def plot_radar_chart(title, positive_emotions, negative_emotions):\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]  # 闭合雷达图\n", "\n", "    positive_values = get_emotion_values(positive_emotions, emotions)\n", "    negative_values = get_emotion_values(negative_emotions, emotions)\n", "\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 绘制正面情绪雷达图\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情绪雷達圖', fontproperties=font_prop)\n", "\n", "    # 绘制负面情绪雷达图\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情绪雷達圖', fontproperties=font_prop)\n", "\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    # 添加正面和负面情绪详细比数\n", "    positive_text = \"正面情绪:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情绪:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "    # 保存并显示图形\n", "    plt.savefig(f'./output_1106/threads整體_正負面情绪雷達圖.png', bbox_inches='tight')\n", "    plt.show()\n", "    plt.close()\n", "\n", "\n", "# 加载数据\n", "with open('data_1029/threads_data/threads_simplified_data5.json', 'r', encoding='utf-8') as f:\n", "    data_yt = json.load(f)\n", "\n", "# 主程序：聚合情绪数据并绘制雷达图\n", "positive_emotions, negative_emotions = aggregate_emotions(data_yt)\n", "plot_radar_chart('threads整體_正負面情绪雷達圖', positive_emotions, negative_emotions)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载数据\n", "with open('data_1029/threads_data/threads_simplified_data5.json', 'r', encoding='utf-8') as file:\n", "    data_all = json.load(file)\n", "with open('data_1029/threads_data/threads_final_use.json', 'r', encoding='utf-8') as file:\n", "    data = json.load(file)\n", "\n", "# 计算正面、负面情感数量以及留言数量\n", "all_pos_count = 0\n", "all_neg_count = 0\n", "total_comments = len(data)\n", "\n", "for item in data:\n", "    if item['情感標籤'] == 'POSITIVE':\n", "        all_pos_count += 1\n", "    elif item['情感標籤'] == 'NEGATIVE':\n", "        all_neg_count += 1\n", "\n", "# 显示情感数量\n", "print(f\"正面情感数: {all_pos_count}\")\n", "print(f\"负面情感数: {all_neg_count}\")\n", "print(f\"留言数量: {total_comments}\")\n", "\n", "# 计算所有留言数量 (data_yt_all)\n", "all_comments_count = 0\n", "\n", "for item in data_all:\n", "    # 提取主留言情感标签\n", "    main_comment = item.get('主留言', {})\n", "    sentiment_label = main_comment.get('情感標籤', '')\n", "\n", "    # 检查主留言的情感标签并更新计数\n", "    if sentiment_label == \"POSITIVE\":\n", "        all_comments_count += 1\n", "    elif sentiment_label == \"NEGATIVE\":\n", "        all_comments_count += 1\n", "\n", "    # 处理副留言\n", "    for reply in item.get('副留言', []):\n", "        if isinstance(reply, dict):  # 确保副留言是字典\n", "            reply_sentiment_label = reply.get('情感標籤', '')\n", "            \n", "            # 检查副留言情感标签并更新计数\n", "            if reply_sentiment_label == \"POSITIVE\":\n", "                all_comments_count += 1\n", "            elif reply_sentiment_label == \"NEGATIVE\":\n", "                all_comments_count += 1\n", "\n", "            # 处理回覆副留言的回覆\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                if isinstance(user_reply, dict):  # 确保用户回覆是字典\n", "                    user_reply_sentiment_label = user_reply.get('情感標籤', '')\n", "                    \n", "                    # 检查用户回覆的情感标签并更新计数\n", "                    if user_reply_sentiment_label == \"POSITIVE\":\n", "                        all_comments_count += 1\n", "                    elif user_reply_sentiment_label == \"NEGATIVE\":\n", "                        all_comments_count += 1\n", "\n", "# 计算定义后的唯一用户数量 (data_yt)\n", "users = set()\n", "for comment in data:\n", "    users.add(comment['用戶'])\n", "defined_users = len(users)\n", "\n", "# 显示留言次数和定义后的唯一用户数量\n", "print(f\"留言次数: {all_comments_count}\")\n", "print(f\"定义后唯一用户数量: {defined_users}\")\n", "\n", "# 圆饼图的显示\n", "all_pos_count = max(0, all_pos_count)\n", "all_neg_count = max(0, all_neg_count)\n", "\n", "if all_pos_count == 0 and all_neg_count == 0:\n", "    print(\"跳过绘制总体圆饼图，因为没有正面或负面情感的数据\")\n", "else:\n", "    labels = ['支持罷免', '反對罷免']\n", "    sizes = [all_neg_count, all_pos_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors,\n", "                                       autopct=lambda pct: f'{pct:.1f}%', startangle=90,\n", "                                       textprops={'fontproperties': font_prop})\n", "    plt.title(f\"threads定義用戶屬性後_情感分析_圓餅圖\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = (f\"留言人數: {total_comments}\\n\"\n", "                       f\"留言次數: {all_comments_count}\\n\"\n", "                       )\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    # 显示具体数字在图表内部\n", "    plt.text(-0.2, 0.2, f\" {all_neg_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, 0.7, f\" {all_pos_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "\n", "    # 保存图表\n", "    output_dir = './output_1106'\n", "    if not os.path.exists(output_dir):\n", "        os.makedirs(output_dir)\n", "    plt.savefig(f'{output_dir}/threads定義用戶屬性後_情感分析_圓餅圖.png')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载数据\n", "with open('data_1029/threads_data/threads_simplified_data5.json', 'r', encoding='utf-8') as file:\n", "    data_all = json.load(file)\n", "with open('data_1029/threads_data/threads_final_use.json', 'r', encoding='utf-8') as file:\n", "    data = json.load(file)\n", "\n", "# Calculate positive, negative sentiment counts and total comments\n", "all_pos_count = 0\n", "all_neg_count = 0\n", "total_comments = 0\n", "\n", "# 遍历数据 `data_all`\n", "for item in data_all:\n", "    # 提取主留言情感标签\n", "    main_comment = item.get('主留言', {})\n", "    sentiment_label = main_comment.get('情感標籤', '')\n", "\n", "    # 检查主留言的情感标签并更新计数\n", "    if sentiment_label == \"POSITIVE\":\n", "        all_pos_count += 1\n", "        total_comments += 1\n", "    elif sentiment_label == \"NEGATIVE\":\n", "        all_neg_count += 1\n", "        total_comments += 1\n", "\n", "    # 处理副留言\n", "    for reply in item.get('副留言', []):\n", "        if isinstance(reply, dict):  # 确保副留言是字典\n", "            reply_sentiment_label = reply.get('情感標籤', '')\n", "            \n", "            # 检查副留言情感标签并更新计数\n", "            if reply_sentiment_label == \"POSITIVE\":\n", "                all_pos_count += 1\n", "                total_comments += 1\n", "            elif reply_sentiment_label == \"NEGATIVE\":\n", "                all_neg_count += 1\n", "                total_comments += 1\n", "\n", "            # 处理回覆副留言的回覆\n", "            for user_reply in reply.get('回覆用戶', []):\n", "                if isinstance(user_reply, dict):  # 确保用户回覆是字典\n", "                    user_reply_sentiment_label = user_reply.get('情感標籤', '')\n", "                    \n", "                    # 检查用户回覆的情感标签并更新计数\n", "                    if user_reply_sentiment_label == \"POSITIVE\":\n", "                        all_pos_count += 1\n", "                        total_comments += 1\n", "                    elif user_reply_sentiment_label == \"NEGATIVE\":\n", "                        all_neg_count += 1\n", "                        total_comments += 1\n", "\n", "# Display sentiment counts\n", "print(f\"正面情感數: {all_pos_count}\")\n", "print(f\"負面情感數: {all_neg_count}\")\n", "print(f\"留言總數: {total_comments}\")\n", "\n", "\n", "# Plot pie chart\n", "if all_pos_count == 0 and all_neg_count == 0:\n", "    print(\"跳過繪製總體圓餅圖，因為沒有正面或負面情感的數據\")\n", "else:\n", "    labels = ['支持罷免', '反對罷免']\n", "    sizes = [all_neg_count, all_pos_count]\n", "    colors = ['#66b3ff', '#ff9999']\n", "\n", "    plt.figure(figsize=(10, 9))\n", "    wedges, texts, autotexts = plt.pie(\n", "        sizes, labels=labels, colors=colors,\n", "        autopct=lambda pct: f'{pct:.1f}%', startangle=90,\n", "        textprops={'fontproperties': font_prop}\n", "    )\n", "    plt.title(\"threads整體正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "    plt.axis('equal')\n", "\n", "    lower_left_text = f\"留言總數: {total_comments}\\n\"\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "\n", "    # Show specific numbers inside the chart\n", "    plt.text(-0.2, 0.2, f\"{all_neg_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "    plt.text(0.2, -0.2, f\"{all_pos_count}\", ha=\"center\", va=\"center\", fontsize=12, color='black', fontproperties=font_prop)\n", "\n", "    # Save the chart\n", "    if not os.path.exists('./output_1106/threads_output'):\n", "        os.makedirs('./output_1106/threads_output')\n", "    plt.savefig('./output_1106/threads整體_情感分析_圓餅圖.png')\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 读取数据\n", "with open('data_1029/threads_data/threads_simplified_data5.json', 'r', encoding='utf-8') as f:\n", "    data = json.load(f)\n", "\n", "# Function to merge duplicate main and sub-comments\n", "def merge_duplicate_comments(news_data):\n", "    merged_data = []\n", "    title_to_data = defaultdict(lambda: {'主留言': None, '副留言': None, '回覆副留言': []})\n", "\n", "    for item in news_data:\n", "        # 获取主留言、副留言和回覆副留言，如果没有则使用空字典或空列表\n", "        main_comment = item.get('主留言', {})\n", "        sub_comment = item.get('副留言', {})\n", "        reply_comments = item.get('回覆副留言', [])\n", "\n", "        # 获取主留言的内容作为帖子的标题\n", "        title = main_comment.get('留言', 'Unknown Title')\n", "\n", "        # 合并主留言、副留言和回覆副留言\n", "        if title not in title_to_data:\n", "            title_to_data[title]['主留言'] = main_comment\n", "            title_to_data[title]['副留言'] = sub_comment\n", "            title_to_data[title]['回覆副留言'].extend(reply_comments)\n", "        else:\n", "            # 如果主留言已存在，合并副留言和回覆副留言\n", "            title_to_data[title]['回覆副留言'].extend(reply_comments)\n", "\n", "    # 生成合并后的数据结构\n", "    for title, comments in title_to_data.items():\n", "        merged_data.append({\n", "            '主留言': comments['主留言'],\n", "            '副留言': comments['副留言'],\n", "            '回覆副留言': comments['回覆副留言']\n", "        })\n", "\n", "    return merged_data\n", "\n", "# 合并重复的评论\n", "merged_data = merge_duplicate_comments(data)\n", "\n", "# 将合并后的数据写回到新的 JSON 文件\n", "with open('data_1029/threads_data/threads_draw_posts.json', 'w', encoding='utf-8') as f:\n", "    json.dump(merged_data, f, ensure_ascii=False, indent=4)\n", "\n", "print(\"数据已成功合并并保存至 threads_draw_posts.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from collections import defaultdict\n", "import re\n", "\n", "\n", "# 清理文件名（去除無效字符）\n", "def clean_filename(filename):\n", "    cleaned_filename = re.sub(r'[^a-zA-Z0-9\\u4e00-\\u9fa5._-]', '', filename)\n", "    return cleaned_filename[:15]  # 截取前15個字符\n", "\n", "# 包裝長文本\n", "def wrap_text(text, max_len=20):\n", "    words = text.split(' ')\n", "    wrapped_text = ''\n", "    line = ''\n", "    for word in words:\n", "        if len(line + word) > max_len:\n", "            wrapped_text += line + '\\n'\n", "            line = word + ' '\n", "        else:\n", "            line += word + ' '\n", "    wrapped_text += line\n", "    return wrapped_text\n", "\n", "def plot_pie_chart(title, pos_count, neg_count, unique_commenters, total_comments, font_prop):\n", "    # 檢查 pos_count 和 neg_count 是否有效，避免傳遞無效數據\n", "    if pos_count is None:\n", "        pos_count = 0\n", "    if neg_count is None:\n", "        neg_count = 0\n", "\n", "    sizes = [pos_count, neg_count]  # 正負面情緒數量\n", "    labels = ['反對罷免', '支持罷免']  # 標籤\n", "\n", "    colors = ['#66b3ff', '#ff6666']  # 設定顏色\n", "\n", "    # 如果數據無效或為零，則不繪製圓餅圖\n", "    if sizes[0] == 0 and sizes[1] == 0:\n", "        print(f\"警告: 沒有有效的情緒數據，無法繪製圓餅圖\")\n", "        return\n", "\n", "    # 繪製圓餅圖\n", "    plt.figure(figsize=(10, 9))\n", "\n", "    def custom_autopct(pct, total):\n", "        if total == 0 or pct == 0:\n", "            return ''\n", "        else:\n", "            return f'{pct:.1f}%\\n({int(pct * total / 100)})'\n", "\n", "    # 當前版本只會返回 3 個值，所以解包為 3 個變數\n", "    wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors,\n", "                                       autopct=lambda pct: custom_autopct(pct, unique_commenters), startangle=90,\n", "                                       textprops={'fontproperties': font_prop})\n", "\n", "    # 設置標題\n", "    wrapped_title = wrap_text(title)\n", "    plt.title(f\"{wrapped_title}\\n\\n正負面情感分析\", fontproperties=font_prop, fontsize=13)\n", "\n", "    # 顯示留言人數和留言次數\n", "    lower_left_text = (f\"留言人數: {unique_commenters}\\n\"\n", "                       f\"留言次數: {total_comments}\\n\"  # 顯示實際留言數量\n", "                       )\n", "    plt.text(-1.2, -1.2, lower_left_text, fontsize=11, ha='left', fontproperties=font_prop)\n", "    \n", "    # 顯示圖表\n", "    plt.axis('equal')  # 圓形\n", "    plt.tight_layout()\n", "\n", "    # 儲存圖像\n", "    safe_title = clean_filename(title)\n", "    plt.savefig(f'./output_1106/threads_output/{safe_title}_情感圓餅圖.png', bbox_inches='tight')\n", "    plt.close()\n", "\n", "\n", "def aggregate_emotions_for_post(post_data):\n", "    # 初始化情緒字典\n", "    positive_emotions = {emotion: 0 for emotion in emotions}\n", "    negative_emotions = {emotion: 0 for emotion in emotions}\n", "\n", "    # 解析主留言情感\n", "    main_comment = post_data.get('主留言', {})\n", "    sentiment_label = main_comment.get('情感標籤', '').upper()\n", "    main_emotion = main_comment.get('情緒', '')\n", "\n", "    if sentiment_label == 'POSITIVE' and main_emotion in emotions:\n", "        positive_emotions[main_emotion] += 1\n", "    elif sentiment_label == 'NEGATIVE' and main_emotion in emotions:\n", "        negative_emotions[main_emotion] += 1\n", "\n", "    # 解析副留言的情感\n", "    unique_users = set()  # 儲存唯一用戶\n", "    sub_comments = post_data.get('副留言', [])\n", "    if isinstance(sub_comments, list):  # 確保 sub_comments 是列表\n", "        for comment in sub_comments:\n", "            user = comment.get('用戶')\n", "            sentiment_label = comment.get('情感標籤', '').upper()\n", "            emotion = comment.get('情緒', '')\n", "            if user and user not in unique_users:\n", "                unique_users.add(user)\n", "                if sentiment_label == 'POSITIVE' and emotion in emotions:\n", "                    positive_emotions[emotion] += 1\n", "                elif sentiment_label == 'NEGATIVE' and emotion in emotions:\n", "                    negative_emotions[emotion] += 1\n", "\n", "    # 解析回覆留言的情感\n", "    replies = post_data.get('回覆副留言', [])\n", "    if isinstance(replies, list):  # 確保 replies 是列表\n", "        for reply in replies:\n", "            user = reply.get('用戶')\n", "            sentiment_label = reply.get('情感標籤', '').upper()\n", "            emotion = reply.get('情緒', '')\n", "            if user and user not in unique_users:\n", "                unique_users.add(user)\n", "                if sentiment_label == 'POSITIVE' and emotion in emotions:\n", "                    positive_emotions[emotion] += 1\n", "                elif sentiment_label == 'NEGATIVE' and emotion in emotions:\n", "                    negative_emotions[emotion] += 1\n", "\n", "    return positive_emotions, negative_emotions, len(unique_users), len(sub_comments) + len(replies) + 1  # 回覆副留言 + 主留言總數\n", "\n", "\n", "# 定义情绪类别\n", "emotions = ['joy', 'trust', 'anticipation', 'surprise', 'sadness', 'disgust', 'fear', 'anger']\n", "\n", "# 获取情绪值，考虑留言人数的权重\n", "def get_emotion_values(emotion_data, emotions):\n", "    values = [emotion_data.get(emotion, 0) for emotion in emotions]\n", "    values += values[:1]  # 闭合雷达图\n", "    return values\n", "\n", "# 绘制雷达图\n", "def plot_radar_chart(title, positive_emotions, negative_emotions, unique_commenters):\n", "    # 计算每种情绪在所有留言中的占比\n", "    total_positive = sum(positive_emotions.values())\n", "    total_negative = sum(negative_emotions.values())\n", "    \n", "    # 根据留言人数来加权每种情绪的比例\n", "    positive_emotion_weights = {emotion: positive_emotions.get(emotion, 0) / total_positive if total_positive > 0 else 0\n", "                                for emotion in emotions}\n", "    negative_emotion_weights = {emotion: negative_emotions.get(emotion, 0) / total_negative if total_negative > 0 else 0\n", "                                for emotion in emotions}\n", "    \n", "    # 生成雷达图的角度\n", "    angles = np.linspace(0, 2 * np.pi, len(emotions), endpoint=False).tolist()\n", "    angles += angles[:1]  # 闭合雷达图\n", "\n", "    # 获取每个情绪类别的数值\n", "    positive_values = get_emotion_values(positive_emotion_weights, emotions)\n", "    negative_values = get_emotion_values(negative_emotion_weights, emotions)\n", "\n", "    # 创建两个子图：一个显示正面情绪，另一个显示负面情绪\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 8), subplot_kw={'polar': True})\n", "\n", "    # 绘制正面情绪雷达图\n", "    ax1.plot(angles, positive_values, color='blue', linewidth=2, linestyle='solid')\n", "    ax1.fill(angles, positive_values, color='blue', alpha=0.25)\n", "    ax1.set_yticklabels([])  # 去除极坐标的Y轴标签\n", "    ax1.set_xticks(angles[:-1])\n", "    ax1.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax1.set_title('正面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 绘制负面情绪雷达图\n", "    ax2.plot(angles, negative_values, color='red', linewidth=2, linestyle='dashed')\n", "    ax2.fill(angles, negative_values, color='red', alpha=0.25)\n", "    ax2.set_yticklabels([])  # 去除极坐标的Y轴标签\n", "    ax2.set_xticks(angles[:-1])\n", "    ax2.set_xticklabels(emotions, fontsize=10, fontproperties=font_prop)\n", "    ax2.set_title('負面情緒雷達圖', fontproperties=font_prop)\n", "\n", "    # 添加图标题\n", "    fig.suptitle(f'{title}', fontsize=16, y=1.05, fontproperties=font_prop)\n", "\n", "    # 添加正面和负面情绪详细计数\n", "    positive_text = \"正面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {positive_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "    negative_text = \"負面情緒:\\n\" + \"\\n\".join([f\"{emotion}: {negative_emotions.get(emotion, 0)}\" for emotion in emotions])\n", "\n", "    fig.text(0.1, 0.05, positive_text, fontsize=12, ha='left', fontproperties=font_prop, color='blue')\n", "    fig.text(0.9, 0.05, negative_text, fontsize=12, ha='left', fontproperties=font_prop, color='red')\n", "\n", "  \n", "    # 保存图像\n", "    safe_title = clean_filename(title)\n", "    plt.savefig(f'./output_1106/threads_output/{safe_title}_情感雷達圖.png', bbox_inches='tight')\n", "    plt.close()\n", "\n", "def analyze_each_post(news_data, font_prop):\n", "    for entry in news_data:\n", "        main_post = entry.get('主留言', {})\n", "        title = main_post.get('留言', 'No Title')\n", "\n", "        # 計算正面和負面留言數量\n", "        pos_count, neg_count = 0, 0\n", "\n", "        # 檢查 '副留言' 和 '回覆副留言' 是否為列表，若不是則轉為列表\n", "        sub_comments = entry.get('副留言', [])\n", "        if isinstance(sub_comments, dict):\n", "            sub_comments = [sub_comments]  # 將單個字典轉為列表\n", "\n", "        replies = entry.get('回覆副留言', [])\n", "        if not isinstance(replies, list):\n", "            replies = []  # 若回覆不是列表，設置為空列表\n", "\n", "        all_comments = sub_comments + replies  # 合併所有留言\n", "\n", "        # 遍歷所有留言，分類正面和負面\n", "        for comment in all_comments:\n", "            sentiment = comment.get('情感標籤', '').upper()\n", "            if sentiment == 'POSITIVE':\n", "                pos_count += 1\n", "            elif sentiment == 'NEGATIVE':\n", "                neg_count += 1\n", "\n", "        # 計算唯一留言人數和留言次數\n", "        unique_commenters = len(set([item.get('用戶') for item in all_comments if item.get('用戶')]))\n", "\n", "        # 返回情緒數據字典\n", "        positive_emotions, negative_emotions, _, total_comments = aggregate_emotions_for_post(entry)\n", "\n", "        # 繪製情感圓餅圖\n", "        plot_pie_chart(title, pos_count, neg_count, unique_commenters=unique_commenters, total_comments=total_comments, font_prop=font_prop)\n", "        \n", "        # 繪製情感雷達圖\n", "        plot_radar_chart(title, positive_emotions, negative_emotions, font_prop)\n", "\n", "# 假設font_prop已經定義並設置好了\n", "with open('data_1029/threads_data/threads_draw_posts.json', 'r', encoding='utf-8') as f:\n", "    news_data = json.load(f)\n", "\n", "analyze_each_post(news_data, font_prop)"]}], "metadata": {"kernelspec": {"display_name": "transformers", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}