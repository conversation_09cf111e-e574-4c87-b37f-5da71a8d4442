# 社交媒體分析爬蟲專案

![專案狀態](https://img.shields.io/badge/狀態-活躍開發-green)
![Python版本](https://img.shields.io/badge/Python-3.8+-blue)
![授權](https://img.shields.io/badge/授權-MIT-green)

> 一個全方位的社交媒體多平台爬蟲工具，專為政治人物輿情分析設計，支援 YouTube、PTT、Threads、Facebook 四大平台的資料收集、用戶分流、情感分析與資料庫儲存。

## 📋 目錄

- [專案概述](#專案概述)
- [核心功能](#核心功能)
- [系統架構](#系統架構)
- [資料夾結構](#資料夾結構)
- [安裝指南](#安裝指南)
- [使用說明](#使用說明)
- [技術詳解](#技術詳解)
- [資料流程](#資料流程)
- [API 參考](#api-參考)
- [常見問題](#常見問題)
- [開發指南](#開發指南)

## 🎯 專案概述

本專案是一個高度模組化的社交媒體爬蟲系統，專為台灣政治輿情分析而設計。系統採用標準化的資料夾結構、支援多平台並行爬取、提供完整的數據處理流程，從 URL 收集到最終的情感分析結果。

### 核心特色

- **🔄 多平台整合**: 統一調用 YouTube、PTT、Threads、Facebook 爬蟲
- **⚡ 高效能設計**: WebDriver 池化、多線程處理、記憶體優化
- **📊 完整分析**: URL 收集 → 留言爬取 → 用戶分流 → 情感分析 → 資料庫儲存
- **🛡️ 穩定可靠**: 中斷恢復、錯誤處理、日誌記錄、資料一致性
- **🎛️ 彈性配置**: 支援日期篩選、平台選擇、除錯模式、批次處理

## 🚀 核心功能

### 1. 多平台爬蟲引擎

| 平台 | 功能 | 資料類型 | 特色功能 |
|------|------|----------|----------|
| **YouTube** | 影片搜尋 + 評論爬取 | 影片資訊、留言、回覆 | 日期篩選、多線程、展開回覆 |
| **PTT** | 看板搜尋 + 推文爬取 | 文章標題、推文、噓文 | 多線程、批次處理 |
| **Threads** | 貼文搜尋 + 回覆爬取 | 貼文內容、回覆留言 | 自動滾動、展開更多 |
| **Facebook** | 個人頁面 + 留言爬取 | 貼文、留言、互動數據 | 登入管理、反爬蟲處理 |

### 2. 高效能優化系統

- **WebDriver 池化**: 重複使用瀏覽器實例，減少啟動時間
- **多線程處理**: URL 收集與留言爬取並行處理
- **記憶體管理**: Chrome 進程自動清理、靜默日誌
- **錯誤恢復**: 自動重試、中斷恢復、狀態檢查

### 3. 完整的資料處理流程

```
URL 收集 → 留言爬取 → 資料清理 → 用戶分流 → 情感分析 → 資料庫儲存
    ↓           ↓           ↓          ↓          ↓           ↓
  href/      data/    processed/   user_data/  final_data/  MongoDB
```

## 🏗️ 系統架構

### 核心模組

```
backend/
├── main.py                    # 🎯 主程式入口
├── crawler/                   # 🕷️ 爬蟲模組
│   ├── multi_platform_crawler.py    # 多平台整合器
│   ├── yt_crawler.py              # YouTube 爬蟲
│   ├── ptt_crawler.py             # PTT 爬蟲  
│   ├── thread_crawler.py          # Threads 爬蟲
│   ├── fb_crawler.py              # Facebook 爬蟲
│   ├── webdriver_pool.py          # WebDriver 池
│   ├── user_data_processor.py     # 用戶分流器
│   ├── gemini_emo_user.py         # 情感分析
│   ├── data_to_mongo.py           # MongoDB 儲存
│   └── organize_directories.py    # 資料夾組織
```

### 流程控制

```python
# 主流程執行順序
1. 資料夾結構初始化      organize_directories.py
2. 多平台爬蟲執行        multi_platform_crawler.py
3. 資料清理與整合        自動執行
4. 用戶資料分流          user_data_processor.py
5. 情感分析處理          gemini_emo_user.py
6. 資料庫儲存            data_to_mongo.py
```
MONGODB_DBNAME=legislator_recall

# Railway MongoDB (如果使用)
RAILWAY_URI=********************************:port
RAILWAY_DBNAME=legislator_recall

# 應用 URL (用於自我 ping)
APP_URL=https://your-app-url.onrender.com

# 可選：明確指定 alldata 目錄路徑
ALLDATA_DIR=/path/to/alldata
```

## 📁 資料夾結構

系統採用標準化的資料夾結構，確保資料組織清晰：

```
backend/crawler/
├── href/                     # 🔗 URL 收集資料
│   ├── youtube/             # YouTube 影片 URL
│   ├── ptt/                 # PTT 文章 URL
│   ├── threads/             # Threads 貼文 URL
│   └── facebook/            # Facebook 貼文 URL
│
├── data/                     # 📊 爬取原始資料
│   ├── youtube/             # YouTube 留言資料
│   ├── ptt/                 # PTT 推文資料
│   ├── threads/             # Threads 回覆資料
│   └── facebook/            # Facebook 留言資料
│
├── processed/                # 🔄 處理後資料
│   ├── alldata/             # 整合所有平台資料
│   ├── user_data/           # 用戶統計分析資料
│   └── final_data/          # 情感分析結果
│
├── logs/                     # 📝 執行日誌
└── backups/                  # 💾 資料備份
```

## 🛠️ 安裝指南

### 系統需求

- **Python**: 3.8 或更高版本
- **作業系統**: Windows / macOS / Linux
- **瀏覽器**: Chrome (自動下載 ChromeDriver)
- **記憶體**: 建議 8GB 以上

### 快速安裝

```bash
# 1. 克隆專案
git clone https://github.com/your-repo/social-media-analize.git
cd social-media-analize

# 2. 創建虛擬環境
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 3. 安裝相依套件
pip install -r backend/requirements.txt

# 4. 安裝額外套件
pip install google-generativeai

# 5. 初始化資料夾結構
cd backend/crawler
python organize_directories.py
```

### 環境配置

```bash
# 設定 MongoDB 連接 (可選)
export MONGODB_URI="mongodb://localhost:27017"

# 設定 Gemini API Key (情感分析需要)
export GEMINI_API_KEY="your-api-key-here"
```

## 📖 使用說明

### 基本使用

```bash
# 進入專案目錄
cd backend

# 基本爬蟲執行 (無頭模式)
python main.py --legislators 葉元之 --days 7

# 除錯模式 (顯示瀏覽器)
python main.py --legislators 葉元之 --days 7 --debug-browser

# 多立委批次處理
python main.py --legislators 葉元之 江啟臣 徐欣瑩 --days 14

# 指定平台爬取
python main.py --legislators 葉元之 --platforms youtube ptt --days 7
```

### 進階參數

| 參數 | 說明 | 預設值 | 範例 |
|------|------|--------|------|
| `--legislators` | 指定立委名單 | 必填 | `--legislators 葉元之 江啟臣` |
| `--days` | 爬取天數 | 7 | `--days 14` |
| `--platforms` | 指定平台 | all | `--platforms youtube ptt` |
| `--debug-browser` | 顯示瀏覽器 | False | `--debug-browser` |
| `--validate-only` | 僅驗證設定 | False | `--validate-only` |
| `--skip-crawl` | 跳過爬蟲階段 | False | `--skip-crawl` |
| `--skip-processing` | 跳過資料處理 | False | `--skip-processing` |

### 模組化執行

```bash
# 僅執行 YouTube 爬蟲
python crawler/yt_crawler.py

# 僅執行用戶分流
python crawler/user_data_processor.py

# 僅執行情感分析
python crawler/gemini_emo_user.py --legislators 葉元之

# 僅執行資料庫儲存
python crawler/data_to_mongo.py
```

## 🔧 技術詳解

### 1. URL 收集策略

**多線程搜尋機制**
```python
# YouTube 搜尋策略
def search_youtube_videos(name, output_dir='./href/youtube', headless=True):
    - 關鍵字搜尋
    - 無限滾動載入
    - 日期解析與篩選
    - 去除 Shorts 影片
    - JSON 格式儲存
```

**PTT 搜尋策略**
```python
# PTT 搜尋策略  
def search_ptt_posts(name, board='Gossiping', days=7):
    - 多看板搜尋
    - 時間範圍篩選
    - 推文數量篩選
    - 批次處理
```

### 2. 留言爬取技術

**智能展開機制**
```python
# 自動點擊展開更多留言/回覆
def click_buttons(driver):
    - 滾動觸發載入
    - 智能按鈕識別
    - 重試機制
    - 錯誤處理
```

**多線程爬取架構**
```python
# WebDriver 池化處理
def crawl_with_pool(name, webdriver_pool, max_threads=3):
    - 線程池管理
    - 資源分配優化
    - 並行處理
    - 結果聚合
```

### 3. WebDriver 優化技術

**池化管理**
```python
class WebDriverPool:
    - 實例重複使用
    - 生命週期管理
    - 記憶體清理
    - 錯誤自動恢復
```

**性能優化選項**
```python
chrome_options = [
    "--headless=new",           # 新版無頭模式
    "--disable-gpu",            # 禁用 GPU
    "--disable-extensions",     # 禁用擴展
    "--single-process",         # 單進程模式
    "--disable-dev-shm-usage",  # 禁用共享記憶體
    "--remote-debugging-port=0" # 避免端口衝突
]
```

### 4. 資料處理流程

**資料清理與標準化**
```python
# 統一資料格式
{
    "platform": "youtube|ptt|threads|facebook",
    "user_name": "使用者名稱",
    "content": "留言內容",
    "timestamp": "2025-01-01 12:00:00",
    "source_url": "原始網址",
    "metadata": {...}
}
```

**用戶分流算法**
```python
# 用戶留言聚合
def process_user_comments():
    - 按用戶名分組
    - 時間排序
    - 重複內容過濾
    - 統計資訊計算
```

### 5. 情感分析引擎

**Gemini AI 整合**
```python
# 多 API Key 輪替
def analyze_emotions():
    - API 負載均衡
    - 批次處理優化
    - 錯誤重試機制
    - 結果標準化
```

**情感標籤系統**
```python
emotions = {
    "正面": ["支持", "讚同", "加油"],
    "負面": ["反對", "批評", "失望"],
    "中性": ["討論", "分析", "說明"]
}
```

## 📊 資料流程

### 完整流程圖

```
爬蟲執行流程：

[開始] → [初始化資料夾結構] → [多平台 URL 收集]
                                       ↓
                                [WebDriver 池]
                               ↙   ↓   ↓   ↘
                        YouTube PTT Threads Facebook
                               ↘   ↓   ↓   ↙
                            [資料清理與標準化]
                                       ↓
                              [用戶分流處理]
                                       ↓
                               [情感分析]
                                       ↓
                              [MongoDB 儲存]
                                       ↓
                                   [完成]
```

### 資料格式轉換

```
原始爬蟲資料 → 標準化格式 → 用戶聚合 → 情感分析 → 最終儲存
    JSON         JSON        JSON       JSON      MongoDB
```

## 🔌 API 參考

### 核心函數

```python
# 主程式接口
def main(legislators, days=7, platforms=['all'], headless=True):
    """主要執行函數"""

# 多平台爬蟲
def crawl_multiple_platforms(legislators, platforms, days, headless=True):
    """多平台爬蟲執行器"""

# WebDriver 池
class WebDriverPool:
    def get_driver():        # 獲取 WebDriver 實例
    def release_driver():    # 釋放 WebDriver 實例
    def cleanup():          # 清理資源

# 用戶分流
def process_user_data(legislators):
    """處理用戶資料分流"""

# 情感分析
def analyze_legislators_emotions(legislators, batch_size=500):
    """立委情感分析"""

# 資料庫操作
class DataToMongo:
    def store_legislator_data():  # 儲存立委資料
    def get_legislator_data():    # 獲取立委資料
    def backup_database():        # 備份資料庫
```

### 配置參數

```python
# 爬蟲配置
CRAWLER_CONFIG = {
    'youtube': {
        'max_videos': 50,
        'max_comments': 1000,
        'scroll_pause': 2
    },
    'ptt': {
        'boards': ['Gossiping', 'HatePolitics'],
        'max_posts': 100
    },
    'threads': {
        'max_posts': 50,
        'scroll_attempts': 20
    },
    'facebook': {
        'login_required': True,
        'max_posts': 30
    }
}
```

## ❓ 常見問題

### Q1: 爬蟲執行失敗怎麼辦？

**A**: 檢查以下項目：
1. 網路連接是否正常
2. Chrome 瀏覽器是否已安裝
3. 相依套件是否完整安裝
4. 目標網站是否有反爬蟲限制

```bash
# 診斷命令
python main.py --validate-only --debug-browser
```

### Q2: 記憶體使用量過高？

**A**: 調整以下參數：
```python
# 減少並行線程數
MAX_THREADS = 2

# 啟用 WebDriver 池化
USE_WEBDRIVER_POOL = True

# 增加垃圾回收頻率
import gc
gc.collect()
```

### Q3: 某個平台無法爬取？

**A**: 平台特定問題解決：
- **YouTube**: 檢查 API 配額
- **PTT**: 確認看板存在
- **Threads**: 檢查登入狀態
- **Facebook**: 需要有效的登入憑證

### Q4: 資料不完整或重複？

**A**: 資料品質控制：
```bash
# 重新執行資料清理
python crawler/organize_directories.py

# 檢查中斷恢復機制
python main.py --legislators 葉元之 --days 7 --resume
```

### Q5: 無頭模式下看不到執行過程？

**A**: 使用除錯模式：
```bash
# 顯示瀏覽器視窗
python main.py --legislators 葉元之 --debug-browser

# 檢查詳細日誌
tail -f logs/crawler.log
```

## 🔨 開發指南

### 新增平台爬蟲

1. **創建爬蟲模組**
```python
# 新建 platform_crawler.py
def crawl_platform(name, headless=True):
    # 實作爬蟲邏輯
    pass

def crawl_platform_with_pool(name, webdriver_pool):
    # 實作 WebDriver 池版本
    pass
```

2. **整合到多平台爬蟲**
```python
# 在 multi_platform_crawler.py 中添加
from platform_crawler import crawl_platform

PLATFORM_CRAWLERS = {
    'new_platform': crawl_platform
}
```

### 自訂資料處理

```python
# 繼承基礎處理器
class CustomProcessor(UserDataProcessor):
    def custom_filter(self, data):
        # 自訂篩選邏輯
        return filtered_data
        
    def custom_analysis(self, data):
        # 自訂分析邏輯
        return analysis_result
```

### 效能調優

```python
# 調整爬蟲參數
PERFORMANCE_CONFIG = {
    'webdriver_pool_size': 5,
    'max_concurrent_threads': 3,
    'scroll_pause_time': 1,
    'request_delay': 0.5,
    'memory_limit': '4GB'
}
```

### 監控與日誌

```python
# 設定詳細日誌
import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/debug.log'),
        logging.StreamHandler()
    ]
)
```

## 📈 版本歷史

### v2.0.0 (2025-01-05)
- ✨ 新增 WebDriver 池化機制
- 🔧 重構資料夾結構標準化
- ⚡ 多線程性能優化
- 🛡️ 增強錯誤處理與恢復機制

### v1.5.0 (2024-12-XX)
- ✨ 新增 Threads 平台支援
- 🔧 改進情感分析準確度
- 📊 完善資料統計功能

### v1.0.0 (2024-11-XX)
- 🎉 初始版本發布
- 📱 支援 YouTube、PTT、Facebook
- 🤖 整合 Gemini AI 情感分析


