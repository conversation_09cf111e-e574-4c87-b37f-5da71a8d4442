#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
YouTube 爬蟲時間訊息修正測試
這個腳本將測試 yt_crawler.py 中的訊息輸出
"""

import sys
import os
import time
import random
from datetime import datetime, timedelta

# 模擬 yt_crawler.py 中的情況
def search_youtube_videos_test():
    # 模擬 search_youtube_videos 函數
    max_wait_time = 60  # 這是實際代碼中的值
    
    print(f"🔍 測試開始...")
    print(f"⚠️ 顯示超時消息: {max_wait_time}秒內沒有新影片，結束搜尋")
    
    # 測試是否有其他地方顯示 30 秒的訊息
    time.sleep(1)
    print(f"⏰ YouTube: 30秒內沒有新影片，結束搜尋") # 這是輸出中的問題訊息
    
    print(f"🔍 測試結束...")

if __name__ == "__main__":
    search_youtube_videos_test()
