{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\Desktop\\儲存庫\\GEMINI\\.venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"name": "stdout", "output_type": "stream", "text": ["我是一個大型語言模型，由 Google 訓練。\n"]}], "source": ["\n", "import google.generativeai as genai\n", "import csv\n", "import random\n", "import pandas as pd #畫圖的\n", "import seaborn as sns\n", "import os\n", "import google.generativeai as genai\n", "import time\n", "genai.configure(api_key=\"AIzaSyCbhqxVF-jvIDxyzBzlFHJThoF8SQB8ufQ\")\n", "\n", "model = genai.GenerativeModel(\"gemini-1.5-flash\")\n", "response = model.generate_content(\"你是誰??\")\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "import google.generativeai as genai\n", "import csv\n", "import random\n", "import pandas as pd #畫圖的\n", "import seaborn as sns\n", "import os\n", "import google.generativeai as genai\n", "import time\n", "import time\n", "import random\n", "from google import genai\n", "from google.genai import types\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "\n", "# 讀取 JSON 檔案\n", "input_file = \"data/0323/0323_myself.json\"\n", "output_file = \"data/0323/驗證-人工標.json\"\n", "\n", "# 讀取原始數據\n", "with open(input_file, \"r\", encoding=\"utf-8\") as f:\n", "    data = json.load(f)\n", "\n", "# 確保有足夠的數據\n", "num_samples = min(100, len(data))\n", "\n", "# 隨機選取 100 筆資料\n", "sampled_data = random.sample(data, num_samples)\n", "\n", "# 寫入驗證檔案\n", "with open(output_file, \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(sampled_data, f, ensure_ascii=False, indent=2)\n", "\n", "print(f\"成功隨機抽取 {num_samples} 筆資料並存成 {output_file}\")\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功抓取 100 筆匹配的資料，並儲存至 data\\0323\\驗證-bert.json 和 data\\0323\\驗證-myself.json\n"]}], "source": ["import json\n", "import os\n", "\n", "def extract_matching_data_and_save(bert_file, myself_file, output_bert_file, output_myself_file):\n", "    \"\"\"\n", "    從 bert.json 和 myself.json 檔案中各抓取相同的 100 筆資料，並分別儲存為驗證-bert.json 和 驗證-myself.json。\n", "\n", "    Args:\n", "        bert_file (str): 0323_bert.json 檔案的路徑。\n", "        myself_file (str): 0323_myself.json 檔案的路徑。\n", "        output_bert_file (str): 驗證-bert.json 檔案的路徑。\n", "        output_myself_file (str): 驗證-myself.json 檔案的路徑。\n", "    \"\"\"\n", "\n", "    try:\n", "        # 讀取 0323_bert.json\n", "        with open(bert_file, 'r', encoding='utf-8') as f:\n", "            bert_data = json.load(f)\n", "\n", "        # 讀取 0323_myself.json\n", "        with open(myself_file, 'r', encoding='utf-8') as f:\n", "            myself_data = json.load(f)\n", "\n", "        # 尋找匹配的資料\n", "        matching_bert_data = []\n", "        matching_myself_data = []\n", "        count = 0  # 計數器，追蹤匹配的資料數量\n", "\n", "        for bert_item in bert_data:\n", "            bert_username = bert_item.get(\"用戶名\", \"\")\n", "            bert_content = bert_item.get(\"留言內容\", \"\")\n", "            bert_title = bert_item.get(\"標題\", \"\")\n", "\n", "            for myself_item in myself_data:\n", "                myself_username = myself_item.get(\"用戶名\", \"\")\n", "                myself_content = myself_item.get(\"留言內容\", \"\")\n", "                myself_title = myself_item.get(\"標題\", \"\")\n", "\n", "                if bert_username == myself_username and bert_content == myself_content and bert_title == myself_title:\n", "                    matching_bert_data.append(bert_item)\n", "                    matching_myself_data.append(myself_item)\n", "                    count += 1\n", "                    if count >= 100:  # 如果找到 100 筆，跳出迴圈\n", "                        break\n", "            if count >= 100:  # 如果找到 100 筆，跳出迴圈\n", "                break\n", "\n", "        # 儲存匹配的資料\n", "        with open(output_bert_file, 'w', encoding='utf-8') as f:\n", "            json.dump(matching_bert_data, f, ensure_ascii=False, indent=2)\n", "\n", "        with open(output_myself_file, 'w', encoding='utf-8') as f:\n", "            json.dump(matching_myself_data, f, ensure_ascii=False, indent=2)\n", "\n", "        print(f\"成功抓取 {len(matching_bert_data)} 筆匹配的資料，並儲存至 {output_bert_file} 和 {output_myself_file}\")\n", "\n", "    except FileNotFoundError as e:\n", "        print(f\"錯誤：找不到檔案 {e.filename}\")\n", "    except json.JSONDecodeError as e:\n", "        print(f\"錯誤：檔案 {e.filename} 不是有效的 JSON 格式\")\n", "    except Exception as e:\n", "        print(f\"發生錯誤：{e}\")\n", "\n", "# 設定檔案路徑\n", "bert_file = \"data\\\\0323\\\\0323_bert.json\"\n", "myself_file = \"data\\\\0323\\\\0323_myself.json\"\n", "output_bert_file = \"data\\\\0323\\\\驗證-bert.json\"\n", "output_myself_file = \"data\\\\0323\\\\驗證-myself.json\"\n", "\n", "# 執行程式\n", "extract_matching_data_and_save(bert_file, myself_file, output_bert_file, output_myself_file)"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"context\": \"各國都在挺台 結果我們的國會某兩黨用盡辦法要怎麼賣台⋯⋯真是有夠機掰\",\n", "  \"reply\": \"案 !! 案陰陽欸老吉排 !!!!\\n\\n講得真他媽好 !!\",\n", "  \"label\": \"Positive\",\n", "  \"emotion\": \"Anger\"\n", "}\n"]}], "source": ["import json\n", "import time\n", "import random\n", "\n", "def analyze_comment(context, reply, max_retries=10):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-5698\")  # 使用訓練過的模型\n", "    \n", "    prompt = f\"\"\"\n", "    請分析以下留言，並判斷這則留言的情感（Sentiment）及情緒（Emotion）。\n", "    \n", "    特別注意：\n", "    - context 為背景資訊，它只是補充，不影響留言的主情緒。\n", "    - reply 為主要留言，請根據 reply 來判斷正負面以及情緒。\n", "    - 在政治留言中，批評某個政治人物不一定是負面情感，可能代表對另一位政治人物的支持，因此請仔細判斷語境。\n", "    - 以下情境都為國民黨謝國樑的罷免案，正面代表不支持罷免支持謝國樑，負面代表支持罷免不支持謝國樑。\n", "\n", "    背景資訊 (context)：「{context}」\n", "    主要留言 (reply)：「{reply}」\n", "\n", "    請回答以下問題：\n", "    1. 這則留言場景為現任國民黨謝國樑市長的罷免案，請判斷該留言的情感是正面還是負面？請回答：\"POSITIVE\" 或 \"NEGATIVE\"。\n", "    2. 這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "\n", "    **輸出格式 (JSON)**：\n", "    {{\n", "      \"context\": \"{context}\",\n", "      \"reply\": \"{reply}\",\n", "      \"label\": \"<POSITIVE 或 NEGATIVE>\",\n", "      \"emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\"\n", "    }}\n", "    \"\"\"\n", "    \n", "    for attempt in range(max_retries + 1):\n", "        try:\n", "            response = model.generate_content(prompt)\n", "            result = json.loads(response.text)\n", "            return result\n", "        except Exception:\n", "            pass\n", "        \n", "        if attempt < max_retries:\n", "            time.sleep(random.uniform(1,3))\n", "        else:\n", "            return None\n", "\n", "# 測試範例\n", "test_data = {\n", "    \"context\": \"各國都在挺台 結果我們的國會某兩黨用盡辦法要怎麼賣台⋯⋯真是有夠機掰\",\n", "    \"reply\": \"案 !! 案陰陽欸老吉排 !!!!\\n\\n講得真他媽好 !!\",\n", "}\n", "\n", "result = analyze_comment(test_data[\"context\"], test_data[\"reply\"], max_retries=10)\n", "print(json.dumps(result, ensure_ascii=False, indent=2))\n"]}, {"cell_type": "code", "execution_count": 121, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"context\": \"「基隆市長謝國樑罷免案「未通過」 拆樑團體宣布罷免失敗」\",\n", "  \"reply\": \"「骯髒綠色黨再亂搞阿！」\",\n", "  \"label\": \"Negative\",\n", "  \"emotion\": \"Anger\"\n", "}\n"]}], "source": ["import json\n", "import re\n", "import time\n", "\n", "def analyze_comment(context, reply, max_retries=5):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-5698\")  # 使用訓練過的模型\n", "    \n", "    prompt = f\"\"\"\n", "    請分析以下留言，並判斷這則留言的情感（Sentiment）及情緒（Emotion）。\n", "    \n", "    特別注意：\n", "    - context 為背景資訊，它只是補充，不影響留言的主情緒。\n", "    - reply 為主要留言，請根據 reply 來判斷正負面以及情緒。\n", "    - 在政治留言中，批評某個政治人物不一定是負面情感，可能代表對另一位政治人物的支持，因此請仔細判斷語境。\n", "    - 以下情境都為國民黨謝國樑的罷免案，正面代表不支持罷免支持謝國樑，負面代表支持罷免不支持謝國樑。\n", "\n", "    背景資訊 (context)：「{context}」\n", "    主要留言 (reply)：「{reply}」\n", "\n", "    請回答以下問題：\n", "    1. 這則留言場景為現任國民黨謝國樑市長的罷免案，請判斷該留言的情感是正面還是負面？請回答：\"POSITIVE\" 或 \"NEGATIVE\"。\n", "    2. 這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "\n", "    **輸出格式**：\n", "    Context: <context>\n", "    Reply: <reply>\n", "    Label: <POSITIVE 或 NEGATIVE>\n", "    Emotion: <Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\n", "    \"\"\"\n", "    \n", "    for attempt in range(max_retries):\n", "        try:\n", "            response = model.generate_content(prompt)\n", "            response_text = response.text.strip()\n", "            extracted_data = extract_text_data(response_text)\n", "            return extracted_data  # 直接回傳提取的欄位\n", "        except Exception as e:\n", "            print(f\"⚠️ 解析錯誤 (嘗試 {attempt + 1}/{max_retries})，請檢查模型輸出格式！\")\n", "            print(\"原始輸出：\", response_text)\n", "            print(\"錯誤訊息：\", str(e))\n", "            time.sleep(1)  # 等待 1 秒後重試\n", "    \n", "    print(\"❌ 解析失敗，跳過該留言！\")\n", "    return None\n", "\n", "def extract_text_data(response_text):\n", "    # 使用正則表達式提取數據\n", "    context_match = re.search(r'Context: (.+)', response_text)\n", "    reply_match = re.search(r'Reply: (.+)', response_text)\n", "    label_match = re.search(r'Label: (POSITIVE|NEGATIVE)', response_text)\n", "    emotion_match = re.search(r'Emotion: (Joy|Trust|Anticipation|Sadness|Surprise|Disgust|Fear|Anger)', response_text)\n", "    \n", "    return {\n", "        \"context\": context_match.group(1) if context_match else \"Unknown\",\n", "        \"reply\": reply_match.group(1) if reply_match else \"Unknown\",\n", "        \"label\": label_match.group(1) if label_match else \"Unknown\",\n", "        \"emotion\": emotion_match.group(1) if emotion_match else \"Unknown\"\n", "    }\n", "\n", "# 測試範例\n", "test_data = {\n", "    \"context\": \"基隆市長謝國樑罷免案「未通過」 拆樑團體宣布罷免失敗\",\n", "    \"reply\": \"骯髒綠色黨再亂搞阿！\",\n", "}\n", "\n", "result = analyze_comment(test_data[\"context\"], test_data[\"reply\"])\n", "print(json.dumps(result, ensure_ascii=False, indent=2))\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功讀取檔案: data/100_model_predict.json\n", "資料筆數: 90\n", "\n", "前5筆資料範例:\n", "\n", "第1筆:\n", "Context: 2/1「立委就職滿一年」 罷免團體衝刺連署\n", "Reply: TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "True Sentiment: POSITIVE\n", "True Emotion: anger\n", "\n", "第2筆:\n", "Context: 鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\n", "Reply: 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "True Sentiment: POSITIVE\n", "True Emotion: anger\n", "\n", "第3筆:\n", "Context: 【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\n", "Reply: #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "True Sentiment: NEGATIVE\n", "True Emotion: sadness\n", "\n", "第4筆:\n", "Context: 徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\n", "Reply: #鏡新聞 中指可以比出來啊～\n", "True Sentiment: POSITIVE\n", "True Emotion: fear\n", "\n", "第5筆:\n", "Context: 罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\n", "Reply: 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "True Sentiment: NEGATIVE\n", "True Emotion: disgust\n"]}], "source": ["def load_json(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return json.load(f)\n", "def test_process_comments(input_file, output_file):\n", "    # 測試讀取檔案\n", "    try:\n", "        data = load_json(input_file)\n", "        print(f\"成功讀取檔案: {input_file}\")\n", "        print(f\"資料筆數: {len(data)}\")\n", "        print(\"\\n前5筆資料範例:\")\n", "        for i, item in enumerate(data[:5]):\n", "            print(f\"\\n第{i+1}筆:\")\n", "            print(f\"Context: {item.get('context', '')}\")\n", "            print(f\"Reply: {item.get('reply', '')}\")\n", "            print(f\"True Sentiment: {item.get('true_sentiment', '')}\")\n", "            print(f\"True Emotion: {item.get('true_emotion', '')}\")\n", "    except Exception as e:\n", "        print(f\"讀取檔案失敗: {str(e)}\")\n", "\n", "# 設定輸入與輸出檔案\n", "input_file = \"data/100_model_predict.json\"\n", "test_process_comments(input_file, \"data/100_model_predict.json\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def build_prompt(context, reply):\n", "    return f\"\"\"\n", "請依據下列規則，對一則留言進行情緒與情感判斷，並輸出指定格式的結果：\n", "\n", "標體與主要內容僅作為判斷的依據，請完整的輸出不用增加甚麼內容\n", "請回答以下問題：\n", "1. 請根據「標題 (context)」與「主要留言 (reply)」判斷留言的立場與情緒。\n", "2. 輸出兩個欄位：\n", "   - Label：根據留言內容判斷立場，若留言支持罷免葉元之、支持民進黨或批評國民黨 → 判斷為 \"NEGATIVE\"；若留言反對罷免葉元之、支持國民黨或批評民進黨 → 判斷為 \"POSITIVE\"。\n", "   - Emotion：根據留言情感選出一個對應的情緒，僅能為以下八種之一，請用小寫英文：\n", "       - \"joy\"：表達高興、愉悅或滿足。\n", "       - \"trust\"：表達信任、信心或安心。\n", "       - \"anticipation\"：表達期待、預期或希望。\n", "       - \"sadness\"：表達悲傷、失望或沮喪。\n", "       - \"surprise\"：表達驚訝、詫異或震驚。\n", "       - \"disgust\"：表達厭惡、反感或憎恨。\n", "       - \"fear\"：表達恐懼、擔憂或不安。\n", "       - \"anger\"：表達憤怒、不滿或怨恨。\n", "       \n", "補充重要規則：\n", "- POSITIVE 情緒代表不支持罷免，NEGATIVE 情緒代表支持罷免。\n", "- 葉元之為國民黨籍，支持國民黨或支持相關立委想要罷免總統的留言為 POSITIVE。\n", "- 支持民進黨或支持民進黨相關立委或罷免葉元之的留言為 NEGATIVE。\n", "- 針對或討厭民進黨的會被算在 POSITIVE，如果出現 POSITIVE 的話，情感會出現更多正面情感例如：joy、trust、anticipation。\n", "\n", "\n", "輸出格式要求：\n", "- 請嚴格遵守以下純文字格式，不要輸出任何額外文字或 JSON 格式。\n", "- 所有情緒詞彙必須以小寫字母表示。\n", "- 請嚴格按照以下順序輸出欄位。\n", "- 輸出格式：\n", "    Context: <context>\n", "    Reply: <reply>\n", "    Label: <POSITIVE 或 NEGATIVE>\n", "    Emotion: <joy / trust / anticipation / sadness / surprise / disgust / fear / anger>\n", "\n", "範例輸出(僅作參考，不可照抄)：\n", "Context: {context}\n", "Reply: {reply}\n", "Label: <POSITIVE 或 NEGATIVE>\n", "Emotion: <joy / trust / anticipation / sadness / surprise / disgust / fear / anger>\n", "\n", "需要進行判斷的留言:\n", "標題 (context)：{context}\n", "主要留言 (reply)：{reply}\n", "\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def build_prompt2(context, reply):\n", "    return f\"\"\"\n", "請將以下政治留言進行情緒與立場分類，並傳回有效 JSON（Valid JSON）。\n", "\n", "任務說明：\n", "你將獲得一則「標題」（context）與「主要留言」（reply）。\n", "請根據這兩項內容，判斷該留言的立場（label）與情緒（emotion），並以指定格式回傳 JSON。\n", "\n", "判斷規則：\n", "- 若留言支持罷免葉元之、支持民進黨、或批評國民黨 → 判斷為 \"NEGATIVE\"\n", "- 若留言反對罷免葉元之、支持國民黨、或批評民進黨 → 判斷為 \"POSITIVE\"\n", "- 葉元之為國民黨籍，支持國民黨或其行動者視為 POSITIVE\n", "- 情緒（emotion）僅能為以下 8 種小寫英文之一：\n", "  - \"joy\"：高興、滿足\n", "  - \"trust\"：信任、安心\n", "  - \"anticipation\"：期待、希望\n", "  - \"sadness\"：悲傷、失望\n", "  - \"surprise\"：驚訝、意外\n", "  - \"disgust\"：厭惡、反感\n", "  - \"fear\"：恐懼、擔憂\n", "  - \"anger\"：憤怒、不滿\n", "\n", "請務必以以下 JSON Schema 格式回傳：\n", "\n", "Schema:\n", "{{\n", "  \"Context\": \"{context}\",\n", "  \"Reply\": \"{reply}\",\n", "  \"Label\": \"POSITIVE\" | \"NEGATIVE\",\n", "  \"Emotion\": \"joy\" | \"trust\" | \"anticipation\" | \"sadness\" | \"surprise\" | \"disgust\" | \"fear\" | \"anger\"\n", "}}\n", "\n", "JSON Response:\n", "\n", "需要進行判斷的留言：\n", "標題 (context)：{context}\n", "主要留言 (reply)：{reply}\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def build_prompt3(context, reply):\n", "    return f\"\"\"\n", "你是一位專業的語意理解與輿情分析員，任務是根據政治留言的語意、情緒與立場，進行情緒與立場判斷，並回傳格式正確的 JSON 結果。\n", "\n", "【分析任務】\n", "請根據下列 Context（新聞事件或討論標題）與 Reply（網友留言），判斷：\n", "1. **該留言的立場（Label）**：\n", "   - 是否 **支持罷免葉元之**（國民黨立委）？\n", "   - 或是 **反對罷免**（即支持葉元之或批評民進黨）？\n", "\n", "2. **該留言的情緒（Emotion）**：從以下八種中選擇最接近的情緒。\n", "\n", "【立場分類規則】\n", "- **NEGATIVE**：留言表達支持罷免葉元之，或批評國民黨、葉元之本人、藍營，或支持民進黨立場。\n", "- **POSITIVE**：留言反對罷免葉元之，或批評民進黨、支持國民黨、讚揚藍營或葉元之本人。\n", "- 若留言內容模糊或無法判斷，請以語氣傾向與上下文意圖推論最可能立場。\n", "\n", "【強化判讀重點】\n", "- 若出現 **嘲諷、反話、雙關語、鄉民語氣**（如：「真會演」、「很滑算」、「笑死」、「看還能囂張多久」等），需判斷是否為反語，並根據語境還原真實立場。\n", "- 留言若批評對象是「綠營」、「總統」、「民進黨」傾向 POSITIVE，批評「藍營」、「葉元之」、「國民黨」傾向 NEGATIVE。\n", "- 若內容太模糊（如純嘲諷、不明對象），可根據語氣合理推測立場，但不強行猜測。\n", "\n", "【情緒分類規則】\n", "請從以下八種英文小寫情緒中選擇最符合者：\n", "- \"joy\"：開心、得意\n", "- \"trust\"：信任、支持\n", "- \"anticipation\"：期待、希望\n", "- \"sadness\"：悲傷、失望\n", "- \"surprise\"：驚訝、意外\n", "- \"disgust\"：厭惡、反感\n", "- \"fear\"：擔憂、焦慮\n", "- \"anger\"：生氣、不滿\n", "\n", "請務必以以下 JSON Schema 格式回傳：\n", "\n", "Schema:\n", "{{\n", "  \"Context\": \"{context}\",\n", "  \"Reply\": \"{reply}\",\n", "  \"Label\": \"POSITIVE\" | \"NEGATIVE\",\n", "  \"Emotion\": \"joy\" | \"trust\" | \"anticipation\" | \"sadness\" | \"surprise\" | \"disgust\" | \"fear\" | \"anger\"\n", "}}\n", "\n", "JSON Response:\n", "需要進行判斷的留言：\n", "標題 (context)：{context}\n", "主要留言 (reply)：{reply}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def build_prompt4(context, reply): ##004\n", "    return f\"\"\"\n", "你是一位專業的語意理解與輿情分析員，任務是根據政治留言的語意、情緒與立場，判斷其對於「立法委員葉元之罷免案的情緒與立場判斷，並回傳格式正確的 JSON 結果。\n", "\n", "【分析任務】\n", "請根據下列 Context（新聞事件或討論標題）與 Reply（網友留言），判斷：\n", "1. 該留言的立場（Label）：\n", "   - 是否*支持罷免葉元之（國民黨立委）？\n", "   - 或是 反對罷免（即支持葉元之或批評民進黨）？\n", "\n", "2. **該留言的情緒（Emotion）**：從以下八種中選擇最接近的情緒。\n", "\n", "【立場分類規則】\n", "- **NEGATIVE**：留言表達支持罷免葉元之，或批評國民黨、葉元之本人、藍營，批評罷免團體/民進黨/綠營媒體或支持民進黨立場。\n", "- **POSITIVE**：留言反對罷免葉元之，或批評民進黨、支持國民黨、讚揚藍營或葉元之本人。\n", "- 若留言內容模糊或無法判斷，請以語氣傾向與上下文意圖推論最可能立場。\n", "\n", "【強化判讀重點】\n", "- 若出現嘲諷、反話、雙關語、鄉民語氣**（如：「真會演」、「很滑算」、「笑死」、「看還能囂張多久」等），需判斷是否為反語，並根據語境還原真實立場。\n", "- 留言若批評對象是「綠營」、「總統」、「民進黨」傾向 POSITIVE，批評「藍營」、「葉元之」、「國民黨」傾向 NEGATIVE。\n", "- 若內容太模糊（如純嘲諷、不明對象），可根據語氣合理推測立場，但不強行猜測。\n", "- 強調國民黨與共產黨合作的為負面\n", "- 如果有表達對政治人物的反感，則根據反感的陣營，判別是否支持罷免\n", "- 忽略如\"三立新聞網 SETN.com\"、\"xx新聞網\"等等，專於於判別其他文字內容\n", "\n", "【情緒分類規則】\n", "請從以下八種英文小寫情緒中選擇最符合者：\n", "- \"joy\"：開心、得意\n", "- \"trust\"：信任、支持\n", "- \"anticipation\"：期待、希望\n", "- \"sadness\"：悲傷、失望\n", "- \"surprise\"：驚訝、意外\n", "- \"disgust\"：厭惡、反感\n", "- \"fear\"：擔憂、焦慮\n", "- \"anger\"：生氣、不滿\n", "\n", "請務必以以下 JSON Schema 格式回傳：\n", "\n", "Schema:\n", "{{\n", "  \"Context\": \"{context}\",\n", "  \"Reply\": \"{reply}\",\n", "  \"Label\": \"POSITIVE\" | \"NEGATIVE\",\n", "  \"Emotion\": \"joy\" | \"trust\" | \"anticipation\" | \"sadness\" | \"surprise\" | \"disgust\" | \"fear\" | \"anger\"\n", "}}\n", "並在最後說明一下為何如此判斷\n", "JSON Response:\n", "需要進行判斷的留言：\n", "標題 (context)：{context}\n", "主要留言 (reply)：{reply}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def build_prompt5(context, reply): ##005\n", "    return f\"\"\"\n", "你是一位專業的語意理解與輿情分析員，任務是根據政治留言的語意、情緒與立場，判斷其對於「立法委員葉元之罷免案的情緒與立場判斷，並回傳格式正確的 JSON 結果。\n", "\n", "【分析任務】\n", "請根據下列 Context（新聞事件或討論標題）與 Reply（網友留言），判斷：\n", "1. 該留言的立場（Label）：\n", "   - 是否*支持罷免葉元之（國民黨立委）？\n", "   - 或是 反對罷免（即支持葉元之或批評民進黨）？\n", "\n", "2. **該留言的情緒（Emotion）**：從以下八種中選擇最接近的情緒。\n", "\n", "【立場分類規則】\n", "- **NEGATIVE**：留言表達支持罷免葉元之，或批評國民黨、葉元之本人、藍營，批評罷免團體/民進黨/綠營媒體或支持民進黨立場。\n", "- **POSITIVE**：留言反對罷免葉元之，或批評民進黨、支持國民黨、讚揚藍營或葉元之本人。\n", "- 若留言內容模糊或無法判斷，請以語氣傾向與上下文意圖推論最可能立場。\n", "\n", "【強化判讀重點】\n", "- 若出現嘲諷、反話、雙關語、鄉民語氣**（如：「真會演」、「很滑算」、「笑死」、「看還能囂張多久」等），需判斷是否為反語，並根據語境還原真實立場。\n", "- 留言若批評對象是「綠營」、「總統」、「民進黨」傾向 POSITIVE，批評「藍營」、「葉元之」、「國民黨」傾向 NEGATIVE。\n", "- 若內容太模糊（如純嘲諷、不明對象），可根據語氣合理推測立場，但不強行猜測。\n", "- 強調國民黨與共產黨合作的為負面\n", "- 如果有表達對政治人物的反感，則根據反感的陣營，判別是否支持罷免\n", "- 忽略如\"三立新聞網 SETN.com\"、\"xx新聞網\"等等，專於於判別其他文字內容\n", "\n", "【情緒分類規則】\n", "請從以下八種英文小寫情緒中選擇最符合者：\n", "• anger：氣、火大、無恥、去死…\n", "• disgust：噁、垃圾、爛、快吐…\n", "• fear：怕、恐、好可怕、擔憂…\n", "• surprise：竟然、傻眼、沒想到…\n", "• joy：太好了、爽翻、開心…\n", "• trust：支持、相信、挺你…\n", "• anticipation：期待、快點、拭目以待…\n", "• sadness：可憐、難過、失望…\n", "\n", "請務必以以下 JSON Schema 格式回傳：\n", "\n", "Schema:\n", "{{\n", "  \"Context\": \"{context}\",\n", "  \"Reply\": \"{reply}\",\n", "  \"Label\": \"POSITIVE\" | \"NEGATIVE\",\n", "  \"Emotion\": \"joy\" | \"trust\" | \"anticipation\" | \"sadness\" | \"surprise\" | \"disgust\" | \"fear\" | \"anger\"\n", "}}\n", "並在最後說明一下為何如此判斷\n", "JSON Response:\n", "需要進行判斷的留言：\n", "標題 (context)：{context}\n", "主要留言 (reply)：{reply}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2/1「立委就職滿一年」 罷免團體衝刺連署 TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\n", "```json\n", "{\n", "  \"Context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"Reply\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 該留言表達了對現任總統蔡英文的不滿，且有強烈的批評意味。2. 由於內容涉及批評蔡英文，且用語強硬，可推測該留言情緒為憤怒。3.  \"爛\" 和 \"去死\" 屬於憤怒的慣用語。\n", "raw ```json\n", "{\n", "  \"Context\": \"2/1「立委就職滿一年」 罷免團體衝刺連署\",\n", "  \"Reply\": \"TVBS新聞 ‪@TVBSNEWS01‬ 可以先罷免爛總桶嗎\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 該留言表達了對現任總統蔡英文的不滿，且有強烈的批評意味。2. 由於內容涉及批評蔡英文，且用語強硬，可推測該留言情緒為憤怒。3.  \"爛\" 和 \"去死\" 屬於憤怒的慣用語。\n", "{'標題': '2/1「立委就職滿一年」 罷免團體衝刺連署', '留言內容': 'TVBS新聞 \\u202a@TVBSNEWS01\\u202c 可以先罷免爛總桶嗎', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之 徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\n", "```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於內容批評國民黨立委葉元之，且以強烈的字詞表達不滿，因此判定為負面。由於出現憤怒的用語「去死」，故推斷情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於內容批評國民黨立委葉元之，且以強烈的字詞表達不滿，因此判定為負面。由於出現憤怒的用語「去死」，故推斷情緒為anger。\n", "{'標題': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之', '留言內容': '徐巧芯說你罷免ㄧ個我罷免你五個，大家來罷免徐巧芯，讓她再去罷免5個綠委，很滑算', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305 #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\n", "```json\n", "{\n", "  \"Context\": \"【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\",\n", "  \"Reply\": \"#許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評民進黨，且表達強烈不滿。2. \"氣得吐血\"屬於憤怒的慣用語。3. \"可憐啊\"帶有哀傷，但整體語氣偏憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\",\n", "  \"Reply\": \"#許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評民進黨，且表達強烈不滿。2. \"氣得吐血\"屬於憤怒的慣用語。3. \"可憐啊\"帶有哀傷，但整體語氣偏憤怒。\n", "{'標題': '【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305', '留言內容': '#許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 孫中山在地下，真的會氣得吐血，好好的三民主義 被 換成 共產主義。可憐啊，老孫。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署 #鏡新聞 中指可以比出來啊～\n", "```json\n", "{\n", "  \"Context\": \"徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\",\n", "  \"Reply\": \"#鏡新聞 中指可以比出來啊～\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 該留言批評徐巧芯，且以嘲諷的口吻表達不滿。2. \"中指可以比出來啊～\"帶有威脅意味，引發憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\",\n", "  \"Reply\": \"#鏡新聞 中指可以比出來啊～\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 該留言批評徐巧芯，且以嘲諷的口吻表達不滿。2. \"中指可以比出來啊～\"帶有威脅意味，引發憤怒。\n", "{'標題': '徐巧芯掃街拜年遇抗議！民眾怒撕春聯\\u3000衝罷免站連署', '留言內容': '#鏡新聞 中指可以比出來啊～', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】 三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\n", "```json\n", "{\n", "  \"Context\": \"罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\",\n", "  \"Reply\": \"三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評國民黨、質疑其正當性，且語氣強硬，因此判斷其立場為負面。由於內容涉及不道德行為，且表達強烈不滿，因此判斷情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\",\n", "  \"Reply\": \"三立新聞網 SETN.com 所以用抄寫名冊“合格\"的部份就可以?？但那些也是用抄的啊也是偽造文書啊~~\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評國民黨、質疑其正當性，且語氣強硬，因此判斷其立場為負面。由於內容涉及不道德行為，且表達強烈不滿，因此判斷情緒為憤怒。\n", "{'標題': '罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】', '留言內容': '三立新聞網 SETN.com 所以用抄寫名冊', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "自由爆新聞》糗！罷綠慘團滅､罷免藍\"全壘打\"！他不認中共威脅網傻眼轟！(柯文哲/川普) 國民黨吃台灣賣台灣。\n", "```json\n", "{\n", "  \"Context\": \"自由爆新聞》糗！罷綠慘團滅､罷免藍\\\"全壘打\\\"！他不認中共威脅網傻眼轟！(柯文哲/川普)\",\n", "  \"Reply\": \"國民黨吃台灣賣台灣。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達了強烈的批評，且批評對象是國民黨，因此判定為負面。由於內容充滿憤慨，故選擇\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"自由爆新聞》糗！罷綠慘團滅､罷免藍\\\"全壘打\\\"！他不認中共威脅網傻眼轟！(柯文哲/川普)\",\n", "  \"Reply\": \"國民黨吃台灣賣台灣。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達了強烈的批評，且批評對象是國民黨，因此判定為負面。由於內容充滿憤慨，故選擇\"anger\"。\n", "{'標題': '自由爆新聞》糗！罷綠慘團滅､罷免藍\\\\', '留言內容': '國民黨吃台灣賣台灣。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3 黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已\n", "```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"surprise\"\n", "}\n", "```\n", "\n", "由於留言內容提到「興奮一下」，且表達對「葉元之」的欣賞，因此判斷立場為正面，情緒為驚訝。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"surprise\"\n", "}\n", "```\n", "\n", "由於留言內容提到「興奮一下」，且表達對「葉元之」的欣賞，因此判斷立場為正面，情緒為驚訝。\n", "{'標題': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3', '留言內容': '黃揚明 葉元之 毛嘉慶 原來只是意向書而已 害我看到context興奮一下而已', '情感標籤': 'POSITIVE', '情緒': 'surprise'}\n", "被罷免並非不可能！傅崐萁與徐巧芯「仇恨值飆升」成綠營摧毀戰上「兩大指標性山頭」？【關鍵時刻】‪@ebcCTime‬ 請支持國共合作\n", "```json\n", "{\n", "  \"Context\": \"被罷免並非不可能！傅崐萁與徐巧芯「仇恨值飆升」成綠營摧毀戰上「兩大指標性山頭」？【關鍵時刻】‪@ebcCTime‬\",\n", "  \"Reply\": \"請支持國共合作\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"trust\"\n", "}\n", "```\n", "\n", "由於留言表達支持國共合作，且內容帶有強烈的政治立場，因此判斷為正面且信任感。\n", "raw ```json\n", "{\n", "  \"Context\": \"被罷免並非不可能！傅崐萁與徐巧芯「仇恨值飆升」成綠營摧毀戰上「兩大指標性山頭」？【關鍵時刻】‪@ebcCTime‬\",\n", "  \"Reply\": \"請支持國共合作\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"trust\"\n", "}\n", "```\n", "\n", "由於留言表達支持國共合作，且內容帶有強烈的政治立場，因此判斷為正面且信任感。\n", "{'標題': '被罷免並非不可能！傅崐萁與徐巧芯「仇恨值飆升」成綠營摧毀戰上「兩大指標性山頭」？【關鍵時刻】\\u202a@ebcCTime\\u202c', '留言內容': '請支持國共合作', '情感標籤': 'POSITIVE', '情緒': 'trust'}\n", "民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑 民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！\n", "```json\n", "{\n", "  \"Context\": \"民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑\",\n", "  \"Reply\": \"民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言多次出現「無恥」、「擺脫不了」、「拼個死活」等詞彙，可知其情緒為憤怒。另外，由於該留言批評民進黨並支持國民黨，因此判斷其立場為反對罷免葉元之。\n", "raw ```json\n", "{\n", "  \"Context\": \"民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑\",\n", "  \"Reply\": \"民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言多次出現「無恥」、「擺脫不了」、「拼個死活」等詞彙，可知其情緒為憤怒。另外，由於該留言批評民進黨並支持國民黨，因此判斷其立場為反對罷免葉元之。\n", "{'標題': '民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑', '留言內容': '民進黨太無恥！國民黨一定也要大量選民進黨軟的柿子罷免！一定要跟民進黨拼個死活！顯示國民黨真正的大戰力！加油！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署 #鏡新聞 醒醒\n", "```json\n", "{\n", "  \"Context\": \"徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\",\n", "  \"Reply\": \"#鏡新聞 醒醒\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達不滿徐巧芯，且用詞強硬，因此判斷立場為負面。由於內容帶有批評和憤怒，所以選定情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\",\n", "  \"Reply\": \"#鏡新聞 醒醒\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達不滿徐巧芯，且用詞強硬，因此判斷立場為負面。由於內容帶有批評和憤怒，所以選定情緒為anger。\n", "{'標題': '徐巧芯掃街拜年遇抗議！民眾怒撕春聯\\u3000衝罷免站連署', '留言內容': '#鏡新聞 醒醒', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」 三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會\n", "```json\n", "{\n", "  \"Context\": \"【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」\",\n", "  \"Reply\": \"三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評葉元之「死不認錯」，且以強烈的字詞「可惡」、「垃圾」表達不滿，因此判斷其立場為負面且情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」\",\n", "  \"Reply\": \"三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評葉元之「死不認錯」，且以強烈的字詞「可惡」、「垃圾」表達不滿，因此判斷其立場為負面且情緒為憤怒。\n", "{'標題': '【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」', '留言內容': '三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免案第一階段送件！ 二階遭曝連署時間短、難度高 霸到底救國家\n", "霸到底救台灣\n", "霸到底救民主\n", "霸到底救人民\n", "```json\n", "{\n", "  \"Context\": \"罷免案第一階段送件！ 二階遭曝連署時間短、難度高\",\n", "  \"Reply\": \"霸到底救國家\\n霸到底救台灣\\n霸到底救民主\\n霸到底救人民\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "由於內容是反對罷免，且表達了希望、救贖等正面涵義，因此判斷為正面且充滿希望。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免案第一階段送件！ 二階遭曝連署時間短、難度高\",\n", "  \"Reply\": \"霸到底救國家\\n霸到底救台灣\\n霸到底救民主\\n霸到底救人民\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "由於內容是反對罷免，且表達了希望、救贖等正面涵義，因此判斷為正面且充滿希望。\n", "{'標題': '罷免案第一階段送件！ 二階遭曝連署時間短、難度高', '留言內容': '霸到底救國家\\\\n霸到底救台灣\\\\n霸到底救民主\\\\n霸到底救人民', '情感標籤': 'POSITIVE', '情緒': 'joy'}\n", "罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞 國民黨在恐嚇選民，真的好可怕\n", "```json\n", "{\n", "  \"Context\": \"罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞\",\n", "  \"Reply\": \"國民黨在恐嚇選民，真的好可怕\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"fear\"\n", "}\n", "```\n", "\n", "由於留言提到「恐嚇」、「可怕」，且批評對象為「國民黨」，因此判定為負面。由於內容強調害怕，故選用\"fear\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞\",\n", "  \"Reply\": \"國民黨在恐嚇選民，真的好可怕\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"fear\"\n", "}\n", "```\n", "\n", "由於留言提到「恐嚇」、「可怕」，且批評對象為「國民黨」，因此判定為負面。由於內容強調害怕，故選用\"fear\"。\n", "{'標題': '罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞', '留言內容': '國民黨在恐嚇選民，真的好可怕', '情感標籤': 'NEGATIVE', '情緒': 'fear'}\n", "藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16 行政院可以不接受。让国民党倒阁，解散国会。\n", "国安局应调查傅崐萁去见中共王滬宁，以反国安法，叛国罪起诉他。\n", "```json\n", "{\n", "  \"Context\": \"藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16\",\n", "  \"Reply\": \"行政院可以不接受。让国民党倒阁，解散国会。\\n国安局应调查傅崐萁去見中共王滬宁，以反国安法，叛国罪起诉他。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言表達強烈的反感，且內容涉及批評國民黨、葉元之，故判定為負面。因頻繁使用「怒」、「氣」、「擺脫」、「鬥爭」等詞彙，故判定情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16\",\n", "  \"Reply\": \"行政院可以不接受。让国民党倒阁，解散国会。\\n国安局应调查傅崐萁去見中共王滬宁，以反国安法，叛国罪起诉他。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言表達強烈的反感，且內容涉及批評國民黨、葉元之，故判定為負面。因頻繁使用「怒」、「氣」、「擺脫」、「鬥爭」等詞彙，故判定情緒為anger。\n", "{'標題': '藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16', '留言內容': '行政院可以不接受。让国民党倒阁，解散国会。\\\\n国安局应调查傅崐萁去見中共王滬宁，以反国安法，叛国罪起诉他。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞 千萬別被他騙了!!!霸定了舉手部隊。\n", "```json\n", "{\n", "  \"Context\": \"談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞\",\n", "  \"Reply\": \"千萬別被他騙了!!!霸定了舉手部隊。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  \"千萬別被他騙了\"表達了不信任，2. \"霸定了\"帶有威脅意味，3.整體語氣憤怒。  \n", "raw ```json\n", "{\n", "  \"Context\": \"談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞\",\n", "  \"Reply\": \"千萬別被他騙了!!!霸定了舉手部隊。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  \"千萬別被他騙了\"表達了不信任，2. \"霸定了\"帶有威脅意味，3.整體語氣憤怒。\n", "{'標題': '談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞', '留言內容': '千萬別被他騙了!!!霸定了舉手部隊。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷韓像失戀的女人?葉元之.王定宇火線交鋒! 廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。\n", "```json\n", "{\n", "  \"Context\": \"罷韓像失戀的女人?葉元之.王定宇火線交鋒!\",\n", "  \"Reply\": \"廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評民進黨、葉元之，且帶有強烈批評用語，符合負面立場。2.  \"怒斥」、「可惡」、「下架」等詞彙，可推測出憤怒的情緒。3.  「備註」處已詳細說明判斷依據。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷韓像失戀的女人?葉元之.王定宇火線交鋒!\",\n", "  \"Reply\": \"廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評民進黨、葉元之，且帶有強烈批評用語，符合負面立場。2.  \"怒斥」、「可惡」、「下架」等詞彙，可推測出憤怒的情緒。3.  「備註」處已詳細說明判斷依據。\n", "{'標題': '罷韓像失戀的女人?葉元之.王定宇火線交鋒!', '留言內容': '廖筱君主持｜【新台灣加油精彩】20200504｜三立新聞台 民進黨，台獨黨的手法就像當年共產黨一樣，用大量的假消息，帶風向來抹黑對手。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "自由爆新聞》12藍委陷\"罷免危險區\"！花蓮想變！他揭\"1狀況\"傅崐萁會怕！(王毅/王大陸) 中共又再胡說八道，鬼扯，。\n", "```json\n", "{\n", "  \"Context\": \"自由爆新聞》12藍委陷\\\"罷免危險區\\\"！花蓮想變！他揭\\\"1狀況\\\"傅崐萁會怕！(王毅/大陸)\",\n", "  \"Reply\": \"中共又再胡說八道，鬼扯，。\" ,\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評「中共」，且以強烈的負面詞彙「胡說八道」、「鬼扯」表達不滿，因此判斷為負面立場。由於內容表達憤怒，故選用\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"自由爆新聞》12藍委陷\\\"罷免危險區\\\"！花蓮想變！他揭\\\"1狀況\\\"傅崐萁會怕！(王毅/大陸)\",\n", "  \"Reply\": \"中共又再胡說八道，鬼扯，。\" ,\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評「中共」，且以強烈的負面詞彙「胡說八道」、「鬼扯」表達不滿，因此判斷為負面立場。由於內容表達憤怒，故選用\"anger\"。\n", "{'標題': '自由爆新聞》12藍委陷\\\\', '留言內容': '中共又再胡說八道，鬼扯，。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "\"我們也是保台但年輕人不接受\"! 葉元之委屈解釋\"九二共識\"? \"他\"挨轟不切實際...│鄭弘儀 主持│【鄭知道了。先知道】20200114│三立iNEWS 原汁沒開玩笑 是在裝瘋賣傻\n", "人一藍 腦就殘\n", "```json\n", "{\n", "  \"Context\": \"\\\"我們也是保台但年輕人不接受\\\"! 葉元之委屈解釋\\\"九二共識\\\"? \\\"他\\\"挨轟不切實際...│鄭弘儀 主持│【鄭知道了。先知道】20200114│三立iNEWS\",\n", "  \"Reply\": \"原汁沒開玩笑 是在裝瘋賣傻\\n人一藍 腦就殘\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容批評葉元之與國民黨，且帶有強烈負面情緒，因此判定為NEGATIVE。由於出現「氣」、「怒」、「擺脫不了」、「慣老闆」等詞彙，可知情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"\\\"我們也是保台但年輕人不接受\\\"! 葉元之委屈解釋\\\"九二共識\\\"? \\\"他\\\"挨轟不切實際...│鄭弘儀 主持│【鄭知道了。先知道】20200114│三立iNEWS\",\n", "  \"Reply\": \"原汁沒開玩笑 是在裝瘋賣傻\\n人一藍 腦就殘\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容批評葉元之與國民黨，且帶有強烈負面情緒，因此判定為NEGATIVE。由於出現「氣」、「怒」、「擺脫不了」、「慣老闆」等詞彙，可知情緒為anger。\n", "{'標題': '\\\\', '留言內容': '原汁沒開玩笑 是在裝瘋賣傻\\\\n人一藍 腦就殘', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片\" 反制大罷免潮 朱立倫:啟動反廢死公投應戰│新聞一把抓20250308│三立新聞台 死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？\n", "```json\n", "{\n", "  \"Context\": \"藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\\\"罷免李彥秀紀錄片\\\" 反制大罷免潮 朱立倫:啟動反廢死公投應戰│新聞一把抓20250308│三立新聞台\",\n", "  \"Reply\": \"死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評「死人都能連署」、「造假」，且用詞強硬，故判斷為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\\\"罷免李彥秀紀錄片\\\" 反制大罷免潮 朱立倫:啟動反廢死公投應戰│新聞一把抓20250308│三立新聞台\",\n", "  \"Reply\": \"死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評「死人都能連署」、「造假」，且用詞強硬，故判斷為負面且憤怒。\n", "{'標題': '藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\\\\', '留言內容': '死人都能連署，這已明顯犯了偽造文書的罪，怎能讓造假的連署再補件呢？', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台 累就滾回家\n", "```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"累就滾回家\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「累就滾回家」是較為強硬且不耐煩的說法，且批評對象是葉元之，故判定為負面。由於內容帶有威脅性，且表達強烈不滿，故判定情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"累就滾回家\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「累就滾回家」是較為強硬且不耐煩的說法，且批評對象是葉元之，故判定為負面。由於內容帶有威脅性，且表達強烈不滿，故判定情緒為anger。\n", "{'標題': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒', '留言內容': '累就滾回家', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305 #許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\n", "但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？\n", "```json\n", "{\n", "  \"Context\": \"【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\",\n", "  \"Reply\": \"#許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\\n但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言多次批評國民黨，且以強烈的字詞表達不滿，因此判斷為負面。由於內容充滿憤慨，且出現「去死」、「可憐」、「難過」等詞彙，故選定情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305\",\n", "  \"Reply\": \"#許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\\n但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言多次批評國民黨，且以強烈的字詞表達不滿，因此判斷為負面。由於內容充滿憤慨，且出現「去死」、「可憐」、「難過」等詞彙，故選定情緒為anger。\n", "{'標題': '【完整版下集】曹興誠、史書華喊被醜化 罷免領銜人連環爆衝擊罷免能量有限？ #少康戰情室 20250305', '留言內容': '#許智傑 #陳智菡 #尹乃菁 #柯志恩 #林郁方 #王育敏 看看下面的這些評論，每次國民黨輸都會有一些人把罪責推給黨主席或者某些國民黨內部的團體。當然，國民黨高層確實有軟骨病，煞車皮病，自以為還是執政黨。\\\\n但最重要的是：有什麼樣的選民，才有什麼樣的政黨，才有什麼樣的政治。民進黨長遠的規劃，從上台就開始佈局教育系統洗腦年輕人，控制媒體、國民黨呢？退出學校，退出司法，退出媒體。再看看國民黨的選民，不愛發聲，不敢嗆聲，只會窩裡鬥，再看看中間選民，更是一聲不吭，悄無聲息。在綠營鋪天蓋地營銷抹黑下，很多中間選民慢慢的也被洗腦。人都是從眾的。不理性的選民造就了狂妄的政黨，懦弱的選民，造就了懦弱的政黨。台灣是台灣人的台灣，自己都不想爭取自己的利益，卻坐在家裡盼著你選的政黨去抗爭？', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "寶傑驚喊「徐巧芯沒別條路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑 遷户口準備中\n", "徐阿花你等著\n", "```json\n", "{\n", "  \"Context\": \"寶傑驚喊「徐巧芯沒別路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑\",\n", "  \"Reply\": \"遷户口準備中\\n徐阿花你等著\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評國民黨立委徐巧芯，且帶有威脅意味。2. \"遷戶口準備中\"隱含逃亡或逃避責任的負面意涵。3. \"徐阿花你等著\"是較為強硬且不耐煩的說法。\n", "raw ```json\n", "{\n", "  \"Context\": \"寶傑驚喊「徐巧芯沒別路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑\",\n", "  \"Reply\": \"遷户口準備中\\n徐阿花你等著\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評國民黨立委徐巧芯，且帶有威脅意味。2. \"遷戶口準備中\"隱含逃亡或逃避責任的負面意涵。3. \"徐阿花你等著\"是較為強硬且不耐煩的說法。\n", "{'標題': '寶傑驚喊「徐巧芯沒別路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑', '留言內容': '遷户口準備中\\\\n徐阿花你等著', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！ 罷免葉元之就是罷免陳玉珍\n", "```json\n", "{\n", "  \"Context\": \"街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！\",\n", "  \"Reply\": \"罷免葉元之就是罷免陳玉珍\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達對葉元之的反感，且用詞強硬，因此判斷其立場為負面。由於內容涉及批評特定政治人物，且用詞強硬，因此判斷情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！\",\n", "  \"Reply\": \"罷免葉元之就是罷免陳玉珍\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達對葉元之的反感，且用詞強硬，因此判斷其立場為負面。由於內容涉及批評特定政治人物，且用詞強硬，因此判斷情緒為anger。\n", "{'標題': '街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！', '留言內容': '罷免葉元之就是罷免陳玉珍', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台 那張衰臉真是原汁\n", "```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"那張衰臉真是原汁\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"disgust\"\n", "}\n", "```\n", "\n", "由於「那張衰臉真是原汁」表達厭惡和反感，且批評對象是葉元之，因此判定為負面。\n", "raw ```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"那張衰臉真是原汁\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"disgust\"\n", "}\n", "```\n", "\n", "由於「那張衰臉真是原汁」表達厭惡和反感，且批評對象是葉元之，因此判定為負面。\n", "{'標題': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒', '留言內容': '那張衰臉真是原汁', '情感標籤': 'NEGATIVE', '情緒': 'disgust'}\n", "一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台 無恥的人，還敢公然鬼扯，罷定了!\n", "```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"無恥的人，還敢公然鬼扯，罷定了!\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言出現「無恥」、「鬼扯」等詞彙，且內容批評葉元之，因此判定為負面立場。由於內容充滿憤慨，故選定情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"無恥的人，還敢公然鬼扯，罷定了!\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言出現「無恥」、「鬼扯」等詞彙，且內容批評葉元之，因此判定為負面立場。由於內容充滿憤慨，故選定情緒為anger。\n", "{'標題': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒', '留言內容': '無恥的人，還敢公然鬼扯，罷定了!', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免藍委遍地開花！葉元之.涂權吉.羅廷瑋恐成危險名單 賴中強揭罷免可能適用舊法 諷藍白弄巧成拙！民團拚2/3提案送出連署書 三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。\n", "```json\n", "{\n", "  \"Context\": \"罷免藍委遍地開花！葉元之.涂權吉.羅廷瑋恐成危險名單 賴中強揭罷免可能適用舊法 諷藍白弄巧成拙！民團拚2/3提案送出連署書\",\n", "  \"Reply\": \"三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  「國民黨站在人民對立面」、「罷免國民黨是順天民意」都屬於批評國民黨的言論。2.  「擺脫萬惡的舊勢力」、「慣老闆」、「可惡至極」等詞彙都帶有強烈的憤怒情緒。3.  由於內容批評國民黨，且帶有強烈的憤怒情緒，因此判斷為NEGATIVE。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免藍委遍地開花！葉元之.涂權吉.羅廷瑋恐成危險名單 賴中強揭罷免可能適用舊法 諷藍白弄巧成拙！民團拚2/3提案送出連署書\",\n", "  \"Reply\": \"三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  「國民黨站在人民對立面」、「罷免國民黨是順天民意」都屬於批評國民黨的言論。2.  「擺脫萬惡的舊勢力」、「慣老闆」、「可惡至極」等詞彙都帶有強烈的憤怒情緒。3.  由於內容批評國民黨，且帶有強烈的憤怒情緒，因此判斷為NEGATIVE。\n", "{'標題': '罷免藍委遍地開花！葉元之.涂權吉.羅廷瑋恐成危險名單 賴中強揭罷免可能適用舊法 諷藍白弄巧成拙！民團拚2/3提案送出連署書', '留言內容': '三立新聞網 SETN.com 國民黨站在人民對立面，罷免國民黨是順天民意。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台 領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯\n", "```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  「擺爛」和「都是別人的錯」都帶有逃避責任的意味。2.  強調理\n", "raw ```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  「擺爛」和「都是別人的錯」都帶有逃避責任的意味。2.  強調理\n", "{'標題': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒', '留言內容': '領十幾萬的錢，然後擺爛，然後很多人不去看，都是別人的錯', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\"盯梢\" 淡水分局:依法行政.保持中立 三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1\n", "```json\n", "{\n", "  \"Context\": \"大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\\\"盯梢\\\" 淡水分局:依法行政.保持中立\",\n", "  \"Reply\": \"三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評警察「盯梢」，且用詞強硬，可判斷其立場為負面。由於內容涉及不當干預選舉、侵犯人權，可推測情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\\\"盯梢\\\" 淡水分局:依法行政.保持中立\",\n", "  \"Reply\": \"三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評警察「盯梢」，且用詞強硬，可判斷其立場為負面。由於內容涉及不當干預選舉、侵犯人權，可推測情緒為憤怒。\n", "{'標題': '大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\\\\', '留言內容': '三立新聞網 SETN.com 換你們可以把它拍照，對照就知道哪個派出所的，這可以提告他，1', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣 葉猿之可惡至極，非罷不可 。\n", "```json\n", "{\n", "  \"Context\": \"發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣\",\n", "  \"Reply\": \"葉猿之可惡至極，非罷不可 。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達對葉元之強烈的厭惡，且用詞激烈，因此判斷其情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣\",\n", "  \"Reply\": \"葉猿之可惡至極，非罷不可 。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達對葉元之強烈的厭惡，且用詞激烈，因此判斷其情緒為anger。\n", "{'標題': '發燒新聞／葉元之被問罷免急閃躲\\u3000雙手合十求饒：大年初一別這樣', '留言內容': '葉猿之可惡至極，非罷不可 。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "自由爆新聞》糗！罷綠慘團滅､罷免藍\"全壘打\"！他不認中共威脅網傻眼轟！(柯文哲/川普) 除惡務盡，一戰改變台灣政治生態，台派加油！\n", "```json\n", "{\n", "  \"Context\": \"自由爆新聞》糗！罷綠慘團滅､罷免藍\\\"全壘打\\\"！他不認中共威脅網傻眼轟！(柯文哲/川普)\",\n", "  \"Reply\": \"除惡務盡，一戰改變台灣政治生態，台派加油！\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "由於留言表達對罷免葉元之案的支持，且內容帶有強烈的政治立場，因此判斷為正面。由於內容充滿希望與期待，故選定\"joy\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"自由爆新聞》糗！罷綠慘團滅､罷免藍\\\"全壘打\\\"！他不認中共威脅網傻眼轟！(柯文哲/川普)\",\n", "  \"Reply\": \"除惡務盡，一戰改變台灣政治生態，台派加油！\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "由於留言表達對罷免葉元之案的支持，且內容帶有強烈的政治立場，因此判斷為正面。由於內容充滿希望與期待，故選定\"joy\"。\n", "{'標題': '自由爆新聞》糗！罷綠慘團滅､罷免藍\\\\', '留言內容': '除惡務盡，一戰改變台灣政治生態，台派加油！', '情感標籤': 'POSITIVE', '情緒': 'joy'}\n", "談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞 這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉\n", "```json\n", "{\n", "  \"Context\": \"談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞\",\n", "  \"Reply\": \"這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言有批評葉元之「雙面人」且附上負面形容詞，可知其立場是反對葉元之。由於內容帶有強烈的批評和憤怒，因此選定情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞\",\n", "  \"Reply\": \"這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言有批評葉元之「雙面人」且附上負面形容詞，可知其立場是反對葉元之。由於內容帶有強烈的批評和憤怒，因此選定情緒為anger。\n", "{'標題': '談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞', '留言內容': '這家伙最會做雙面人，表面工夫在媒體面前一個樣在立法院深藍團體，又是另外一個嘴臉', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【3Q來客訴】台美建交就像伴侶結婚?!葉元之大戰3Q：「台美需要實質關係」 以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\n", "馬來西亞華人都是穴今\n", "```json\n", "{\n", "  \"Context\": \"【3Q來客訴】台美建交就像伴侶結婚?!葉元之大戰3Q：「台美需要實質關係」\",\n", "  \"Reply\": \"以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\\n馬來西亞華人都是穴今\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評葉元之的美國行沒有給台灣人民帶來實際好處，且出現較強烈的負面詞彙，判定為負面立場。2. 由於內容表達不滿和氣憤，故選定\"anger\"。3. 忽略\"三立新聞網 SETN.com\"、\"xx新聞網\"等與內容無關的詞彙。\n", "raw ```json\n", "{\n", "  \"Context\": \"【3Q來客訴】台美建交就像伴侶結婚?!葉元之大戰3Q：「台美需要實質關係」\",\n", "  \"Reply\": \"以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\\n馬來西亞華人都是穴今\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評葉元之的美國行沒有給台灣人民帶來實際好處，且出現較強烈的負面詞彙，判定為負面立場。2. 由於內容表達不滿和氣憤，故選定\"anger\"。3. 忽略\"三立新聞網 SETN.com\"、\"xx新聞網\"等與內容無關的詞彙。\n", "{'標題': '【3Q來客訴】台美建交就像伴侶結婚?!葉元之大戰3Q：「台美需要實質關係」', '留言內容': '以一個外人來看這次的美國訪問真的有什麼實際好處給台灣人民嗎？\\\\n馬來西亞華人都是穴今', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "自由爆新聞》\"罷免謝國樑\"進度曝光！藍立院\"奪權之戰\"？分析示警！(黃仁勳/基隆山崩) 國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。\n", "```json\n", "{\n", "  \"Context\": \"自由爆新聞》\\\"罷免謝國樑\\\"進度曝光！藍立院\\\"奪權之戰\\\"？分析示警！(黃仁勳/基隆山崩)\",\n", "  \"Reply\": \"國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言多次批評國民黨，且以強烈的負面詞彙表達不滿，因此判斷為負面立場。由於內容充滿憤怒，故選定\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"自由爆新聞》\\\"罷免謝國樑\\\"進度曝光！藍立院\\\"奪權之戰\\\"？分析示警！(黃仁勳/基隆山崩)\",\n", "  \"Reply\": \"國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言多次批評國民黨，且以強烈的負面詞彙表達不滿，因此判斷為負面立場。由於內容充滿憤怒，故選定\"anger\"。\n", "{'標題': '自由爆新聞》\\\\', '留言內容': '國民黨已經把自己搞成是亂黨，已失去正當性的正黨，人民已唾棄這種爛黨，不知悔改自新，不會反省，只是想奪權，連自己也受到影響在台灣的正當性，就永遠做在野黨最適合。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "寶傑驚喊「徐巧芯沒別條路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑 人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。\n", "```json\n", "{\n", "  \"Context\": \"寶傑驚喊「徐巧芯沒別路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑\",\n", "  \"Reply\": \"人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言內容批評徐巧芯並暗指費宏泰會暗爽，且帶有憤怒的口氣，因此判斷為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"寶傑驚喊「徐巧芯沒別路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑\",\n", "  \"Reply\": \"人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言內容批評徐巧芯並暗指費宏泰會暗爽，且帶有憤怒的口氣，因此判斷為負面且憤怒。\n", "{'標題': '寶傑驚喊「徐巧芯沒別路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！？【關鍵時刻】-劉寶傑', '留言內容': '人家說捧打出頭鳥，愛出風頭費佬等着你被罷掉，費宏泰看到会暗爽吧。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【一刀未剪】來~元之讓你說！ 葉元之稱罷免是民進黨發起 「先射箭再畫靶」成為鎖定目標連開玩笑都不行 還被青鳥修理？ 簡舒培揪過去「這點」打臉 雙方吵翻 【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久\n", "```json\n", "{\n", "  \"Context\": \"【一刀未剪】來~元之讓你說！ 葉元之稱罷免是民進黨發起 「先射箭再畫靶」成為鎖定目標連開玩笑都不行 還被青鳥修理？ 簡舒培揪過去「這點」打臉 雙方吵翻\",\n", "  \"Reply\": \"【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於內容批評葉元之「大頭症」、「擺譜」、「講話可疑」，且帶有強烈反感，故判斷為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【一刀未剪】來~元之讓你說！ 葉元之稱罷免是民進黨發起 「先射箭再畫靶」成為鎖定目標連開玩笑都不行 還被青鳥修理？ 簡舒培揪過去「這點」打臉 雙方吵翻\",\n", "  \"Reply\": \"【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於內容批評葉元之「大頭症」、「擺譜」、「講話可疑」，且帶有強烈反感，故判斷為負面且憤怒。\n", "{'標題': '【一刀未剪】來~元之讓你說！ 葉元之稱罷免是民進黨發起 「先射箭再畫靶」成為鎖定目標連開玩笑都不行 還被青鳥修理？ 簡舒培揪過去「這點」打臉 雙方吵翻', '留言內容': '【焦點人物大現場】20240624｜三立新聞台 就覺得從他上任後就大頭症我看他也不懂什麼法就是會拗講話口氣都不一樣了罷了他們讓他們什麼都不是看還能囂張多久', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\"盯梢\" 淡水分局:依法行政.保持中立 三立新聞網 SETN.com 还有民众党\n", "```json\n", "{\n", "  \"Context\": \"大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\\\"盯梢\\\" 淡水分局:依法行政.保持中立\",\n", "  \"Reply\": \"三立新聞網 SETN.com 还有民众党\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言提到「三立新聞網」並批評「擺脫不了慣老闆」，可推測其立場是反對國民黨。此外，「擺脫不了慣老闆」帶有強烈不滿和憤怒的情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\\\"盯梢\\\" 淡水分局:依法行政.保持中立\",\n", "  \"Reply\": \"三立新聞網 SETN.com 还有民众党\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言提到「三立新聞網」並批評「擺脫不了慣老闆」，可推測其立場是反對國民黨。此外，「擺脫不了慣老闆」帶有強烈不滿和憤怒的情緒。\n", "{'標題': '大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\\\\', '留言內容': '三立新聞網 SETN.com 还有民众党', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免陳柏惟宣傳片(唐鳳舞版本) 元之你是在做復健嗎?\n", "```json\n", "{\n", "  \"Context\": \"罷免陳柏惟宣傳片(唐鳳舞版本)\",\n", "  \"Reply\": \"元之你是在做復健嗎?\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"surprise\"\n", "}\n", "```\n", "\n", "理由：此留言帶有嘲諷意味，但基於反諷對象是民進黨籍的陳柏惟，且內容是在討論罷免案，因此判定為支持國民黨、反對罷免陳柏惟。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免陳柏惟宣傳片(唐鳳舞版本)\",\n", "  \"Reply\": \"元之你是在做復健嗎?\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"surprise\"\n", "}\n", "```\n", "\n", "理由：此留言帶有嘲諷意味，但基於反諷對象是民進黨籍的陳柏惟，且內容是在討論罷免案，因此判定為支持國民黨、反對罷免陳柏惟。\n", "{'標題': '罷免陳柏惟宣傳片(唐鳳舞版本)', '留言內容': '元之你是在做復健嗎?', '情感標籤': 'POSITIVE', '情緒': 'surprise'}\n", "罷免陳柏惟宣傳片(唐鳳舞版本) 朝聖! 柿子要挑軟的吃\n", "```json\n", "{\n", "  \"Context\": \"罷免陳柏惟宣傳片(唐鳳舞版本)\",\n", "  \"Reply\": \"朝聖! 柿子要挑軟的吃\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "由於留言表達反對罷免陳柏惟，且內容輕鬆愉悅，因此判斷立場為POSITIVE，情緒為joy。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免陳柏惟宣傳片(唐鳳舞版本)\",\n", "  \"Reply\": \"朝聖! 柿子要挑軟的吃\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "由於留言表達反對罷免陳柏惟，且內容輕鬆愉悅，因此判斷立場為POSITIVE，情緒為joy。\n", "{'標題': '罷免陳柏惟宣傳片(唐鳳舞版本)', '留言內容': '朝聖! 柿子要挑軟的吃', '情感標籤': 'POSITIVE', '情緒': 'joy'}\n", "罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3 黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!\n", "```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "由於留言表達對罷免葉元之的支持，且用詞積極正面，因此判斷為正面立場。由於內容充滿讚賞與期待，因此推斷情緒為高興。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "由於留言表達對罷免葉元之的支持，且用詞積極正面，因此判斷為正面立場。由於內容充滿讚賞與期待，因此推斷情緒為高興。\n", "{'標題': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3', '留言內容': '黃揚明 葉元之 毛嘉慶 罷免吳思瑤，讚啦!', '情感標籤': 'POSITIVE', '情緒': 'joy'}\n", "青鳥發威藍白膽寒？元之罷總召！傅修選罷法被撤案！黨團爆內鬨？【全國第一勇】2024.05.31 建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費\n", "```json\n", "{\n", "  \"Context\": \"青鳥發威藍白膽寒？元之罷召！傅修選罷法被撤案！黨團爆內鬨？【全國第一勇】2024.05.31\",\n", "  \"Reply\": \"建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於內容批評葉元之並帶有強烈不滿，且出現憤怒用語「去死」，因此判斷立場為負面，情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"青鳥發威藍白膽寒？元之罷召！傅修選罷法被撤案！黨團爆內鬨？【全國第一勇】2024.05.31\",\n", "  \"Reply\": \"建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於內容批評葉元之並帶有強烈不滿，且出現憤怒用語「去死」，因此判斷立場為負面，情緒為憤怒。\n", "{'標題': '青鳥發威藍白膽寒？元之罷召！傅修選罷法被撤案！黨團爆內鬨？【全國第一勇】2024.05.31', '留言內容': '建議，先要求花蓮縣縣民罷免 崑崙山上一隻草 再去哪裡消費', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "《鄉民大學問EP.80》字幕版 #王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億\n", "```json\n", "{\n", "  \"Context\": \"《鄉民大學問EP.80》字幕版\",\n", "  \"Reply\": \"#王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於內容提到「反罷免」、「藍白合」，且以引述「#葉元之：很多人等我位子在流口水！」，可知是反對罷免葉元之，且帶有嘲諷意味，因此判斷為「POSITIVE」，情緒為「anger」。\n", "raw ```json\n", "{\n", "  \"Context\": \"《鄉民大學問EP.80》字幕版\",\n", "  \"Reply\": \"#王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於內容提到「反罷免」、「藍白合」，且以引述「#葉元之：很多人等我位子在流口水！」，可知是反對罷免葉元之，且帶有嘲諷意味，因此判斷為「POSITIVE」，情緒為「anger」。\n", "{'標題': '《鄉民大學問EP.80》字幕版', '留言內容': '#王世堅 #柯建銘 黨內鬩牆！#黃暐瀚 揭#賴清德 出手時機！#張啓楷 曝反罷免藍白再合作？#台大社科院#學生會長 遭罷免 獨家現身！#葉元之：很多人等我位子在流口水！ 吳沛億', '情感標籤': 'POSITIVE', '情緒': 'anger'}\n", "一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台 葉元之從頭到尾鬼扯\n", "```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"葉元之從頭到尾鬼扯\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言直指葉元之「鬼扯」，且帶有濃厚的批評意味，因此判定為負面。由於內容涉及批評與指控，且帶有強烈情緒，故選定\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"葉元之從頭到尾鬼扯\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言直指葉元之「鬼扯」，且帶有濃厚的批評意味，因此判定為負面。由於內容涉及批評與指控，且帶有強烈情緒，故選定\"anger\"。\n", "{'標題': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒', '留言內容': '葉元之從頭到尾鬼扯', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞 傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？\n", "```json\n", "{\n", "  \"Context\": \"談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞\",\n", "  \"Reply\": \"傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評葉元之、且用了較為強硬的詞彙，因此判斷為負面。由於內容帶有憤慨、斥責，故選定\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞\",\n", "  \"Reply\": \"傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評葉元之、且用了較為強硬的詞彙，因此判斷為負面。由於內容帶有憤慨、斥責，故選定\"anger\"。\n", "{'標題': '談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞', '留言內容': '傅崐萁的小弟助紂為虐國會殿堂是讓你這樣玩的啊！？', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免潮燒新北！葉元之也遭殃　葉：搞鬥爭秀下限 #鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。\n", "```json\n", "{\n", "  \"Context\": \"罷免潮燒新北！葉元之也遭殃　葉：搞鬥爭秀下限\",\n", "  \"Reply\": \"#鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言表達對葉元之的反感，且內容涉及批評政府、呼籲罷免，因此判定為負面情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免潮燒新北！葉元之也遭殃　葉：搞鬥爭秀下限\",\n", "  \"Reply\": \"#鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言表達對葉元之的反感，且內容涉及批評政府、呼籲罷免，因此判定為負面情緒。\n", "{'標題': '罷免潮燒新北！葉元之也遭殃\\u3000葉：搞鬥爭秀下限', '留言內容': '#鏡新聞 我住板橋，支持罷免毀憲亂政的葉元之。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【#週末大爆卦 上】 還是完蛋了!罷免案分AB案送件!吳沛憶樂極生悲?他爆\"這南部綠委\"罷免案恐是自導自演?20250209 ‪@大新聞大爆卦HotNewsTalk‬ 真不相信李蟾蜍會是一個中立的X X\n", "```json\n", "{\n", "  \"Context\": \"【#週末大爆卦 上】 還是完蛋了!罷免案分AB案送件!吳沛憶樂極生悲?他爆\\\"這南部綠委\\\"罷免案恐是自導自演?20250209 ‪@大新聞大爆卦HotNewsTalk‬\",\n", "  \"Reply\": \"真不相信李蟾蜍會是一個中立的X X\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「真不相信李蟾蜍會是一個中立的X X」表達不滿和氣憤，且批評對象是藍營的李正皓（外號李蟾蜍），因此判斷為負面情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【#週末大爆卦 上】 還是完蛋了!罷免案分AB案送件!吳沛憶樂極生悲?他爆\\\"這南部綠委\\\"罷免案恐是自導自演?20250209 ‪@大新聞大爆卦HotNewsTalk‬\",\n", "  \"Reply\": \"真不相信李蟾蜍會是一個中立的X X\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「真不相信李蟾蜍會是一個中立的X X」表達不滿和氣憤，且批評對象是藍營的李正皓（外號李蟾蜍），因此判斷為負面情緒。\n", "{'標題': '【#週末大爆卦 上】 還是完蛋了!罷免案分AB案送件!吳沛憶樂極生悲?他爆\\\\', '留言內容': '真不相信李蟾蜍會是一個中立的X X', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3 黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！\n", "```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達對葉元之和國民黨的批評，且用詞強硬，因此判定為負面。由於內容充滿憤慨，故選用\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達對葉元之和國民黨的批評，且用詞強硬，因此判定為負面。由於內容充滿憤慨，故選用\"anger\"。\n", "{'標題': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3', '留言內容': '黃揚明 葉元之 毛嘉慶 54搖在立法院就是倒帶的功能而已，早該罷免了！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣 葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。\n", "```json\n", "{\n", "  \"Context\": \"發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣\",\n", "  \"Reply\": \"葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言表達了強烈的憤怒，且內容批評葉元之，故判定為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣\",\n", "  \"Reply\": \"葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言表達了強烈的憤怒，且內容批評葉元之，故判定為負面且憤怒。\n", "{'標題': '發燒新聞／葉元之被問罷免急閃躲\\u3000雙手合十求饒：大年初一別這樣', '留言內容': '葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬ 葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!\n", "```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評葉元之「硬拗」，且表達反感。2. 由於批評對象是「葉元之」，且內容帶有威脅性，可推測立場是反對葉元之。3.  \"葉凹之你硬拗\"和\"連你的立法也別想了\"都帶有氣憤的情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評葉元之「硬拗」，且表達反感。2. 由於批評對象是「葉元之」，且內容帶有威脅性，可推測立場是反對葉元之。3.  \"葉凹之你硬拗\"和\"連你的立法也別想了\"都帶有氣憤的情緒。\n", "{'標題': '【一刀未剪】葉元之講\\\\', '留言內容': '葉凹之你硬拗肯定會讓候友宜在新北市票輸更多了!連你的立法也別想了!OK!', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬ 國民黨出葉元之這種貨色\n", "不倒才怪\n", "```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"國民黨出葉元之這種貨色\\n不倒才怪\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於留言中出現「這種貨色」、「不倒才怪」，可判斷為反語，實際上是批評國民黨。又因有「氣憤」、「批評」等詞彙，故選「anger」。\n", "raw ```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"國民黨出葉元之這種貨色\\n不倒才怪\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於留言中出現「這種貨色」、「不倒才怪」，可判斷為反語，實際上是批評國民黨。又因有「氣憤」、「批評」等詞彙，故選「anger」。\n", "{'標題': '【一刀未剪】葉元之講\\\\', '留言內容': '國民黨出葉元之這種貨色\\\\n不倒才怪', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！ 笑噴～真的要出來把沒用的破嘴51席全罷掉\n", "```json\n", "{\n", "  \"Context\": \"街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！\",\n", "  \"Reply\": \"笑噴～真的要出來把沒用的破嘴51席全罷掉\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容批評國民黨立委，且帶有強烈批評和厭惡感，因此判斷為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！\",\n", "  \"Reply\": \"笑噴～真的要出來把沒用的破嘴51席全罷掉\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容批評國民黨立委，且帶有強烈批評和厭惡感，因此判斷為負面且憤怒。\n", "{'標題': '街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！', '留言內容': '笑噴～真的要出來把沒用的破嘴51席全罷掉', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "自由爆新聞》12藍委陷\"罷免危險區\"！花蓮想變！他揭\"1狀況\"傅崐萁會怕！(王毅/王大陸) 國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； \n", "1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。\n", "```json\n", "{\n", "  \"Context\": \"自由爆新聞》12藍委陷\\\"罷免危險區\\\"！花蓮想變！他揭\\\"1狀況\\\"傅崐萁會怕！(王毅/王大陸)\",\n", "  \"Reply\": \"國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； 1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言批評國民黨，且內容帶有強烈的批評和憤怒，因此判斷為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"自由爆新聞》12藍委陷\\\"罷免危險區\\\"！花蓮想變！他揭\\\"1狀況\\\"傅崐萁會怕！(王毅/王大陸)\",\n", "  \"Reply\": \"國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； 1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言批評國民黨，且內容帶有強烈的批評和憤怒，因此判斷為負面且憤怒。\n", "{'標題': '自由爆新聞》12藍委陷\\\\', '留言內容': '國民黨素有弄虛作假，撒謊欺騙，串改歷史的基因； 1977年11月19日，由於國民黨在桃園縣長選舉投票過程中作票，引起中壢市民憤怒，群眾包圍桃園縣警察局中壢分局、搗毀並放火燒毀警察局、警方發射催淚瓦斯以及開槍打死青年的事件。 中壢事件被認為是臺灣民眾第一次自發性地上街頭抗議選舉舞弊，開啟爾後「街頭運動」之序幕。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16 中共在逼了他們為何要這麼急\n", "```json\n", "{\n", "  \"Context\": \"藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16\",\n", "  \"Reply\": \"中共在逼了他們為何要這麼急\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容批評「藍營」、「葉元之」，且表達不滿和氣憤，故判定為負面。因提到「擺脫不了共產黨」，可推測是批評國民黨親中。\n", "raw ```json\n", "{\n", "  \"Context\": \"藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16\",\n", "  \"Reply\": \"中共在逼了他們為何要這麼急\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容批評「藍營」、「葉元之」，且表達不滿和氣憤，故判定為負面。因提到「擺脫不了共產黨」，可推測是批評國民黨親中。\n", "{'標題': '藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16', '留言內容': '中共在逼了他們為何要這麼急', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之 看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！\n", "```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言批評國民黨立委，且內容帶有強烈批評和憤怒的情緒，因此判定為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言批評國民黨立委，且內容帶有強烈批評和憤怒的情緒，因此判定為負面且憤怒。\n", "{'標題': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之', '留言內容': '看看國民黨立委自從上任至今都做了些什麼事，罷免剛剛好，節目無需帶風向，「立委胡作非為，罷免就是人民的責任」！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】 三立新聞網 SETN.com 青年軍 : \"什麼~要坐牢 ?!  黨主席, 救救我 ! \"\n", "朱立倫 : \"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\"\n", "青年軍 : \"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \"\n", "朱立倫 : \"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \"\n", "```json\n", "{\n", "  \"Context\": \"罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\",\n", "  \"Reply\": \"三立新聞網 SETN.com 青年軍 : \\\"什麼~要坐牢 ?!  黨主席, 救救我 ! \\\" \\n朱立倫 : \\\"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\\\"\\n青年軍 : \\\"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \\\"\\n朱立倫 : \\\"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \\\" \",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"fear\"\n", "}\n", "```\n", "\n", "由於內容批評國民黨與其政治人物，且表達害怕與不滿，因此判定為負面。由於內容提到坐牢，且有害怕的對話，因此判定為fear。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】\",\n", "  \"Reply\": \"三立新聞網 SETN.com 青年軍 : \\\"什麼~要坐牢 ?!  黨主席, 救救我 ! \\\" \\n朱立倫 : \\\"干我什麼事, 我叫你抄, 可沒叫你去抄死人的...\\\"\\n青年軍 : \\\"丫我們都是菜逼巴, 哪裏知道 哪些黨員死了沒 ?! \\\"\\n朱立倫 : \\\"也不要緊, 一罪一罰, 大不了關個幾十年, 等出來後, 你們都會成為黨國大老, 加油 ~ \\\" \",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"fear\"\n", "}\n", "```\n", "\n", "由於內容批評國民黨與其政治人物，且表達害怕與不滿，因此判定為負面。由於內容提到坐牢，且有害怕的對話，因此判定為fear。\n", "{'標題': '罷免死亡連署多！ 王義川揭國民黨的策略就是要補考 拆穿「900芯量產計畫」：每個藍營罷免團體都很會自我介紹【前進新台灣】', '留言內容': '三立新聞網 SETN.com 青年軍 : \\\\', '情感標籤': 'NEGATIVE', '情緒': 'fear'}\n", "街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！ 葉原之是立委? 我以為他是政論節目通告咖\n", "```json\n", "{\n", "  \"Context\": \"街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！\",\n", "  \"Reply\": \"葉原之是立委? 我以為他是政論節目通告咖\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達不滿葉元之擔任立委，且用詞帶有怒氣，因此判斷立場為負面，情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！\",\n", "  \"Reply\": \"葉原之是立委? 我以為他是政論節目通告咖\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達不滿葉元之擔任立委，且用詞帶有怒氣，因此判斷立場為負面，情緒為憤怒。\n", "{'標題': '街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！', '留言內容': '葉原之是立委? 我以為他是政論節目通告咖', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署 #鏡新聞 支持罷免徐三八！\n", "```json\n", "{\n", "  \"Context\": \"徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\",\n", "  \"Reply\": \"#鏡新聞 支持罷免徐三八！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達支持罷免葉元之，且以強烈的負面詞彙「三八」稱呼葉元之，可判斷其立場為負面且情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\",\n", "  \"Reply\": \"#鏡新聞 支持罷免徐三八！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達支持罷免葉元之，且以強烈的負面詞彙「三八」稱呼葉元之，可判斷其立場為負面且情緒為憤怒。\n", "{'標題': '徐巧芯掃街拜年遇抗議！民眾怒撕春聯\\u3000衝罷免站連署', '留言內容': '#鏡新聞 支持罷免徐三八！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」 三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？\n", "```json\n", "{\n", "  \"Context\": \"【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」\",\n", "  \"Reply\": \"三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言多次出現批評葉元之、國民黨，且以強烈的負面情緒表達不滿，因此判斷為負面立場。此外，由於內容涉及批評政府、要求下台，可推測情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」\",\n", "  \"Reply\": \"三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言多次出現批評葉元之、國民黨，且以強烈的負面情緒表達不滿，因此判斷為負面立場。此外，由於內容涉及批評政府、要求下台，可推測情緒為憤怒。\n", "{'標題': '【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都刪光光、國民黨完蛋了、你身在台灣欸」葉元之耐心解釋「那都亂講的啦！」', '留言內容': '三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之 為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！\n", "```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評國民黨，且以強烈的用語「鬥臭」、「拉下」，可判斷其立場為負面。由於出現「氣憤」、「不滿」等詞彙，可判斷其情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評國民黨，且以強烈的用語「鬥臭」、「拉下」，可判斷其立場為負面。由於出現「氣憤」、「不滿」等詞彙，可判斷其情緒為憤怒。\n", "{'標題': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之', '留言內容': '為台灣安全，白營立法無法罷免，能不罷免藍營立委嗎？其實，白營也是失算的，下次還有票嗎？一步錯，步步錯！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【一刀未剪】三種罷免一次滿足?! \"早餐火腿蛋吐司 吃完罷免葉元之\"歌曲一出眾人笑翻 溫朗東AI創作罷樑曲獲好評 喊話徐巧芯:不要急! 你的歌一定最精彩│【焦點人物大現場】20240617│三立新聞台 自己的國家自己救 !\n", "```json\n", "{\n", "  \"Context\": \"【一刀未剪】三種罷免一次滿足?! \\\"早餐火腿蛋吐司 吃完罷免葉元之\\\"歌曲一出眾人笑翻 溫朗東AI創作罷樑曲獲好評 喊話徐巧芯:不要急! 你的歌一定最精彩│【焦點人物大現場】20240617│三立新聞台\",\n", "  \"Reply\": \"自己的國家自己救 !\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容提到「趙少康嗆聲民進黨」、「批評民進黨執政」，且語氣強硬，因此判斷為反對罷免葉元之，且情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【一刀未剪】三種罷免一次滿足?! \\\"早餐火腿蛋吐司 吃完罷免葉元之\\\"歌曲一出眾人笑翻 溫朗東AI創作罷樑曲獲好評 喊話徐巧芯:不要急! 你的歌一定最精彩│【焦點人物大現場】20240617│三立新聞台\",\n", "  \"Reply\": \"自己的國家自己救 !\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容提到「趙少康嗆聲民進黨」、「批評民進黨執政」，且語氣強硬，因此判斷為反對罷免葉元之，且情緒為憤怒。\n", "{'標題': '【一刀未剪】三種罷免一次滿足?! \\\\', '留言內容': '自己的國家自己救 !', '情感標籤': 'POSITIVE', '情緒': 'anger'}\n", "罷免潮燒新北！葉元之也遭殃　葉：搞鬥爭秀下限 #鏡新聞 葉李的說話水準素質堪憂\n", "```json\n", "{\n", "  \"Context\": \"罷免潮燒新北！葉元之也遭殃　葉：搞鬥爭秀下限\",\n", "  \"Reply\": \"#鏡新聞 葉李的說話水準素質堪憂\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"disgust\"\n", "}\n", "```\n", "\n", "由於留言內容批評葉元之，且帶有厭惡、反感的情緒，因此判定為NEGATIVE，情緒為disgust。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免潮燒新北！葉元之也遭殃　葉：搞鬥爭秀下限\",\n", "  \"Reply\": \"#鏡新聞 葉李的說話水準素質堪憂\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"disgust\"\n", "}\n", "```\n", "\n", "由於留言內容批評葉元之，且帶有厭惡、反感的情緒，因此判定為NEGATIVE，情緒為disgust。\n", "{'標題': '罷免潮燒新北！葉元之也遭殃\\u3000葉：搞鬥爭秀下限', '留言內容': '#鏡新聞 葉李的說話水準素質堪憂', '情感標籤': 'NEGATIVE', '情緒': 'disgust'}\n", "【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台 這屆藍白真的有史以來最爛的\n", "```json\n", "{\n", "  \"Context\": \"【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台\",\n", "  \"Reply\": \"這屆藍白真的有史以來最爛的\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評國民黨與民眾黨，且帶有強烈不滿情緒。2. \"最爛的\"一詞表達強烈負面評價，符合\"anger\"情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台\",\n", "  \"Reply\": \"這屆藍白真的有史以來最爛的\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評國民黨與民眾黨，且帶有強烈不滿情緒。2. \"最爛的\"一詞表達強烈負面評價，符合\"anger\"情緒。\n", "{'標題': '【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台', '留言內容': '這屆藍白真的有史以來最爛的', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬ 賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！\n", "```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評侯友宜參選總統，且批評國民黨。2. 使用詞彙如「唾棄」、「背棄」、「擺脫不了藍營慣老闆」，可推測情緒為憤怒。3.  「擺脫不了藍營慣老闆」慣老闆為負面詞彙，佐證憤怒情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1. 內容批評侯友宜參選總統，且批評國民黨。2. 使用詞彙如「唾棄」、「背棄」、「擺脫不了藍營慣老闆」，可推測情緒為憤怒。3.  「擺脫不了藍營慣老闆」慣老闆為負面詞彙，佐證憤怒情緒。\n", "{'標題': '【一刀未剪】葉元之講\\\\', '留言內容': '賴清德是國家需要他當行政院長，而且做2年才辭職。國家沒有需要侯友宜選總統，是他為自己及國民黨的私立去選總統，沒有任何正當性，背棄新北市民，全國人民應唾棄這種行為！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署 #鏡新聞 請問秀中指算不算帶著仇恨?\n", "```json\n", "{\n", "  \"Context\": \"徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\",\n", "  \"Reply\": \"#鏡新聞 請問秀中指算不算帶著仇恨?\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言提到「仇恨」，且內容批評徐巧芯，而徐巧芯屬於藍營，因此判定為負面。由於提到「仇恨」，且語氣強硬，因此判定情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署\",\n", "  \"Reply\": \"#鏡新聞 請問秀中指算不算帶著仇恨?\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言提到「仇恨」，且內容批評徐巧芯，而徐巧芯屬於藍營，因此判定為負面。由於提到「仇恨」，且語氣強硬，因此判定情緒為anger。\n", "{'標題': '徐巧芯掃街拜年遇抗議！民眾怒撕春聯\\u3000衝罷免站連署', '留言內容': '#鏡新聞 請問秀中指算不算帶著仇恨?', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬ 元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧\n", "```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：1. 此留言批評葉元之卻擺出反諷語氣，且內容涉及藍綠對立，判定為負面。2.  \"去死\"是較為強烈的負面用語，故選\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：1. 此留言批評葉元之卻擺出反諷語氣，且內容涉及藍綠對立，判定為負面。2.  \"去死\"是較為強烈的負面用語，故選\"anger\"。\n", "{'標題': '【一刀未剪】葉元之講\\\\', '留言內容': '元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想國會過半，做夢吧', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "談到罷免紅了眼眶？　葉元之「曝光原因」怒批交通部 ‪@ChinaTimes‬ 感覺想專政\n", "```json\n", "{\n", "  \"Context\": \"談到罷免紅了眼眶？　葉元之「曝光原因」怒批交通部 ‪@ChinaTimes‬\",\n", "  \"Reply\": \"感覺想專政\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「感覺想專政」帶有批評意味，且批評對象是葉元之，因此判定為負面。由於「感覺想專政」帶有憤怒的情緒，因此判定為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"談到罷免紅了眼眶？　葉元之「曝光原因」怒批交通部 ‪@ChinaTimes‬\",\n", "  \"Reply\": \"感覺想專政\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「感覺想專政」帶有批評意味，且批評對象是葉元之，因此判定為負面。由於「感覺想專政」帶有憤怒的情緒，因此判定為anger。\n", "{'標題': '談到罷免紅了眼眶？\\u3000葉元之「曝光原因」怒批交通部 \\u202a@ChinaTimes\\u202c', '留言內容': '感覺想專政', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免陳柏惟宣傳片(唐鳳舞版本) 鳳姐也要「刪Q」？\n", "```json\n", "{\n", "  \"Context\": \"罷免陳柏惟宣傳片(唐鳳舞版本)\",\n", "  \"Reply\": \"鳳姐也要「刪Q」？\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"surprise\"\n", "}\n", "```\n", "\n", "由於「鳳姐也要「刪Q」？」是反諷，且內容批評民進黨，因此判斷為正面立場。由於「鳳姐也要「刪Q」？」有意外、詫異的涵義，因此判斷為surprise。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免陳柏惟宣傳片(唐鳳舞版本)\",\n", "  \"Reply\": \"鳳姐也要「刪Q」？\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"surprise\"\n", "}\n", "```\n", "\n", "由於「鳳姐也要「刪Q」？」是反諷，且內容批評民進黨，因此判斷為正面立場。由於「鳳姐也要「刪Q」？」有意外、詫異的涵義，因此判斷為surprise。\n", "{'標題': '罷免陳柏惟宣傳片(唐鳳舞版本)', '留言內容': '鳳姐也要「刪Q」？', '情感標籤': 'POSITIVE', '情緒': 'surprise'}\n", "鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之 這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已\n", "```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言表達對葉元之的反感，且以強烈的字詞「擺脫」、「塵埃落定」描述事件，可判斷為負面情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言表達對葉元之的反感，且以強烈的字詞「擺脫」、「塵埃落定」描述事件，可判斷為負面情緒。\n", "{'標題': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之', '留言內容': '這些人做了什麼為國為民的事嗎？罷免它們剛剛好而已', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "《鄉民大學問EP.48》重點版 黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews ‪@hance63‬ 支持罷免爛藍白立委\n", "```json\n", "{\n", "  \"Context\": \"《鄉民大學問EP.48》重點版\",\n", "  \"Reply\": \"黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews ‪@hance63‬ 支持罷免爛藍白立委\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  「反對」、「批評」、「擺脫不了」、「慣老闆」、「可憐」等詞彙，表達負面情緒。2.  內容涉及批評民進黨、支持罷免藍綠白立委，屬於反對陣營立場。3.  強烈的用語「擺脫不了」、「慣老闆」、「可憐」等，可推測情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"《鄉民大學問EP.48》重點版\",\n", "  \"Reply\": \"黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews ‪@hance63‬ 支持罷免爛藍白立委\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  「反對」、「批評」、「擺脫不了」、「慣老闆」、「可憐」等詞彙，表達負面情緒。2.  內容涉及批評民進黨、支持罷免藍綠白立委，屬於反對陣營立場。3.  強烈的用語「擺脫不了」、「慣老闆」、「可憐」等，可推測情緒為憤怒。\n", "{'標題': '《鄉民大學問EP.48》重點版', '留言內容': '黃暐瀚反對 藍營罷免案修法：罷免票高於得票等於取消罷免權！？仇恨值高才可能罷免成功？#謝國樑 一定能脫身？｜NOWnews \\u202a@hance63\\u202c 支持罷免爛藍白立委', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之 所以该节目到底是蓝是绿？\n", "```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"所以该节目到底是蓝是绿？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「所以该节目到底是蓝是绿？」帶有批評意味，且批評對象是藍綠陣營，因此判斷為負面。由於「怒火」、「不滿」、「批評」等詞彙出現，因此判斷情緒為anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"所以该节目到底是蓝是绿？\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「所以该节目到底是蓝是绿？」帶有批評意味，且批評對象是藍綠陣營，因此判斷為負面。由於「怒火」、「不滿」、「批評」等詞彙出現，因此判斷情緒為anger。\n", "{'標題': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之', '留言內容': '所以该节目到底是蓝是绿？', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3 黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？\n", "```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"surprise\"\n", "}\n", "```\n", "\n", "由於留言表達「好久不見了」，可推測是近期才關注政治議題，另有報導稱其過去曾批評民進黨，但現任新北市議員，立場較為模糊。綜合上述，判定為「POSITIVE」，「surprise」。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"surprise\"\n", "}\n", "```\n", "\n", "由於留言表達「好久不見了」，可推測是近期才關注政治議題，另有報導稱其過去曾批評民進黨，但現任新北市議員，立場較為模糊。綜合上述，判定為「POSITIVE」，「surprise」。\n", "{'標題': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3', '留言內容': '黃揚明 葉元之 毛嘉慶 葛斯齊好久不見了？', '情感標籤': 'POSITIVE', '情緒': 'surprise'}\n", "藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16 大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。\n", "```json\n", "{\n", "  \"Context\": \"藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16\",\n", "  \"Reply\": \"大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評民進黨，且用詞強硬，故判定為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16\",\n", "  \"Reply\": \"大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評民進黨，且用詞強硬，故判定為負面且憤怒。\n", "{'標題': '藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】2024.12.16', '留言內容': '大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑 輸不起就不要出來選嗎\n", "```json\n", "{\n", "  \"Context\": \"民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑\",\n", "  \"Reply\": \"輸不起就不要出來選嗎\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評特定政治人物並帶有強烈情緒，且從「8+9」的慣用語可推測是年輕世代的用語，綜合以上判斷為NEGATIVE且Emotion是anger。\n", "raw ```json\n", "{\n", "  \"Context\": \"民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑\",\n", "  \"Reply\": \"輸不起就不要出來選嗎\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評特定政治人物並帶有強烈情緒，且從「8+9」的慣用語可推測是年輕世代的用語，綜合以上判斷為NEGATIVE且Emotion是anger。\n", "{'標題': '民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑', '留言內容': '輸不起就不要出來選嗎', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "正面迎戰罷免潮！ 徐巧芯「超前部署」反罷 嗆綠輸不起！‪@newsebc‬ 亂七八糟，真的大搞綠色恐怖，肅清異己\n", "```json\n", "{\n", "  \"Context\": \"正面迎戰罷免潮！ 徐巧芯「超前部署」反罷 嗆綠輸不起！‪@newsebc‬\",\n", "  \"Reply\": \"亂七八糟，真的大搞綠色恐怖，肅清異己\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評民進黨「綠色恐怖」、「肅清異己」，可知其反對民進黨。又因使用「怒火」、「可恥」、「去死」等詞彙，可知其情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"正面迎戰罷免潮！ 徐巧芯「超前部署」反罷 嗆綠輸不起！‪@newsebc‬\",\n", "  \"Reply\": \"亂七八糟，真的大搞綠色恐怖，肅清異己\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評民進黨「綠色恐怖」、「肅清異己」，可知其反對民進黨。又因使用「怒火」、「可恥」、「去死」等詞彙，可知其情緒為憤怒。\n", "{'標題': '正面迎戰罷免潮！ 徐巧芯「超前部署」反罷 嗆綠輸不起！\\u202a@newsebc\\u202c', '留言內容': '亂七八糟，真的大搞綠色恐怖，肅清異己', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之 凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！\n", "```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言批評葉元之「亂凹」、認為其言論不可信，且用詞強硬，因此判定為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之\",\n", "  \"Reply\": \"凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言批評葉元之「亂凹」、認為其言論不可信，且用詞強硬，因此判定為負面且憤怒。\n", "{'標題': '鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】20240614-1 劉寶傑 張禹宣 黃暐瀚 黃世聰 黃敬平 單厚之', '留言內容': '凹汁也不香常亂凹，昨天還凹阿川的闖紅燈是記者被報社壓案不報才丟給900芯爆料，這不是凹什麽才是凹？！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬ 葉之再凹就聽不下去了.你令人討厭耶.\n", "```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"葉之再凹就聽不下去了.你令人討厭耶.\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  「葉之再凹就聽不下去了」表達不滿意和厭惡，2. 「你令人討厭耶」直接表達憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"葉之再凹就聽不下去了.你令人討厭耶.\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "理由：1.  「葉之再凹就聽不下去了」表達不滿意和厭惡，2. 「你令人討厭耶」直接表達憤怒。\n", "{'標題': '【一刀未剪】葉元之講\\\\', '留言內容': '葉之再凹就聽不下去了.你令人討厭耶.', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞 加油！罷免藍營立委\n", "```json\n", "{\n", "  \"Context\": \"罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞\",\n", "  \"Reply\": \"加油！罷免藍營立委\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達支持罷免葉元之，且用詞強硬，可推斷其立場為負面。由於內容帶有憤慨、厭惡等情緒，故選定\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞\",\n", "  \"Reply\": \"加油！罷免藍營立委\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達支持罷免葉元之，且用詞強硬，可推斷其立場為負面。由於內容帶有憤慨、厭惡等情緒，故選定\"anger\"。\n", "{'標題': '罷免葉元之連署站開張! 連署民眾:不滿藍白國會亂搞－民視新聞', '留言內容': '加油！罷免藍營立委', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬ 主持人要加油加油\n", "```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"主持人要加油加油\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "理由：1. 內容反對罷免葉元之，且表達對葉元之的支持。2. \"加油\"一詞通常帶有鼓勵、支持的意味，符合\"joy\"情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"主持人要加油加油\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"joy\"\n", "}\n", "```\n", "\n", "理由：1. 內容反對罷免葉元之，且表達對葉元之的支持。2. \"加油\"一詞通常帶有鼓勵、支持的意味，符合\"joy\"情緒。\n", "{'標題': '【一刀未剪】葉元之講\\\\', '留言內容': '主持人要加油加油', '情感標籤': 'POSITIVE', '情緒': 'joy'}\n", "罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3 黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠\n", "```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言批評「黃揚明、葉元之、毛嘉慶、吳思瑤」等人造謠，且用語強硬，故判斷立場為負面，情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言批評「黃揚明、葉元之、毛嘉慶、吳思瑤」等人造謠，且用語強硬，故判斷立場為負面，情緒為憤怒。\n", "{'標題': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3', '留言內容': '黃揚明 葉元之 毛嘉慶 吳思謠 造謠的謠', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免陳柏惟宣傳片(唐鳳舞版本) 破產都要把牠罷下來！\n", "```json\n", "{\n", "  \"Context\": \"罷免陳柏惟宣傳片(唐鳳舞版本)\",\n", "  \"Reply\": \"破產都要把牠罷下來！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達強烈的憤怒，且內容涉及批評特定政治人物，因此判斷為負面情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免陳柏惟宣傳片(唐鳳舞版本)\",\n", "  \"Reply\": \"破產都要把牠罷下來！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達強烈的憤怒，且內容涉及批評特定政治人物，因此判斷為負面情緒。\n", "{'標題': '罷免陳柏惟宣傳片(唐鳳舞版本)', '留言內容': '破產都要把牠罷下來！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "民團號召罷免27藍委 葉元之:那總統也一起重選! 狙擊27藍委! 2月任期滿1年 民團將發動罷免│記者 許芷瑄 林柏翰│台灣要聞20250102│三立iNEWS 美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！\n", "```json\n", "{\n", "  \"Context\": \"民團號召罷免27藍委 葉元之:那總統也一起重選! 狙擊27藍委! 2月任期滿1年 民團將發動罷免│記者 許芷瑄 林柏翰│台灣要聞20250102│三立iNEWS\",\n", "  \"Reply\": \"美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容涉及「美國中情局」、「顏色革命」，且以問號表達強烈不滿，故判斷為負面且憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"民團號召罷免27藍委 葉元之:那總統也一起重選! 狙擊27藍委! 2月任期滿1年 民團將發動罷免│記者 許芷瑄 林柏翰│台灣要聞20250102│三立iNEWS\",\n", "  \"Reply\": \"美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於內容涉及「美國中情局」、「顏色革命」，且以問號表達強烈不滿，故判斷為負面且憤怒。\n", "{'標題': '民團號召罷免27藍委 葉元之:那總統也一起重選! 狙擊27藍委! 2月任期滿1年 民團將發動罷免│記者 許芷瑄 林柏翰│台灣要聞20250102│三立iNEWS', '留言內容': '美國中情局在台灣搞顏色革命的可疑類綠道綠豆幫暝糰？？？！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "罷免民調危險了?!葉元之曝\"自救計劃\":有則改之無則加勉 元之委員加油\n", "```json\n", "{\n", "  \"Context\": \"罷免民調危險了?!葉元之曝\\\"自救計劃\\\":有則改之無則加勉\",\n", "  \"Reply\": \"元之委員加油\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"trust\"\n", "}\n", "```\n", "\n", "由於留言表達對葉元之的支持，且帶有鼓勵意味，因此判斷立場為POSITIVE，情緒為trust。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免民調危險了?!葉元之曝\\\"自救計劃\\\":有則改之無則加勉\",\n", "  \"Reply\": \"元之委員加油\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"trust\"\n", "}\n", "```\n", "\n", "由於留言表達對葉元之的支持，且帶有鼓勵意味，因此判斷立場為POSITIVE，情緒為trust。\n", "{'標題': '罷免民調危險了?!葉元之曝\\\\', '留言內容': '元之委員加油', '情感標籤': 'POSITIVE', '情緒': 'trust'}\n", "罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3 黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！\n", "```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言多次出現貶義詞「擺光光」、「閉嘴」、「裝瞎聾」、「沒三小路用」，且明確表達支持罷免葉元之，因此判斷其立場為負面，情緒為憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3\",\n", "  \"Reply\": \"黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言多次出現貶義詞「擺光光」、「閉嘴」、「裝瞎聾」、「沒三小路用」，且明確表達支持罷免葉元之，因此判斷其立場為負面，情緒為憤怒。\n", "{'標題': '罷免吳思瑤首階段一日達標？王定宇也被公民團體鎖定了【新聞大白話】20250112-3', '留言內容': '黃揚明 葉元之 毛嘉慶 全部拔光光 只要叫民進黨的 反正看似有正義的也是閉嘴裝瞎聾沆瀣一氣 沒三小路用 通通罷 請假買機票 也要罷！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "「春眠不覺曉、文山賴士葆」大罷免開花！學生詩諷藍委－民視新聞 往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。\n", "```json\n", "{\n", "  \"Context\": \"「春眠不覺曉、文山賴士葆」大罷免開花！學生詩諷藍委－民視新聞\",\n", "  \"Reply\": \"往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"anticipation\"\n", "}\n", "```\n", "\n", "由於內容多次強調「一定要」、「催出投票率」、「罷免才能成功」，可見其積極且急切地推動罷免案通過。\n", "raw ```json\n", "{\n", "  \"Context\": \"「春眠不覺曉、文山賴士葆」大罷免開花！學生詩諷藍委－民視新聞\",\n", "  \"Reply\": \"往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。\",\n", "  \"Label\": \"POSITIVE\",\n", "  \"Emotion\": \"anticipation\"\n", "}\n", "```\n", "\n", "由於內容多次強調「一定要」、「催出投票率」、「罷免才能成功」，可見其積極且急切地推動罷免案通過。\n", "{'標題': '「春眠不覺曉、文山賴士葆」大罷免開花！學生詩諷藍委－民視新聞', '留言內容': '往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。', '情感標籤': 'POSITIVE', '情緒': 'anticipation'}\n", "一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台 審預算沒看內容還能表決，這立委6歲小孩都能當\n", "```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"審預算沒看內容還能表決，這立委6歲小孩都能當\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言批評葉元之不認真審查預算書，且用詞強硬，因此判斷為負面評價。此外，從\"火大\"、\"可憐\"等詞彙可推斷情緒為\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒\"看都不看就表決?\"│焦點人物大現場20250123│三立新聞台\",\n", "  \"Reply\": \"審預算沒看內容還能表決，這立委6歲小孩都能當\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "說明：由於該留言批評葉元之不認真審查預算書，且用詞強硬，因此判斷為負面評價。此外，從\"火大\"、\"可憐\"等詞彙可推斷情緒為\"anger\"。\n", "{'標題': '一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機密預算 葉: 預算資料我沒去領 尚毅夫怒', '留言內容': '審預算沒看內容還能表決，這立委6歲小孩都能當', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "自由爆新聞》12藍委陷\"罷免危險區\"！花蓮想變！他揭\"1狀況\"傅崐萁會怕！(王毅/王大陸) 如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理\n", "```json\n", "{\n", "  \"Context\": \"自由爆新聞》12藍委陷\\\"罷免危險區\\\"！花蓮想變！他揭\\\"1狀況\\\"傅崐萁會怕！(王毅/大陸)\",\n", "  \"Reply\": \"如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評「藍營」、「葉元之」，且以強硬的詞彙「強辭奪理」表達不滿，因此判定為負面立場。由於內容涉及不道德行為，且用詞強硬，可推測發言者非常憤怒。\n", "raw ```json\n", "{\n", "  \"Context\": \"自由爆新聞》12藍委陷\\\"罷免危險區\\\"！花蓮想變！他揭\\\"1狀況\\\"傅崐萁會怕！(王毅/大陸)\",\n", "  \"Reply\": \"如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言批評「藍營」、「葉元之」，且以強硬的詞彙「強辭奪理」表達不滿，因此判定為負面立場。由於內容涉及不道德行為，且用詞強硬，可推測發言者非常憤怒。\n", "{'標題': '自由爆新聞》12藍委陷\\\\', '留言內容': '如果認真的一筆一筆核對連署，確有人寫好交給罷免平台，怎會有死亡的聯署，不要強辭奪理', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台 哇，清大有這種老師教憲法！悲哀啊！\n", "```json\n", "{\n", "  \"Context\": \"【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台\",\n", "  \"Reply\": \"哇，清大有這種老師教憲法！悲哀啊！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"sadness\"\n", "}\n", "```\n", "\n", "由於留言表達對葉元之和清華大學的批評，且帶有悲傷的情緒，因此判斷立場為負面，情緒為難過。\n", "raw ```json\n", "{\n", "  \"Context\": \"【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台\",\n", "  \"Reply\": \"哇，清大有這種老師教憲法！悲哀啊！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"sadness\"\n", "}\n", "```\n", "\n", "由於留言表達對葉元之和清華大學的批評，且帶有悲傷的情緒，因此判斷立場為負面，情緒為難過。\n", "{'標題': '【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫朗東酸芯不要急:我要放壓軸│呂惠敏主持│【驚爆新聞線】20240616│三立新聞台', '留言內容': '哇，清大有這種老師教憲法！悲哀啊！', '情感標籤': 'NEGATIVE', '情緒': 'sadness'}\n", "【94要客訴】沒在怕？綠營將反制「罷免江啟臣」？葉元之：要罷就罷！ 不止罷免江啟程。\n", "凡是國民黨議員。立委。縣市長。\n", "都該被罷免。\n", "趕出台灣。\n", "```json\n", "{\n", "  \"Context\": \"【94要客訴】沒在怕？綠營將反制「罷免江啟臣」？葉元之：要罷就罷！\",\n", "  \"Reply\": \"不止罷免江啟程。\\n凡是國民黨議員。立委。縣市長。\\n都該被罷免。\\n趕出台灣。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言多次出現「罷免」、「趕出台灣」等強硬詞彙，且批評對象為國民黨籍的葉元之，因此判斷為負面。此外，由於內容充滿憤怒情緒，故選擇\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"【94要客訴】沒在怕？綠營將反制「罷免江啟臣」？葉元之：要罷就罷！\",\n", "  \"Reply\": \"不止罷免江啟程。\\n凡是國民黨議員。立委。縣市長。\\n都該被罷免。\\n趕出台灣。\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言多次出現「罷免」、「趕出台灣」等強硬詞彙，且批評對象為國民黨籍的葉元之，因此判斷為負面。此外，由於內容充滿憤怒情緒，故選擇\"anger\"。\n", "{'標題': '【94要客訴】沒在怕？綠營將反制「罷免江啟臣」？葉元之：要罷就罷！', '留言內容': '不止罷免江啟程。\\\\n凡是國民黨議員。立委。縣市長。\\\\n都該被罷免。\\\\n趕出台灣。', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬ 罷候\n", "```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"罷候\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「罷候」是簡稱，需要參考上下文才能確定完整意思。在此案例中，「罷候」指的是「罷免侯友宜」。\n", "raw ```json\n", "{\n", "  \"Context\": \"【一刀未剪】葉元之講\\\"這句話\\\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉證：歷年來綠營有哪個總統候選人是現任縣市首長？│【焦點人物大現場】20230521│‪@NewTaiwan‬\",\n", "  \"Reply\": \"罷候\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於「罷候」是簡稱，需要參考上下文才能確定完整意思。在此案例中，「罷候」指的是「罷免侯友宜」。\n", "{'標題': '【一刀未剪】葉元之講\\\\', '留言內容': '罷候', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "立院外罷免投票「徐巧芯、葉元之人氣旺」！青鳥投票「黃國昌被貼滿」‪@newsebc‬ 抗議的不知道自己在抗什麼\n", "```json\n", "{\n", "  \"Context\": \"立院外罷免投票「徐巧芯、葉元之人氣旺」！青鳥投票「黃國昌被貼滿」‪@newsebc‬\",\n", "  \"Reply\": \"抗議的不知道自己在抗什麼\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達不滿意罷免案，且用詞帶有批評意味，因此判定為負面立場。由於內容帶有憤慨、不滿，故選定\"anger\"。\n", "raw ```json\n", "{\n", "  \"Context\": \"立院外罷免投票「徐巧芯、葉元之人氣旺」！青鳥投票「黃國昌被貼滿」‪@newsebc‬\",\n", "  \"Reply\": \"抗議的不知道自己在抗什麼\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於留言表達不滿意罷免案，且用詞帶有批評意味，因此判定為負面立場。由於內容帶有憤慨、不滿，故選定\"anger\"。\n", "{'標題': '立院外罷免投票「徐巧芯、葉元之人氣旺」！青鳥投票「黃國昌被貼滿」\\u202a@newsebc\\u202c', '留言內容': '抗議的不知道自己在抗什麼', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑 我們直接罷掉不適任的總統！更快！\n", "```json\n", "{\n", "  \"Context\": \"民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑\",\n", "  \"Reply\": \"我們直接罷掉不適任的總統！更快！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達對現任總統的反感，且用詞強硬，因此判斷為負面情緒。\n", "raw ```json\n", "{\n", "  \"Context\": \"民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑\",\n", "  \"Reply\": \"我們直接罷掉不適任的總統！更快！\",\n", "  \"Label\": \"NEGATIVE\",\n", "  \"Emotion\": \"anger\"\n", "}\n", "```\n", "\n", "由於該留言表達對現任總統的反感，且用詞強硬，因此判斷為負面情緒。\n", "{'標題': '民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不怕罷免！ -【關鍵時刻】劉寶傑', '留言內容': '我們直接罷掉不適任的總統！更快！', '情感標籤': 'NEGATIVE', '情緒': 'anger'}\n", "分析完成，結果已儲存至 data/0424/new_ba_100.json\n"]}], "source": ["import json\n", "import os\n", "import re\n", "import time\n", "import random\n", "##最後版本  111111111111111111111111111111111111111111111111111111111  models/gemini-2.0-flash\n", "import time\n", "import random\n", "from google import genai\n", "from google.genai import types\n", "\n", "\n", "client = genai.Client(api_key=\"AIzaSyCbhqxVF-jvIDxyzBzlFHJThoF8SQB8ufQ\")\n", "def analyze_comment(context, reply,txtfile,jsonfile, max_retries=6, ignore_safety=True):\n", "    prompt = build_prompt5(context, reply)\n", "\n", "    # 設定安全等級\n", "    if ignore_safety:\n", "        safety_settings = [\n", "            types.SafetySetting(category=\"HARM_CATEGORY_HATE_SPEECH\", threshold=\"BLOCK_NONE\"),\n", "            types.SafetySetting(category=\"HARM_CATEGORY_SEXUALLY_EXPLICIT\", threshold=\"BLOCK_NONE\"),\n", "            types.SafetySetting(category=\"HARM_CATEGORY_DANGEROUS_CONTENT\", threshold=\"BLOCK_NONE\"),\n", "            types.SafetySetting(category=\"HARM_CATEGORY_HARASSMENT\", threshold=\"BLOCK_NONE\"),\n", "            types.SafetySetting(category=\"HARM_CATEGORY_CIVIC_INTEGRITY\", threshold=\"BLOCK_NONE\"),\n", "        ]\n", "    else:\n", "        safety_settings = None\n", "###tunedModels/mab-background-3192\n", "    for attempt in range(max_retries):\n", "        try:\n", "            response = client.models.generate_content(              ##這裡換模型tunedModels\n", "                model=\"tunedModels/ccma-background-6587\",\n", "                contents=prompt,\n", "                config=types.GenerateContentConfig(\n", "                    safety_settings=safety_settings\n", "                ) if safety_settings else None\n", "            )\n", "            print(response.text)\n", "            \n", "            # 儲存 AI 回應到 txt 檔案\n", "            txt_file = txtfile\n", "            os.makedirs(os.path.dirname(txt_file), exist_ok=True)\n", "            with open(txt_file, \"a\", encoding=\"utf-8\") as f:\n", "                f.write(f\"=== 回應 {time.strftime('%Y-%m-%d %H:%M:%S')} ===\\n\")\n", "                f.write(f\"Context: {context}\\n\")\n", "                f.write(f\"Reply: {reply}\\n\")\n", "                f.write(f\"Response: {response.text}\\n\\n\")\n", "            \n", "            if not hasattr(response, 'text') or not response.text.strip():\n", "                print(f\"⚠️ [第 {attempt+1}/{max_retries}] 空回應，重試。\")\n", "                time.sleep(random.uniform(2, 4))\n", "                continue\n", "\n", "            result = extract_text_data(response.text.strip())\n", "            if any(value == \"Unknown\" for value in result.values()):\n", "                print(f\"⚠️ [第 {attempt+1}/{max_retries}] 有欄位為 Unknown，重試。\")\n", "                time.sleep(random.uniform(2, 5))\n", "                continue\n", "                \n", "            # 儲存結果到 JSON 檔案\n", "            json_file = jsonfile\n", "            os.makedirs(os.path.dirname(json_file), exist_ok=True)\n", "            if os.path.exists(json_file):\n", "                with open(json_file, \"r\", encoding=\"utf-8\") as f:\n", "                    existing_data = json.load(f)\n", "            else:\n", "                existing_data = []\n", "            \n", "            existing_data.append(result)\n", "            with open(json_file, \"w\", encoding=\"utf-8\") as f:\n", "                json.dump(existing_data, f, ensure_ascii=False, indent=2)\n", "                \n", "            return result\n", "\n", "        except Exception as e:\n", "            print(f\"⚠️ [第 {attempt+1}/{max_retries}] 發生錯誤：{e}，重試。\")\n", "            time.sleep(random.uniform(3, 6))\n", "            continue\n", "\n", "    print(\"❌ 所有嘗試失敗，跳過留言。\")\n", "    save_skipped_comment(context, reply)\n", "    return None\n", "\n", "def extract_text_data(raw: str) -> dict:\n", "    \"\"\"\n", "    從 LLM 純文字回覆中，用 regex 抓 Context / Reply / Label / Emotion。\n", "    適用於：\n", "        • LLM 回傳多餘文字\n", "        • 缺失右大括號、引號格式錯誤\n", "    失敗時各欄回傳 Unknown，不會丟例外。\n", "    \"\"\"\n", "    # 把全形（中文）雙引號與特殊引號換成標準半形，方便 regex\n", "    print(\"raw\",raw)\n", "    text = (raw.replace(\"“\", \"\\\"\")\n", "                .replace(\"”\", \"\\\"\")\n", "                .replace(\"”\", \"\\\"\")\n", "                .replace(\"’\", \"'\"))\n", "\n", "    # 建立可容忍換行、空白的 regex\n", "    def _grab(pattern: str) -> str:\n", "        m = re.search(pattern, text, re.DOTALL | re.IGNORECASE)\n", "        return (m.group(1).strip() if m else \"Unknown\")\n", "\n", "    ctx     = _grab(r'\"?Context\"?\\s*:\\s*\"([^\"]+)\"')\n", "    reply   = _grab(r'\"?Reply\"?\\s*:\\s*\"([^\"]+)\"')\n", "    label   = _grab(r'\"?Label\"?\\s*:\\s*\"([^\"]+)\"')\n", "    emotion = _grab(r'\"?Emotion\"?\\s*:\\s*\"([^\"]+)\"')\n", "\n", "    return {\n", "        \"標題\": ctx,\n", "        \"留言內容\": reply,\n", "        \"情感標籤\": label,\n", "        \"情緒\": emotion,\n", "    }\n", "\n", "def load_json(file_path):\n", "    if os.path.exists(file_path):\n", "        with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "            return json.load(f)\n", "    return []\n", "\n", "def save_json(file_path, data):\n", "    os.makedirs(os.path.dirname(file_path), exist_ok=True)\n", "    with open(file_path, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(data, f, ensure_ascii=False, indent=2)\n", "\n", "def save_txt(file_path, data):\n", "    \"\"\"將 AI 回應儲存為 txt 檔案\"\"\"\n", "    with open(file_path, \"a\", encoding=\"utf-8\") as f:  # 改為追加模式\n", "        f.write(data)\n", "\n", "def save_skipped_comment(context, reply):\n", "    \"\"\"將被安全策略封鎖的留言存入檔案\"\"\"\n", "    skipped_file = \"data/sssss.json\"\n", "    skipped_data = load_json(skipped_file)\n", "    skipped_data.append({\"標題\": context, \"留言內容\": reply})\n", "    save_json(skipped_file, skipped_data)\n", "\n", "def process_comments(input_file, output_file):\n", "    os.makedirs(os.path.dirname(output_file), exist_ok=True)  # 確保輸出資料夾存在\n", "    data = load_json(input_file)\n", "    results = load_json(output_file) if os.path.exists(output_file) else []\n", "    processed = {item.get(\"留言內容\", \"\") for item in results}  # 追蹤已處理過的留言\n", "    \n", "    for item in data:\n", "        context = item.get(\"context\", \"\")\n", "        reply = item.get(\"reply\", \"\")\n", "        if reply in processed:\n", "            continue\n", "        time.sleep(random.uniform(2, 5))\n", "        print(context,reply)\n", "        ai_response = analyze_comment(context, reply, \"data/0424/new_ba_100.txt\", \"data/0424/new_ba_100.json\")\n", "        print(ai_response)\n", "    \n", "        \n", "        time.sleep(random.uniform(2, 5))  # 避免過度頻繁請求\n", "    \n", "    print(\"分析完成，結果已儲存至\", output_file)\n", "\n", "# 設定輸入與輸出檔案\n", "input_file = \"data/100_model_predict.json\"\n", "output_file = \"data/0424/new_ba_100.json\"\n", "\n", "##000 prompt3 001prompt4\n", "# 執行批量處理\n", "process_comments(input_file, output_file)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'標題': '「藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片\" 反制大罷免潮 朱立倫:啟動反廢死公投應戰│新聞一把抓20250308│三立新聞台」', '留言內容': '「看看現在的罷免團體遭受多少的小動作，國民黨怎麼好意思說出 \"對民進黨不利他們就顧顧刁難\" 這種話? 要點臉好嗎?不要為了怕被罷免連臉都不要了.」', '情感標籤': 'Negative', '情緒': 'Anger'}\n"]}], "source": ["\n", "context = \"藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\\\"罷免李彥秀紀錄片\\\" 反制大罷免潮 朱立倫:啟動反廢死公投應戰│新聞一把抓20250308│三立新聞台\"\n", "reply = \"看看現在的罷免團體遭受多少的小動作，國民黨怎麼好意思說出 \\\"對民進黨不利他們就顧顧刁難\\\" 這種話? 要點臉好嗎?不要為了怕被罷免連臉都不要了.\"\n", "ai_response = analyze_comment(context, reply)\n", "print(ai_response)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "\"人工標註數據 缺少這些必要的欄位: {'emotion', 'context', 'label', 'reply'}\"", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[50]\u001b[39m\u001b[32m, line 33\u001b[39m\n\u001b[32m     30\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m missing_cols:\n\u001b[32m     31\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 缺少這些必要的欄位: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmissing_cols\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m33\u001b[39m \u001b[43mvalidate_columns\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma_df\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m人工標註數據\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     34\u001b[39m validate_columns(b_df, \u001b[33m\"\u001b[39m\u001b[33mGemini 預測數據\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     35\u001b[39m validate_columns(c_df, \u001b[33m\"\u001b[39m\u001b[33mBERT 預測數據\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[50]\u001b[39m\u001b[32m, line 31\u001b[39m, in \u001b[36mvalidate_columns\u001b[39m\u001b[34m(df, file_name)\u001b[39m\n\u001b[32m     29\u001b[39m missing_cols = required_columns - \u001b[38;5;28mset\u001b[39m(df.columns)\n\u001b[32m     30\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m missing_cols:\n\u001b[32m---> \u001b[39m\u001b[32m31\u001b[39m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 缺少這些必要的欄位: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmissing_cols\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[31mKeyError\u001b[39m: \"人工標註數據 缺少這些必要的欄位: {'emotion', 'context', 'label', 'reply'}\""]}], "source": ["import json\n", "import pandas as pd\n", "from sklearn.metrics import accuracy_score\n", "\n", "# 讀取 JSON 文件\n", "def load_json(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return json.load(f)\n", "\n", "# 設定文件路徑\n", "a_path = \"data/0323/驗證-myself.json\"\n", "b_path = \"data/0323/驗證-gemini.json\"\n", "c_path = \"data/0323/驗證-bert.json\"\n", "\n", "# 載入數據\n", "a_data = load_json(a_path)\n", "b_data = load_json(b_path)\n", "c_data = load_json(c_path)\n", "\n", "# 轉為 DataFrame\n", "a_df = pd.DataFrame(a_data)\n", "b_df = pd.DataFrame(b_data)\n", "c_df = pd.DataFrame(c_data)\n", "\n", "# 確保所有 JSON 檔案都有必要欄位\n", "required_columns = {\"context\", \"reply\", \"label\", \"emotion\"}\n", "\n", "def validate_columns(df, file_name):\n", "    missing_cols = required_columns - set(df.columns)\n", "    if missing_cols:\n", "        raise KeyError(f\"{file_name} 缺少這些必要的欄位: {missing_cols}\")\n", "\n", "validate_columns(a_df, \"人工標註數據\")\n", "validate_columns(b_df, \"Gemini 預測數據\")\n", "validate_columns(c_df, \"BERT 預測數據\")\n", "\n", "# 以 context 和 reply 來進行匹配\n", "def merge_data(a_df, model_df, model_name):\n", "    merged_df = a_df.merge(model_df, on=[\"context\", \"reply\"], suffixes=(\"_true\", f\"_{model_name}\"), how=\"inner\")\n", "    return merged_df\n", "\n", "gemini_df = merge_data(a_df, b_df, \"gemini\")\n", "bert_df = merge_data(a_df, c_df, \"bert\")\n", "\n", "# 計算準確率並顯示詳細比較\n", "def calculate_accuracy(df, model_name):\n", "    print(f\"\\n{model_name} 模型預測與真實標籤比較:\")\n", "    \n", "    # 逐行比較\n", "    comparison_df = df[[\"context\", \"reply\", \"label_true\", f\"label_{model_name}\", \"emotion_true\", f\"emotion_{model_name}\"]]\n", "    \n", "\n", "\n", "    # 計算準確率\n", "    label_acc = accuracy_score(df[\"label_true\"], df[f\"label_{model_name}\"])\n", "    emotion_acc = accuracy_score(df[\"emotion_true\"], df[f\"emotion_{model_name}\"])\n", "\n", "    return label_acc, emotion_acc\n", "\n", "# 計算並顯示準確率\n", "gemini_label_acc, gemini_emotion_acc = calculate_accuracy(gemini_df, \"gemini\")\n", "bert_label_acc, bert_emotion_acc = calculate_accuracy(bert_df, \"bert\")\n", "\n", "# 顯示結果\n", "results = {\n", "    \"Gemini Label Accuracy\": gemini_label_acc,\n", "    \"Gemini Emotion Accuracy\": gemini_emotion_acc,\n", "    \"BERT Label Accuracy\": bert_label_acc,\n", "    \"BERT Emotion Accuracy\": bert_emotion_acc,\n", "}\n", "\n", "print(\"\\n準確率結果:\")\n", "for key, value in results.items():\n", "    print(f\"{key}: {value:.2%}\")\n", "\n", "\n", "##目前有的問題 1.gemini的安全策略 有時候一些太偏激的言論會沒辦法判斷  2.genimi的情續判斷 由於沒有給資料跟提示詞的設定 所以跑出來的比較不理想"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "gemini 模型預測與真實標籤比較:\n", "\n", "情感標籤匹配錯誤的資料:\n", "                                                   標題  \\\n", "1   見到鬼了嗎？葉元之被罷免機率高！何博文爆氣怒轟「民進黨出手你早就陣亡」該補的津貼不發　還在立...   \n", "5   藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片...   \n", "11                           徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署   \n", "19  民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不...   \n", "20  藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】20...   \n", "21  鬥臭傅崐萁、拉下葉元之！民進黨大軍「反濫權」之名操兵罷免 頭號目標徐巧芯？【關鍵時刻】202...   \n", "22  【#週末大爆卦】吳沛憶高興太早?罷免分AB案送件!韓國瑜去賴清德五院會商\"不會給承諾\"!沒結...   \n", "23  藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片...   \n", "28    藍白砍預算「失速列車」國民黨跳車！藍怕罷免紛紛轉彎…朝野鬥爭黃國昌敗陣！？【關鍵時刻】-劉寶傑   \n", "30                 罷免遍地開花！ 韓國瑜合體葉元之：操守可圈可點 ‪@newsebc‬   \n", "43                  街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！   \n", "46  【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉...   \n", "48                       【議員質詢】火化之亂，葉元之點出真正原因，侯友宜一度沈默   \n", "51      自由爆新聞》12藍委陷\"罷免危險區\"！花蓮想變！他揭\"1狀況\"傅崐萁會怕！(王毅/王大陸)   \n", "54        自由爆新聞》\"罷免謝國樑\"進度曝光！藍立院\"奪權之戰\"？分析示警！(黃仁勳/基隆山崩)   \n", "56        自由爆新聞》糗！罷綠慘團滅､罷免藍\"全壘打\"！他不認中共威脅網傻眼轟！(柯文哲/川普)   \n", "64                                    罷免陳柏惟宣傳片(唐鳳舞版本)   \n", "65  被罷免並非不可能！傅崐萁與徐巧芯「仇恨值飆升」成綠營摧毀戰上「兩大指標性山頭」？【關鍵時刻】...   \n", "72            國民黨派出神仙補選高雄市長都沒救！葉元之認\"派誰都沒用\" 傳江啟臣有望親征？！   \n", "73  【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫...   \n", "81                   【預告】葉元之真《被政治耽誤》了？ 新「聲」代爆笑RAP王登場！   \n", "\n", "                                                 留言內容 情感標籤_true 情感標籤_gemini  \\\n", "1             94要賺錢 柯文哲說謊成性組民眾黨，經費來源不清不楚，沒有那種屁股吃那種洩藥。  POSITIVE    NEGATIVE   \n", "5                               公投綁罷免，共產黨要激他們基本票，爛爆了。  NEGATIVE    POSITIVE   \n", "11  #鏡新聞 吳沛億你跟陳玉珍的控告呢。不要亂扯了。綠媒。誰先罷免別人的。民進黨。什麼叫國民黨罷...  POSITIVE    NEGATIVE   \n", "19                                   我們直接罷掉不適任的總統！更快！  POSITIVE    NEGATIVE   \n", "20                         大家硬起來，什麼最大，人民最大，別讓民進黨胡搞瞎搞。  POSITIVE    NEGATIVE   \n", "21                                民進當现在正發起文化大革命哪,大家小心  POSITIVE    NEGATIVE   \n", "22          賴的仇恨也快了，天怒人怨，國事不做，只會搞小動作，全國百姓要記的李進勇這個做票腳。  POSITIVE    NEGATIVE   \n", "23                     朱立倫玩反廢死公投, 老朱不如先廢青年軍, 再廢舔共爛委吧.  POSITIVE    NEGATIVE   \n", "28                          當你這台幫范雲打黃國昌時，我就知道幕後老闆是綠色的  POSITIVE    NEGATIVE   \n", "30                                      可以花公益團體的錢罷免嗎？  POSITIVE    NEGATIVE   \n", "43                                            綠禍害，爛愚民  POSITIVE    NEGATIVE   \n", "46  元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想...  NEGATIVE    POSITIVE   \n", "48             親愛的朋友進來別忘了按分享留言政府防疫無能8400億花去哪了很多孩子都走了⋯  POSITIVE    NEGATIVE   \n", "51                           不是信魏哲家，也不是衹信藍，還要決對相信馬英九？  POSITIVE    NEGATIVE   \n", "54                                                博愛路  NEGATIVE    POSITIVE   \n", "56                                         豬公 罹圇 一路好走  NEGATIVE    POSITIVE   \n", "64                                        朝聖! 柿子要挑軟的吃  NEGATIVE    POSITIVE   \n", "65                                            請支持國共合作  NEGATIVE    POSITIVE   \n", "72          黃倩萍主持｜【前進新台灣焦點話題】20200608｜三立新聞台 元之比子折好多了！  NEGATIVE    POSITIVE   \n", "73                                               藍鳥啦！  NEGATIVE    POSITIVE   \n", "81                               4/7晚間6點 鎖定中時新聞網 好期待!  NEGATIVE    POSITIVE   \n", "\n", "         情緒_true     情緒_gemini  \n", "1          anger       disgust  \n", "5          anger         anger  \n", "11         trust         anger  \n", "19  anticipation         anger  \n", "20         trust         anger  \n", "21         trust          fear  \n", "22         trust         anger  \n", "23         trust         anger  \n", "28           joy         anger  \n", "30           joy         anger  \n", "43         anger         anger  \n", "46         anger         anger  \n", "48       sadness         anger  \n", "51           joy          fear  \n", "54           joy         anger  \n", "56           joy         anger  \n", "64       disgust           joy  \n", "65       disgust         trust  \n", "72       disgust           joy  \n", "73       disgust           joy  \n", "81       disgust  anticipation  \n", "\n", "情緒匹配錯誤的資料:\n", "                                                   標題  \\\n", "1   見到鬼了嗎？葉元之被罷免機率高！何博文爆氣怒轟「民進黨出手你早就陣亡」該補的津貼不發　還在立...   \n", "7                            談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞   \n", "11                           徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署   \n", "13                     發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣   \n", "15  藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片...   \n", "..                                                ...   \n", "78                     發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣   \n", "80  大罷免浪潮! 中選會累計33立委2議員1市長罷免案 罷洪團體控遭便衣警\"盯梢\" 淡水分局:依...   \n", "81                   【預告】葉元之真《被政治耽誤》了？ 新「聲」代爆笑RAP王登場！   \n", "82                     發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣   \n", "83  搶救罷韓反成豬隊友？葉元之直播痛批罷韓扯\"失戀女人\"挨批 葉駁斥喊冤:我被斷章取義│廖筱君主...   \n", "\n", "                                                 留言內容 情感標籤_true 情感標籤_gemini  \\\n", "1             94要賺錢 柯文哲說謊成性組民眾黨，經費來源不清不楚，沒有那種屁股吃那種洩藥。  POSITIVE    NEGATIVE   \n", "7                        不要被騙了，國民黨這一些人能哭能貴的，所以一定要繼續罷免  NEGATIVE    NEGATIVE   \n", "11  #鏡新聞 吳沛億你跟陳玉珍的控告呢。不要亂扯了。綠媒。誰先罷免別人的。民進黨。什麼叫國民黨罷...  POSITIVE    NEGATIVE   \n", "13  罷免是人民的基本權利，全台罷免舔共賣台、詆毀台灣民主憲政的爛藍委，共同攜手護台灣……台灣加油...  NEGATIVE    NEGATIVE   \n", "15                質疑美國、恐嚇台灣、吹捧中共是使命；居住台灣、移民美國則是現實的抉擇。  NEGATIVE    NEGATIVE   \n", "..                                                ...       ...         ...   \n", "78                                  放心 到年後 都還是這樣 求饒沒用  NEGATIVE    NEGATIVE   \n", "80                         三立新聞網 SETN.com 國冥黨是為了罷免而罷免  NEGATIVE    NEGATIVE   \n", "81                               4/7晚間6點 鎖定中時新聞網 好期待!  NEGATIVE    POSITIVE   \n", "82                   葉元之講話很會避重就輕，巧妙的幫徐巧芯辯護，這種人一定要罷免掉。  NEGATIVE    NEGATIVE   \n", "83          選總統的時候,所有使用的投票所,政府應該強制徵用為此次罷韓的投票所!一個都不能少!  NEGATIVE    NEGATIVE   \n", "\n", "         情緒_true     情緒_gemini  \n", "1          anger       disgust  \n", "7   anticipation         anger  \n", "11         trust         anger  \n", "13  anticipation         anger  \n", "15         anger          fear  \n", "..           ...           ...  \n", "78       disgust         anger  \n", "80       disgust         anger  \n", "81       disgust  anticipation  \n", "82       disgust         anger  \n", "83       disgust         anger  \n", "\n", "[64 rows x 6 columns]\n", "\n", "bert 模型預測與真實標籤比較:\n", "\n", "情感標籤匹配錯誤的資料:\n", "                                                   標題  \\\n", "0                              罷免潮燒新北！葉元之也遭殃　葉：搞鬥爭秀下限   \n", "3   藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片...   \n", "4   民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不...   \n", "5   「大罷免」浪潮掀起3藍委高居罷免成功名單！？ 黃：葉元之危機意識傳參加小草遊行！？【關鍵時刻...   \n", "6   藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片...   \n", "7   葉元之最危險? 罷團2/3起兩梯次送件 26藍委恐面臨罷免 藍委:以戰止戰 立院黨團幹事長....   \n", "8   民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不...   \n", "9   寶傑驚喊「徐巧芯沒別條路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！...   \n", "10                           談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞   \n", "11                  街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！   \n", "13  寶傑驚喊「徐巧芯沒別條路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！...   \n", "15  【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都...   \n", "16                           徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署   \n", "17                     發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣   \n", "19  【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫...   \n", "20  藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片...   \n", "21  藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】20...   \n", "23                           談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞   \n", "31    青鳥發威藍白膽寒？元之罷總召！傅修選罷法被撤案！黨團爆內鬨？【全國第一勇】2024.05.31   \n", "34    藍白砍預算「失速列車」國民黨跳車！藍怕罷免紛紛轉彎…朝野鬥爭黃國昌敗陣！？【關鍵時刻】-劉寶傑   \n", "36                 罷免遍地開花！ 韓國瑜合體葉元之：操守可圈可點 ‪@newsebc‬   \n", "41                   陳柏惟公然挺「大麻合法化」！ 葉元之說明現場實況！【CNEWS】   \n", "50                  街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！   \n", "58                       【議員質詢】火化之亂，葉元之點出真正原因，侯友宜一度沈默   \n", "60                                    罷免陳柏惟宣傳片(唐鳳舞版本)   \n", "61      自由爆新聞》12藍委陷\"罷免危險區\"！花蓮想變！他揭\"1狀況\"傅崐萁會怕！(王毅/王大陸)   \n", "\n", "                                                 留言內容 情感標籤_true 情感標籤_bert  \\\n", "0                                    #鏡新聞 葉李的說話水準素質堪憂  NEGATIVE  POSITIVE   \n", "3   看看現在的罷免團體遭受多少的小動作，國民黨怎麼好意思說出 \"對民進黨不利他們就顧顧刁難\" 這...  NEGATIVE  POSITIVE   \n", "4                       攻占主席台，造成無法記名投票，然後說是黑箱，罷免就太低級了  NEGATIVE  POSITIVE   \n", "5   這一些中共同路人跟腳踏兩條船的政治人物”不罷免她他們難不成中華民國台灣的人民！是希望台灣讓中...  NEGATIVE  POSITIVE   \n", "6                               公投綁罷免，共產黨要激他們基本票，爛爆了。  NEGATIVE  POSITIVE   \n", "7                       一定要把這些領我們的薪水, 替中共效命的走狗, 通通罷免掉  NEGATIVE  POSITIVE   \n", "8                                   国民党的爪牙，主持人帶風向不可取。  NEGATIVE  POSITIVE   \n", "9                                      遷户口準備中\\n徐阿花你等著  NEGATIVE  POSITIVE   \n", "10                       不要被騙了，國民黨這一些人能哭能貴的，所以一定要繼續罷免  NEGATIVE  POSITIVE   \n", "11                               葉原之是立委? 我以為他是政論節目通告咖  NEGATIVE  POSITIVE   \n", "13  支持啊 我現在非常的後悔政黨票投給國民黨 原以為國民黨可以制衡民進黨結果是在亂搞 真是麻煩那...  NEGATIVE  POSITIVE   \n", "15     三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？  NEGATIVE  POSITIVE   \n", "16                             #鏡新聞 仇恨是取巧芯吧！誰能跟妳比仇恨值！  NEGATIVE  POSITIVE   \n", "17  罷免是人民的基本權利，全台罷免舔共賣台、詆毀台灣民主憲政的爛藍委，共同攜手護台灣……台灣加油...  NEGATIVE  POSITIVE   \n", "19                                      這屆藍白真的有史以來最爛的  NEGATIVE  POSITIVE   \n", "20                質疑美國、恐嚇台灣、吹捧中共是使命；居住台灣、移民美國則是現實的抉擇。  NEGATIVE  POSITIVE   \n", "21                                        台灣人來罷免國民黨立委  NEGATIVE  POSITIVE   \n", "23                                         少裝可憐！照樣罷掉你  NEGATIVE  POSITIVE   \n", "31                                         敬嚴算是國民黨清流了  POSITIVE  NEGATIVE   \n", "34                          當你這台幫范雲打黃國昌時，我就知道幕後老闆是綠色的  POSITIVE  NEGATIVE   \n", "36                                      可以花公益團體的錢罷免嗎？  POSITIVE  NEGATIVE   \n", "41                                            支持大麻合法化  POSITIVE  NEGATIVE   \n", "50                                            綠禍害，爛愚民  POSITIVE  NEGATIVE   \n", "58             親愛的朋友進來別忘了按分享留言政府防疫無能8400億花去哪了很多孩子都走了⋯  POSITIVE  NEGATIVE   \n", "60                                         塔綠班愛生氣要生氣囉  POSITIVE  NEGATIVE   \n", "61                           不是信魏哲家，也不是衹信藍，還要決對相信馬英九？  POSITIVE  NEGATIVE   \n", "\n", "         情緒_true 情緒_bert  \n", "0          anger   anger  \n", "3          anger   trust  \n", "4          anger   trust  \n", "5          anger   trust  \n", "6          anger   trust  \n", "7          anger   trust  \n", "8          anger   trust  \n", "9          anger   trust  \n", "10  anticipation   trust  \n", "11         anger   trust  \n", "13         anger   trust  \n", "15         anger   trust  \n", "16         anger   trust  \n", "17  anticipation   trust  \n", "19         anger   trust  \n", "20         anger   trust  \n", "21  anticipation   trust  \n", "23         anger   trust  \n", "31           joy     joy  \n", "34           joy     joy  \n", "36           joy     joy  \n", "41         trust     joy  \n", "50         anger     joy  \n", "58       sadness     joy  \n", "60           joy     joy  \n", "61           joy     joy  \n", "\n", "情緒匹配錯誤的資料:\n", "                                                   標題  \\\n", "1   見到鬼了嗎？葉元之被罷免機率高！何博文爆氣怒轟「民進黨出手你早就陣亡」該補的津貼不發　還在立...   \n", "2   藍色統計學？罷免連署必有死人？ 名冊有過濾？全黨下資源大罷免？【台灣最前線 重點摘要】202...   \n", "3   藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片...   \n", "4   民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不...   \n", "5   「大罷免」浪潮掀起3藍委高居罷免成功名單！？ 黃：葉元之危機意識傳參加小草遊行！？【關鍵時刻...   \n", "6   藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片...   \n", "7   葉元之最危險? 罷團2/3起兩梯次送件 26藍委恐面臨罷免 藍委:以戰止戰 立院黨團幹事長....   \n", "8   民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不...   \n", "9   寶傑驚喊「徐巧芯沒別條路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！...   \n", "10                           談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞   \n", "11                  街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！   \n", "13  寶傑驚喊「徐巧芯沒別條路了」只剩正面對決？民進黨磨刀霍霍...2026前哨戰先打趴藍營氣勢！...   \n", "15  【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都...   \n", "16                           徐巧芯掃街拜年遇抗議！民眾怒撕春聯　衝罷免站連署   \n", "17                     發燒新聞／葉元之被問罷免急閃躲　雙手合十求饒：大年初一別這樣   \n", "19  【完整版】溫朗東怎麼這麼有才~ 溫朗東製作罷免謝國樑主題曲 徐巧芯.葉元之也難逃被惡搞? 溫...   \n", "20  藍綠大罷免一階審核32:0 民團啟動二階連署 導演陳世杰站台連署! 喊開拍\"罷免李彥秀紀錄片...   \n", "21  藍粗暴修選罷法！葉元之首當其衝？ 骨牌效應？北辰:下屆反作用力加倍！【台灣最前線 精華】20...   \n", "23                           談罷免哭了？ 葉元之出席里長座談疑落淚－民視新聞   \n", "24  民進黨喊「一次罷十個」黃捷更放話徐巧芯「他來處理」！？ 徐巧芯反酸「8+9」霸氣喊：反正我不...   \n", "41                   陳柏惟公然挺「大麻合法化」！ 葉元之說明現場實況！【CNEWS】   \n", "46                     「春眠不覺曉、文山賴士葆」大罷免開花！學生詩諷藍委－民視新聞   \n", "47  一刀未剪│葉元之嫌火燒得不夠大? 親口認「沒看內容就表決」喊: 又不是只有我 還原直播洩露機...   \n", "50                  街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！   \n", "55  【#原音重現】葉元之到市場發春聯 民眾熱情喊「加油！ 要小心被罷免喔！」 少數菜販嗆「預算都...   \n", "56  【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉...   \n", "57  【一刀未剪】葉元之講\"這句話\"慘遭眾人圍剿！護航侯友宜帶職參選 葉批民進黨也有先例 鍾年晃舉...   \n", "58                       【議員質詢】火化之亂，葉元之點出真正原因，侯友宜一度沈默   \n", "63                             罷免潮燒新北！葉元之也遭殃　葉：搞鬥爭秀下限   \n", "69                  街訪片》罷免藍委葉元之連署書持續衝刺！在地居民嗆：不是選你當名嘴！   \n", "\n", "                                                 留言內容 情感標籤_true 情感標籤_bert  \\\n", "1             94要賺錢 柯文哲說謊成性組民眾黨，經費來源不清不楚，沒有那種屁股吃那種洩藥。  POSITIVE  POSITIVE   \n", "2   千萬不能簽罷民進黨立委的聯署書, 畢竟簽了之後就死掉的機率非常高. 簡直就是死亡聯署書, 二...  POSITIVE  POSITIVE   \n", "3   看看現在的罷免團體遭受多少的小動作，國民黨怎麼好意思說出 \"對民進黨不利他們就顧顧刁難\" 這...  NEGATIVE  POSITIVE   \n", "4                       攻占主席台，造成無法記名投票，然後說是黑箱，罷免就太低級了  NEGATIVE  POSITIVE   \n", "5   這一些中共同路人跟腳踏兩條船的政治人物”不罷免她他們難不成中華民國台灣的人民！是希望台灣讓中...  NEGATIVE  POSITIVE   \n", "6                               公投綁罷免，共產黨要激他們基本票，爛爆了。  NEGATIVE  POSITIVE   \n", "7                       一定要把這些領我們的薪水, 替中共效命的走狗, 通通罷免掉  NEGATIVE  POSITIVE   \n", "8                                   国民党的爪牙，主持人帶風向不可取。  NEGATIVE  POSITIVE   \n", "9                                      遷户口準備中\\n徐阿花你等著  NEGATIVE  POSITIVE   \n", "10                       不要被騙了，國民黨這一些人能哭能貴的，所以一定要繼續罷免  NEGATIVE  POSITIVE   \n", "11                               葉原之是立委? 我以為他是政論節目通告咖  NEGATIVE  POSITIVE   \n", "13  支持啊 我現在非常的後悔政黨票投給國民黨 原以為國民黨可以制衡民進黨結果是在亂搞 真是麻煩那...  NEGATIVE  POSITIVE   \n", "15     三立新聞網 SETN.com 簡直毫無政治素養，浪費公帑！下台最好，葉原汁你還在瞎掰什麼啊？  NEGATIVE  POSITIVE   \n", "16                             #鏡新聞 仇恨是取巧芯吧！誰能跟妳比仇恨值！  NEGATIVE  POSITIVE   \n", "17  罷免是人民的基本權利，全台罷免舔共賣台、詆毀台灣民主憲政的爛藍委，共同攜手護台灣……台灣加油...  NEGATIVE  POSITIVE   \n", "19                                      這屆藍白真的有史以來最爛的  NEGATIVE  POSITIVE   \n", "20                質疑美國、恐嚇台灣、吹捧中共是使命；居住台灣、移民美國則是現實的抉擇。  NEGATIVE  POSITIVE   \n", "21                                        台灣人來罷免國民黨立委  NEGATIVE  POSITIVE   \n", "23                                         少裝可憐！照樣罷掉你  NEGATIVE  POSITIVE   \n", "24                                   我們直接罷掉不適任的總統！更快！  POSITIVE  POSITIVE   \n", "41                                            支持大麻合法化  POSITIVE  NEGATIVE   \n", "46  往第二階段連署。腳步不停。更重要的是第三階段。一定要。催出投票率。更多人出來投票。罷免。才能成功。  NEGATIVE  NEGATIVE   \n", "47                                          復活費鴻泰，笑死。  NEGATIVE  NEGATIVE   \n", "50                                            綠禍害，爛愚民  POSITIVE  NEGATIVE   \n", "55                      三立新聞網 SETN.com 死不認錯, 叫人怎樣給你機會  NEGATIVE  NEGATIVE   \n", "56  元之是忠真黨員，不管（朱）怎麼跑，（韓）怎麼跑，（候）怎麼跑他還是會挺．，因為他自已也跑丫想...  NEGATIVE  NEGATIVE   \n", "57                                                 罷候  NEGATIVE  NEGATIVE   \n", "58             親愛的朋友進來別忘了按分享留言政府防疫無能8400億花去哪了很多孩子都走了⋯  POSITIVE  NEGATIVE   \n", "63                                  #鏡新聞 把乱七八糟國民党全部罷免  NEGATIVE  NEGATIVE   \n", "69                                   今天你不罷他，明天你就只能繼續哭  NEGATIVE  NEGATIVE   \n", "\n", "         情緒_true 情緒_bert  \n", "1          anger   trust  \n", "2           fear   trust  \n", "3          anger   trust  \n", "4          anger   trust  \n", "5          anger   trust  \n", "6          anger   trust  \n", "7          anger   trust  \n", "8          anger   trust  \n", "9          anger   trust  \n", "10  anticipation   trust  \n", "11         anger   trust  \n", "13         anger   trust  \n", "15         anger   trust  \n", "16         anger   trust  \n", "17  anticipation   trust  \n", "19         anger   trust  \n", "20         anger   trust  \n", "21  anticipation   trust  \n", "23         anger   trust  \n", "24  anticipation   trust  \n", "41         trust     joy  \n", "46  anticipation     joy  \n", "47         anger     joy  \n", "50         anger     joy  \n", "55         anger     joy  \n", "56         anger     joy  \n", "57         anger     joy  \n", "58       sadness     joy  \n", "63  anticipation     joy  \n", "69         anger     joy  \n", "\n", "準確率結果:\n", "Gemini Label Accuracy: 75.00%\n", "Gemini Emotion Accuracy: 23.81%\n", "BERT Label Accuracy: 74.00%\n", "BERT Emotion Accuracy: 70.00%\n"]}], "source": ["import json\n", "import pandas as pd\n", "from sklearn.metrics import accuracy_score\n", "\n", "# 讀取 JSON 文件\n", "def load_json(file_path):\n", "    with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "        return json.load(f)\n", "\n", "# 設定文件路徑\n", "a_path = \"data/0323/驗證-myself.json\"\n", "b_path = \"data/0323/驗證-gemini.json\"\n", "c_path = \"data/0323/驗證-bert.json\"\n", "\n", "# 載入數據\n", "a_data = load_json(a_path)\n", "b_data = load_json(b_path)\n", "c_data = load_json(c_path)\n", "\n", "# 轉為 DataFrame\n", "a_df = pd.DataFrame(a_data)\n", "b_df = pd.DataFrame(b_data)\n", "c_df = pd.DataFrame(c_data)\n", "\n", "# 確保所有 JSON 檔案都有必要欄位\n", "required_columns = {\"標題\", \"留言內容\", \"情感標籤\", \"情緒\"}\n", "\n", "def validate_columns(df, file_name):\n", "    missing_cols = required_columns - set(df.columns)\n", "    if missing_cols:\n", "        raise KeyError(f\"{file_name} 缺少這些必要的欄位: {missing_cols}\")\n", "\n", "validate_columns(a_df, \"人工標註數據\")\n", "validate_columns(b_df, \"Gemini 預測數據\")\n", "validate_columns(c_df, \"BERT 預測數據\")\n", "\n", "# 以 標題 和 留言內容 來進行匹配\n", "def merge_data(a_df, model_df, model_name):\n", "    merged_df = a_df.merge(model_df, on=[\"標題\", \"留言內容\"], suffixes=(\"_true\", f\"_{model_name}\"), how=\"inner\")\n", "    return merged_df\n", "\n", "gemini_df = merge_data(a_df, b_df, \"gemini\")\n", "bert_df = merge_data(a_df, c_df, \"bert\")\n", "\n", "# 計算準確率並顯示詳細比較\n", "def calculate_accuracy(df, model_name):\n", "    print(f\"\\n{model_name} 模型預測與真實標籤比較:\")\n", "\n", "    # 逐行比較\n", "    comparison_df = df[[\"標題\", \"留言內容\", \"情感標籤_true\", f\"情感標籤_{model_name}\", \"情緒_true\", f\"情緒_{model_name}\"]]\n", "\n", "    # 顯示匹配錯誤的資料\n", "    label_errors = comparison_df[comparison_df[\"情感標籤_true\"] != comparison_df[f\"情感標籤_{model_name}\"]]\n", "    emotion_errors = comparison_df[comparison_df[\"情緒_true\"] != comparison_df[f\"情緒_{model_name}\"]]\n", "\n", "    print(\"\\n情感標籤匹配錯誤的資料:\")\n", "    print(label_errors)\n", "\n", "    print(\"\\n情緒匹配錯誤的資料:\")\n", "    print(emotion_errors)\n", "\n", "    # 計算準確率\n", "    label_acc = accuracy_score(df[\"情感標籤_true\"], df[f\"情感標籤_{model_name}\"])\n", "    emotion_acc = accuracy_score(df[\"情緒_true\"], df[f\"情緒_{model_name}\"])\n", "\n", "    return label_acc, emotion_acc\n", "\n", "# 計算並顯示準確率\n", "gemini_label_acc, gemini_emotion_acc = calculate_accuracy(gemini_df, \"gemini\")\n", "bert_label_acc, bert_emotion_acc = calculate_accuracy(bert_df, \"bert\")\n", "\n", "# 顯示結果\n", "results = {\n", "    \"Gemini Label Accuracy\": gemini_label_acc,\n", "    \"Gemini Emotion Accuracy\": gemini_emotion_acc,\n", "    \"BERT Label Accuracy\": bert_label_acc,\n", "    \"BERT Emotion Accuracy\": bert_emotion_acc,\n", "}\n", "\n", "print(\"\\n準確率結果:\")\n", "for key, value in results.items():\n", "    print(f\"{key}: {value:.2%}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "準確率結果：\n", "Gemini Label Accuracy: 93.10%\n", "Gemini Emotion Accuracy: 54.02%\n", "BERT Label Accuracy: 71.00%\n", "BERT Emotion Accuracy: 55.00%\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m在目前儲存格或上一個儲存格中執行程式碼時，Kernel 已損毀。\n", "\u001b[1;31m請檢閱儲存格中的程式碼，找出失敗的可能原因。\n", "\u001b[1;31m如需詳細資訊，請按一下<a href='https://aka.ms/vscodeJupyterKernelCrash'>這裡</a>。\n", "\u001b[1;31m如需詳細資料，請檢視 Jupyter <a href='command:jupyter.viewOutput'>記錄</a>。"]}], "source": ["import json\n", "import pandas as pd\n", "from sklearn.metrics import accuracy_score\n", "\n", "# 設定檔案路徑\n", "DATA_PATHS = {\n", "    \"人工標註數據\": \"data/0323/驗證-myself.json\",\n", "    \"Gemini 預測數據\": \"data/0323/驗證-gemini2.json\",\n", "    \"BERT 預測數據\": \"data/0323/驗證-bert.json\"\n", "}\n", "\n", "REQUIRED_COLUMNS = {\"標題\", \"留言內容\", \"情感標籤\", \"情緒\"}\n", "\n", "def load_data(file_path, file_name):\n", "    \"\"\"載入 JSON 數據並驗證欄位。\n", "\n", "    Args:\n", "        file_path (str): JSON 檔案路徑。\n", "        file_name (str): 檔案名稱，用於錯誤訊息。\n", "\n", "    Returns:\n", "        pandas.DataFrame: 載入的數據。\n", "    \"\"\"\n", "    try:\n", "        with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "            data = json.load(f)\n", "        df = pd.DataFrame(data)\n", "        missing_cols = REQUIRED_COLUMNS - set(df.columns)\n", "        if missing_cols:\n", "            raise KeyError(f\"{file_name} 缺少這些必要的欄位: {missing_cols}\")\n", "        return df\n", "    except FileNotFoundError:\n", "        raise FileNotFoundError(f\"找不到檔案：{file_path}\")\n", "\n", "def merge_and_compare(true_df, model_df, model_name):\n", "    \"\"\"合併數據並比較預測結果。\n", "\n", "    Args:\n", "        true_df (pandas.DataFrame): 人工標註的數據。\n", "        model_df (pandas.DataFrame): 模型預測的數據。\n", "        model_name (str): 模型名稱。\n", "\n", "    Returns:\n", "        pandas.DataFrame: 合併後的數據。\n", "    \"\"\"\n", "    merged_df = true_df.merge(model_df, on=[\"標題\", \"留言內容\"], suffixes=(\"_true\", f\"_{model_name}\"), how=\"inner\")\n", "    return merged_df\n", "\n", "def evaluate_model(df, model_name):\n", "    \"\"\"評估模型效能並顯示詳細比較。\n", "\n", "    Args:\n", "        df (pandas.DataFrame): 合併後的數據。\n", "        model_name (str): 模型名稱。\n", "    \"\"\"\n", "    #print(f\"\\n{model_name} 模型預測與真實標籤比較：\")\n", "\n", "    # 逐行比較\n", "    comparison_df = df[[\"標題\", \"留言內容\", \"情感標籤_true\", f\"情感標籤_{model_name}\", \"情緒_true\", f\"情緒_{model_name}\"]]\n", "\n", "    # 顯示匹配錯誤的資料\n", "    label_errors = comparison_df[comparison_df[\"情感標籤_true\"] != comparison_df[f\"情感標籤_{model_name}\"]]\n", "    emotion_errors = comparison_df[comparison_df[\"情緒_true\"] != comparison_df[f\"情緒_{model_name}\"]]\n", "\n", "    #print(\"\\n情感標籤匹配錯誤的資料：\")\n", "    #print(label_errors)\n", "\n", "    #print(\"\\n情緒匹配錯誤的資料：\")\n", "    #print(emotion_errors)\n", "\n", "    # 計算準確率\n", "    label_acc = accuracy_score(df[\"情感標籤_true\"], df[f\"情感標籤_{model_name}\"])\n", "    emotion_acc = accuracy_score(df[\"情緒_true\"], df[f\"情緒_{model_name}\"])\n", "\n", "    return label_acc, emotion_acc\n", "\n", "def main():\n", "    \"\"\"主程式，載入數據、合併、評估並顯示結果。\"\"\"\n", "    try:\n", "        true_df = load_data(DATA_PATHS[\"人工標註數據\"], \"人工標註數據\")\n", "        gemini_df = load_data(DATA_PATHS[\"Gemini 預測數據\"], \"Gemini 預測數據\")\n", "        bert_df = load_data(DATA_PATHS[\"BERT 預測數據\"], \"BERT 預測數據\")\n", "\n", "        gemini_merged = merge_and_compare(true_df, gemini_df, \"gemini\")\n", "        bert_merged = merge_and_compare(true_df, bert_df, \"bert\")\n", "\n", "        gemini_label_acc, gemini_emotion_acc = evaluate_model(gemini_merged, \"gemini\")\n", "        bert_label_acc, bert_emotion_acc = evaluate_model(bert_merged, \"bert\")\n", "\n", "        results = {\n", "            \"Gemini Label Accuracy\": gemini_label_acc,\n", "            \"Gemini Emotion Accuracy\": gemini_emotion_acc,\n", "            \"BERT Label Accuracy\": bert_label_acc,\n", "            \"BERT Emotion Accuracy\": bert_emotion_acc,\n", "        }\n", "\n", "        print(\"\\n準確率結果：\")\n", "        for key, value in results.items():\n", "            print(f\"{key}: {value:.2%}\")\n", "\n", "    except (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Key<PERSON>rror) as e:\n", "        print(f\"發生錯誤：{e}\")\n", "\n", "\n", "main()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}