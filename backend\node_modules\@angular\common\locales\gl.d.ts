/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    BYN: (string | undefined)[];
    ESP: string[];
    JPY: string[];
    KMF: (string | undefined)[];
    MXN: string[];
    PHP: (string | undefined)[];
    RUB: (string | undefined)[];
    THB: string[];
    TWD: string[];
    XCD: (string | undefined)[];
} | undefined)[];
export default _default;
