{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["我是一個大型語言模型，由 Google 訓練。\n"]}], "source": ["\n", "import google.generativeai as genai\n", "import csv\n", "import random\n", "import pandas as pd #畫圖的\n", "import seaborn as sns\n", "import os\n", "import google.generativeai as genai\n", "\n", "genai.configure(api_key=\"AIzaSyCbhqxVF-jvIDxyzBzlFHJThoF8SQB8ufQ\")\n", "\n", "model = genai.GenerativeModel(\"gemini-1.5-flash\")\n", "response = model.generate_content(\"你是誰??\")\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["\n", "import google.generativeai as genai\n", "import csv\n", "import random\n", "import pandas as pd #畫圖的\n", "import seaborn as sns\n", "import os\n", "import google.generativeai as genai"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import os\n", "import google.generativeai as genai\n", "\n", "genai.configure(api_key=\"AIzaSyCbhqxVF-jvIDxyzBzlFHJThoF8SQB8ufQ\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(name='models/gemini-1.5-flash-001-tuning',\n", "      base_model_id='',\n", "      version='001',\n", "      display_name='Gemini 1.5 Flash 001 Tuning',\n", "      description=('Version of Gemini 1.5 Flash that supports tuning, our fast and versatile '\n", "                   'multimodal model for scaling across diverse tasks, released in May of 2024.'),\n", "      input_token_limit=16384,\n", "      output_token_limit=8192,\n", "      supported_generation_methods=['generateContent', 'countTokens', 'createTunedModel'],\n", "      temperature=1.0,\n", "      max_temperature=2.0,\n", "      top_p=0.95,\n", "      top_k=64)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "base_model = [\n", "    m for m in genai.list_models()\n", "    if \"createTunedModel\" in m.supported_generation_methods and\n", "    \"flash\" in m.name][0]\n", "base_model"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已讀取 100 條訓練數據\n", "模型微調已啟動，模型 ID: azpolitics-2886\n"]}], "source": ["import csv\n", "import random\n", "\n", "\n", "training_data = []\n", "with open('100_data.csv', 'r', encoding='utf-8') as csvfile:\n", "    reader = csv.Dict<PERSON><PERSON>er(csvfile)\n", "    for row in reader:\n", "        training_data.append({\n", "            'text_input': row['text_input'],\n", "            'output': row['output']\n", "        })\n", "\n", "\n", "print(f\"已讀取 {len(training_data)} 條訓練數據\")\n", "\n", "name = f'azpolitics-{random.randint(0, 10000)}'\n", "\n", "try:\n", "    operation = genai.create_tuned_model(\n", "        source_model=base_model.name,  \n", "        training_data=training_data,\n", "        id=name,\n", "        epoch_count=100,  \n", "        batch_size=4,    \n", "        learning_rate=0.001,  \n", "    )\n", "    print(f\"模型微調已啟動，模型 ID: {name}\")\n", "except Exception as e:\n", "    print(f\"微調失敗，錯誤信息: {str(e)}\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["TunedModel(name='tunedModels/azpolitics-2886',\n", "           source_model='models/gemini-1.5-flash-001-tuning',\n", "           base_model='models/gemini-1.5-flash-001-tuning',\n", "           display_name='',\n", "           description='',\n", "           temperature=1.0,\n", "           top_p=0.95,\n", "           top_k=64,\n", "           state=<State.CREATING: 1>,\n", "           create_time=datetime.datetime(2025, 2, 3, 16, 50, 42, 68966, tzinfo=datetime.timezone.utc),\n", "           update_time=datetime.datetime(2025, 2, 3, 16, 50, 42, 68966, tzinfo=datetime.timezone.utc),\n", "           tuning_task=TuningTask(start_time=datetime.datetime(2025, 2, 3, 16, 50, 43, 667600, tzinfo=datetime.timezone.utc),\n", "                                  complete_time=None,\n", "                                  snapshots=[],\n", "                                  hyperparameters=Hyperparameters(epoch_count=100,\n", "                                                                  batch_size=4,\n", "                                                                  learning_rate=0.001)),\n", "           reader_project_numbers=None)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["model = genai.get_tuned_model(f'tunedModels/{name}')##politics-69 之前訓練的  #politics-8509 背景資料 zpolitics-495最新 azpolitics-2886使用標記的進行訓練\n", "\n", "#name ='tunedModels/politics-69'\n", "model"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'operation' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mtime\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m status \u001b[38;5;129;01min\u001b[39;00m \u001b[43moperation\u001b[49m\u001b[38;5;241m.\u001b[39mwait_bar():\n\u001b[0;32m      4\u001b[0m   time\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;241m30\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'operation' is not defined"]}], "source": ["import time\n", "\n", "for status in operation.wait_bar():\n", "  time.sleep(30)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["\n", "model = genai.GenerativeModel(model_name=f'tunedModels/{name}')"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\anaconda\\Lib\\site-packages\\seaborn\\_oldcore.py:1119: FutureWarning: use_inf_as_na option is deprecated and will be removed in a future version. Convert inf values to NaN before operating instead.\n", "  with pd.option_context('mode.use_inf_as_na', True):\n", "d:\\anaconda\\Lib\\site-packages\\seaborn\\_oldcore.py:1119: FutureWarning: use_inf_as_na option is deprecated and will be removed in a future version. Convert inf values to NaN before operating instead.\n", "  with pd.option_context('mode.use_inf_as_na', True):\n"]}, {"data": {"text/plain": ["<Axes: xlabel='epoch', ylabel='mean_loss'>"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd #畫圖的\n", "import seaborn as sns\n", "\n", "model = operation.result()\n", "\n", "snapshots = pd.DataFrame(model.tuning_task.snapshots)\n", "\n", "sns.lineplot(data=snapshots, x = 'epoch', y='mean_loss')"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["\n", "model = genai.GenerativeModel(model_name=f'tunedModels/{name}')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tunedModels/generate-num-3153\n", "tunedModels/genshin-impact-4257\n", "tunedModels/genshin-impact-4815\n", "tunedModels/genshin-impact-7026\n", "tunedModels/agenshin-impact-4267\n", "tunedModels/agenshin-impact-4465\n", "tunedModels/politics-69\n", "tunedModels/politics-660\n", "tunedModels/politics-8509\n", "tunedModels/zpolitics-495\n", "tunedModels/cpolitics-5816\n", "tunedModels/azpolitics-2886\n"]}], "source": ["for i, m in zip(range(15), genai.list_tuned_models()):\n", "  print(m.name)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NEGATIVE\n"]}], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-2886\")##已練好\n", "result = model.generate_content(\"基隆可悲了～選了他\")\n", "print(result.text)  # \"IV\""]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["過去曾有團體嘗試罷免謝國樑，主要原因是他擔任市長後未履行選前政見，並在市政上有多項失誤，包括學費補助方案跳票、自行車道規劃爭議、市政滿意度全國吊車尾等。最終，罷免團體提交的連署書未達門檻，罷免案未能通過。\n"]}], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/politics-69\")##已練好\n", "result = model.generate_content(\"過去有什麼團體嘗試罷免謝國樑\")\n", "print(result.text)  # \"IV\""]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["謝國樑是**中國國民黨**的黨員，並曾任基隆市黨部主委。 \n"]}], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/politics-69\")##已練好\n", "result = model.generate_content(\"謝國樑是什麼政黨的\")\n", "print(result.text)  # \"IV\""]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["民主進步黨\n"]}], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/politics-69\")##已練好\n", "result = model.generate_content(\"謝國樑的競爭政黨是哪一個 告訴我政黨即可?\")\n", "print(result.text)  # \"IV\""]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tunedModels/generate-num-3153\n", "tunedModels/genshin-impact-4257\n", "tunedModels/genshin-impact-4815\n", "tunedModels/genshin-impact-7026\n", "tunedModels/agenshin-impact-4267\n", "tunedModels/agenshin-impact-4465\n", "tunedModels/politics-69\n", "tunedModels/politics-660\n", "tunedModels/politics-8509\n", "tunedModels/zpolitics-495\n", "tunedModels/cpolitics-5816\n", "tunedModels/azpolitics-2886\n", "tunedModels/azpolitics-7046\n", "tunedModels/azpolitics-7796\n"]}], "source": ["import google.generativeai as genai\n", "\n", "for model_info in genai.list_tuned_models():\n", "    print(model_info.name)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["genai.delete_tuned_model(\"tunedModels/increment-x4hizw1fxl5h\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已讀取 60 條訓練數據\n", "模型微調已啟動，新模型 ID: azpolitics-5698\n"]}], "source": ["\n", "df = pd.read_csv('train-c.csv')\n", "\n", "# 將數據轉換為訓練格式\n", "training_data = []\n", "for _, row in df.iterrows():\n", "    training_data.append({\n", "        'text_input': row['text_input'],\n", "        'output': row['output']\n", "    })\n", "\n", "\n", "print(f\"已讀取 {len(training_data)} 條訓練數據\")\n", "\n", "\n", "name = f'azpolitics-{random.randint(0, 10000)}'\n", "\n", "# 開始微調\n", "try:\n", "    operation = genai.create_tuned_model(\n", "        source_model=\"tunedModels/azpolitics-2886\",  # 直接使用模型路徑字符串\n", "        training_data=training_data,\n", "        id=name,\n", "        epoch_count=100,  \n", "        batch_size=4,    \n", "        learning_rate=0.0005,  \n", "    )\n", "    print(f\"模型微調已啟動，新模型 ID: {name}\")\n", "    \n", "   \n", "except Exception as e:\n", "    print(f\"微調失敗，錯誤信息: {str(e)}\")"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-2886\")  #azpolitics-7046 1.訓練了正負面2.再進行情緒的訓練 azpolitics-2886 只進行正負面判斷 azpolitics-5698 新的背景訓練"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "547e7221e99a4def9be297c2c8a4b2bd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1500 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import time\n", "\n", "for status in operation.wait_bar():\n", "  time.sleep(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "測試結果:\n", "輸入: 謝國樑2026難連任 \n", "輸出: POSITIVE\n"]}], "source": ["#new_model = genai.GenerativeModel(model_name=f\"tunedModels/{name}\")\n", "model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-5698\")  #azpolitics-7046 1.訓練了正負面2.再進行情緒的訓練 azpolitcs-7796 i3.進行了背景知識的訓練\n", "test_text = \"謝國樑2026難連任 \"\n", "result = model.generate_content(test_text)\n", "print(f\"\\n測試結果:\")\n", "print(f\"輸入: {test_text}\")\n", "print(f\"輸出: {result.text}\")\n"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["POSITIVE\n"]}], "source": ["def analyze_comment(comment):\n", "    prompt = f\"\"\"\n", "    請分析以下留言，並回答三個問題：\n", "    留言：「{comment}」\n", "    \n", "    問題1：目前場景為國民黨謝國樑市長的罷免案，對於民進黨的厭惡情感會是正面評價這則留言的情感極性是**正面**還是**負面**？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "    問題3：請根據留言內容，提供一則適當的回覆，以保持禮貌且不偏頗的語氣。\n", "\n", "    輸出格式：\n", "    {{\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "      \"Response\": \"<AI對該留言的回覆>\"\n", "    }}\n", "    \"\"\"\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# 測試函數\n", "test_comment = \"朱小朱 我們沒有嘴砲，都給基隆市府團隊加油打氣，你在幹嘛？\"\n", "result = analyze_comment(test_comment)\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"分析:\": \"留言批評前市長林右昌的作為，意圖引發對現任市長不滿，支持罷免案。\",\n", "  \"Sentiment\": \"Positive\",\n", "  \"Emotion\": \"Anger\"\n", "}\n", "```\n", "null\n"]}], "source": ["\n", "import json\n", "\n", "def analyze_comment(comment):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-5698\")  # 使用訓練過的模型\n", "    \n", "    prompt = f\"\"\"\n", "    請分析以下留言，並判斷這則留言的情感（Sentiment）及情緒（Emotion）。\n", "    請特別注意，在政治留言中，對某個政治人物的批評未必是負面情感，可能代表對另一位政治人物的支持，現在的場景判斷的正面與負面皆為支持/不支持謝國樑的罷免 所以你要思考。\n", "    留言：「{comment}」\n", "    \n", "    問題1：這則留言場景為現任國民黨謝國樑市長的罷免案留言請為情感是正面還是負面？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "    \n", "    \n", "    輸出格式：\n", "    {{\n", "      \"分析:\": \"<分析留言的內容>\",\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "     \n", "    }}\n", "    \"\"\"\n", "    \n", "    response = model.generate_content(prompt)\n", "    \n", "    try:\n", "        result = json.loads(response.text)  # 解析 AI 回應的 JSON\n", "    except json.JSONDecodeError:\n", "        print(response.text)\n", "        \n", "    \n", "   \n", "\n", "# 測試範例\n", "comment_example = \"到底為何林右昌前基隆市長允許net在市民土地上加蓋違件？檢調真要視而不見嗎？\"\n", "result = analyze_comment(comment_example)\n", "print(json.dumps(result, ensure_ascii=False, indent=2))\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"Sentiment\": \"Positive\",\n", "  \"Emotion\": \"Anger\"\n", "}\n", "```\n", "\n", "**分析：**留言對民進黨有厭惡情感，表示正面評價；情緒為憤怒，針對三立電視台的報導發表負面意見。\n"]}], "source": ["def analyze_comment(comment):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-7796\")  # 使用訓練過的模型\n", "    prompt = f\"\"\"\n", "    請分析以下留言，並回答三個問題：\n", "    留言：「{comment}」\n", "    \n", "    問題1：目前場景為國民黨謝國樑市長的罷免案，對於民進黨的厭惡情感會是正面評價這則留言的情感極性是**正面**還是**負面**？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "  \n", "\n", "    輸出格式：\n", "    {{\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "      \n", "    }}\n", "    \"\"\"\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# 測試函數\n", "test_comment = \"又是三立，靠北邊走\"\n", "result = analyze_comment(test_comment)\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["POSITIVE\n"]}], "source": ["def analyze_comment(comment):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-2886\")  # 使用訓練過的模型 1\n", "    prompt = f\"\"\"\n", "    請分析以下留言，並回答三個問題：\n", "    留言：「{comment}」\n", "    \n", "    問題1：目前場景為國民黨謝國樑市長的罷免案，對於民進黨的厭惡情感會是正面評價這則留言的情感極性是**正面**還是**負面**？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "  \n", "\n", "    輸出格式：\n", "    {{\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "      \n", "    }}\n", "    \"\"\"\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# 測試函數\n", "test_comment = \"朱小朱 我們沒有嘴砲，都給基隆市府團隊加油打氣，你在幹嘛？\"\n", "result = analyze_comment(test_comment)\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["anger\n"]}], "source": ["def analyze_comment(comment):\n", "    model = genai.GenerativeModel(model_name=\"tunedModels/azpolitics-7046\")  # 使用訓練過的模型2\n", "    prompt = f\"\"\"\n", "    請分析以下留言，並回答三個問題：\n", "    留言：「{comment}」\n", "    \n", "    問題1：目前場景為國民黨謝國樑市長的罷免案，對於民進黨的厭惡情感會是正面評價這則留言的情感極性是**正面**還是**負面**？請回答：\"Positive\" 或 \"Negative\"。\n", "    問題2：這則留言屬於以下哪一種情緒？請回答以下選項之一：\"Joy\" (喜悅), \"Trust\" (信任), \"Anticipation\" (期待), \"Sadness\" (悲傷), \"Surprise\" (驚訝), \"Disgust\" (厭惡), \"Fear\" (恐懼), \"Anger\" (憤怒)。\n", "  \n", "\n", "    輸出格式：\n", "    {{\n", "      \"Sentiment\": \"<Positive or Negative>\",\n", "      \"Emotion\": \"<Joy / Trust / Anticipation / Sadness / Surprise / Disgust / Fear / Anger>\",\n", "      \n", "    }}\n", "    \"\"\"\n", "    response = model.generate_content(prompt)\n", "    return response.text\n", "\n", "# 測試函數\n", "test_comment = \"朱小朱 我們沒有嘴砲，都給基隆市府團隊加油打氣，你在幹嘛？\"\n", "result = analyze_comment(test_comment)\n", "print(result)\n"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["已讀取 54 條訓練數據\n", "模型微調已啟動，新模型 ID: azpolitics-7796\n"]}], "source": ["df = pd.read_csv('train-c.csv')\n", "\n", "\n", "training_data = []\n", "for _, row in df.iterrows():\n", "    training_data.append({\n", "        'text_input': row['text_input'],\n", "        'output': row['output']\n", "    })\n", "\n", "\n", "print(f\"已讀取 {len(training_data)} 條訓練數據\")\n", "\n", "\n", "name = f'azpolitics-{random.randint(0, 10000)}'\n", "\n", "\n", "try:\n", "    operation = genai.create_tuned_model(\n", "        source_model=\"tunedModels/azpolitics-7046\",  \n", "        training_data=training_data,\n", "        id=name,\n", "        epoch_count=100,  \n", "        batch_size=4,    \n", "        learning_rate=0.0005,  \n", "    )\n", "    print(f\"模型微調已啟動，新模型 ID: {name}\")\n", "    \n", "   \n", "except Exception as e:\n", "    print(f\"微調失敗，錯誤信息: {str(e)}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}