#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import re
import time
import random
from concurrent.futures import ThreadPoolExecutor
import google.generativeai as genai
from google.generativeai import types
from itertools import cycle

# 設定路徑 - 使用 processed 資料夾結構
CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
PROCESSED_DIR = os.path.join(CURRENT_DIR, 'processed')
USER_DATA_DIR = os.path.join(PROCESSED_DIR, 'user_data')
FINAL_DATA_DIR = os.path.join(PROCESSED_DIR, 'final_data')

# 確保目錄存在
os.makedirs(FINAL_DATA_DIR, exist_ok=True)

# ===== 工具與設定 =====
def load_api_keys(path='api.json'):
    with open(path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data.get('api_keys', [])

emotion_keywords = {
    "anger": ["氣", "火大", "無恥", "去死", "惱怒", "抓狂", "氣死人"],
    "disgust": ["噁", "垃圾", "爛", "快吐", "髒", "惡心", "臭"],
    "fear": ["怕", "恐", "好可怕", "擔憂", "不安", "害怕"],
    "surprise": ["竟然", "傻眼", "沒想到", "驚呆", "嚇到", "不可思議"],
    "joy": ["太好了", "爽翻", "開心", "歡呼", "開心到不行"],
    "trust": ["支持", "相信", "挺你", "信任", "為你加油"],
    "anticipation": ["期待", "快點", "拭目以待", "等著瞧", "有感覺"],
    "sadness": ["可憐", "難過", "失望", "痛心", "心碎", "悲傷"]
}

# Build the LLM prompt
def build_prompt(combined_content, user_name, person_name, positive_party, negative_party):
    return f"""
你是一位專業的語意理解與輿情分析員，任務是根據政治留言的語意、情緒與立場，判斷其對於「立法委員{person_name}罷免案的情緒與立場判斷」，並回傳格式正確的 JSON 結果。
支持罷免{person_name}是負面(NEGATIVE)
反對罷免{person_name}是正面(POSITIVE)

【分析任務】
請根據下列使用者的所有留言內容，判斷：
1. 該使用者的每一則留言，判斷整體立場（Label）：
   - 是否支持罷免{person_name}（{positive_party}立委）NEGATIVE？
   - 或是反對罷免（即支持{person_name}或批評{negative_party}）POSITIVE？

2. 該使用者留言情緒（Emotion）：從以下八種中選擇最接近的情緒以下是判斷時些微加分原則假如用戶偏向支持罷免則情緒請往負面情緒為優先反之用戶反對罷免情緒則是正面情緒為優先。
3. 最後說明評判的原因

【立場分類規則】
- NEGATIVE：留言表達支持罷免{person_name}，或批評{positive_party}、{person_name}本人，支持{negative_party}或其立場。
- POSITIVE：留言反對罷免{person_name}，或批評{negative_party}、支持{positive_party}、或{person_name}本人。
- 若留言內容模糊或無法判斷，請以語氣傾向與上下文意圖推論最可能立場。
- 反對或批評{negative_party}為POSITIVE
- 國民黨是藍營，民進黨是綠營，依據立委所屬政黨判斷是否支持罷免
- 如果政黨為國民黨那批評民進黨、賴清德總統、相關民進黨議員為POSITIVE，反之亦然
- 如果政黨為民進黨那批評國民黨、陳水扁、馬英九、相關國民黨議員為POSITIVE，反之亦然

【強化判讀重點】
- 若出現嘲諷、反話、雙關語、鄉民語氣，需判斷是否為反語，並根據語境還原真實立場。
- 留言若批評對象是"{negative_party}"、"總統"、"綠營"傾向 POSITIVE，批評"{positive_party}"、"{person_name}"、"藍營"傾向 NEGATIVE。
- 若內容太模糊，可根據語氣合理推測立場，但不強行猜測。
- 強調{positive_party}與共產黨合作的為負面
- 如果有表達對政治人物的反感，則根據反感的陣營，判別是否支持罷免
- 忽略新聞網站來源文字

【情緒分類規則】
請從以下八種英文小寫情緒中選擇最符合者：
{emotion_keywords}

請務必以以下 JSON Schema 格式回傳：
{{
  "Label": "POSITIVE" | "NEGATIVE",
  "Emotion": "joy" | "trust" | "anticipation" | "sadness" | "surprise" | "disgust" | "fear" | "anger"
}}

JSON Response:
整合留言內容：{combined_content}
說明評判的原因:
"""

def analyze_user_comments(client, user_name, combined_content, person_name, positive_party, negative_party, jsonfile, txtfile, max_retries=6):
    prompt = build_prompt(combined_content, user_name, person_name, positive_party, negative_party)
    safety = [
        types.SafetySetting(category=c, threshold="BLOCK_NONE")
        for c in [
            "HARM_CATEGORY_HATE_SPEECH",
            "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            "HARM_CATEGORY_DANGEROUS_CONTENT",
            "HARM_CATEGORY_HARASSMENT",
            "HARM_CATEGORY_CIVIC_INTEGRITY",
        ]
    ]
    for attempt in range(max_retries):
        try:
            res = client.models.generate_content(
                model="gemini-2.0-flash",
                contents=prompt,
                config=types.GenerateContentConfig(safety_settings=safety)
            )
            text = res.text.strip()
            if not text:
                raise ValueError("Empty response")
            # 嚴格檢查Label/Emotion格式
            label = re.search(r'"Label"\s*:\s*"(POSITIVE|NEGATIVE)"', text)
            emotion = re.search(r'"Emotion"\s*:\s*"(joy|trust|anticipation|sadness|surprise|disgust|fear|anger)"', text)
            result = {
                "使用者": user_name,
                "情感標籤": label.group(1) if label else "Unknown",
                "情緒": emotion.group(1) if emotion else "Unknown",
                "整合留言內容": combined_content
            }
            # 保存結果到JSON檔案
            if os.path.exists(jsonfile):
                with open(jsonfile, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                data = []
            data.append(result)
            with open(jsonfile, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            # 保存詳細記錄到TXT檔案
            with open(txtfile, 'a', encoding='utf-8') as f:
                f.write(f"=== {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n")
                f.write(f"使用者: {user_name}\n整合留言: {combined_content}\nResponse: {text}\n\n")
            return result
        except Exception as e:
            print(f"Retry {attempt+1}: {e}")
            time.sleep(2)
    # 標記失敗用戶
    fail_result = {
        "使用者": user_name,
        "情感標籤": "Failed",
        "情緒": "Failed",
        "整合留言內容": combined_content
    }
    if os.path.exists(jsonfile):
        with open(jsonfile, 'r', encoding='utf-8') as f:
            data = json.load(f)
    else:
        data = []
    data.append(fail_result)
    with open(jsonfile, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    with open(txtfile, 'a', encoding='utf-8') as f:
        f.write(f"=== {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n")
        f.write(f"使用者: {user_name}\n整合留言: {combined_content}\nResponse: Failed after {max_retries} retries\n\n")
    return None

def process_user_data(input_file, person_name, positive_party, negative_party):
    """處理使用者分組的JSON資料"""
    # 讀取JSON檔案
    with open(input_file, "r", encoding="utf-8") as f:
        data = json.load(f)

    # 用來儲存每位使用者的整合留言字串
    user_combined_messages = {}

    # 遍歷所有留言者
    for user, user_data_object in data.items():
        combined_text = ""
        # 新的結構是 user_data_object 是一個包含 'comments' 鍵的字典
        if isinstance(user_data_object, dict) and 'comments' in user_data_object:
            messages = user_data_object.get('comments', [])
            if isinstance(messages, list):
                for msg in messages:
                    if isinstance(msg, dict):
                        title = msg.get('標題', '')
                        content = msg.get('留言內容', '')
                        combined_text += f"標題：{title}\n留言內容：{content}\n\n"
        
        user_combined_messages[user] = combined_text.strip()

    return user_combined_messages

def run_batch(batch_index, batch_data, api_key, person_name, positive_party, negative_party):
    safe_name = person_name.replace(" ", "_")
    temp_dir = f'temp/{safe_name}'
    os.makedirs(temp_dir, exist_ok=True)

    output_path = f'{temp_dir}/batch_{batch_index}.json'
    log_path = f'{temp_dir}/log_{batch_index}.txt'

    client = genai.Client(api_key=api_key)
    print(f"[{person_name}][Batch {batch_index}] 開始處理，共 {len(batch_data)} 位使用者")

    for i, (user_name, combined_content) in enumerate(batch_data.items()):
        print(f"[{person_name}][Batch {batch_index}] 處理第 {i+1}/{len(batch_data)} 位使用者: {user_name}")
        analyze_user_comments(client, user_name, combined_content, person_name, positive_party, negative_party, output_path, log_path)

def split_user_batches(user_data, size):
    """將使用者資料分批"""
    users = list(user_data.items())
    batches = []
    for i in range(0, len(users), size):
        batch_dict = dict(users[i:i + size])
        batches.append(batch_dict)
    return batches

def merge_temp_results(person_name):
    safe_name = person_name.replace(" ", "_")
    temp_dir = f'temp/{safe_name}'
    output_file = os.path.join(FINAL_DATA_DIR, f'{safe_name}_使用者分析.json')
    log_output = os.path.join(FINAL_DATA_DIR, f'{safe_name}_使用者分析_log.txt')
    os.makedirs(FINAL_DATA_DIR, exist_ok=True)

    all_results = []
    with open(log_output, 'w', encoding='utf-8') as log_out:
        for fname in sorted(os.listdir(temp_dir)):
            path = os.path.join(temp_dir, fname)
            if fname.endswith(".json"):
                with open(path, 'r', encoding='utf-8') as f:
                    batch_data = json.load(f)
                    all_results.extend(batch_data)
            elif fname.endswith(".txt"):
                with open(path, 'r', encoding='utf-8') as log_in:
                    log_out.write(f"\n=== {fname} ===\n")
                    log_out.write(log_in.read())

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)

    print(f"✅ 合併完成：{output_file}（共 {len(all_results)} 位使用者）")

def process_api_key_group(task_batches, api_keys):
    """確保每個 batch 都能被分配到一個 API key（循環分配）"""
    futures = []
    key_cycle = cycle(api_keys)
    with ThreadPoolExecutor(max_workers=len(api_keys)) as executor:
        for task_batch in task_batches:
            api_key = next(key_cycle)
            futures.append(
                executor.submit(
                    run_batch,
                    task_batch['batch_index'],
                    task_batch['batch'],
                    api_key,
                    task_batch['person'],
                    task_batch['positive_party'],
                    task_batch['negative_party']
                )
            )
    for future in futures:
        future.result()

def main():
    batch_size = 500  # 每批次處理50位使用者
    api_keys = load_api_keys()
    
    # 確保至少有API鍵可用
    if len(api_keys) < 1:
        print("錯誤：至少需要1個API鍵")
        return
    
    # 每組API鍵的數量
    group_size = 16
    
    # 將API鍵分組
    api_key_groups = [api_keys[i:i+group_size] for i in range(0, len(api_keys), group_size)]
    
    print(f"載入了 {len(api_keys)} 個API鍵，分為 {len(api_key_groups)} 組")

    tasks = [
        {
            "input_path": "USER/丁學忠_user.json",
            "person": "丁學忠",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/傅崐萁_user.json",
            "person": "傅崐萁",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/廖偉翔_user.json",
            "person": "廖偉翔",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/楊瓊瓔_user.json",
            "person": "楊瓊瓔",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/江啟臣_user.json",
            "person": "江啟臣",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/游顥_user.json",
            "person": "游顥",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/呂玉玲_user.json",
            "person": "呂玉玲",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/廖先翔_user.json",
            "person": "廖先翔",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/張智倫_user.json",
            "person": "張智倫",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/邱若華_user.json",
            "person": "邱若華",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/魯明哲_user.json",
            "person": "魯明哲",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/萬美玲_user.json",
            "person": "萬美玲",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/羅明才_user.json",
            "person": "羅明才",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/林思銘_user.json",
            "person": "林思銘",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/林德福_user.json",
            "person": "林德福",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/鄭正鈐_user.json",
            "person": "鄭正鈐",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/賴士葆_user.json",
            "person": "賴士葆",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/涂權吉_user.json",
            "person": "涂權吉",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/徐欣瑩_user.json",
            "person": "徐欣瑩",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/李彥秀_user.json",
            "person": "李彥秀",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/林沛祥_user.json",
            "person": "林沛祥",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/洪孟楷_user.json",
            "person": "洪孟楷",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/牛煦庭_user.json",
            "person": "牛煦庭",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        },
        {
            "input_path": "USER/王鴻薇_user.json",
            "person": "王鴻薇",
            "positive_party": "國民黨",
            "negative_party": "民進黨"
        }
    ]

    # 準備所有批次任務
    all_batches = []
    persons = []

    for task in tasks:
        input_path = task["input_path"]
        person = task["person"]
        positive_party = task["positive_party"]
        negative_party = task["negative_party"]

        try:
            # 處理使用者分組資料
            user_data = process_user_data(input_path, person, positive_party, negative_party)
            batches = split_user_batches(user_data, batch_size)
            print(f"\n📦 {person}: {len(user_data)} 位使用者 → {len(batches)} 個批次")

            for i, batch in enumerate(batches):
                all_batches.append({
                    'batch_index': f"{person}_{i}",
                    'batch': batch,
                    'person': person,
                    'positive_party': positive_party,
                    'negative_party': negative_party
                })
            
            persons.append(person)
        except Exception as e:
            print(f"處理任務 {person} 時發生錯誤: {e}")
    
    print(f"共有 {len(all_batches)} 個批次任務")
    process_api_key_group(all_batches, api_keys)
    
    print("\n📂 所有批次已完成，開始合併暫存資料...")
    for p in persons:
        merge_temp_results(p)

    print("\n✅ 所有任務完成，結果存於 data/")

def analyze_legislators_emotions(legislators=None, batch_size=500, quiet=False):
    """
    分析多位立委的情感資料（完整分析）
    
    Args:
        legislators: 立委列表，預設為 None（處理所有立委）
        batch_size: 批次大小
        quiet: 是否安靜模式
    """
    if not legislators:
        # 如果沒有指定立委，從 user_data 目錄讀取所有立委
        if os.path.exists(USER_DATA_DIR):
            user_files = [f for f in os.listdir(USER_DATA_DIR) if f.endswith('_gemini_format.json')]
            legislators = [f.replace('_gemini_format.json', '') for f in user_files]
        else:
            print(f"ERROR: {USER_DATA_DIR} 目錄不存在")
            return
    
    if not quiet:
        print(f"開始分析 {len(legislators)} 位立委的情感資料...")
    
    # 建立任務列表
    tasks = []
    for legislator in legislators:
        input_path = os.path.join(USER_DATA_DIR, f"{legislator}_gemini_format.json")
        if os.path.exists(input_path):
            tasks.append({
                "input_path": input_path,
                "person": legislator,
                "positive_party": "國民黨",  # 可根據實際情況調整
                "negative_party": "民進黨"
            })
        elif not quiet:
            print(f"警告: {input_path} 不存在，跳過")
    
    if not tasks:
        if not quiet:
            print("沒有找到任何立委資料檔案")
        return
    
    # 執行分析流程
    api_keys = load_api_keys()
    if len(api_keys) < 1:
        print("錯誤：至少需要1個API鍵")
        return
    
    # 準備所有批次任務
    all_batches = []
    persons = []
    
    for task in tasks:
        try:
            user_data = process_user_data(task["input_path"], task["person"], 
                                        task["positive_party"], task["negative_party"])
            batches = split_user_batches(user_data, batch_size)
            
            if not quiet:
                print(f"📦 {task['person']}: {len(user_data)} 位使用者 → {len(batches)} 個批次")
            
            for i, batch in enumerate(batches):
                all_batches.append({
                    'batch_index': f"{task['person']}_{i}",
                    'batch': batch,
                    'person': task['person'],
                    'positive_party': task['positive_party'],
                    'negative_party': task['negative_party']
                })
            
            persons.append(task['person'])
        except Exception as e:
            if not quiet:
                print(f"處理 {task['person']} 時發生錯誤: {e}")
    
    if not quiet:
        print(f"共有 {len(all_batches)} 個批次任務")
    
    # 處理所有批次
    process_api_key_group(all_batches, api_keys)
    
    if not quiet:
        print("\n📂 所有批次已完成，開始合併暫存資料...")
    
    # 合併結果
    for person in persons:
        merge_temp_results(person)
    
    if not quiet:
        print("\n✅ 所有任務完成，結果存於 data/")

def analyze_legislators_emotions_incremental(legislators=None, batch_size=500, quiet=False):
    """
    增量分析立委的情感資料（只處理新資料）
    
    Args:
        legislators: 立委列表
        batch_size: 批次大小  
        quiet: 是否安靜模式
    """
    if not quiet:
        print("執行增量情感分析...")
    
    # 檢查是否已有分析結果
    processed_legislators = []
    if legislators:
        for legislator in legislators:
            result_file = os.path.join(FINAL_DATA_DIR, f"{legislator}_使用者分析.json")
            if not os.path.exists(result_file):
                processed_legislators.append(legislator)
            elif not quiet:
                print(f"跳過 {legislator}，已有分析結果")
    
    if processed_legislators:
        analyze_legislators_emotions(processed_legislators, batch_size, quiet)
    elif not quiet:
        print("所有立委都已有分析結果，跳過分析階段")

# ...existing code...