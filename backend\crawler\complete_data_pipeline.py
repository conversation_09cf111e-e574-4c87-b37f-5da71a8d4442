#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整數據處理管道
爬蟲 → 情感分析 → MongoDB更新 → 前端數據準備
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
from collections import defaultdict, Counter

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, '..'))

from enhanced_wordcloud_processor import EnhancedWordCloudProcessor
from data_to_mongo_v2 import DataToMongo
from pymongo import MongoClient

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleMongoDBManager:
    """簡化的MongoDB管理器"""

    def __init__(self):
        self.client = MongoClient('mongodb://localhost:27017/')
        self.db = self.client['legislator_recall']

    def find_one(self, collection_name, query):
        """查找單個文檔"""
        return self.db[collection_name].find_one(query)

    def insert_one(self, collection_name, document):
        """插入單個文檔"""
        return self.db[collection_name].insert_one(document)

    def update_one(self, collection_name, query, update, upsert=False):
        """更新單個文檔"""
        return self.db[collection_name].update_one(query, update, upsert=upsert)

    def close(self):
        """關閉連接"""
        if self.client:
            self.client.close()

class CompleteDataPipeline:
    """完整數據處理管道"""
    
    def __init__(self):
        self.wordcloud_processor = EnhancedWordCloudProcessor()
        self.mongodb_manager = SimpleMongoDBManager()
        
    def process_legislator_complete(self, legislator_name: str) -> Dict[str, Any]:
        """
        完整處理單個立委的數據
        
        Args:
            legislator_name: 立委姓名
            
        Returns:
            處理結果統計
        """
        logger.info(f"🚀 開始完整處理立委: {legislator_name}")
        
        result = {
            'legislator': legislator_name,
            'crawler_data_count': 0,
            'new_records': 0,
            'updated_records': 0,
            'word_cloud_keywords': 0,
            'monthly_stats': {},
            'legislators_updated': False,
            'processing_time': 0
        }
        
        start_time = datetime.now()
        
        try:
            # 1. 從processed/final_data讀取分析結果
            final_data_path = os.path.join(current_dir, 'processed', 'final_data', f'{legislator_name}_使用者分析.json')
            
            if not os.path.exists(final_data_path):
                logger.warning(f"⚠️ 找不到 {legislator_name} 的分析結果文件")
                return result
            
            with open(final_data_path, 'r', encoding='utf-8') as f:
                analysis_data = json.load(f)
            
            # 2. 處理crawler_data更新
            crawler_result = self._update_crawler_data(legislator_name, analysis_data)
            result.update(crawler_result)
            
            # 3. 生成文字雲
            wordcloud_result = self._generate_wordcloud(legislator_name, analysis_data)
            result['word_cloud_keywords'] = len(wordcloud_result)
            
            # 4. 生成月份統計
            monthly_result = self._generate_monthly_stats(legislator_name, analysis_data)
            result['monthly_stats'] = monthly_result
            
            # 5. 更新legislators集合
            legislators_result = self._update_legislators_collection(
                legislator_name, 
                wordcloud_result, 
                monthly_result,
                result['crawler_data_count']
            )
            result['legislators_updated'] = legislators_result
            
            # 計算處理時間
            result['processing_time'] = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"✅ {legislator_name} 處理完成: {result['crawler_data_count']} 條記錄")
            
        except Exception as e:
            logger.error(f"❌ 處理 {legislator_name} 時發生錯誤: {e}")
            result['error'] = str(e)
        
        return result
    
    def _update_crawler_data(self, legislator_name: str, analysis_data: Dict) -> Dict[str, int]:
        """更新crawler_data集合"""
        logger.info(f"📝 更新 {legislator_name} 的crawler_data...")
        
        new_count = 0
        updated_count = 0
        
        try:
            # 獲取用戶評論數據
            user_comments = analysis_data.get('faust666', [])
            
            for comment in user_comments:
                # 準備MongoDB文檔
                doc = {
                    'name': legislator_name,
                    '用戶': comment.get('用戶', ''),
                    '內容': comment.get('內容', ''),
                    '情感標籤': comment.get('情感標籤', 'NEUTRAL'),
                    '情緒': comment.get('情緒', 'neutral'),
                    '平台': comment.get('平台', 'unknown'),
                    '時間': self._parse_time(comment.get('時間', '')),
                    '處理時間': datetime.now()
                }
                
                # 檢查是否已存在（基於用戶+內容+時間）
                existing = self.mongodb_manager.find_one('crawler_data', {
                    'name': legislator_name,
                    '用戶': doc['用戶'],
                    '內容': doc['內容'],
                    '時間': doc['時間']
                })
                
                if existing:
                    # 更新現有記錄
                    self.mongodb_manager.update_one(
                        'crawler_data',
                        {'_id': existing['_id']},
                        {'$set': doc}
                    )
                    updated_count += 1
                else:
                    # 插入新記錄
                    self.mongodb_manager.insert_one('crawler_data', doc)
                    new_count += 1
            
            logger.info(f"✅ crawler_data更新完成: 新增 {new_count}, 更新 {updated_count}")
            
        except Exception as e:
            logger.error(f"❌ 更新crawler_data失敗: {e}")
        
        return {
            'crawler_data_count': new_count + updated_count,
            'new_records': new_count,
            'updated_records': updated_count
        }
    
    def _generate_wordcloud(self, legislator_name: str, analysis_data: Dict) -> List[Dict[str, Any]]:
        """生成文字雲數據"""
        logger.info(f"☁️ 生成 {legislator_name} 的文字雲...")
        
        try:
            # 收集所有文本內容
            text_data = []
            user_comments = analysis_data.get('faust666', [])
            
            for comment in user_comments:
                content = comment.get('內容', '')
                if content and len(content.strip()) > 5:
                    text_data.append(content)
            
            # 使用強化處理器生成關鍵詞
            keywords = self.wordcloud_processor.extract_keywords(
                ' '.join(text_data), 
                max_words=50
            )
            
            logger.info(f"✅ 文字雲生成完成: {len(keywords)} 個關鍵詞")
            return keywords
            
        except Exception as e:
            logger.error(f"❌ 生成文字雲失敗: {e}")
            return []
    
    def _generate_monthly_stats(self, legislator_name: str, analysis_data: Dict) -> Dict[str, Dict]:
        """生成動態密度的月份統計數據"""
        logger.info(f"📊 生成 {legislator_name} 的動態密度月份統計...")

        # 首先收集所有日期級別的數據
        daily_stats = defaultdict(lambda: {
            '正面': 0, '負面': 0, '中性': 0,
            'joy': 0, 'anger': 0, 'sadness': 0, 'fear': 0,
            'surprise': 0, 'disgust': 0, 'trust': 0, 'anticipation': 0
        })

        try:
            user_comments = analysis_data.get('faust666', [])

            # 收集日期級別的統計
            for comment in user_comments:
                time_str = comment.get('時間', '')
                comment_time = self._parse_time(time_str)

                if comment_time:
                    # 生成日期鍵 (YYYY-MM-DD格式)
                    date_key = comment_time.strftime('%Y-%m-%d')

                    # 統計情感
                    sentiment = comment.get('情感標籤', 'NEUTRAL')
                    if sentiment == 'POSITIVE':
                        daily_stats[date_key]['正面'] += 1
                    elif sentiment == 'NEGATIVE':
                        daily_stats[date_key]['負面'] += 1
                    else:
                        daily_stats[date_key]['中性'] += 1

                    # 統計情緒
                    emotion = comment.get('情緒', 'neutral').lower()
                    if emotion in daily_stats[date_key]:
                        daily_stats[date_key][emotion] += 1

            # 生成動態密度的時間點統計
            result = self._generate_dynamic_time_series(daily_stats)

            logger.info(f"✅ 動態密度月份統計完成: {len(result)} 個時間點")
            return result

        except Exception as e:
            logger.error(f"❌ 生成月份統計失敗: {e}")
            return {}

    def _generate_dynamic_time_series(self, daily_stats: Dict) -> Dict[str, Dict]:
        """
        根據前端需求生成動態密度的時間序列數據

        時間範圍對應的數據點密度：
        - 1年: 一個月一個點 (12個點)
        - 6個月: 一個月兩個點 (12個點)
        - 3個月: 一個月3個點 (9個點)
        - 1個月: 5天一點 (6個點)
        - 2周: 兩天一點 (7個點)
        - 1周: 每天一點 (7個點)
        """
        if not daily_stats:
            return {}

        from datetime import datetime, timedelta

        # 解析並排序日期
        date_data = []
        for date_str, stats in daily_stats.items():
            try:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                date_data.append((date_obj, stats))
            except ValueError:
                continue

        if not date_data:
            return {}

        date_data.sort(key=lambda x: x[0])
        start_date = date_data[0][0]
        end_date = date_data[-1][0]
        total_days = (end_date - start_date).days + 1

        # 根據數據範圍決定聚合策略
        if total_days <= 7:
            # 1周: 每天一點
            return self._aggregate_by_days(date_data, 1)
        elif total_days <= 14:
            # 2周: 兩天一點
            return self._aggregate_by_days(date_data, 2)
        elif total_days <= 30:
            # 1個月: 5天一點
            return self._aggregate_by_days(date_data, 5)
        elif total_days <= 90:
            # 3個月: 一個月3個點 (10天一點)
            return self._aggregate_by_days(date_data, 10)
        elif total_days <= 180:
            # 6個月: 一個月兩個點 (15天一點)
            return self._aggregate_by_days(date_data, 15)
        else:
            # 1年+: 一個月一個點
            return self._aggregate_by_months(date_data)

    def _aggregate_by_days(self, date_data: List, interval_days: int) -> Dict[str, Dict]:
        """按天數間隔聚合數據"""
        result = {}
        current_group = []
        current_start = None

        for i, (date_obj, stats) in enumerate(date_data):
            if current_start is None:
                current_start = date_obj
                current_group = [stats]
            elif (date_obj - current_start).days >= interval_days or i == len(date_data) - 1:
                # 聚合當前組的數據
                aggregated = self._aggregate_stats_group(current_group)
                key = current_start.strftime('%Y-%m-%d')
                result[key] = aggregated

                # 開始新組
                current_start = date_obj
                current_group = [stats]
            else:
                current_group.append(stats)

        return result

    def _aggregate_by_months(self, date_data: List) -> Dict[str, Dict]:
        """按月聚合數據"""
        monthly_groups = defaultdict(list)

        for date_obj, stats in date_data:
            month_key = date_obj.strftime('%Y-%m')
            monthly_groups[month_key].append(stats)

        result = {}
        for month_key, stats_list in monthly_groups.items():
            result[month_key] = self._aggregate_stats_group(stats_list)

        return result

    def _aggregate_stats_group(self, stats_list: List[Dict]) -> Dict:
        """聚合統計數據組"""
        aggregated = {
            '正面': 0, '負面': 0, '中性': 0,
            'joy': 0, 'anger': 0, 'sadness': 0, 'fear': 0,
            'surprise': 0, 'disgust': 0, 'trust': 0, 'anticipation': 0
        }

        for stats in stats_list:
            for key, value in stats.items():
                if key in aggregated:
                    aggregated[key] += value

        return aggregated

    def _update_legislators_collection(self, legislator_name: str, wordcloud_data: List,
                                     monthly_stats: Dict, total_comments: int) -> bool:
        """更新legislators集合"""
        logger.info(f"🏛️ 更新 {legislator_name} 的legislators集合...")
        
        try:
            # 計算總體統計
            total_positive = sum(stats.get('正面', 0) for stats in monthly_stats.values())
            total_negative = sum(stats.get('負面', 0) for stats in monthly_stats.values())
            total_neutral = sum(stats.get('中性', 0) for stats in monthly_stats.values())
            
            # 計算情緒統計
            emotion_totals = defaultdict(int)
            for stats in monthly_stats.values():
                for emotion in ['joy', 'anger', 'sadness', 'fear', 'surprise', 'disgust', 'trust', 'anticipation']:
                    emotion_totals[emotion] += stats.get(emotion, 0)
            
            # 獲取現有數據進行增量更新
            existing_doc = self.mongodb_manager.find_one('legislators', {'name': legislator_name})

            if existing_doc:
                # 增量更新：累加數字
                existing_positive = existing_doc.get('情感分析', {}).get('反對罷免人數', 0)
                existing_negative = existing_doc.get('情感分析', {}).get('支持罷免人數', 0)
                existing_neutral = existing_doc.get('情感分析', {}).get('中性人數', 0)

                # 累加情感統計
                final_positive = existing_positive + total_positive
                final_negative = existing_negative + total_negative
                final_neutral = existing_neutral + total_neutral

                # 累加情緒統計
                existing_emotions = existing_doc.get('情緒分析', {})
                final_emotions = {}
                for emotion in ['joy', 'anger', 'sadness', 'fear', 'surprise', 'disgust', 'trust', 'anticipation']:
                    existing_count = existing_emotions.get(emotion, 0)
                    new_count = emotion_totals.get(emotion, 0)
                    final_emotions[emotion] = existing_count + new_count

                # 合併月份統計（保留舊數據，添加新數據）
                existing_monthly = existing_doc.get('月份情感統計', {})
                final_monthly_stats = existing_monthly.copy()
                final_monthly_stats.update(monthly_stats)

                # 累加用戶數和留言數
                existing_user_count = existing_doc.get('用戶數', 0)
                existing_comment_count = existing_doc.get('留言數', 0)

            else:
                # 新文檔：直接使用當前數據
                final_positive = total_positive
                final_negative = total_negative
                final_neutral = total_neutral
                final_emotions = dict(emotion_totals)
                final_monthly_stats = monthly_stats
                existing_user_count = 0
                existing_comment_count = 0

            # 準備更新文檔
            update_doc = {
                'name': legislator_name,
                '情感分析': {
                    '反對罷免人數': final_positive,
                    '支持罷免人數': final_negative,
                    '中性人數': final_neutral
                },
                '情緒分析': final_emotions,
                'word_cloud': wordcloud_data,  # 詞雲可以覆蓋（因為是重新計算的）
                '月份情感統計': final_monthly_stats,
                '用戶數': existing_user_count + len(set(comment.get('用戶', '') for comment in monthly_stats.values())),
                '留言數': existing_comment_count + total_comments,
                '最後更新時間': datetime.now()
            }

            # 更新或插入
            result = self.mongodb_manager.update_one(
                'legislators',
                {'name': legislator_name},
                {'$set': update_doc},
                upsert=True
            )
            
            logger.info(f"✅ legislators集合更新完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新legislators集合失敗: {e}")
            return False
    
    def _parse_time(self, time_str: str) -> datetime:
        """解析時間字符串"""
        if not time_str:
            return datetime.now()
        
        try:
            # 嘗試多種時間格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(time_str, fmt)
                except ValueError:
                    continue
            
            # 如果都失敗，返回當前時間
            return datetime.now()
            
        except Exception:
            return datetime.now()

def process_single_legislator(legislator_name: str) -> Dict[str, Any]:
    """處理單個立委的完整數據管道"""
    pipeline = CompleteDataPipeline()
    return pipeline.process_legislator_complete(legislator_name)

def process_multiple_legislators(legislator_names: List[str]) -> Dict[str, Any]:
    """批量處理多個立委"""
    logger.info(f"🚀 開始批量處理 {len(legislator_names)} 位立委...")
    
    pipeline = CompleteDataPipeline()
    results = {}
    
    for i, legislator in enumerate(legislator_names, 1):
        logger.info(f"📋 處理進度: {i}/{len(legislator_names)} - {legislator}")
        results[legislator] = pipeline.process_legislator_complete(legislator)
    
    # 生成總結報告
    total_records = sum(r.get('crawler_data_count', 0) for r in results.values())
    total_keywords = sum(r.get('word_cloud_keywords', 0) for r in results.values())
    successful = sum(1 for r in results.values() if r.get('legislators_updated', False))
    
    summary = {
        'total_legislators': len(legislator_names),
        'successful_updates': successful,
        'total_crawler_records': total_records,
        'total_keywords': total_keywords,
        'results': results
    }
    
    logger.info(f"🎉 批量處理完成: {successful}/{len(legislator_names)} 成功")
    return summary

def main():
    """主函數"""
    # 測試單個立委
    test_legislator = '牛煦庭'
    logger.info(f"🧪 測試處理立委: {test_legislator}")
    
    result = process_single_legislator(test_legislator)
    
    print("\n" + "="*50)
    print("處理結果:")
    print("="*50)
    for key, value in result.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    main()
