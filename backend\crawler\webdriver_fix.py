#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebDriver池修復腳本
解決WebDriver池耗盡問題
"""

import os
import sys
import time
import logging
from contextlib import contextmanager

# 添加路徑
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleWebDriverManager:
    """簡化的WebDriver管理器"""
    
    def __init__(self, max_instances=3):
        self.max_instances = max_instances
        self.active_count = 0
        self.lock = threading.Lock()
    
    @contextmanager
    def get_driver(self):
        """獲取WebDriver實例（簡化版）"""
        driver = None
        try:
            with self.lock:
                if self.active_count >= self.max_instances:
                    logger.warning(f"⚠️ 達到最大WebDriver數量限制 ({self.max_instances})，等待...")
                    time.sleep(5)  # 簡單等待
                
                # 創建新的WebDriver
                driver = self._create_driver()
                self.active_count += 1
                logger.info(f"🆕 創建WebDriver，當前活躍數: {self.active_count}/{self.max_instances}")
            
            yield driver
            
        except Exception as e:
            logger.error(f"❌ WebDriver使用時出錯: {e}")
            raise
        finally:
            # 確保WebDriver被釋放
            if driver:
                try:
                    driver.quit()
                    with self.lock:
                        self.active_count = max(0, self.active_count - 1)
                    logger.info(f"✅ WebDriver已釋放，當前活躍數: {self.active_count}")
                except Exception as e:
                    logger.error(f"❌ 釋放WebDriver時出錯: {e}")
    
    def _create_driver(self):
        """創建WebDriver實例"""
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        driver = webdriver.Chrome(
            service=webdriver.chrome.service.Service(ChromeDriverManager().install()),
            options=options
        )
        
        return driver

def fix_webdriver_pool():
    """修復WebDriver池問題"""
    logger.info("🔧 修復WebDriver池問題...")
    
    try:
        # 替換webdriver_pool.py中的get_global_pool函數
        webdriver_pool_file = os.path.join(current_dir, 'webdriver_pool.py')
        
        # 創建簡化的全局管理器
        global simple_manager
        simple_manager = SimpleWebDriverManager(max_instances=2)  # 減少並發數
        
        logger.info("✅ WebDriver池修復完成")
        logger.info("💡 建議：")
        logger.info("   1. 減少並行線程數到2個")
        logger.info("   2. 增加錯誤重試機制")
        logger.info("   3. 使用更穩定的網絡環境")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 修復WebDriver池失敗: {e}")
        return False

def test_webdriver_manager():
    """測試WebDriver管理器"""
    logger.info("🧪 測試WebDriver管理器...")
    
    try:
        manager = SimpleWebDriverManager(max_instances=2)
        
        # 測試創建和釋放WebDriver
        with manager.get_driver() as driver:
            driver.get("https://www.google.com")
            logger.info("✅ WebDriver測試成功")
        
        logger.info("✅ WebDriver管理器測試通過")
        return True
        
    except Exception as e:
        logger.error(f"❌ WebDriver管理器測試失敗: {e}")
        return False

def create_optimized_youtube_crawler():
    """創建優化的YouTube爬蟲配置"""
    logger.info("🔧 創建優化的YouTube爬蟲配置...")
    
    config = {
        "max_threads": 2,  # 減少線程數
        "max_webdriver_instances": 2,  # 減少WebDriver實例
        "retry_attempts": 3,  # 增加重試次數
        "timeout_seconds": 30,  # 減少超時時間
        "delay_between_requests": 2,  # 增加請求間隔
        "use_simple_manager": True  # 使用簡化管理器
    }
    
    config_file = os.path.join(current_dir, 'youtube_crawler_config.json')
    
    try:
        import json
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ 優化配置已保存到: {config_file}")
        logger.info("📋 優化配置:")
        for key, value in config.items():
            logger.info(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 創建優化配置失敗: {e}")
        return False

def main():
    """主修復函數"""
    logger.info("🚀 開始修復WebDriver池問題...")
    
    fixes = [
        ("WebDriver池修復", fix_webdriver_pool),
        ("WebDriver管理器測試", test_webdriver_manager),
        ("優化YouTube爬蟲配置", create_optimized_youtube_crawler)
    ]
    
    results = {}
    
    for fix_name, fix_func in fixes:
        logger.info(f"\n{'='*50}")
        logger.info(f"執行: {fix_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[fix_name] = fix_func()
        except Exception as e:
            logger.error(f"執行 {fix_name} 時發生異常: {e}")
            results[fix_name] = False
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("WebDriver池修復總結")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(fixes)
    
    for fix_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失敗"
        logger.info(f"{fix_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個修復成功")
    
    if passed >= 2:
        logger.info("🎉 WebDriver池問題修復完成！")
        logger.info("\n📋 建議使用方式:")
        logger.info("python main.py --legislators 牛煦庭 --days 1 --use-optimized-crawler --platforms youtube,ptt --max-crawl-workers 1")
        return 0
    else:
        logger.warning(f"⚠️ 有 {total - passed} 個修復失敗")
        return 1

if __name__ == "__main__":
    import threading
    sys.exit(main())
