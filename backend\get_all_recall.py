from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
from pymongo import MongoClient
import json
import os
import time
import re
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service # 導入 Service

# --- MongoDB 配置 ---
MONGO_URI = "mongodb://localhost:27017/" # 替換為你的 MongoDB 連接字符串
DB_NAME = "legislator_recall" # 替換為你的資料庫名稱
COLLECTION_NAME = "legislators" # 你的集合名稱

# --- 網頁爬蟲配置 ---
PAGE_URL = "https://amaochen0110.github.io/Unseat/"

def get_recall_data_from_website():
    """
    從罷免網站爬取立委的最新罷免數據和狀態。
    返回一個包含立委數據的列表。
    """
    driver = None
    recall_data_list = []
    try:
        # 使用 Service 物件來啟動 WebDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service)
        driver.get(PAGE_URL)
        wait = WebDriverWait(driver, 10)

        try:
            close_button = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'button.popup-close-btn')))
            close_button.click()
            time.sleep(1) # 等待彈出視窗關閉
        except TimeoutException:
            print("彈出視窗關閉按鈕未在預期時間內出現，可能沒有彈出視窗或已自動關閉。")

        wait.until(EC.presence_of_element_located((By.CLASS_NAME, 'person-item')))
        time.sleep(1) # 額外等待確保所有內容渲染完成

        soup = BeautifulSoup(driver.page_source, 'html.parser')
        person_items = soup.find_all('div', class_='person-item')
        print(f"從網站找到的 person-item 數量：{len(person_items)}")

        for item in person_items:
            data = {}

            # 抓取行政區和姓名
            name_group = item.find('div', class_='name-text-group')
            if name_group:
                area_span = name_group.find('span', class_='person-name-area')
                fullname_span = name_group.find('span', class_='person-name-fullname')
                data['行政區'] = area_span.text.strip() if area_span else None
                data['姓名'] = fullname_span.text.strip() if fullname_span else None
            
            # 如果沒有姓名，則跳過此項，因為姓名是識別立委的關鍵
            if not data.get('姓名'):
                continue

            # 抓取目標資訊
            goal_info = item.find('div', class_='goal-info')
            if goal_info:
                count_span = goal_info.find('span', class_='count-info')
                min_threshold_span = goal_info.find('span', class_='min-threshold')
                min_target_span = goal_info.find('span', re.compile(r'min-target'))
                data['目前收件'] = count_span.text.replace('目前收件：', '').replace(',', '').strip() if count_span else None
                data['罷免門檻'] = min_threshold_span.text.replace('門檻：', '').replace(',', '').strip() if min_threshold_span else None
                data['罷免目標'] = min_target_span.text.replace('目標：', '').replace(',', '').strip() if min_target_span else None
            
            # 抓取進度資訊
            raw_web_status = None
            data['status'] = "未知狀態" # 這是我們判斷後要用的「狀態」欄位

            progress_block = item.find('div', class_='progress-block')
            if progress_block:
                progress_percent_div = progress_block.find('div', class_='day-info')
                if progress_percent_div and '進度：' in progress_percent_div.text:
                    data['進度'] = progress_percent_div.text.replace('進度：', '').strip()

                all_day_info_divs = progress_block.find_all('div', class_='day-info')
                if len(all_day_info_divs) >= 2:
                    days_status_text = all_day_info_divs[1].text.strip()
                    
                    match = re.match(r'(?:第?)(\d+)天(?:/(\d+)天)?\s*(.*)', days_status_text)
                    if match:
                        data['已進行天數'] = match.group(1).strip()
                        data['總天數'] = match.group(2).strip() if match.group(2) else "未知"
                        raw_web_status = match.group(3).strip() if match.group(3) else "無原始狀態文字"
                    else:
                        num_days_match = re.match(r'(\d+)天', days_status_text)
                        if num_days_match:
                            data['已進行天數'] = num_days_match.group(1).strip()
                            data['總天數'] = "未知"
                            raw_web_status = "無原始狀態文字"
                        else:
                            data['已進行天數'] = "未知"
                            data['總天數'] = "未知"
                            raw_web_status = days_status_text
                            
                    status_tag_within_div = all_day_info_divs[1].find('div')
                    if status_tag_within_div and status_tag_within_div.text.strip():
                        raw_web_status = status_tag_within_div.text.strip()
                
                # 這裡不再儲存 data['網站原始狀態']，因為它會被移除

            # --- 判斷單一 status 欄位 (主要依賴數字，輔助明確關鍵字) ---
            current_count_int = 0
            threshold_int = 0
            total_days_int = 0
            elapsed_days_int = 0
            
            try:
                current_count_int = int(data.get('目前收件', '0').replace('+', ''))
                threshold_int = int(data.get('罷免門檻', '0'))
                elapsed_days_int = int(data.get('已進行天數', '0')) if str(data.get('已進行天數', '0')).isdigit() else 0
                total_days_int = int(data.get('總天數', '0')) if str(data.get('總天數', '0')).isdigit() else 0
            except (ValueError, TypeError):
                data['status'] = '資料解析錯誤'
                recall_data_list.append(data)
                continue

            # 優先處理網站上明確的最終結果或階段性狀態
            if raw_web_status and raw_web_status != "無原始狀態文字":
                raw_web_status_lower = raw_web_status.lower()
                if '罷免成功' in raw_web_status_lower:
                    data['status'] = '三階罷免成功'
                elif '罷免失敗' in raw_web_status_lower or '否決' in raw_web_status_lower or '未通過' in raw_web_status_lower or '不成立' in raw_web_status_lower:
                    if '連署' in raw_web_status_lower or '二階' in raw_web_status_lower:
                         data['status'] = '二階失敗'
                    else:
                         data['status'] = '三階罷免失敗'
                elif '已中止' in raw_web_status_lower:
                    data['status'] = '罷免中止'
                elif '已成立' in raw_web_status_lower or '投票進行中' in raw_web_status_lower or '罷免中' in raw_web_status_lower:
                    data['status'] = '三階投票進行中'
            
            # 如果經過原始狀態判斷後仍未確定狀態，則依靠數字判斷
            if data['status'] == "未知狀態":
                if threshold_int > 0:
                    if current_count_int >= threshold_int:
                        # 達門檻，並已過期程，則推斷為二階通過，進入三階
                        if elapsed_days_int > 0 and total_days_int > 0 and elapsed_days_int > total_days_int:
                            data['status'] = '三階投票進行中'
                        else: # 達門檻但未過期程
                            data['status'] = '二階連署進行中 (已達門檻)'
                    else: # 未達門檻
                        if elapsed_days_int > 0 and total_days_int > 0 and elapsed_days_int > total_days_int:
                            data['status'] = '二階失敗'
                        else: # 未達門檻且未過期程
                            data['status'] = '二階連署進行中'
                else:
                    data['status'] = '未知狀態 (門檻數據缺失)'

            recall_data_list.append(data)
            
    except (TimeoutException, NoSuchElementException) as se:
        print(f"Selenium 操作超時或元素未找到: {se}")
    except Exception as e:
        print(f"網頁爬取時發生錯誤：{e}")
    finally:
        if driver:
            driver.quit()
    return recall_data_list

def update_mongodb_legislators(recall_data_list):
    """
    根據爬取到的數據更新 MongoDB 中立委文件的 recall_data 子欄位。
    使用模糊匹配查找立委姓名，並移除 '網站原始狀態' 欄位。
    """
    client = None
    try:
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        legislators_collection = db[COLLECTION_NAME]

        print(f"\n嘗試連接到資料庫: {DB_NAME} 和集合: {COLLECTION_NAME}")

        # 儲存當前在罷免名單上的立委姓名，用於後續刪除操作
        legislator_names_on_recall_list = []

        for record in recall_data_list:
            legislator_name_from_web = record.get('姓名') # 來自網頁的姓名
            if legislator_name_from_web:
                legislator_names_on_recall_list.append(legislator_name_from_web)

                # --- 使用正則表達式進行模糊匹配 ---
                # 清理網頁姓名，移除首尾空白
                cleaned_name = legislator_name_from_web.strip()
                
                # 正則表達式模式：精確匹配整個字串，但允許「鳳」與「凤」互換
                # re.escape() 確保姓名中的特殊字元（如 . * + ?）被正確處理
                # 如果你有多個字元問題，可以在這裡增加替換規則
                pattern_str = re.escape(cleaned_name)
                # 假設你確定是「鳳」字的問題，可以這樣處理：
                # pattern_str = pattern_str.replace('鳳', '[鳳凤]') 
                
                # 使用 re.IGNORECASE 如果需要忽略大小寫，雖然中文姓名通常不需要
                query = {"name": {"$regex": pattern_str}} # 這裡可以直接用 pattern_str

                # 查找匹配的立委，只更新找到的第一個
                # 如果有多個匹配，只會更新第一個。如果期望更新所有匹配，需用 find_one_and_update 或 find 迴圈
                matched_legislator = legislators_collection.find_one(query)

                if matched_legislator:
                    # 構建只更新 recall_data 子欄位的字典
                    # **注意：這裡不再包含 '網站原始狀態' 欄位**
                    recall_data_update_fields = {
                        "recall_data.行政區": record.get('行政區'),
                        "recall_data.姓名": record.get('姓名'),   # 保持與網頁抓取的一致性
                        "recall_data.目前收件": record.get('目前收件'),
                        "recall_data.罷免門檻": record.get('罷免門檻'),
                        "recall_data.罷免目標": record.get('罷免目標'),
                        "recall_data.進度": record.get('進度'),
                        "recall_data.已進行天數": record.get('已進行天數'),
                        "recall_data.總天數": record.get('總天數'),
                        "recall_data.狀態": record.get('status') # 我們的判斷狀態
                    }
                    
                    # 使用 $unset 移除舊的 '網站原始狀態' 欄位，以防它之前存在
                    unset_fields = {"recall_data.網站原始狀態": ""}
                    
                    update_result = legislators_collection.update_one(
                        {"_id": matched_legislator["_id"]}, # 使用匹配到的 _id 進行精確更新
                        {"$set": recall_data_update_fields, "$unset": unset_fields}
                    )
                    
                    if update_result.matched_count > 0:
                        print(f"成功更新立委 {matched_legislator['name']} (匹配到 '{legislator_name_from_web}') 的 recall_data，並移除 '網站原始狀態'。")
                    else:
                        print(f"未能更新立委 {matched_legislator['name']} 的 recall_data (可能是競態條件)。")
                else:
                    print(f"未找到立委 '{legislator_name_from_web}' 的記錄，無法更新 recall_data。")
                    print(f"  嘗試匹配模式: {pattern_str}")
            else:
                print("發現一筆沒有姓名的立委記錄，已跳過更新 recall_data。")

        print(f"\n已更新 {len(recall_data_list)} 筆立委的罷免數據 (recall_data)。")

        # --- 刪除不在罷免名單上的立委 ---
        print("\n準備刪除 MongoDB 中不在當前罷免名單上的立委資料...")
        # 這裡的 $nin 操作符使用精確匹配，所以如果字元差異導致新增失敗，它將會被刪除
        query_to_delete = {"name": {"$nin": legislator_names_on_recall_list}}
        delete_result = legislators_collection.delete_many(query_to_delete)
        print(f"成功刪除 {delete_result.deleted_count} 筆不在當前罷免名單上的立委資料。")

    except Exception as e:
        print(f"MongoDB 操作時發生錯誤: {e}")
    finally:
        if client:
            client.close()
            print("MongoDB 連接已關閉。")

if __name__ == "__main__":
    print("--- 開始執行罷免資料更新程序 ---")
    
    # 步驟 1: 爬取最新數據
    print("正在從網站爬取最新罷免數據...")
    latest_recall_data = get_recall_data_from_website()
    
    if latest_recall_data:
        print("\n爬取數據完成，開始更新 MongoDB...")
        # 步驟 2: 更新 MongoDB
        update_mongodb_legislators(latest_recall_data)
        print("\n--- 罷免資料更新程序完成 ---")
    else:
        print("\n未爬取到任何罷免數據，MongoDB 未進行更新。")