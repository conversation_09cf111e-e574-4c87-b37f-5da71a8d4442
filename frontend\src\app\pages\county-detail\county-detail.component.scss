.county-detail-container {
  max-width: 1200px;
  margin: 2rem auto;
  background: #fff;
  border-radius: 1.2rem;
  box-shadow: 0 2px 16px 0 rgba(0,0,0,0.07);
  padding: 2rem 2.5rem;

  h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
    font-size: 1.8rem;
  }
}

// 罷免狀態篩選器
.recall-status-filter {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;

  .filter-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;

    .total-count {
      margin-left: auto;
      font-size: 0.9rem;
      color: #666;
      font-weight: normal;
    }
  }

  .filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;

    .status-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.6rem 1.2rem;
      border: 2px solid #e9ecef;
      border-radius: 2rem;
      background: white;
      color: #666;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &.active {
        border-color: #4f8cff;
        background: #4f8cff;
        color: white;
      }

      .count {
        font-size: 0.8rem;
        opacity: 0.8;
      }

      // 不同狀態的顏色
      &.btn-petition-ongoing.active { background: #17a2b8; border-color: #17a2b8; }
      &.btn-petition-failed.active { background: #6c757d; border-color: #6c757d; }
      &.btn-recall-ongoing.active { background: #ffc107; border-color: #ffc107; color: #333; }
      &.btn-recall-failed.active { background: #dc3545; border-color: #dc3545; }
      &.btn-recall-success.active { background: #28a745; border-color: #28a745; }
    }
  }
}
.district-list {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
}
.district-list button {
  background: #e3eaff;
  border: none;
  border-radius: 2rem;
  padding: 0.5rem 1.2rem;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.district-list button.selected, .district-list button:hover {
  background: #4f8cff;
  color: #fff;
  box-shadow: 0 2px 8px #4f8cff44;
}
.stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.2rem;
}
.stat-item {
  background: #f8f8ff;
  border-radius: 0.7rem;
  padding: 0.7rem 1.2rem;
  box-shadow: 0 1px 4px #b3c6ff33;
  text-align: center;
}
.stat-key {
  display: block;
  font-size: 0.95rem;
  color: #4f8cff;
}
.stat-value {
  font-size: 1.3rem;
  font-weight: bold;
  color: #222;
}
.politician-list {
  list-style: none;
  padding: 0;
}
.politician-list li {
  margin-bottom: 0.7rem;
}
.politician-list a {
  color: #4f8cff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
  cursor: pointer;
}
.politician-list a:hover {
  color: #ff41f8;
  text-decoration: underline;
}

// 新的立委卡片樣式
.loading {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: #666;
}

.politicians-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.politician-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: #4f8cff;
  }

  .politician-photo {
    margin-bottom: 1rem;

    img {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #f0f0f0;
      transition: border-color 0.3s ease;
    }
  }

  &:hover .politician-photo img {
    border-color: #4f8cff;
  }

  .politician-name {
    font-size: 1.3rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .politician-district {
    font-size: 1rem;
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.4;
  }

  .survey-info {
    font-size: 0.9rem;
    color: #4f8cff;
    background: #f0f6ff;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    display: inline-block;
    font-weight: 500;
  }

  .recall-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.85rem;
    font-weight: 500;

    &.status-petition-ongoing {
      background: rgba(23, 162, 184, 0.1);
      color: #17a2b8;
      border: 1px solid rgba(23, 162, 184, 0.3);
    }

    &.status-petition-failed {
      background: rgba(108, 117, 125, 0.1);
      color: #6c757d;
      border: 1px solid rgba(108, 117, 125, 0.3);
    }

    &.status-recall-ongoing {
      background: rgba(255, 193, 7, 0.1);
      color: #e0a800;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    &.status-recall-failed {
      background: rgba(220, 53, 69, 0.1);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
    }

    &.status-recall-success {
      background: rgba(40, 167, 69, 0.1);
      color: #28a745;
      border: 1px solid rgba(40, 167, 69, 0.3);
    }

    &.status-survey {
      background: rgba(108, 117, 125, 0.1);
      color: #6c757d;
      border: 1px solid rgba(108, 117, 125, 0.3);
    }

    &.status-petition-success {
      background: rgba(40, 167, 69, 0.1);
      color: #28a745;
      border: 1px solid rgba(40, 167, 69, 0.3);
    }
  }
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #999;
  font-size: 1.1rem;
}

// 響應式設計
@media (max-width: 768px) {
  .county-detail-container {
    margin: 1rem;
    padding: 1.5rem;
  }

  .politicians-container {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 1rem;
  }

  .politician-card {
    padding: 1.2rem;

    .politician-photo img {
      width: 60px;
      height: 60px;
    }

    .politician-name {
      font-size: 1.1rem;
    }

    .politician-district {
      font-size: 0.9rem;
    }
  }
}
