#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整流程驗證腳本
驗證從多平台URL收集到MongoDB存儲的完整流程
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s'
)
logger = logging.getLogger(__name__)

def verify_flow_architecture():
    """驗證流程架構"""
    logger.info("🔍 驗證完整流程架構...")
    
    expected_flow = [
        "1. 多平台並行收集URL (YouTube + PTT + Threads 同時)",
        "2. 逐平台多線程爬取內容 (YouTube 4線程 → PTT 4線程 → Threads 4線程)",
        "3. 合併平台數據到 processed/alldata/",
        "4. 統計用戶數據到 processed/user_data/",
        "5. Gemini情感分析到 processed/final_data/",
        "6. 存儲到MongoDB (crawler_data + legislators)",
        "7. 處理下一位立委 (重複1-6)"
    ]
    
    logger.info("📋 預期流程架構:")
    for step in expected_flow:
        logger.info(f"   {step}")
    
    return True

def verify_url_collection_parallel():
    """驗證URL收集並行機制"""
    logger.info("🔍 驗證URL收集並行機制...")
    
    try:
        # 檢查optimized_crawler_manager.py中的並行實現
        manager_file = os.path.join(os.path.dirname(__file__), 'crawler', 'optimized_crawler_manager.py')
        
        with open(manager_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查關鍵實現
        checks = [
            ('ThreadPoolExecutor', '使用線程池並行執行'),
            ('max_workers=3', '3個線程同時收集URL'),
            ('future_to_platform', '平台任務映射'),
            ('as_completed', '等待所有任務完成')
        ]
        
        missing = []
        for check, desc in checks:
            if check in content:
                logger.info(f"   ✅ {desc}: 找到 {check}")
            else:
                missing.append(f"{desc}: 缺少 {check}")
        
        if missing:
            logger.error(f"   ❌ 缺少實現: {missing}")
            return False
        else:
            logger.info("   ✅ URL收集並行機制實現正確")
            return True
            
    except Exception as e:
        logger.error(f"❌ 驗證URL收集並行機制失敗: {e}")
        return False

def verify_content_crawling_sequential():
    """驗證內容爬取順序機制"""
    logger.info("🔍 驗證內容爬取順序機制...")
    
    try:
        manager_file = os.path.join(os.path.dirname(__file__), 'crawler', 'optimized_crawler_manager.py')
        
        with open(manager_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查順序執行實現
        checks = [
            ('for i, platform in enumerate(platforms', '順序遍歷平台'),
            ('crawl_youtube_comments_with_pool', 'YouTube多線程爬取'),
            ('crawl_ptt_with_pool', 'PTT多線程爬取'),
            ('crawl_threads_with_pool', 'Threads多線程爬取'),
            ('max_workers', '多線程配置')
        ]
        
        missing = []
        for check, desc in checks:
            if check in content:
                logger.info(f"   ✅ {desc}: 找到 {check}")
            else:
                missing.append(f"{desc}: 缺少 {check}")
        
        if missing:
            logger.error(f"   ❌ 缺少實現: {missing}")
            return False
        else:
            logger.info("   ✅ 內容爬取順序機制實現正確")
            return True
            
    except Exception as e:
        logger.error(f"❌ 驗證內容爬取順序機制失敗: {e}")
        return False

def verify_data_processing_flow():
    """驗證數據處理流程"""
    logger.info("🔍 驗證數據處理流程...")
    
    try:
        processor_file = os.path.join(os.path.dirname(__file__), 'crawler', 'complete_data_processor.py')
        
        with open(processor_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查處理步驟
        steps = [
            ('step1_merge_platform_data', '合併平台數據'),
            ('step2_create_user_format', '創建用戶格式'),
            ('step3_gemini_analysis', 'Gemini情感分析'),
            ('step4_mongodb_storage', 'MongoDB存儲'),
            ('processed/alldata', 'alldata目錄'),
            ('processed/user_data', 'user_data目錄'),
            ('processed/final_data', 'final_data目錄')
        ]
        
        missing = []
        for step, desc in steps:
            if step in content:
                logger.info(f"   ✅ {desc}: 找到 {step}")
            else:
                missing.append(f"{desc}: 缺少 {step}")
        
        if missing:
            logger.error(f"   ❌ 缺少實現: {missing}")
            return False
        else:
            logger.info("   ✅ 數據處理流程實現正確")
            return True
            
    except Exception as e:
        logger.error(f"❌ 驗證數據處理流程失敗: {e}")
        return False

def verify_main_integration():
    """驗證主程序整合"""
    logger.info("🔍 驗證主程序整合...")
    
    try:
        main_file = os.path.join(os.path.dirname(__file__), 'main.py')
        
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查整合邏輯
        integrations = [
            ('OptimizedCrawlerManager', '優化爬蟲管理器'),
            ('crawl_politician_optimized', '優化爬取方法'),
            ('complete_data_processor', '完整數據處理器'),
            ('for i, legislator in enumerate', '逐一處理立委'),
            ('skip_processing', '跳過處理選項')
        ]
        
        missing = []
        for integration, desc in integrations:
            if integration in content:
                logger.info(f"   ✅ {desc}: 找到 {integration}")
            else:
                missing.append(f"{desc}: 缺少 {integration}")
        
        if missing:
            logger.error(f"   ❌ 缺少整合: {missing}")
            return False
        else:
            logger.info("   ✅ 主程序整合實現正確")
            return True
            
    except Exception as e:
        logger.error(f"❌ 驗證主程序整合失敗: {e}")
        return False

def verify_user_format():
    """驗證用戶格式"""
    logger.info("🔍 驗證用戶格式...")
    
    expected_format = {
        "user_id": {
            "comments": [
                {
                    "標題": "文章標題",
                    "留言內容": "用戶留言",
                    "情感標籤": "NEGATIVE/POSITIVE",
                    "情緒": "anger/joy/neutral",
                    "日期": "2025-07-08",
                    "source": "youtube/ptt/threads"
                }
            ],
            "latest_date": "2025-07-08",
            "comment_count": 1
        }
    }
    
    logger.info("📋 預期用戶格式:")
    logger.info("   ✅ 以用戶ID為鍵的字典結構")
    logger.info("   ✅ comments數組包含所有評論")
    logger.info("   ✅ 每個評論包含標題、內容、情感、情緒、日期、來源")
    logger.info("   ✅ latest_date為最新評論日期")
    logger.info("   ✅ comment_count為評論總數")
    
    return True

def main():
    """主驗證函數"""
    logger.info("🚀 開始完整流程驗證...")
    
    verifications = [
        ("流程架構", verify_flow_architecture),
        ("URL收集並行機制", verify_url_collection_parallel),
        ("內容爬取順序機制", verify_content_crawling_sequential),
        ("數據處理流程", verify_data_processing_flow),
        ("主程序整合", verify_main_integration),
        ("用戶格式", verify_user_format)
    ]
    
    results = {}
    
    for verification_name, verification_func in verifications:
        logger.info(f"\n{'='*50}")
        logger.info(f"驗證: {verification_name}")
        logger.info(f"{'='*50}")
        
        try:
            results[verification_name] = verification_func()
        except Exception as e:
            logger.error(f"驗證 {verification_name} 時發生異常: {e}")
            results[verification_name] = False
    
    # 總結
    logger.info(f"\n{'='*50}")
    logger.info("完整流程驗證總結")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(verifications)
    
    for verification_name, result in results.items():
        status = "✅ 通過" if result else "❌ 失敗"
        logger.info(f"{verification_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n總計: {passed}/{total} 個驗證通過")
    
    if passed == total:
        logger.info("🎉 完整流程驗證全部通過！")
        logger.info("\n📋 流程確認無誤:")
        logger.info("✅ 多平台並行收集URL → 逐平台多線程爬取 → 數據處理 → 下一位立委")
        logger.info("✅ 所有組件正確實現，流程架構完整")
        return 0
    else:
        logger.warning(f"⚠️ 有 {total - passed} 個驗證失敗")
        return 1

if __name__ == "__main__":
    sys.exit(main())
