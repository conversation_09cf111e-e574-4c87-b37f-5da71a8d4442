"""
多進程處理數據塊的輔助函數
"""

from collections import Counter
from datetime import datetime
import time
import jieba
import os

# 載入停用詞
def load_stopwords():
    """載入停用詞文件，使用緩存提升效率"""
    stopwords = set()

    try:
        stopwords_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'stopwords.txt')
        with open(stopwords_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):  # 忽略空行和註釋
                    stopwords.add(line)
    except Exception as e:
        print(f"⚠️ 載入停用詞失敗: {e}")
        # 使用基本停用詞作為後備
        stopwords = {'的', '了', '是', '我', '也', '和', '就', '都', '不', '在', '會', '要', '很'}

    return stopwords

def process_data_chunk(task):
    """
    多進程中每個進程負責處理的函數
    
    參數:
        task: 包含處理需要的所有數據的字典
        
    返回:
        處理後的部分結果
    """
    try:
        data = task['data']
        sentiment_field = task['sentiment_field']
        emotion_field = task['emotion_field']
        date_field = task['date_field']
        text_fields = task['text_fields']
        positive_sentiments = task['positive_sentiments']
        negative_sentiments = task['negative_sentiments']
        process_wordcloud = task['process_wordcloud']
        chunk_id = task['chunk_id']
        
        
        # 載入停用詞
        stop_words = load_stopwords()
        
        # 初始化結果數據結構
        sentiment_counts = {"support_count": 0, "oppose_count": 0}
        raw_oppose_recall_emotions = Counter()
        raw_support_recall_emotions = Counter()
        word_counter = Counter()  # 總是創建詞頻計數器
        time_series_data = {}
        
        # 記錄開始時間
        start_time = time.time()
        
        # 處理每條數據
        matched_records = 0
        for i, item in enumerate(data):
            # 情感分析處理
            if sentiment_field and sentiment_field in item:
                sentiment = str(item[sentiment_field]).strip().upper()
                
                if sentiment in positive_sentiments:
                    sentiment_counts['oppose_count'] += 1
                    sentiment_key = 'oppose'
                    matched_records += 1
                    
                    # 情緒處理
                    if emotion_field and emotion_field in item:
                        emotion = str(item[emotion_field]).strip().lower()
                        if emotion:
                            raw_oppose_recall_emotions[emotion] += 1
                            
                elif sentiment in negative_sentiments:
                    sentiment_counts['support_count'] += 1
                    sentiment_key = 'support'
                    matched_records += 1
                    
                    # 情緒處理
                    if emotion_field and emotion_field in item:
                        emotion = str(item[emotion_field]).strip().lower()
                        if emotion:
                            raw_support_recall_emotions[emotion] += 1
                else:
                    # 無法判斷的情感，跳過
                    continue
                
                # 時間序列處理
                if date_field and date_field in item:
                    date_str = None
                    if isinstance(item[date_field], datetime):
                        date_str = item[date_field].strftime('%Y-%m-%d')
                    elif isinstance(item[date_field], str):
                        # 嘗試解析日期字符串
                        try:
                            date_str = item[date_field]
                            # 驗證日期格式
                            datetime.strptime(date_str, '%Y-%m-%d')
                        except ValueError:
                            # 日期格式不正確，跳過
                            continue
                        
                    if date_str:
                        if date_str not in time_series_data:
                            time_series_data[date_str] = {"support": 0, "oppose": 0}
                        time_series_data[date_str][sentiment_key] += 1
            
            # 詞雲處理 (只在第一個進程中處理)
            if process_wordcloud and i % 3 == 0:  # 採樣以減少處理量
                text_content = ""
                for field in text_fields:
                    if field in item and item[field]:
                        text_content += str(item[field]) + " "                
                if text_content:
                    # 使用 jieba 切詞
                    words = jieba.cut(text_content)
                    for word in words:
                        word = word.strip()
                        if len(word) >= 2 and not word.isdigit() and word not in stop_words:
                            word_counter[word] += 1
        
        # 計算處理時間
        elapsed_time = time.time() - start_time
        
        # 計算處理結果統計
        print(f"✓ 進程 {chunk_id} 完成處理: 匹配記錄={matched_records}/{len(data)}, 情感={sentiment_counts}, 詞數={len(word_counter)}, 時間序列={len(time_series_data)}個日期, 耗時={elapsed_time:.2f}秒")
        
        # 返回結果
        return {
            'sentiment_counts': sentiment_counts,
            'oppose_emotions': dict(raw_oppose_recall_emotions),
            'support_emotions': dict(raw_support_recall_emotions),
            'word_counter': dict(word_counter),  # 總是返回詞頻計數器
            'time_series': time_series_data,
            'chunk_id': chunk_id
        }
    except Exception as e:
        print(f"❌ 進程 {task.get('chunk_id', 'unknown')} 處理失敗: {str(e)}")
        import traceback
        traceback.print_exc()
        # 返回空結果
        return {
            'sentiment_counts': {"support_count": 0, "oppose_count": 0},
            'oppose_emotions': {},
            'support_emotions': {},
            'word_counter': {},  # 返回空的詞頻計數器而不是None
            'time_series': {},
            'chunk_id': task.get('chunk_id', 'unknown'),
            'error': str(e)
        }
