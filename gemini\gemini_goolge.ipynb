{"cells": [{"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["\n", "import google.generativeai as genai\n", "import csv\n", "import random\n", "import pandas as pd #畫圖的\n", "import seaborn as sns\n", "import os\n", "import google.generativeai as genai\n", "import csv\n", "import json\n", "import time\n", "import random\n", "import re"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["genai.configure(api_key=\"AIzaSyCbhqxVF-jvIDxyzBzlFHJThoF8SQB8ufQ\")\n", "\n", "model = genai.GenerativeModel(\"gemini-1.5-flash\")\n", "response = model.generate_content(\"你是誰??\")\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["謝國樑（1975年10月5日－），是一位中華民國政治人物和企業家。他是中國國民黨籍，出生於基隆市。目前擔任基隆市市長，於2022年基隆市市長選舉中當選。他曾任三屆立法委員。\n", "\n", "<style>\n", ".container {\n", "  align-items: center;\n", "  border-radius: 8px;\n", "  display: flex;\n", "  font-family: Google Sans, Roboto, sans-serif;\n", "  font-size: 14px;\n", "  line-height: 20px;\n", "  padding: 8px 12px;\n", "}\n", ".chip {\n", "  display: inline-block;\n", "  border: solid 1px;\n", "  border-radius: 16px;\n", "  min-width: 14px;\n", "  padding: 5px 16px;\n", "  text-align: center;\n", "  user-select: none;\n", "  margin: 0 8px;\n", "  -webkit-tap-highlight-color: transparent;\n", "}\n", ".carousel {\n", "  overflow: auto;\n", "  scrollbar-width: none;\n", "  white-space: nowrap;\n", "  margin-right: -12px;\n", "}\n", ".headline {\n", "  display: flex;\n", "  margin-right: 4px;\n", "}\n", ".gradient-container {\n", "  position: relative;\n", "}\n", ".gradient {\n", "  position: absolute;\n", "  transform: translate(3px, -9px);\n", "  height: 36px;\n", "  width: 9px;\n", "}\n", "@media (prefers-color-scheme: light) {\n", "  .container {\n", "    background-color: #fafafa;\n", "    box-shadow: 0 0 0 1px #0000000f;\n", "  }\n", "  .headline-label {\n", "    color: #1f1f1f;\n", "  }\n", "  .chip {\n", "    background-color: #ffffff;\n", "    border-color: #d2d2d2;\n", "    color: #5e5e5e;\n", "    text-decoration: none;\n", "  }\n", "  .chip:hover {\n", "    background-color: #f2f2f2;\n", "  }\n", "  .chip:focus {\n", "    background-color: #f2f2f2;\n", "  }\n", "  .chip:active {\n", "    background-color: #d8d8d8;\n", "    border-color: #b6b6b6;\n", "  }\n", "  .logo-dark {\n", "    display: none;\n", "  }\n", "  .gradient {\n", "    background: linear-gradient(90deg, #fafafa 15%, #fafafa00 100%);\n", "  }\n", "}\n", "@media (prefers-color-scheme: dark) {\n", "  .container {\n", "    background-color: #1f1f1f;\n", "    box-shadow: 0 0 0 1px #ffffff26;\n", "  }\n", "  .headline-label {\n", "    color: #fff;\n", "  }\n", "  .chip {\n", "    background-color: #2c2c2c;\n", "    border-color: #3c4043;\n", "    color: #fff;\n", "    text-decoration: none;\n", "  }\n", "  .chip:hover {\n", "    background-color: #353536;\n", "  }\n", "  .chip:focus {\n", "    background-color: #353536;\n", "  }\n", "  .chip:active {\n", "    background-color: #464849;\n", "    border-color: #53575b;\n", "  }\n", "  .logo-light {\n", "    display: none;\n", "  }\n", "  .gradient {\n", "    background: linear-gradient(90deg, #1f1f1f 15%, #1f1f1f00 100%);\n", "  }\n", "}\n", "</style>\n", "<div class=\"container\">\n", "  <div class=\"headline\">\n", "    <svg class=\"logo-light\" width=\"18\" height=\"18\" viewBox=\"9 9 35 35\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n", "      <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M42.8622 27.0064C42.8622 25.7839 42.7525 24.6084 42.5487 23.4799H26.3109V30.1568H35.5897C35.1821 32.3041 33.9596 34.1222 32.1258 35.3448V39.6864H37.7213C40.9814 36.677 42.8622 32.2571 42.8622 27.0064V27.0064Z\" fill=\"#4285F4\"/>\n", "      <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.3109 43.8555C30.9659 43.8555 34.8687 42.3195 37.7213 39.6863L32.1258 35.3447C30.5898 36.3792 28.6306 37.0061 26.3109 37.0061C21.8282 37.0061 18.0195 33.9811 16.6559 29.906H10.9194V34.3573C13.7563 39.9841 19.5712 43.8555 26.3109 43.8555V43.8555Z\" fill=\"#34A853\"/>\n", "      <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.6559 29.8904C16.3111 28.8559 16.1074 27.7588 16.1074 26.6146C16.1074 25.4704 16.3111 24.3733 16.6559 23.3388V18.8875H10.9194C9.74388 21.2072 9.06992 23.8247 9.06992 26.6146C9.06992 29.4045 9.74388 32.022 10.9194 34.3417L15.3864 30.8621L16.6559 29.8904V29.8904Z\" fill=\"#FBBC05\"/>\n", "      <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.3109 16.2386C28.85 16.2386 31.107 17.1164 32.9095 18.8091L37.8466 13.8719C34.853 11.082 30.9659 9.3736 26.3109 9.3736C19.5712 9.3736 13.7563 13.245 10.9194 18.8875L16.6559 23.3388C18.0195 19.2636 21.8282 16.2386 26.3109 16.2386V16.2386Z\" fill=\"#EA4335\"/>\n", "    </svg>\n", "    <svg class=\"logo-dark\" width=\"18\" height=\"18\" viewBox=\"0 0 48 48\" xmlns=\"http://www.w3.org/2000/svg\">\n", "      <circle cx=\"24\" cy=\"23\" fill=\"#FFF\" r=\"22\"/>\n", "      <path d=\"M33.76 34.26c2.75-2.56 4.49-6.37 4.49-11.26 0-.89-.08-1.84-.29-3H24.01v5.99h8.03c-.4 2.02-1.5 3.56-3.07 4.56v.75l3.91 2.97h.88z\" fill=\"#4285F4\"/>\n", "      <path d=\"M15.58 25.77A8.845 8.845 0 0 0 24 31.86c1.92 0 3.62-.46 4.97-1.31l4.79 3.71C31.14 36.7 27.65 38 24 38c-5.93 0-11.01-3.4-13.45-8.36l.17-1.01 4.06-2.85h.8z\" fill=\"#34A853\"/>\n", "      <path d=\"M15.59 20.21a8.864 8.864 0 0 0 0 5.58l-5.03 3.86c-.98-2-1.53-4.25-1.53-6.64 0-2.39.55-4.64 1.53-6.64l1-.22 3.81 2.98.22 1.08z\" fill=\"#FBBC05\"/>\n", "      <path d=\"M24 14.14c2.11 0 4.02.75 5.52 1.98l4.36-4.36C31.22 9.43 27.81 8 24 8c-5.93 0-11.01 3.4-13.45 8.36l5.03 3.85A8.86 8.86 0 0 1 24 14.14z\" fill=\"#EA4335\"/>\n", "    </svg>\n", "    <div class=\"gradient-container\"><div class=\"gradient\"></div></div>\n", "  </div>\n", "  <div class=\"carousel\">\n", "    <a class=\"chip\" href=\"https://vertexaisearch.cloud.google.com/grounding-api-redirect/AQXblrys5rCHE3SF_niVshHonHfNXPA9BN7prnALpV-LZ84OSgts0BvuA6aAnayyQlIRJMDyC-xUq-Rh_1ucK8OBOyJbXF9kshRy8t3zPrWmgkBOupx8_2anPodbkX3G7P3yuleLFu63JJkxvctAvGEx1G9Wi6-D26LzdG0uTcD488AzhFCmYSMqaQhPy1M0s4kGt3Cy_GKXGOvhvsRKMD2krep6jR31EOOjuegeIXGfV2h10w==\">謝國樑 是誰</a>\n", "  </div>\n", "</div>\n", "\n"]}], "source": ["from google import genai\n", "from google.genai.types import Tool, GenerateContentConfig, GoogleSearch\n", "\n", "# Replace with your actual API key\n", "api_key = \"AIzaSyCbhqxVF-jvIDxyzBzlFHJThoF8SQB8ufQ\"\n", "\n", "client = genai.Client(api_key=api_key)\n", "\n", "model_id = \"gemini-2.0-flash\"\n", "\n", "google_search_tool = Tool(\n", "    google_search=GoogleSearch()\n", ")\n", "\n", "response = client.models.generate_content(\n", "    model=model_id,\n", "    contents=\"謝國樑是誰?\",\n", "    config=GenerateContentConfig(\n", "        tools=[google_search_tool],\n", "        response_modalities=[\"TEXT\"],\n", "    )\n", ")\n", "\n", "for each in response.candidates[0].content.parts:\n", "    print(each.text)\n", "\n", "# To get grounding metadata as web content.\n", "print(response.candidates[0].grounding_metadata.search_entry_point.rendered_content)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["葉元之最近發生了什麼?,**辦公室勞檢與職場霸凌指控：**\n", "\n", "*   葉元之被指控職場霸凌助理，導致助理因工作壓力過大病逝。\n", "*   台北市勞動局於3月18日前往葉元之立法院辦公室進行勞動檢查，發現現場無當事勞工的出勤紀錄，也未設置職場暴力預防措施，因此被要求限期一個月改善。\n", "*   勞動部長洪申翰表示，政治人物應善待助理，並願意與立法院討論勞檢流程。\n", "葉元之最近發生了什麼?,**前助理告別式爭議：**\n", "\n", "*   葉元之前助理方旭的告別式上，出現了「汪喵都歡迎，葉元之莫進。離塵遠遊去，慣老闆俱燼」的掛軸，表達家屬對葉元之的強烈不滿。\n", "*   方旭的遺孀公開發文，控訴葉元之在方旭住院前退保，導致其在住院期間沒有勞保保障，並批評葉元之「冷血無情」。\n", "葉元之最近發生了什麼?,**被爆料要求司機違規：**\n", "\n", "*   有報導指出，葉元之為了趕通告，要求公務車司機違規，但罰單卻要司機繳納。葉元之否認此指控，並聲稱一定會提告。\n", "葉元之最近發生了什麼?,**面臨罷免：**\n", "\n", "*   葉元之目前正麵臨罷免危機，罷免團體已經啟動第二階段連署，並在短時間內收到超過萬份連署書。\n", "\n", "總之，葉元之最近爭議纏身，包括職場霸凌指控、勞檢不合格、與前助理家屬的糾紛，以及罷免危機等。\n", "\n"]}], "source": ["def generate_content_with_param(contents):\n", "    response = client.models.generate_content(\n", "        model=model_id,\n", "        contents=contents,\n", "        config=GenerateContentConfig(\n", "            tools=[google_search_tool],\n", "            response_modalities=[\"TEXT\"],\n", "        )\n", "    )\n", "\n", "    # 取得生成的內容\n", "    response_text = response.candidates[0].content.parts[0].text\n", "\n", "    # 去除每個項目前的 \"*\" 符號並加入內容前綴\n", "    lines = response_text.split(\"\\n\")\n", "    formatted_lines = []\n", "\n", "    # 從第三行開始處理\n", "    for i, line in enumerate(lines):\n", "        if i >= 2:  # 跳過前兩行\n", "            if line.startswith(\"*\"):\n", "                # 去除每個項目的 \"*\" 並加入參數內容作為前綴\n", "                formatted_lines.append(f\"{contents},{line[1:].strip()}\")\n", "            else:\n", "                formatted_lines.append(line.strip())\n", "\n", "    return formatted_lines\n", "    # 打印與該內容相關的搜索元數據\n", "    # print(response.candidates[0].grounding_metadata.search_\n", "formatted_lines = generate_content_with_param(\"葉元之最近發生了什麼?\")\n", "for line in formatted_lines:\n", "    print(line)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["\n", "def generate_json_from_data(raw_data: str):\n", "    prompt = f\"\"\"\n", "注意如果文本中有\"*\"符號 請把它刪除掉保持文檔的乾淨\n", "以下是我整理的一些問答內容，請你幫我將它們轉換為標準 JSON 格式，每個問答對應一個物件，格式如下：\n", "[\n", "  {{\n", "    \"question\": \"問題內容\",\n", "    \"answer\": \"對應答案\"\n", "  }},\n", "  ...\n", "]\n", "\n", "請依此格式輸出，不需要多餘說明，只給我 JSON 結果。\n", "以下是原始資料：\n", "{raw_data}\n", "\"\"\"\n", "\n", "    response = client.models.generate_content(\n", "        model=\"gemini-2.0-flash\",  # 使用 pro 模型較適合摘要與結構化輸出 model_id = \"gemini-2.0-flash\"\n", "        contents=[prompt]\n", "    )\n", "\n", "    # 回傳 Gemini 整理後的 JSON 結果（純文字）\n", "    return response.text\n", "\n", "\n", "# 🔸 呼叫整理成 JSON\n", "#json_output = generate_json_from_data(data_to_save)\n", "#print(json_output)\n"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [], "source": ["\n", "def append_to_json(text, output_filename=\"train_data_QA.json\"):\n", "    \"\"\"\n", "    從字串中提取 \"question\" 和 \"answer\" 並新增到現有的 JSON 檔案中。\n", "    如果檔案不存在，則創建一個新的檔案。\n", "\n", "    Args:\n", "        text (str): 包含問題和答案的字串。\n", "        output_filename (str, optional): 輸出 JSON 檔案的名稱。預設為 \"extracted_data.json\"。\n", "    \"\"\"\n", "    extracted_data = []\n", "    matches = re.findall(r'{\\s*\"question\":\\s*\"([^\"]*)\",\\s*\"answer\":\\s*\"([^\"]*)\"\\s*}', text)\n", "\n", "    for match in matches:\n", "        question = match[0]\n", "        answer = match[1]\n", "        extracted_data.append({\"question\": question, \"answer\": answer})\n", "\n", "    if extracted_data:\n", "        existing_data = []\n", "        if os.path.exists(output_filename):\n", "            try:\n", "                with open(output_filename, 'r', encoding='utf-8') as f:\n", "                    existing_data = json.load(f)\n", "            except json.JSONDecodeError:\n", "                print(f\"警告：現有的 {output_filename} 檔案不是有效的 JSON 格式，將會被覆蓋。\")\n", "            except FileNotFoundError:\n", "                # 這不應該發生，因為我們已經檢查過檔案是否存在\n", "                pass\n", "\n", "        # 將新提取的資料加入到現有資料中\n", "        combined_data = existing_data + extracted_data\n", "\n", "        try:\n", "            with open(output_filename, 'w', encoding='utf-8') as f:\n", "                json.dump(combined_data, f, ensure_ascii=False, indent=4)\n", "            print(f\"已成功提取並新增資料至 {output_filename}\")\n", "        except Exception as e:\n", "            print(f\"保存 JSON 檔案時發生錯誤：{e}\")\n", "    else:\n", "        print(\"未在字串中找到符合模式的 'question' 和 'answer'。\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["questions = [\n", "    \"葉元之最近發生了什麼?\",\n", "    \"葉元之是誰?\",\n", "    \"葉元之的背景?\",\n", "    \"葉元之的政見?\",\n", "    \"葉元之的學歷?\",\n", "    \"葉元之的最近的新聞發生了什麼?\",\n", "    \"葉元之的罷免案?\",\n", "    \"葉元之的罷免案的進度?\",\n", "    \"葉元之的罷免案的連署人?\",\n", "    \"葉元之的罷免案的罷免理由?\",\n", "    \"葉元之的罷免案的罷免時間?\",\n", "    \"葉元之的在國民黨的地位?\",\n", "    \"國民黨的立委有哪些?\",\n", "    \"國民黨的背景?\",\n", "    \"國民黨最近發生了甚麼?\",\n", "    \"民進黨最近發生了甚麼?\",\n", "    \"民進黨的立委有哪些?\",\n", "    \"民進黨的歷史?\",\n", "    \"民進黨的背景\",\n", "    \"葉元之最近提出了哪些法案或提案？\",\n", "    \"葉元之在立法院的委員會是哪些？\",\n", "    \"葉元之對台灣的能源政策有什麼看法？\",\n", "    \"除了罷免案，葉元之還面臨過其他爭議嗎？\",\n", "    \"最近國民黨在兩岸關係上有什麼新的表態？\",\n", "    \"國民黨內有哪些不同派系？\",\n", "    \"國民黨青年世代的政治人物有哪些值得關注？\",\n", "    \"民進黨目前在台灣社會面臨的主要批評是什麼？\",\n", "    \"民進黨在推動轉型正義方面做了哪些努力？\",\n", "    \"民進黨的能源政策與國民黨有何不同？\",\n", "    \"除了立委，民進黨目前在地方執政的情況如何？\",\n", "    \"台灣民眾對國民黨和民進黨的支持度近期有什麼變化？\",\n", "    \"葉元之所屬的選區是哪個？這個選區的政治生態如何？\",\n", "    \"國民黨和民進黨在面對台灣的國際空間議題上有哪些不同的策略？\",\n", "    \"未來一年內，台灣政壇可能出現哪些重要的政治議題或事件？\"\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["json_output:\n", "```json\n", "[\n", "  {\n", "    \"question\": \"葉元之的最近的新聞發生了什麼?\",\n", "    \"answer\": \"葉元之的最近的新聞發生了什麼?前助理過世爭議： 葉元之的前助理過世，告別式外出現「葉元之莫進」的看板，引起爭議。家屬指控葉元之在助理身體不適時立即准辭和退保，導致住院期間無勞健保。葉元之則表示尊重家屬，並強調自己沒有辭退該助理，退保程序由立院人事處負責。目前，北市勞檢處已介入調查。\\n葉元之的最近的新聞發生了什麼?遭控霸凌爭議： 資深媒體人吳崑玉爆料葉元之疑似霸凌助理，助理的遺孀也指控葉元之准辭和退保導致助理無勞健保。政治粉專《不演了新聞台》則認為，此事出現180度轉變。\\n葉元之的最近的新聞發生了什麼?反罷免活動： 國民黨在葉元之選區舉行「還錢於民」政策說明會，黨主席朱立倫表示這場仗很重要。\\n葉元之的最近的新聞發生了什麼?史書華衝民眾黨宣講會： 網紅牙醫史書華衝到民眾黨的宣講會現場，要求黃國昌簽署罷免葉元之的連署書，與民眾黨幕僚發生推擠。\\n葉元之的最近的新聞發生了什麼?辦公室遭勞檢： 葉元之的國會辦公室遭到勞檢。葉元之表示，沒有不當辭退助理。\"\n", "  }\n", "]\n", "```\n", "已成功提取並新增資料至 train_data_QA0322.json\n", "休息 8 秒...\n"]}], "source": ["questions = [\n", "    \"慣老闆 葉元之\"\n", " \n", "]\n", "\n", "# 儲存問答結果的列表\n", "data_to_save = \"\"\n", "\n", "# 執行每個問題並儲存一段完整答案\n", "for question in questions:\n", "    formatted_lines = generate_content_with_param(question)\n", "\n", "    if formatted_lines:  # 有回答才儲存\n", "        full_answer = \"\\n\".join(formatted_lines).strip().replace(\",\", \"\")  # 組成一段並移除逗號\n", "        data_to_save = f\"Q: {question}\\nA: {full_answer}\\n\\n\"\n", "        json_output = generate_json_from_data(data_to_save)\n", "        print(\"json_output:\\n\" + json_output)\n", "        append_to_json(json_output,\"train_data_QA0322.json\")\n", "        sleep_time = random.randint(5, 15)\n", "        print(f\"休息 {sleep_time} 秒...\")\n", "        time.sleep(sleep_time)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["csv_filename = \"question_answers.csv\"\n", "with open(csv_filename, mode='w', newline='', encoding='utf-8') as file:\n", "    writer = csv.writer(file)\n", "    writer.writerow([\"Question\", \"Answer\"])\n", "    writer.writerows(data_to_save)\n", "\n", "print(f\"結果已儲存為 {csv_filename}\")"]}], "metadata": {"kernelspec": {"display_name": "transformers", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}