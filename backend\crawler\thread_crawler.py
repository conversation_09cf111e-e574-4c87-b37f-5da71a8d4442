from dotenv import load_dotenv
load_dotenv()
import os
import csv
import json
import time
import random
import threading
import queue
import tempfile
import shutil
import traceback
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, SessionNotCreatedException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re

THREADS_EMAIL = os.environ.get('THREADS_EMAIL')
THREADS_PASSWORD = os.environ.get('THREADS_PASSWORD')

def initialize_driver(headless=True, retries=3, thread_id=None):
    """
    建立Chrome瀏覽器驅動，支援headless模式與自動重試
    
    參數:
        headless: 是否使用無頭模式 (預設為True)
        retries: 重試次數 (預設為3次)
        thread_id: 執行緒ID，用於生成唯一的 remote-debugging-port
        
    返回:
        webdriver: Chrome瀏覽器驅動實例
    """
    # 設定獨立的臨時目錄以避免多執行緒衝突
    for attempt in range(retries):
        temp_dir = None
        try:
            # 為每個執行緒創建一個臨時目錄
            temp_dir = tempfile.mkdtemp(prefix=f"threads_crawler_{thread_id or 'main'}_")
            print(f"使用臨時目錄: {temp_dir}")
            
            options = Options()
            
            # 設定 headless 模式
            if headless:
                options.add_argument("--headless=new")  # 使用新版 headless 模式
            
            # 基本設定
            options.add_argument('--mute-audio')
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")  # 避免 GPU 相關問題
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-infobars")
            
            # 設定唯一的 remote-debugging-port 以避免衝突
            port = 9222 + (thread_id or 0) % 1000  # 確保端口在一個合理範圍內
            options.add_argument(f"--remote-debugging-port={port}")
            
            # 使用臨時目錄作為用戶數據目錄
            options.add_argument(f"--user-data-dir={temp_dir}")
            options.add_argument(f"--disk-cache-dir={os.path.join(temp_dir, 'cache')}")
            
            options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            options.add_argument("--window-size=1920,1080")
            options.add_argument("--start-maximized")  # 啟動時最大化
            options.add_argument("--log-level=3")
            
            # 禁用可能導致 WebDriver 會話問題的功能
            options.add_argument("--disable-features=NetworkService,NetworkServiceInProcess")
            options.add_argument("--disable-background-networking")
            
            # 添加性能優化選項
            options.add_argument("--disable-default-apps")
            options.add_argument("--disable-popup-blocking")
            options.add_argument("--disable-sync")
            options.add_argument("--disable-translate")
            
            # 通過環境變量禁用共享內存使用
            os.environ['CHROME_SHARED_MEM_TYPE'] = 'none'
            
            # 使用明確的 chromedriver 路徑
            service = Service(ChromeDriverManager().install())
            
            # 設置明確的超時，避免卡住
            service.start()
            
            driver = webdriver.Chrome(service=service, options=options)
            driver._temp_dir = temp_dir  # 存儲臨時目錄以便稍後清理
            print(f"成功建立Threads爬蟲Chrome瀏覽器驅動 (執行緒ID: {thread_id or 'main'}){' (headless模式)' if headless else ''}")
            return driver
        except SessionNotCreatedException as e:
            if attempt < retries - 1:
                print(f"建立Threads爬蟲Chrome瀏覽器驅動失敗 (嘗試 {attempt+1}/{retries}): {e}")
                # 在重試前等待一些時間，避免短時間內創建過多瀏覽器實例
                time.sleep(2 + attempt * 2)  # 隨著重試次數增加等待時間
                
                # 清理臨時目錄
                if temp_dir and os.path.exists(temp_dir):
                    try:
                        shutil.rmtree(temp_dir, ignore_errors=True)
                    except:
                        pass
            else:
                print(f"在 {retries} 次嘗試後無法建立Threads爬蟲Chrome瀏覽器驅動: {e}")
                raise
        except Exception as e:
            print(f"建立Threads爬蟲Chrome瀏覽器驅動時發生非預期錯誤: {e}")
            if attempt < retries - 1:
                time.sleep(2 + attempt * 2)
                
                # 清理臨時目錄
                if temp_dir and os.path.exists(temp_dir):
                    try:
                        shutil.rmtree(temp_dir, ignore_errors=True)
                    except:
                        pass
            else:
                raise

def login_to_threads(driver, username, password):
    driver.set_window_size(1920, 1080)
    login_url = "https://www.threads.net/login/?hl=zh-tw"
    driver.get(login_url)
    try:
        username_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "input[autocomplete='username']"))
        )
        password_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='password']"))
        )
        username_input.send_keys(username)
        password_input.send_keys(password)
        password_input.send_keys(Keys.RETURN)

        print("登入成功或登入過程已開始。")
        time.sleep(15)
    except Exception as e:
        print(f"登入過程中出現問題: {e}")

def search_in_threads(driver, input_name):
    """在 Threads 中搜尋關鍵字"""
    time.sleep(5)
    driver.get("https://www.threads.net/search")
    time.sleep(3)
    
    print(f"🔍 在 Threads 搜尋: {input_name}")
    
    # 嘗試多種搜尋框選擇器
    search_selectors = [
        'input[type="search"]',
        'input[placeholder*="Search"]',
        'input[placeholder*="搜尋"]',
        'input[aria-label*="Search"]',
        'input[aria-label*="搜尋"]',
        '//input[@type="search"]',
        '//input[contains(@placeholder, "Search")]'
    ]
    
    search_box = None
    for selector in search_selectors:
        try:
            if selector.startswith('//'):
                search_box = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
            else:
                search_box = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
            print(f"   ✅ 找到搜尋框: {selector}")
            break
        except:
            print(f"   ❌ 搜尋框選擇器失效: {selector}")
            continue
    
    if search_box:
        try:
            search_box.clear()
            search_box.send_keys(input_name)
            search_box.send_keys(Keys.RETURN)
            print(f"   ✅ 成功搜尋: {input_name}")
            time.sleep(3)
        except Exception as e:
            print(f"   ❌ 輸入搜尋關鍵字失敗: {e}")
    
    else:
        print(f"   ❌ 無法找到搜尋框，嘗試直接導航...")
        # 嘗試直接導航到搜尋結果頁面
        search_url = f"https://www.threads.net/search?q={input_name}"
        driver.get(search_url)
        time.sleep(3)

def scrape_post_urls_with_time(driver, max_scroll_attempts=10, cutoff_date=None):
    """
    收集 Threads 貼文URL並同時抓取發布時間，支援日期篩選
    
    參數:
        driver: WebDriver 實例
        max_scroll_attempts: 最大滾動次數
        cutoff_date: 截止日期，只收集此日期之後的貼文
        
    返回:
        list: 包含 URL 和時間資訊的字典列表
    """
    post_data = []
    processed_urls = set()
    counter = 0
    last_height = driver.execute_script("return document.body.scrollHeight")
    
    print(f"🔍 開始收集 Threads 貼文URL和時間，最大滾動次數: {max_scroll_attempts}")
    if cutoff_date:
        if isinstance(cutoff_date, str):
            try:
                cutoff_date = datetime.strptime(cutoff_date, '%Y-%m-%d')
            except:
                cutoff_date = None
        print(f"📅 將篩選 {cutoff_date.strftime('%Y-%m-%d') if cutoff_date else '無'} 之後的貼文")
    
    while counter <= max_scroll_attempts:
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        
        try:
            # 找到所有貼文容器
            post_containers = driver.find_elements(By.CSS_SELECTOR, "article, div[role='article']")
            
            new_posts_found = 0
            for container in post_containers:
                try:
                    # 找到貼文連結
                    post_link = None
                    link_elements = container.find_elements(By.CSS_SELECTOR, "a[href*='/post/']")
                    
                    for link in link_elements:
                        url = link.get_attribute('href')
                        if url and '/post/' in url:
                            post_link = url
                            break
                    
                    if not post_link or post_link in processed_urls:
                        continue
                    
                    # 找到時間元素
                    time_info = None
                    relative_time = None
                    
                    # 方法1: 尋找 time 元素並優先使用 datetime 屬性（最精確）
                    try:
                        time_elements = container.find_elements(By.CSS_SELECTOR, "time")
                        for time_elem in time_elements:
                            datetime_attr = time_elem.get_attribute('datetime')
                            if datetime_attr:
                                try:
                                    # 解析 ISO 格式時間 (例如 2025-07-08T00:55:17.000Z)
                                    parsed_time = datetime.fromisoformat(datetime_attr.replace('Z', '+00:00'))
                                    time_info = parsed_time.strftime('%Y-%m-%d')
                                    
                                    # 同時獲取相對時間文字用於顯示
                                    try:
                                        # 先嘗試獲取 abbr 元素內的文字
                                        abbr_elems = time_elem.find_elements(By.CSS_SELECTOR, "abbr")
                                        if abbr_elems:
                                            relative_time = abbr_elems[0].text.strip()
                                            # 如果仍是空的，嘗試獲取 span 元素
                                            if not relative_time:
                                                span_elems = time_elem.find_elements(By.CSS_SELECTOR, "span span")
                                                if span_elems:
                                                    relative_time = span_elems[0].text.strip()
                                        else:
                                            # 嘗試直接獲取 span 元素
                                            span_elems = time_elem.find_elements(By.CSS_SELECTOR, "span")
                                            if span_elems:
                                                relative_time = span_elems[0].text.strip()
                                                # 嘗試更深層的 span
                                                if not relative_time:
                                                    inner_spans = time_elem.find_elements(By.CSS_SELECTOR, "span span")
                                                    if inner_spans:
                                                        relative_time = inner_spans[0].text.strip()
                                    except Exception as e:
                                        print(f"⚠️  無法從時間元素獲取相對時間文字: {e}")
                                        relative_time = "Unknown"
                                    
                                    print(f"🕐 找到精確時間: datetime='{datetime_attr}' -> date='{time_info}', text='{relative_time}'")
                                    break
                                except Exception as e:
                                    print(f"⚠️  解析 datetime 屬性失敗: {datetime_attr} -> {e}")
                                    continue
                    except Exception as e:
                        print(f"⚠️  查找 time 元素失敗: {e}")
                    
                    # 方法2: 如果沒找到 time 元素，嘗試找包含時間文字的元素
                    if not time_info:
                        try:
                            time_text_elements = container.find_elements(By.XPATH, ".//span[contains(text(), '小時') or contains(text(), '分鐘') or contains(text(), '天') or contains(text(), '週') or contains(text(), '月')]")
                            for elem in time_text_elements:
                                text = elem.text.strip()
                                if any(keyword in text for keyword in ['小時', '分鐘', '天', '週', '月']):
                                    relative_time = text
                                    # 使用現有的時間轉換函數
                                    current_time = datetime.now()
                                    converted_time = convert_relative_time_to_datetime(text, current_time)
                                    if isinstance(converted_time, datetime):
                                        time_info = converted_time.strftime('%Y-%m-%d')
                                        print(f"🕐 相對時間轉換: '{text}' -> '{time_info}'")
                                    break
                        except Exception as e:
                            print(f"⚠️  查找時間文字失敗: {e}")
                    
                    # 如果還是沒找到時間，使用當前時間
                    if not time_info:
                        time_info = datetime.now().strftime('%Y-%m-%d')
                        relative_time = "Unknown"
                        print(f"⚠️  未找到時間信息，使用當前日期: {time_info}")
                    
                    # 日期篩選
                    if cutoff_date and time_info != datetime.now().strftime('%Y-%m-%d'):
                        try:
                            post_date = datetime.strptime(time_info, '%Y-%m-%d')
                            if post_date < cutoff_date:
                                print(f"⏳ 跳過舊貼文: {relative_time} ({time_info})")
                                continue
                            else:
                                print(f"✅ 收集新貼文: {relative_time} ({time_info})")
                        except:
                            pass  # 日期解析失敗，仍然收集
                    
                    # 添加到結果
                    processed_urls.add(post_link)
                    post_entry = {
                        'url': post_link,
                        'time': relative_time,
                        'date': time_info,
                        'added_time': datetime.now().strftime('%Y-%m-%d')
                    }
                    post_data.append(post_entry)
                    new_posts_found += 1
                    
                except Exception as e:
                    print(f"⚠️  處理貼文容器時出錯: {e}")
                    continue
            
            # 顯示進度
            if new_posts_found > 0:
                print(f"🔄 Threads: 已收集 {len(post_data)} 個貼文 (+{new_posts_found})")
            elif counter % 3 == 0:
                print(f"🔄 Threads: 已收集 {len(post_data)} 個貼文 (滾動 {counter}/{max_scroll_attempts})")
            
        except Exception as e:
            print(f"❌ 滾動 {counter} 時出錯: {e}")
        
        counter += 1
        time.sleep(2)
        
        # 檢查是否到達頁面底部
        new_height = driver.execute_script("return document.body.scrollHeight")
        if new_height == last_height:
            print(f"📄 已到達頁面底部")
            break
        last_height = new_height
    
    driver.execute_script("window.scrollTo(0, 0)")
    time.sleep(2)
    
    # 輸出時間統計
    if post_data:
        dates = [entry['date'] for entry in post_data if entry['date'] != datetime.now().strftime('%Y-%m-%d')]
        if dates:
            print(f"📊 收集的貼文日期範圍: {min(dates)} 到 {max(dates)}")
    
    print(f"✅ Threads: 搜尋完成，共收集 {len(post_data)} 個貼文（包含時間資訊）")
    return post_data

# 保留原有函數作為向後兼容
def scrape_post_urls(driver, max_scroll_attempts=10):
    """向後兼容的函數，只返回URL列表"""
    post_data = scrape_post_urls_with_time(driver, max_scroll_attempts)
    return [entry['url'] for entry in post_data]

def fetch_users(driver):
    unique_users = []
    user_elements = driver.find_elements(By.CSS_SELECTOR, '.x6s0dn4.x78zum5.x1q0g3np')
    for user_element in user_elements:
        text = user_element.text.replace('\n', '').strip()
        unique_users.append(text)
    unique_users = [user for user in unique_users if user]
    return unique_users

def fetch_comments(driver):
    detailed_soup = BeautifulSoup(driver.page_source, 'html.parser')
    time.sleep(2)
    driver.execute_script("window.scrollTo(0, 0);")
    spans = detailed_soup.select('span.x1lliihq.x1plvlek.xryxfnj.x1n2onr6.x193iq5w.xeuugli.x1fj9vlw.x13faqbe.x1vvkbs.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.x1i0vuye.xjohtrz.xo1l8bm.xp07o12.x1yc453h.xat24cr.xdj266r')
    unique_article = [span.text.strip() for span in spans]
    return unique_article

def fetch_likes(driver):
    soupss = BeautifulSoup(driver.page_source, 'lxml')
    all_pages = soupss.select('body._ammd._aqff.system-fonts--body.segoe')
    all_pages_combined = ' '.join([page.text for page in all_pages])
    likes = re.findall(r'讚(\d+)', all_pages_combined)
    if likes:
        likes = likes[1:]
    return [int(like) for like in likes]

def fetch_times(driver):
    detailed_soup = BeautifulSoup(driver.page_source, 'html.parser')
    time_elements = detailed_soup.select('span.html-span.xdj266r.x11i5rnm.xat24cr.x1mh8g0r.xexx8yu.x4uap5.x18d9i69.xkhd6sd.x1hl2dhg.x16tdsg8.x1vvkbs')
    unique_times = [time_element.text.strip() for time_element in time_elements]
    return unique_times

def clean_comments1(comments):
    return [comment.replace('\xa0', ' ').replace('\n', ' ').replace('翻譯', '') for comment in comments]

# --- 新增的時間轉換函式 ---
def convert_relative_time_to_datetime(relative_time_str, current_scrape_time):
    """
    將相對時間字串（例如「3小時」、「17分鐘」、「1天」）
    轉換為基於抓取時間的實際 datetime 物件。
    
    參數:
        relative_time_str: 相對時間字串 (例如 "2小時", "5分鐘", "3天")
        current_scrape_time: 當前爬蟲時間，作為基準時間
    
    返回:
        datetime 物件或原始字串（如果無法解析）
    """
    relative_time_str = relative_time_str.strip()
    
    # 統一處理數字部分，處理可能的字符編碼問題
    digits_map = {'０': '0', '１': '1', '２': '2', '３': '3', '４': '4', 
                 '５': '5', '６': '6', '７': '7', '８': '8', '９': '9'}
    
    for char, digit in digits_map.items():
        relative_time_str = relative_time_str.replace(char, digit)
    
    # 特殊處理：Threads常見的「剛剛」表示
    if "剛剛" in relative_time_str or relative_time_str == "現在" or relative_time_str == "now":
        return current_scrape_time
    
    # 特殊處理：處理「昨天」和「前天」等表示
    if "昨天" in relative_time_str:
        return current_scrape_time - timedelta(days=1)
    if "前天" in relative_time_str:
        return current_scrape_time - timedelta(days=2)
    if "今天" in relative_time_str:
        return current_scrape_time
    
    # 正則表達式匹配數字和單位
    match = re.search(r'(\d+)\s*(秒|分鐘|小時|天|週|月|年)', relative_time_str)
    if match:
        value = int(match.group(1))
        unit = match.group(2)
        
        if "秒" in unit:
            return current_scrape_time - timedelta(seconds=value)
        elif "分鐘" in unit:
            return current_scrape_time - timedelta(minutes=value)
        elif "小時" in unit:
            return current_scrape_time - timedelta(hours=value)
        elif "天" in unit:
            return current_scrape_time - timedelta(days=value)
        elif "週" in unit:
            return current_scrape_time - timedelta(weeks=value)
        elif "月" in unit: # 處理「X個月」或「X月」
            return current_scrape_time - timedelta(days=value * 30) # 簡化為30天
        elif "年" in unit:
            return current_scrape_time - timedelta(days=value * 365) # 簡化為365天
    
    # 處理英文表示
    eng_match = re.search(r'(\d+)\s*(s|sec|second|seconds|m|min|minute|minutes|h|hr|hour|hours|d|day|days|w|week|weeks|mo|month|months|y|yr|year|years)', relative_time_str, re.IGNORECASE)
    if eng_match:
        value = int(eng_match.group(1))
        unit = eng_match.group(2).lower()
        
        if unit in ['s', 'sec', 'second', 'seconds']:
            return current_scrape_time - timedelta(seconds=value)
        elif unit in ['m', 'min', 'minute', 'minutes']:
            return current_scrape_time - timedelta(minutes=value)
        elif unit in ['h', 'hr', 'hour', 'hours']:
            return current_scrape_time - timedelta(hours=value)
        elif unit in ['d', 'day', 'days']:
            return current_scrape_time - timedelta(days=value)
        elif unit in ['w', 'week', 'weeks']:
            return current_scrape_time - timedelta(weeks=value)
        elif unit in ['mo', 'month', 'months']:
            return current_scrape_time - timedelta(days=value * 30)
        elif unit in ['y', 'yr', 'year', 'years']:
            return current_scrape_time - timedelta(days=value * 365)
    
    # 嘗試解析可能是 "MM月DD日" 的格式
    if "月" in relative_time_str and "日" in relative_time_str and "年" not in relative_time_str:
        try:
            month_day_match = re.findall(r'(\d+)[月日]', relative_time_str)
            if len(month_day_match) >= 2:
                month, day = map(int, month_day_match[:2])
                # 假設是今年
                return datetime(current_scrape_time.year, month, day, 0, 0, 0)
        except ValueError:
            pass # 解析失敗則繼續下一個嘗試
    
    # 嘗試解析可能是 "YYYY年MM月DD日" 的格式
    if "年" in relative_time_str and "月" in relative_time_str and "日" in relative_time_str:
        try:
            year_month_day_match = re.findall(r'(\d+)[年月日]', relative_time_str)
            if len(year_month_day_match) >= 3:
                year, month, day = map(int, year_month_day_match[:3])
                return datetime(year, month, day, 0, 0, 0)
        except ValueError:
            pass # 解析失敗則繼續下一個嘗試
    
    # 無法轉換則返回原始字串
    return relative_time_str # 無法轉換則返回原始字串
# --- 時間轉換函式結束 ---

def save_to_json(user_list, time_list, content_list, reply_users, reply_comments, reply_likes, reply_times, file_name):
    main_post = {
        "用戶": user_list[0] if user_list else '',
        "時間": time_list[0] if time_list else '',
        "內容": content_list[0] if content_list else ''
    }
    replies = []
    for i in range(len(reply_comments)):
        reply = {
            "用戶": reply_users[i] if i < len(reply_users) else '',
            "留言": reply_comments[i],
            "按讚數": reply_likes[i] if i < len(reply_likes) else 0,
            "時間": reply_times[i] if i < len(reply_times) else ''
        }
        replies.append(reply)
    data = {
        "主貼文": main_post,
        "回覆": replies
    }
    if os.path.isfile(file_name):
        with open(file_name, "r", encoding="utf-8") as file:
            existing_data = json.load(file)
        existing_data.append(data)
    else:
        existing_data = [data]
    with open(file_name, "w", encoding="utf-8") as file:
        json.dump(existing_data, file, ensure_ascii=False, indent=4)
    print(f"已保存至 {file_name}")

def get_logged_in_cookies(driver):
    return driver.get_cookies()

def set_cookies_to_driver(driver, cookies):
    try:
        driver.get("https://www.threads.net")
    except Exception:
        pass
    for cookie in cookies:
        try:
            driver.add_cookie(cookie)
        except Exception:
            pass
    try:
        driver.refresh()
        time.sleep(2)
    except Exception:
        pass
        
def extract_post_id_from_url(url):
    """
    從 Threads 的 URL 中提取貼文 ID
    
    參數:
        url: Threads 貼文的完整 URL
        
    返回:
        貼文 ID 字串或 None
    """
    if not url:
        return None
        
    # 嘗試多種 Threads URL 格式
    # 格式1: https://www.threads.net/@username/post/XYZ
    match1 = re.search(r'/post/([^/?&#]+)', url)
    if match1:
        return match1.group(1)
    
    # 格式2: https://www.threads.net/t/XYZ 或 https://threads.net/t/XYZ
    match2 = re.search(r'/t/([^/?&#]+)', url)
    if match2:
        return match2.group(1)
    
    # 格式3: 可能的短 URL 格式
    match3 = re.search(r'threads\.net/([^/?&#]+)$', url)
    if match3:
        return match3.group(1)
    
    # 如果沒有找到匹配的模式，嘗試提取最後一個路徑部分
    parts = url.rstrip('/').split('/')
    if parts and parts[-1] and not parts[-1].startswith(('?', '#')):
        if len(parts[-1]) > 5:  # 假設 ID 至少有 5 個字符
            return parts[-1]
    
    return None

def crawl_single_url(driver, url, cookies):
    """
    爬取單一 Threads 貼文 URL
    
    參數:
        driver: WebDriver 實例
        url: 要爬取的 URL
        cookies: 登入 cookies
    
    返回:
        (貼文資料, 錯誤訊息)
    """
    # 設置頁面加載超時
    driver.set_page_load_timeout(30)
    
    # 設置 cookies
    try:
        set_cookies_to_driver(driver, cookies)
    except Exception as e:
        print(f"設置 cookies 時出錯: {e}")
    
    retry_count = 0
    max_retries = 3
    last_error = None
    
    while retry_count < max_retries:
        try:
            # 嘗試載入頁面
            print(f"嘗試載入頁面: {url} (嘗試 {retry_count+1}/{max_retries})")
            driver.get(url)
            
            # 等待頁面主要元素加載
            element_present = False
            wait_time = 30  # 初始等待時間
            
            try:
                WebDriverWait(driver, wait_time).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, '.x6s0dn4.x78zum5.x1q0g3np'))
                )
                element_present = True
            except TimeoutException:
                # 如果超時，增加等待時間再試一次
                try:
                    print(f"頁面載入超時 ({wait_time}秒)，增加等待時間再試一次...")
                    WebDriverWait(driver, wait_time * 2).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, '.x6s0dn4.x78zum5.x1q0g3np'))
                    )
                    element_present = True
                except TimeoutException:
                    print(f"頁面載入超時 ({wait_time*2}秒)，可能是網絡問題或頁面結構變化")
            
            # 如果頁面元素未載入，重試
            if not element_present:
                retry_count += 1
                print(f"頁面未完全載入，將重試 ({retry_count}/{max_retries})")
                # 清除瀏覽器緩存和cookies，重新設置
                try:
                    driver.delete_all_cookies()
                    driver.execute_script("window.localStorage.clear();")
                    driver.execute_script("window.sessionStorage.clear();")
                    set_cookies_to_driver(driver, cookies)
                except Exception as e:
                    print(f"清除瀏覽器狀態時出錯: {e}")
                time.sleep(5)  # 等待一段時間後重試
                continue
            
            # 給頁面一些額外時間完全載入JS和動態內容
            time.sleep(random.uniform(3, 5))

            # 在這裡獲取爬取當下時間作為基準
            current_scrape_time = datetime.now()
            
            # 提取貼文 ID
            post_id = extract_post_id_from_url(url)
            if not post_id:
                print(f"無法從URL提取貼文ID: {url}")

            try:
                # 抓取頁面數據
                unique_users = fetch_users(driver)
                unique_article = fetch_comments(driver)
                likes = fetch_likes(driver)
                
                # 直接從 DOM 中獲取時間元素，優先使用 datetime 屬性
                main_post_time = None
                main_post_relative_time = "Unknown"
                main_post_formatted_time = None
                
                try:
                    # 尋找時間元素 - 優先使用 time 元素的 datetime 屬性
                    time_elements = driver.find_elements(By.CSS_SELECTOR, "time[datetime]")
                    if time_elements:
                        for time_elem in time_elements:
                            datetime_attr = time_elem.get_attribute('datetime')
                            if datetime_attr:
                                try:
                                    # 解析 ISO 格式時間
                                    main_post_time = datetime.fromisoformat(datetime_attr.replace('Z', '+00:00'))
                                    main_post_formatted_time = main_post_time.strftime("%Y-%m-%d")
                                    
                                    # 獲取相對時間文字
                                    try:
                                        abbr_elem = time_elem.find_element(By.CSS_SELECTOR, "abbr")
                                        if abbr_elem:
                                            main_post_relative_time = abbr_elem.text.strip()
                                            if not main_post_relative_time:
                                                span_elem = abbr_elem[0].find_element(By.CSS_SELECTOR, "span")
                                                if span_elem:
                                                    main_post_relative_time = span_elem.text.strip()
                                    except:
                                        try:
                                            # 嘗試直接獲取相對時間
                                            span_elem = time_elem.find_element(By.CSS_SELECTOR, "span")
                                            if span_elem:
                                                main_post_relative_time = span_elem.text.strip()
                                        except:
                                            main_post_relative_time = "Unknown"
                                    
                                    print(f"🕐 主貼文時間: {main_post_formatted_time} ({main_post_relative_time})")
                                    break
                                except Exception as e:
                                    print(f"⚠️  解析貼文時間出錯: {e}")
                except Exception as e:
                    print(f"⚠️  獲取貼文時間元素失敗: {e}")
                
                # 如果無法從 datetime 屬性獲取時間，嘗試使用傳統方法
                if not main_post_time:
                    unique_times = fetch_times(driver)
                    if unique_times and len(unique_times) > 0:
                        main_post_relative_time = unique_times[0]
                        main_post_time = convert_relative_time_to_datetime(main_post_relative_time, current_scrape_time)
                        main_post_formatted_time = main_post_time.strftime("%Y-%m-%d") if isinstance(main_post_time, datetime) else main_post_relative_time
                        print(f"🕐 通過相對時間獲取主貼文時間: {main_post_formatted_time} (相對時間: {main_post_relative_time})")
                
                # 如果仍然無法獲取時間，使用當前時間
                if not main_post_formatted_time:
                    main_post_formatted_time = current_scrape_time.strftime("%Y-%m-%d")
                    print(f"⚠️  無法獲取主貼文時間，使用當前時間: {main_post_formatted_time}")
                
                # 檢查是否成功抓取到主要數據
                if not unique_users or not unique_article:
                    print(f"未能獲取到足夠的數據, 用戶: {len(unique_users)}, 文章: {len(unique_article)}")
                    # 捲動頁面嘗試加載更多內容
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(2)
                    driver.execute_script("window.scrollTo(0, 0);")
                    time.sleep(2)
                    
                    # 再次嘗試抓取
                    unique_users = fetch_users(driver)
                    unique_article = fetch_comments(driver)
                    likes = fetch_likes(driver)
                
                # 處理獲取到的數據
                final_likes = [likes[i] if i < len(likes) else 0 for i in range(len(unique_article))]
                user_comments = clean_comments1(unique_article)

                # 為確保用戶列表與評論匹配，如果用戶列表不足，補齊空字串
                for i in range(len(user_comments)):
                    if i >= len(unique_users):
                        unique_users.append('')

                # 處理回覆時間
                unique_times = fetch_times(driver) if 'unique_times' not in locals() else unique_times
                converted_reply_times = []
                for i in range(1, len(unique_times) if unique_times else 0): # 從第二個時間（回覆時間）開始
                    converted_time = convert_relative_time_to_datetime(unique_times[i], current_scrape_time)
                    formatted_time = converted_time.strftime("%Y-%m-%d") if isinstance(converted_time, datetime) else converted_time
                    converted_reply_times.append(formatted_time)
                
                # 為每個回覆生成唯一 ID
                replies_with_ids = []
                for i in range(len(user_comments) - 1):
                    reply_id = None
                    if post_id and unique_users[i+1] and i < len(converted_reply_times):
                        # 創建唯一 ID：貼文ID-用戶名-時間戳
                        reply_id = f"{post_id}-{unique_users[i+1]}-{converted_reply_times[i]}"
                    
                    replies_with_ids.append({
                        "threads_comment_id": reply_id,
                        "用戶": unique_users[i+1] if i + 1 < len(unique_users) else '',
                        "留言": user_comments[i+1],
                        "按讚數": final_likes[i+1] if i + 1 < len(final_likes) else 0,
                        "時間": converted_reply_times[i] if i < len(converted_reply_times) else ''
                    })

                # 構建最終數據結構
                post_data = {
                    "source": "threads",
                    "threads_post_id": post_id,
                    "threads_post_url": url,
                    "主貼文": {
                        "用戶": unique_users[0] if unique_users else '',
                        "時間": main_post_formatted_time,
                        "相對時間": main_post_relative_time,
                        "內容": user_comments[0] if user_comments else ''
                    },
                    "回覆": replies_with_ids,
                    "抓取時間": current_scrape_time.strftime("%Y-%m-%d %H:%M:%S")
                }
                
                # 檢查數據是否有效
                is_main_post_empty = not any(post_data["主貼文"].values())
                is_replies_empty = not post_data["回覆"]
                
                if not is_main_post_empty:  # 只要主貼文有內容就接受
                    return post_data, None
                else:
                    print(f"主貼文內容為空，可能未能正確抓取: {url}")
                    retry_count += 1
            except Exception as e:
                print(f"處理頁面數據時出錯: {e}")
                last_error = f"處理數據錯誤: {str(e)}"
                retry_count += 1
                time.sleep(3)  # 等待一段時間後重試
                
        except TimeoutException as te:
            print(f"頁面載入超時: {te}")
            last_error = f"頁面載入超時: {str(te)}"
            retry_count += 1
            
            # 重新初始化瀏覽器
            try:
                driver.quit()
                driver = initialize_driver(True)
                if driver is not None:
                    driver.set_page_load_timeout(30)
                    set_cookies_to_driver(driver, cookies)
            except Exception as e:
                print(f"重新初始化瀏覽器時出錯: {e}")
                
            time.sleep(5)  # 等待一段時間後重試
            
        except Exception as e:
            print(f"爬取URL時發生未預期錯誤: {e}")
            last_error = f"未預期錯誤: {str(e)}"
            retry_count += 1
            time.sleep(3)  # 等待一段時間後重試
    
    # 如果所有重試都失敗
    return None, last_error or f"重試{max_retries}次後仍然失敗"

def fetch_url_with_timeout(target, args=(), timeout=180):
    """
    帶超時功能的URL抓取函数，避免執行緒卡住
    
    參數:
        target: 目標函數
        args: 傳遞給目標函數的參數
        timeout: 超時時間（秒）
    
    返回:
        (結果, 錯誤訊息)
    """
    result = {}
    event = threading.Event()
    
    def wrapper():
        try:
            value, error = target(*args)
            result['value'] = value
            result['error'] = error
        except Exception as e:
            result['value'] = None
            result['error'] = str(e)
        finally:
            event.set()  # 告訴主執行緒此工作已完成
    
    # 創建一個執行緒運行目標函數
    t = threading.Thread(target=wrapper, daemon=True)
    t.start()
    
    # 等待執行緒完成或超時
    is_timeout = not event.wait(timeout)
    
    # 如果執行緒仍在運行，表示發生超時
    if is_timeout:
        driver = args[0] if args and isinstance(args[0], webdriver.Chrome) else None
        if driver:
            try:
                # 嘗試強制關閉頁面
                print(f"操作超時 ({timeout}秒)，嘗試強制停止頁面載入...")
                driver.execute_script("window.stop();")
                
                # 嘗試使用新的執行緒優雅地關閉瀏覽器
                def close_browser():
                    try:
                        driver.close()
                    except:
                        try:
                            driver.quit()
                        except:
                            pass
                
                # 不阻塞主執行緒，嘗試關閉瀏覽器
                threading.Thread(target=close_browser, daemon=True).start()
            except:
                pass
        return None, f'操作超時（{timeout}秒）'
    
    # 檢查結果中是否有錯誤
    if 'error' in result and result['error']:
        return result.get('value'), result.get('error')
    
    return result.get('value'), result.get('error')

def crawl_worker(url_queue, cookies, headless, result_list, lock, failed_log_path=None, name=None, last_crawled_time=None, worker_id=1):
    """
    工作執行緒函數：處理佇列中的URL
    
    參數:
        url_queue: URL佇列
        cookies: 已登入的cookies
        headless: 是否使用無界面模式
        result_list: 結果列表
        lock: 執行緒鎖
        failed_log_path: 失敗URL日誌路徑
        name: 搜尋關鍵字
        last_crawled_time: 上次爬取時間
        worker_id: 工作執行緒ID
    """
    print(f"Threads爬蟲工作執行緒 {worker_id} 開始執行")
    driver = None
    processed_urls = 0
    last_driver_restart = time.time()
    max_driver_time = 30 * 60  # 30分鐘後強制重啟瀏覽器
    
    try:
        while True:
            try:
                # 設置超時，避免佇列卡住
                url = url_queue.get(timeout=10)  # 設置10秒超時，避免永久阻塞
            except queue.Empty:
                print(f"Threads爬蟲工作執行緒 {worker_id} 佇列為空，退出")
                break
                
            if url is None:
                url_queue.task_done()
                print(f"Threads爬蟲工作執行緒 {worker_id} 收到退出信號")
                break
                
            # 檢查是否需要重啟瀏覽器
            current_time = time.time()
            driver_running_time = current_time - last_driver_restart
            
            # 如果瀏覽器運行時間超過閾值或處理了5個URL，則重啟瀏覽器
            need_restart = (processed_urls > 0 and processed_urls % 5 == 0) or (driver_running_time > max_driver_time)
            
            # 如果需要重啟或還沒有創建瀏覽器，則初始化瀏覽器
            if driver is None or need_restart:
                # 先關閉現有瀏覽器（如果有）
                if driver is not None:
                    try:
                        print(f"Threads爬蟲工作執行緒 {worker_id} 重啟瀏覽器 (已處理 {processed_urls} URLs, 運行時間 {driver_running_time:.1f} 秒)")
                        driver.quit()
                        # 清理臨時目錄
                        if hasattr(driver, '_temp_dir') and driver._temp_dir and os.path.exists(driver._temp_dir):
                            try:
                                shutil.rmtree(driver._temp_dir, ignore_errors=True)
                            except Exception as e:
                                print(f"清理臨時目錄時出錯: {e}")
                    except Exception as e:
                        print(f"關閉瀏覽器時出錯: {e}")
                    driver = None
                
                # 創建新的瀏覽器實例
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        # 使用執行緒ID初始化瀏覽器
                        driver = initialize_driver(headless, thread_id=worker_id)
                        print(f"Threads爬蟲工作執行緒 {worker_id} 成功建立瀏覽器")
                        last_driver_restart = time.time()
                        break
                    except Exception as e:
                        print(f"Threads爬蟲工作執行緒 {worker_id} 嘗試 {attempt+1}/{max_retries} 建立瀏覽器失敗: {e}")
                        if attempt < max_retries - 1:
                            time.sleep(3 * (attempt + 1))  # 逐漸增加等待時間
                        else:
                            # 最後一次嘗試失敗，記錄錯誤並繼續下一個URL
                            print(f"Threads爬蟲工作執行緒 {worker_id} 建立瀏覽器失敗")
                
            # 如果仍然無法建立瀏覽器，跳過此URL
            if driver is None:
                print(f"Threads爬蟲工作執行緒 {worker_id} 無法建立瀏覽器，跳過URL: {url}")
                url_queue.task_done()
                # 將URL標記為失敗並記錄
                if failed_log_path and name:
                    with lock:
                        record_failed_url(failed_log_path, name, url, "無法建立瀏覽器")
                continue
                
            try:
                print(f"Threads爬蟲工作執行緒 {worker_id} 處理URL: {url}")
                post_data, error = fetch_url_with_timeout(
                    crawl_single_url, args=(driver, url, cookies), timeout=180
                )
                processed_urls += 1
                
                if post_data:
                    # 檢查貼文日期是否晚於上次爬取時間
                    should_add = True
                    if last_crawled_time:
                        main_post_time_str = post_data["主貼文"]["時間"]
                        try:
                            # 嘗試多種日期格式
                            main_post_time = None
                            for date_format in ['%Y-%m-%d', '%Y/%m/%d', '%m/%d/%Y', '%d/%m/%Y', '%Y年%m月%d日']:
                                try:
                                    main_post_time = datetime.strptime(main_post_time_str, date_format)
                                    break
                                except ValueError:
                                    continue
                            

                            if main_post_time and main_post_time <= last_crawled_time:
                                print(f"跳過舊貼文: {url}, 日期為 {main_post_time.strftime('%Y-%m-%d')} (早於 {last_crawled_time.strftime('%Y-%m-%d')})")
                                should_add = False
                            elif main_post_time:
                                print(f"處理新貼文: {url}, 日期為 {main_post_time.strftime('%Y-%m-%d')} (晚於 {last_crawled_time.strftime('%Y-%m-%d')})")
                        except (ValueError, TypeError, AttributeError) as e:
                            print(f"解析貼文日期時出錯: {main_post_time_str}, 錯誤: {e}")
                            # 如果日期格式無法解析，仍然保留貼文
                            print(f"無法解析日期，預設處理貼文: {url}")
                            pass
                    
                    if should_add:
                        with lock:
                            # 檢查這個貼文 ID 是否已經存在於結果列表中
                            post_id = post_data.get("threads_post_id")
                            existing_post = next((p for p in result_list if p.get("threads_post_id") == post_id), None)
                            
                            if existing_post:
                                # 更新現有貼文
                                existing_post.update(post_data)
                                print(f"更新貼文: {url}")
                            else:
                                # 添加新貼文
                                result_list.append(post_data)
                                print(f"添加新貼文: {url}")
                else:
                    print(f"Threads爬蟲工作執行緒 {worker_id}: {url} 失敗，原因: {error}")
                    if failed_log_path is not None and name is not None:
                        with lock:
                            record_failed_url(failed_log_path, name, url, error)
            except Exception as e:
                print(f"Threads爬蟲工作執行緒 {worker_id} 處理URL時發生錯誤: {url}, 錯誤: {e}")
                if failed_log_path is not None and name is not None:
                    with lock:
                        record_failed_url(failed_log_path, name, url, str(e))
            finally:
                # 清除緩存，釋放記憶體
                if driver:
                    try:
                        driver.execute_script("window.sessionStorage.clear();")
                        driver.execute_script("window.localStorage.clear();")
                        driver.execute_script("window.stop();")
                    except Exception as e:
                        print(f"清除瀏覽器緩存時出錯: {e}")
                
                # 關閉當前 URL 的處理
                url_queue.task_done()
    except Exception as e:
        print(f"Threads爬蟲工作執行緒 {worker_id} 發生未預期錯誤: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 爬蟲執行結束，關閉瀏覽器
        if driver:
            try:
                print(f"Threads爬蟲工作執行緒 {worker_id} 結束工作，關閉瀏覽器")
                driver.quit()
                # 清理臨時目錄
                if hasattr(driver, '_temp_dir') and driver._temp_dir and os.path.exists(driver._temp_dir):
                    try:
                        shutil.rmtree(driver._temp_dir, ignore_errors=True)
                    except Exception as e:
                        print(f"清理臨時目錄時出錯: {e}")
            except Exception as e:
                print(f"Threads爬蟲工作執行緒 {worker_id} 關閉瀏覽器時出錯: {e}")

def record_failed_url(log_path, name, url, error):
    """記錄失敗的URL到日誌文件"""
    try:
        if os.path.exists(log_path):
            with open(log_path, 'r', encoding='utf-8') as f:
                try:
                    failed_data = json.load(f)
                except Exception:
                    failed_data = {}
        else:
            failed_data = {}
        
        if name not in failed_data:
            failed_data[name] = []
        
        # 添加帶有時間戳和錯誤信息的失敗記錄
        failed_data[name].append({
            "url": url,
            "error": str(error),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(failed_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"記錄失敗URL時出錯: {e}")

def multi_thread_crawl_posts(post_urls, cookies, output_path, headless=True, num_threads=1, name=None, 
                       last_crawled_time=None, processed_post_ids=None):
    """
    多執行緒爬取貼文，支援增量式更新
    
    參數:
        post_urls: 要爬取的貼文URL列表
        cookies: 登入cookies
        output_path: 輸出路徑
        headless: 是否使用無界面模式
        num_threads: 執行緒數量
        name: 搜尋關鍵字
        last_crawled_time: 上次爬取的時間點
        processed_post_ids: 已處理過的貼文ID集合
    """
    start_time = time.time()
    print(f"開始多執行緒爬取，使用 {num_threads} 個執行緒，headless={headless}")
    
    # 建立輸出目錄（如果不存在）
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 載入已有的資料（如果存在）
    results = []
    if os.path.exists(output_path):
        try:
            with open(output_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            print(f"已載入 {len(results)} 筆現有資料")
        except Exception as e:
            print(f"載入現有資料時出錯: {e}")
            # 如果檔案損壞，建立備份並從頭開始
            if os.path.getsize(output_path) > 0:
                backup_path = f"{output_path}.bak.{int(time.time())}"
                try:
                    import shutil
                    shutil.copy2(output_path, backup_path)
                    print(f"已建立損壞檔案的備份: {backup_path}")
                except Exception as be:
                    print(f"建立備份時出錯: {be}")
    
    # 檢查URL列表
    if not post_urls:
        print("沒有URL需要處理")
        return results
    
    print(f"共有 {len(post_urls)} 個URL需要處理")
    
    # 初始化執行緒安全的資源
    lock = threading.Lock()
    url_queue = queue.Queue()
    
    # 將URL加入佇列，同時過濾已處理的貼文
    urls_to_process = []
    for url in post_urls:
        # 檢查URL是否包含已處理過的貼文ID
        post_id = extract_post_id_from_url(url)
        if processed_post_ids and post_id in processed_post_ids:
            print(f"跳過已處理過的貼文: {post_id}")
            continue
        urls_to_process.append(url)
    
    print(f"過濾後實際需要處理的URL數量: {len(urls_to_process)}")
    
    # 如果沒有URL需要處理，直接返回
    if not urls_to_process:
        print("過濾後沒有URL需要處理")
        return results
    
    # 根據URL數量調整執行緒數
    adjusted_num_threads = min(num_threads, len(urls_to_process))
    if adjusted_num_threads < num_threads:
        print(f"由於URL數量較少，調整執行緒數量為: {adjusted_num_threads}")
        num_threads = adjusted_num_threads
    
    # 計算每個執行緒要處理的URL數量，進行分批處理
    batch_size = max(1, min(20, len(urls_to_process) // num_threads))  # 每批最多20個URL
    print(f"每個執行緒將分批處理，每批 {batch_size} 個URL")
    
    # 分批加入佇列，避免一次性加入太多URL
    for i, url in enumerate(urls_to_process):
        url_queue.put(url)
        
        # 每加入一批URL，就暫停一下，避免佇列一次性填充太多
        if (i + 1) % (batch_size * num_threads) == 0 and i < len(urls_to_process) - 1:
            print(f"已加入 {i+1} 個URL到佇列，暫停一下...")
            time.sleep(1)  # 短暫暫停，讓執行緒有機會處理佇列
    
    # 添加結束標記
    for _ in range(num_threads):
        url_queue.put(None)
    
    failed_log_path = os.path.join(os.path.dirname(output_path), 'failed.json')
    threads = []
    
    # 啟動工作執行緒
    for i in range(num_threads):
        worker_id = i + 1
        t = threading.Thread(
            target=crawl_worker, 
            args=(url_queue, cookies, headless, results, lock, failed_log_path, name, last_crawled_time, worker_id), 
            daemon=True,
            name=f"Threads-Worker-{worker_id}"  # 為執行緒設置名稱，方便調試
        )
        threads.append(t)
        t.start()
        # 添加更長的延遲，避免同時啟動多個瀏覽器造成資源競爭
        time.sleep(3)
    
    # 定期保存中間結果，避免長時間爬蟲中斷導致的資料丟失
    total_urls = len(urls_to_process)
    save_interval = max(1, min(50, total_urls // 10))  # 每完成約10%的工作就保存一次，但最多50條
    
    try:
        # 監控爬蟲進度
        last_remaining = url_queue.qsize() - num_threads  # 減去結束標記
        last_save_time = time.time()
        last_active_check = time.time()
        stalled_count = 0
        
        while any(t.is_alive() for t in threads):
            try:
                # 每3秒檢查一次進度
                time.sleep(3)
                
                # 計算進度
                remaining = url_queue.qsize() - num_threads  # 減去結束標記
                remaining = max(0, remaining)  # 確保不是負數
                processed = total_urls - remaining
                
                # 檢查是否有進度變化
                progress_changed = remaining != last_remaining
                
                # 如果進度有變化，輸出進度信息
                if progress_changed:
                    progress = (processed / total_urls) * 100 if total_urls > 0 else 100
                    elapsed = time.time() - start_time
                    speed = processed / elapsed if elapsed > 0 else 0
                    
                    print(f"進度: {processed}/{total_urls} ({progress:.1f}%), "
                          f"速度: {speed:.2f} URLs/秒, 已用時間: {elapsed:.1f}秒")
                    
                    # 如果處理了足夠多的URL或者過了足夠長的時間，保存中間結果
                    if (processed % save_interval == 0 or 
                            time.time() - last_save_time > 300):  # 5分鐘自動保存一次
                        with lock:
                            try:
                                with open(output_path, 'w', encoding='utf-8') as f:
                                    json.dump(results, f, ensure_ascii=False, indent=2)
                                print(f"已儲存中間結果，共 {len(results)} 筆資料")
                                last_save_time = time.time()
                            except Exception as e:
                                print(f"儲存中間結果時出錯: {e}")
                    
                    last_remaining = remaining
                    stalled_count = 0  # 重置停滯計數
                else:
                    # 如果超過一定時間沒有進度變化，檢查執行緒健康狀況
                    current_time = time.time()
                    if current_time - last_active_check > 60:  # 每分鐘檢查一次
                        print("檢查執行緒活動狀態...")
                        active_threads = [t for t in threads if t.is_alive()]
                        print(f"當前活動執行緒數: {len(active_threads)}/{len(threads)}")
                        
                        # 記錄沒有進度的次數
                        stalled_count += 1
                        
                        # 如果連續10分鐘沒有進度，可能是卡住了
                        if stalled_count >= 10:
                            print(f"警告: 爬蟲似乎已停滯 {stalled_count} 分鐘，考慮重啟...")
                            # 這裡可以添加重啟邏輯，例如保存當前進度並重新啟動爬蟲
                        
                        last_active_check = current_time
                        
                        # 即使沒有進度也定期保存
                        if current_time - last_save_time > 600:  # 10分鐘強制保存一次
                            with lock:
                                try:
                                    with open(output_path, 'w', encoding='utf-8') as f:
                                        json.dump(results, f, ensure_ascii=False, indent=2)
                                    print(f"已強制儲存中間結果，共 {len(results)} 筆資料")
                                    last_save_time = current_time
                                except Exception as e:
                                    print(f"強制儲存中間結果時出錯: {e}")
            except Exception as e:
                print(f"監控爬蟲進度時出錯: {e}")
                
            # 檢查是否所有執行緒都已完成或卡住
            if all(not t.is_alive() for t in threads):
                print("所有執行緒已完成")
                break
                
    except KeyboardInterrupt:
        print("收到中斷信號，等待執行緒安全退出...")
        # 讓執行緒有時間安全退出
        for t in threads:
            t.join(timeout=30)
        print("執行緒已安全退出")
    finally:
        # 等待所有執行緒結束
        for t in threads:
            try:
                t.join(timeout=10)
            except:
                pass
                
        # 不管發生什麼，都確保儲存目前的結果
        try:
            with lock:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"已儲存最終結果，共 {len(results)} 筆資料")
        except Exception as e:
            print(f"儲存最終結果時出錯: {e}")
    
    # 計算總耗時
    total_time = time.time() - start_time
    print(f"多執行緒爬取完成! 共處理 {total_urls} 個URL，耗時 {total_time:.1f} 秒，"
          f"平均速度: {total_urls/total_time:.2f} URLs/秒 (最終結果數: {len(results)})")
    
    return results

def crawl_threads_all_in_one(
    name,
    output_dir=None,
    headless=True,
    num_threads=4,
    ext_driver=None,
    ext_cookies=None,
    last_crawled_time=None
):
    """
    一行完成 threads 登入、搜尋、取得 post_urls 並多執行緒爬取所有貼文，支援增量式更新
    
    參數:
        name: 要搜尋的關鍵字
        output_dir: 輸出目錄
        headless: 是否使用無界面模式
        num_threads: 執行緒數量
        ext_driver: 外部提供的 WebDriver 實例
        ext_cookies: 外部提供的 cookies
        last_crawled_time: 上次爬取的時間點 (datetime 物件)
    """
    start_time = time.time()
    
    # 設定預設輸出目錄
    if output_dir is None:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(current_dir, 'data', 'threads')
    
    print(f"開始爬取 Threads 關鍵字: {name}, headless={headless}, 執行緒數量={num_threads}")
    
    # 確保輸出目錄存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 建立href目錄 - 修正路徑
    current_dir = os.path.dirname(os.path.abspath(__file__))
    href_dir = os.path.join(current_dir, 'href', 'threads')
    os.makedirs(href_dir, exist_ok=True)
    
    # 輸出和URL檔案路徑 - 統一命名格式
    out_path = os.path.join(output_dir, f'{name}.json')
    post_urls_path = os.path.join(href_dir, f'{name}.json')
    
    driver = None
    close_driver = False
    
    try:
        # 使用外部提供的 driver 或建立新的 driver
        if ext_driver is not None:
            driver = ext_driver
            print("使用外部提供的 WebDriver")
        else:
            try:
                print("為搜尋階段建立新的 WebDriver...")
                driver = initialize_driver(headless=headless, thread_id=0)  # 使用thread_id=0表示主執行緒
                close_driver = True  # 標記需要在結束時關閉 driver
                print("已建立新的 WebDriver")
            except Exception as e:
                print(f"建立 WebDriver 失敗: {e}")
                return None
        
        # 設置 cookies 或登入
        try:
            if ext_cookies is not None:
                set_cookies_to_driver(driver, ext_cookies)
                print("已設置外部提供的 cookies")
            else:
                if not THREADS_EMAIL or not THREADS_PASSWORD:
                    print("缺少 Threads 帳號或密碼，請檢查環境變數")
                    return None
                
                print(f"使用帳號 {THREADS_EMAIL} 登入 Threads...")
                login_to_threads(driver, THREADS_EMAIL, THREADS_PASSWORD)
                print("登入完成，等待頁面載入...")
                time.sleep(10)  # 給予足夠時間登入完成
        except Exception as e:
            print(f"設置 cookies 或登入時出錯: {e}")
            return None
            
        # 搜尋並抓取貼文 URL 和時間
        post_data = []
        try:
            print(f"搜尋關鍵字: {name}")
            search_in_threads(driver, name)
            print("抓取貼文 URL 和時間...")
            
            # 處理 last_crawled_time 參數進行日期篩選
            cutoff_date = None
            if last_crawled_time:
                if isinstance(last_crawled_time, str):
                    try:
                        cutoff_date = datetime.strptime(last_crawled_time, '%Y-%m-%d')
                    except:
                        cutoff_date = None
                elif hasattr(last_crawled_time, 'strftime'):
                    cutoff_date = last_crawled_time
                    
                if cutoff_date:
                    print(f"增量式更新模式，篩選 {cutoff_date.strftime('%Y-%m-%d')} 之後的貼文")
            
            # 使用新的函數收集URL和時間
            post_data = scrape_post_urls_with_time(driver, max_scroll_attempts=10, cutoff_date=cutoff_date)
            print(f"找到 {len(post_data)} 個貼文（包含時間資訊）")
            
            # 檢查是否找到任何資料
            if not post_data:
                print(f"警告: 未找到任何貼文，關鍵字 '{name}' 可能沒有匹配的結果")
                return []
        except Exception as e:
            print(f"搜尋或抓取貼文時出錯: {e}")
            import traceback
            traceback.print_exc()
            return None
        
        # 如果存在上次爬取時間，嘗試載入先前爬取的貼文列表以進行對比
        processed_post_ids = set()
        if last_crawled_time:
            print(f"增量式更新模式，過濾上次爬取（{last_crawled_time}）之前的貼文")
            # 嘗試載入先前的資料以檢查已處理的貼文ID
            try:
                if os.path.exists(out_path):
                    with open(out_path, 'r', encoding='utf-8') as f:
                        previous_data = json.load(f)
                        for post in previous_data:
                            if 'threads_post_id' in post:
                                processed_post_ids.add(post['threads_post_id'])
                    print(f"已載入 {len(processed_post_ids)} 個先前處理過的貼文ID")
            except Exception as e:
                print(f"載入先前資料時出錯: {e}")
        
        # 儲存 post_data 到 href/threads（包含時間資訊）
        try:
            with open(post_urls_path, 'w', encoding='utf-8') as f:
                json.dump(post_data, f, ensure_ascii=False, indent=2)
            print(f"已儲存貼文資料（包含時間）到 {post_urls_path}")
        except Exception as e:
            print(f"儲存貼文資料時出錯: {e}")
        
        # 獲取 cookies
        cookies = ext_cookies if ext_cookies is not None else get_logged_in_cookies(driver)
        
        # 如果我們使用外部提供的 driver，或者已經完成搜尋階段，可以關閉它了
        if ext_driver is None and close_driver:
            try:
                driver.quit()
                # 清理臨時目錄
                if hasattr(driver, '_temp_dir') and driver._temp_dir and os.path.exists(driver._temp_dir):
                    try:
                        shutil.rmtree(driver._temp_dir, ignore_errors=True)
                    except Exception as e:
                        print(f"清理臨時目錄時出錯: {e}")
                driver = None
                print("已關閉搜尋用的 WebDriver")
            except Exception as e:
                print(f"關閉 WebDriver 時出錯: {e}")
        
        # 定期保存中間結果，避免長時間爬蟲中斷導致的資料丟失
        total_urls = len(post_data)
        save_interval = max(1, min(50, total_urls // 10))  # 每完成約10%的工作就保存一次，但最多50條
        
        # 檢查執行緒數量是否合適
        try:
            # 依據 URL 數量調整執行緒數
            if total_urls < num_threads * 2:
                old_num_threads = num_threads
                num_threads = max(1, min(num_threads, total_urls))
                print(f"URL 數量較少 ({total_urls})，調整執行緒數量: {old_num_threads} -> {num_threads}")
        except Exception as e:
            print(f"調整執行緒數量時出錯: {e}")
            
        # 進行網路連線測試
        try:
            import socket
            socket.create_connection(("www.threads.net", 443), timeout=10)
            print("網路連線測試成功")
        except Exception as e:
            print(f"網路連線測試失敗: {e}")
            print("繼續執行，但可能會出現網路問題")
        
        # 執行多執行緒爬取
        print(f"開始多執行緒爬取，使用 {num_threads} 個執行緒")
        
        # 從 post_data 提取 URL 列表傳給多執行緒函數
        post_urls = [entry['url'] for entry in post_data]
        
        results = multi_thread_crawl_posts(
            post_urls, cookies, out_path, 
            headless=headless, 
            num_threads=num_threads, 
            name=name, 
            last_crawled_time=last_crawled_time, 
            processed_post_ids=processed_post_ids
        )
        
        # 檢查結果
        if results is None:
            print("爬取過程中發生錯誤，返回 None")
            return None
            
        if not results:
            print("爬取完成，但沒有獲取到任何結果")
        
        # 計算總耗時
        total_time = time.time() - start_time
        print(f"完成爬取 Threads 關鍵字: {name}, 共 {len(results)} 筆資料, "
              f"耗時 {total_time:.1f} 秒")
        
        return results
    except Exception as e:
        print(f"爬取 Threads 時發生未預期錯誤: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        # 確保 driver 被正確關閉（如果是我們建立的）
        if driver is not None and close_driver:
            try:
                driver.quit()
                # 清理臨時目錄
                if hasattr(driver, '_temp_dir') and driver._temp_dir and os.path.exists(driver._temp_dir):
                    try:
                        shutil.rmtree(driver._temp_dir, ignore_errors=True)
                    except Exception as e:
                        print(f"清理臨時目錄時出錯: {e}")
                print("已關閉 WebDriver")
            except Exception as e:
                print(f"關閉 WebDriver 時出錯: {e}")
        
        # 釋放記憶體
        try:
            import gc
            gc.collect()
        except:
            pass

def crawl_threads_with_pool(name, output_dir, webdriver_pool, last_crawled_time=None, max_threads=3):
    """
    使用 WebDriver 池進行 Threads 爬取

    參數:
        name: 搜尋關鍵字
        output_dir: 輸出目錄
        webdriver_pool: WebDriver 池實例
        last_crawled_time: 上次爬取時間
        max_threads: 最大執行緒數

    返回:
        dict: 爬取結果
    """
    print(f"🚀 使用 WebDriver 池爬取 Threads，關鍵字: {name}")

    try:
        # 確保輸出目錄存在
        os.makedirs(output_dir, exist_ok=True)

        # 設定輸出檔案路徑 - 修正為與其他爬蟲一致的命名
        output_file = os.path.join(output_dir, f"{name}.json")

        # 嘗試使用現有的爬蟲函數
        try:
            # 使用池中的driver進行爬取
            with webdriver_pool.get_driver() as driver:
                print(f"🔍 開始爬取 Threads: {name}")

                # 調用現有的爬蟲函數
                result = crawl_threads_all_in_one(name, output_dir, headless=True, last_crawled_time=last_crawled_time, ext_driver=driver)

                # 檢查實際的輸出檔案（使用正確的路徑）
                actual_output_file = os.path.join(output_dir, f'{name}.json')

                if os.path.exists(actual_output_file):
                    # 讀取結果檔案獲取統計信息
                    try:
                        with open(actual_output_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        return {
                            'success': True,
                            'data_file': actual_output_file,
                            'count': len(data) if isinstance(data, list) else 0,
                            'message': f'成功爬取 {len(data) if isinstance(data, list) else 0} 條Threads資料'
                        }
                    except Exception as read_e:
                        print(f"⚠️ 讀取結果檔案失敗: {read_e}")
                        return {
                            'success': True,
                            'data_file': actual_output_file,
                            'count': 0,
                            'message': 'Threads爬取完成，但無法統計數量'
                        }
                else:
                    print(f"⚠️ Threads爬取未產生結果檔案: {actual_output_file}")
                    # 檢查是否有其他可能的檔案名稱
                    alt_files = [
                        os.path.join(output_dir, f'threads_{name}.json'),
                        os.path.join(output_dir, f'{name}_threads.json')
                    ]

                    for alt_file in alt_files:
                        if os.path.exists(alt_file):
                            print(f"✅ 找到替代檔案: {alt_file}")
                            try:
                                with open(alt_file, 'r', encoding='utf-8') as f:
                                    data = json.load(f)
                                return {
                                    'success': True,
                                    'data_file': alt_file,
                                    'count': len(data) if isinstance(data, list) else 0,
                                    'message': f'成功爬取 {len(data) if isinstance(data, list) else 0} 條Threads資料'
                                }
                            except:
                                continue

                    return {
                        'success': False,
                        'error': 'Threads爬取未產生結果檔案',
                        'count': 0
                    }

        except Exception as crawl_e:
            print(f"❌ Threads爬取過程出錯: {crawl_e}")
            return {
                'success': False,
                'error': f'Threads爬取失敗: {str(crawl_e)}',
                'count': 0
            }

    except Exception as e:
        print(f"❌ Threads爬蟲初始化失敗: {e}")
        return {
            'success': False,
            'error': f'Threads爬蟲初始化失敗: {str(e)}',
            'count': 0
        }
